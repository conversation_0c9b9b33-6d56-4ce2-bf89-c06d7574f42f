I parametri dell'applicazione sono letti direttamente da db e, se non presenti, vengono scritti all'avvio dell'app con dei valori di default.

*****

I log applicativi (diversi da quelli scritti in DB event_log) vengono scritti su file; viene creato un file a ogni avvio dell'app; massimo sei files di log per app

*****

certificatesJob contatta SIF per scaricare certificati e li scrive in coda. Parte all'ora CERTIFICATE_DOWNLOAD_TIME_START e si ripete ogni CERTIFICATE_DOWNLOAD_TIME_START_HOUR_INTERVAL ore. Per sicurezza si spegne alle CERTIFICATE_DOWNLOAD_TIME_STOP. Scrive i due certificati in messaggi separati sequenzialmente e ognuno ha CERTIFICATE_DOWNLOAD_RETRIES_LIMIT tentativi da fare ogni CERTIFICATE_DOWNLOAD_RETRY_MINUTES_INTERVAL minuti solo se si riceve uno degli errori presenti in CertificatesCodeErrorEnum. Solo uno dei tre applicativi si occuperà dei certificati, solo quello che vincerà la contesa gestita tramite record scritto in tabella Dossier nel db (valore sentinella chiamato "certificates.concurrency.request" in application properties). Per poter gestire l'alta affidabilità usiamo la tabella CertificateRequestStatus che contiene dei macro stati del job presenti in CertificateStatusEnum. Le app che perdono contesa per scaricare certificati capiscono che l’app puo’ essere crashata; capiamo che l'app e' andata in crash tramite controllo sul piu recente (non necessariamente odierno) stato scheduled_reset_certificate_concurrency_job salvato sul db; se lo stato e' assente (mai nessuno ha scaricato a buon fine i certificati, possibile solo quando il db e' vuoto) oppure l'ultima volta che tutto e' andato a buon fine e' stato almeno (non oggi, bensì ieri, l’altro ieri etc) ieri. Inoltre guarda commento sopra process() di CertificatesJob per conoscere bug.

*****

cleanDatabaseJob cancella i dati in event log più vecchi di DAYS_FROM_TODAY_FOR_DB_CLEANING giorni ogni DB_CLEANING_FREQUENCY_DAYS giorni. Se DAYS_FROM_TODAY_FOR_DB_CLEANING = -1 allora disabilito cancellazione

*****

RegistrationConsumer parte all'avvio dell'app e non si ferma mai (non è un job ma un kafka consumer)
consuma messaggi dalla coda e prova a vincere concorrenza (in quanto altre app stanno leggendo lo stesso messaggio, cosa che garantisce anche l'alta affidabilità) al fine di scrivere il dossier nel DB

*****

dossierSenderJob parte all'avvio dell'app e non si ferma mai
il job legge i fascicoli (quelli letti dal consumer della stessa applicazione) presenti in DB (perchè scritti da un dossierConsumer che li ha letti a sua volta dalla coda Kafka) e fa partire una thread (thread di una CachedThreadPool) per ricostruire l'oggetto dossier, inviarlo al SIF e ricevere (quindi salvare su DB) la transactionId (essendo una chiamata async)
NOTA IMPORTANTE: l'alta affidabilità non è garantita (se crasha l'app #1 allora nessun'altra app processerà i dossier consumati da app #1 finchè non sarà fatta ripartire proprio app #1)

*****

dossierResponseJob parte all'avvio dell'app e non si ferma mai
prende tutti i dossier per cui il dossierSender (della sua stessa app) ha scritto la transactionId
ogni DOSSIER_TRANSACTION_CHECK_FREQUENCY_SECONDS secondi controlla lo stato della transazione SIF
non istanziamo thread per dossier già in elaborazione da un'altra thread
la thread contatta il sif per controllare lo stato della transazione e se il SIF risponde OK allora cancella quel dossier e suoi riferimenti; altrimenti aggiunge nuovo stato per quel dossier al fine di avere una data di ultimo contatto aggiornata
NOTA IMPORTANTE: l'alta affidabilità non è garantita (se crasha l'app #1 allora nessun'altra app processerà i dossier consumati da app #1 finchè non sarà fatta ripartire proprio app #1)

*****

spegnimento: graceful, aspetto anche che finiscano thread già in esecuzione, etc...

*****

il codice commentato in GatewayApplication e RegistrationConsumer può tornare utile per generare molti messaggi fake in coda

*****

---> RIVEDERE QUANTO SCRITTO IN ALTO DOPO MODIFICHE SUCCESSIVE AL 15/12/2023

*****

Se un applicativo X1 va in crash dopo aver vinto la contesa relativa alla ricezione di un dossier da Kafka e l'applicativo X1 viene riavviato dopo 1 giorno (pari alla durata di conservazione di messaggi in coda Kafka) allora perderò per sempre quel dossier -> Se l'host è morto e richiede più di 1 giorno per il suo ripristino, allora avviare subito X1 su altro host. Potrebbe non essere un vero problema perchè il viaggiatore ha già "risolto manualmente/fisicamente" con Polaria (in quanto l'egate lo avrà rifiutato o se ne accorgerà la Polaria stessa in postazione manuale)
