/*
package com.reco.ees.gateway;

import com.reco.ees.gateway.controller.PreFileController;
import com.reco.ees.gateway.enums.CheckPreFileEnum;
import com.reco.ees.gateway.repository.DossierRepository;
import com.reco.ees.gateway.repository.model.Dossier;
import com.reco.ees.gateway.service.LogService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.ResponseEntity;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;


public class PreFileControllerTest {

    @InjectMocks
    private PreFileController preFileController;

    @Mock
    private DossierRepository dossierRepository;

    @Mock
    private LogService logService;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    @DisplayName("Should return NOTFOUND when Dossier is not found")
    public void shouldReturnNotFoundWhenDossierIsNotFound() {
        when(dossierRepository.findByDocumentNumberAndCountryIssue(anyString(), anyString())).thenReturn(null);

        ResponseEntity<PreFileController.CheckPreFileResponse> response = preFileController.checkPreFile("idClient", "document_number", "country_issue");

        assertEquals(CheckPreFileEnum.NOTFOUND.getCheckPreFileEnumValue(), response.getBody().getStatus());
    }

    @Test
    @DisplayName("Should return FORMANUAL when Dossier directionEgate is false")
    public void shouldReturnForManualWhenDossierDirectionEgateIsFalse() {
        Dossier dossier = new Dossier();
        dossier.setDirectionEgate(false);
        when(dossierRepository.findByDocumentNumberAndCountryIssue(anyString(), anyString())).thenReturn(dossier);

        ResponseEntity<PreFileController.CheckPreFileResponse> response = preFileController.checkPreFile("idClient", "document_number", "country_issue");

        assertEquals(CheckPreFileEnum.FORMANUAL.getCheckPreFileEnumValue(), response.getBody().getStatus());
    }

    @Test
    @DisplayName("Should return MISSING4EGATE when Dossier hasSifProcessedDossier is false")
    public void shouldReturnMissing4EgateWhenDossierHasSifProcessedDossierIsFalse() {
        Dossier dossier = new Dossier();
        dossier.setDirectionEgate(true);
        dossier.setHasSifProcessedDossier(false);
        when(dossierRepository.findByDocumentNumberAndCountryIssue(anyString(), anyString())).thenReturn(dossier);

        ResponseEntity<PreFileController.CheckPreFileResponse> response = preFileController.checkPreFile("idClient", "document_number", "country_issue");

        assertEquals(CheckPreFileEnum.MISSING4EGATE.getCheckPreFileEnumValue(), response.getBody().getStatus());
    }

    @Test
    @DisplayName("Should return EXIST4EGATE when Dossier is valid for Egate")
    public void shouldReturnExist4EgateWhenDossierIsValidForEgate() {
        Dossier dossier = new Dossier();
        dossier.setDirectionEgate(true);
        dossier.setHasSifProcessedDossier(true);
        when(dossierRepository.findByDocumentNumberAndCountryIssue(anyString(), anyString())).thenReturn(dossier);

        ResponseEntity<PreFileController.CheckPreFileResponse> response = preFileController.checkPreFile("idClient", "document_number", "country_issue");

        assertEquals(CheckPreFileEnum.EXIST4EGATE.getCheckPreFileEnumValue(), response.getBody().getStatus());
    }
}*/
