create table event_log
(
    id            varchar(255) not null
        primary key,
    application   varchar(255),
    eventdate     timestamp(6),
    request       text,
    response      text,
    response_code varchar(255)
);

alter table event_log
    owner to docker;

INSERT INTO public.event_log (id, application, eventdate, request, response, response_code) VALUES ('c098f5a5-9802-425e-8560-925c03a3e7b1', 'gateway_2_ees', '2024-10-15 12:30:38.573000', 'SifAuthJob request for: PS-KIOSK-0900403002A', e'{
  "status" : "ACTIVE",
  "dateOfExpire" : 253402297199000,
  "passwordExpirationDate" : 253368079138000,
  "isAuthenticated" : true,
  "message" : "message",
  "userName" : "userName",
  "maxInvalidPasswordAttempts" : 3,
  "lastLoginDateTime" : 1728988238527,
  "security" : [ {
    "idMachine" : "PS-KIOSK-0900403002A",
    "securityToken" : "securityTokenPS-KIOSK-0900403002A"
  } ]
}', 'Active');
INSERT INTO public.event_log (id, application, eventdate, request, response, response_code) VALUES ('b630934e-9fb8-46a5-9867-bc4abe038924', 'gateway_2_ees', '2024-10-15 12:30:38.593000', 'SifAuthJob completed successfully', null, null);
INSERT INTO public.event_log (id, application, eventdate, request, response, response_code) VALUES ('b787c14e-e5e3-478a-825a-072025a85d46', 'gateway_2_ees', '2024-10-15 12:30:41.365000', 'CertificatesJob completed successfully', null, null);
