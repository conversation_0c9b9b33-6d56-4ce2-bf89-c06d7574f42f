<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns="http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:shared="http://www.europa.eu/schengen/shared/xsd/v1" xmlns:ees="http://www.europa.eu/schengen/ees/xsd/v1" xmlns:vdta="http://www.europa.eu/schengen/vis/xsd/v3/types/Application" xmlns:vdtc="http://www.europa.eu/schengen/vis/xsd/v3/types/Common" xmlns:vdt="http://www.europa.eu/schengen/vis/xsd/v3/types/Common" targetNamespace="http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1">
	<xs:import namespace="http://www.europa.eu/schengen/ees/xsd/v1" schemaLocation="../../../ees/xsd/inc/MessageStructureTypes.xsd"/>
	<xs:import namespace="http://www.europa.eu/schengen/ees/xsd/v1" schemaLocation="../../../ees/xsd/inc/SearchBaseTypes.xsd"/>
	<xs:import namespace="http://www.europa.eu/schengen/ees/xsd/v1" schemaLocation="../../../ees/xsd/inc/VISRelatedTypes.xsd"/>
	<xs:import namespace="http://www.europa.eu/schengen/ees/xsd/v1" schemaLocation="../../../ees/xsd/inc/BiometricTypes.xsd"/>
	<xs:import namespace="http://www.europa.eu/schengen/ees/xsd/v1" schemaLocation="../../../ees/xsd/DataTypes.xsd"/>
	<xs:import namespace="http://www.europa.eu/schengen/shared/xsd/v1" schemaLocation="../../../shared/xsd/DataTypes.xsd"/>
	<xs:import namespace="http://www.europa.eu/schengen/vis/xsd/v3/types/Application" schemaLocation="../../../vis-ns/xsd/DataTypeDictionary/ApplicationDataTypes.xsd"/>
	<xs:import namespace="http://www.europa.eu/schengen/vis/xsd/v3/types/Common" schemaLocation="../../../vis-ns/xsd/DataTypeDictionary/CommonDataTypes.xsd"/>
	<xs:complexType name="HeaderRequestType">
		<xs:complexContent>
			<xs:extension base="ees:HeaderBaseType"/>
		</xs:complexContent>
	</xs:complexType>
	<xs:complexType name="HeaderResponseType">
		<xs:complexContent>
			<xs:extension base="ees:HeaderBaseType"/>
		</xs:complexContent>
	</xs:complexType>
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<xs:simpleType name="DossierIDType">
		<xs:annotation>
			<xs:documentation>Description: The dossier number is internally used to link applications where the applicant is the same natural person.</xs:documentation>
		</xs:annotation>
		<xs:restriction base="vdtc:NumRD">
			<xs:totalDigits value="18"/>
		</xs:restriction>
	</xs:simpleType>
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<xs:complexType name="HeaderVISBaseType">
		<xs:sequence>
			<xs:element name="TransactionID">
				<xs:annotation>
					<xs:documentation>Description: Identifier of the logical session.</xs:documentation>
					<xs:documentation>Rule: Contains leading zeroes.</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:minLength value="1"/>
						<xs:maxLength value="255"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="SystemID" type="shared:SystemIDType">
				<xs:annotation>
					<xs:documentation>Identification of the system that sends the message.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Variant" type="vdtc:ST21_VariantType">
				<xs:annotation>
					<xs:documentation>The Variant is a code table value that indicates to which operation and variant the message.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="TestCaseID" type="ees:TestCaseIDType" minOccurs="0">
				<xs:annotation>
					<xs:documentation>The Test Case ID is a long value that is helpful for troubleshooting and reporting during and after test campaigns.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Authority" type="shared:AuthorityUniqueIDType">
				<xs:annotation>
					<xs:documentation>Authority which entered/updated the data, authorised entry, etc. (it is assumed that the same authority enters the data as authorised entry).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Timestamp" type="xs:dateTime">
				<xs:annotation>
					<xs:documentation>Message creation timestamp. Field to be populated by the system sending the request and to be logged independently from message reception time.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="User" type="vdtc:CT70_UserType">
				<xs:annotation>
					<xs:documentation>Description: Identification of the User issuing the message.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="EndUserRole" type="vdtc:ST12_EndUserRoleType">
				<xs:annotation>
					<xs:documentation source="Description">Description: EndUserRole.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="EndUserID" type="vdtc:EndUserIDType" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Description: The EndUser is the application or officer acting for a USER. The VIS application do not manage the End-Users. It is the responsibility of the USER to authenticate its EndUser. An EndUser-ID may be present in the message header for USER usage only. The USER has the responsibility to provide the appropriate EndUser-ID.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<xs:complexType name="ReturnCodesVISType">
		<xs:sequence>
			<xs:element name="ErrorCodes" minOccurs="0">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="ErrorCode" type="shared:ST14_ErrorCodeType" maxOccurs="unbounded"/>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="WarningCodes" minOccurs="0">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="WarningCode" type="shared:ST15_WarningCodeType" maxOccurs="unbounded"/>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="FieldCodes" minOccurs="0">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="FieldCode" type="vdtc:FieldCodeType" maxOccurs="unbounded"/>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="InfoCodes" minOccurs="0">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="InfoCode" type="shared:ST16_InfoCodeType" maxOccurs="unbounded"/>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<xs:complexType name="IdentificationInVISRequestMessageType">
		<xs:sequence>
			<xs:element name="Paging" type="ees:PagingRequestType" minOccurs="0"/>
			<xs:element name="ScopeModifiers" minOccurs="0">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="VisaApplications" type="ees:ST508_VisaApplicationsModifierType">
							<xs:annotation>
								<xs:documentation>If not specified then visa applications are not returned.
                                </xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="SearchData">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="FP">
							<xs:complexType>
								<xs:sequence>
									<xs:element name="NISTFile" type="ees:FPNISTFile"/>
								</xs:sequence>
								<xs:attribute name="configuration" type="shared:ST509_BiometricSearchConfigurationType"/>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<xs:complexType name="TravelDocumentSearchVISRequestType">
		<xs:annotation>
			<xs:documentation>Fields of the search operation

				[Article 16(1)(a)] Surname (family name); first name or names (given names); date of birth; nationality or nationalities; sex.
				[Article 16(1)(b)] The type and number of the travel document or documents and three letter code of the issuing country of the travel document or documents.
				[Article 16(1)(c)] The date of expiry of the validity of the travel document or documents.

			</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="FamilyName" type="ees:AnyNameSearchField">
				<xs:annotation>
					<xs:documentation>Surname (family name); first name(s) (given names) (with any name modifier).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="FirstName" type="ees:NameSearchField" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>First name(s) (given names).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DateOfBirth" type="ees:PseudoDateSearchField">
				<xs:annotation>
					<xs:documentation>Date of birth.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Nationalities">
				<xs:annotation>
					<xs:documentation>Current nationality or nationalities.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:complexContent>
						<xs:extension base="NationalitiesSearchType">
							<xs:attribute name="Weight" type="vdtc:WeightType" use="optional">
								<xs:annotation>
									<xs:documentation>Description: End-user defined weights</xs:documentation>
								</xs:annotation>
							</xs:attribute>
						</xs:extension>
					</xs:complexContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="Gender" type="ees:CT04_GenderSearchField">
				<xs:annotation>
					<xs:documentation>Sex.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DocumentNumber" type="ees:NumberSearchField">
				<xs:annotation>
					<xs:documentation>Number of the travel document.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DocumentType" type="ees:CT512_TravelDocumentTypeSearchField">
				<xs:annotation>
					<xs:documentation>Type of the travel document.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ValidUntil" type="ees:PseudoDateSearchField">
				<xs:annotation>
					<xs:documentation>The date of expiry of the validity of the travel document or documents.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<xs:complexType name="PersonalDataSearchVISRequestType">
		<xs:annotation>
			<xs:documentation>Fields of the search operation

				[Article 16(1)(a)] Surname (family name); first name or names (given names); date of birth; nationality or nationalities; sex.
				[Article 16(1)(b)] The type and number of the travel document or documents and three letter code of the issuing country of the travel document or documents.
				[Article 16(1)(c)] The date of expiry of the validity of the travel document or documents.

			</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="FamilyName" type="ees:AnyNameSearchField" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Surname (family name); first name(s) (given names) (with any name modifier).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="FirstName" type="ees:NameSearchField" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>First name(s) (given names).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DateOfBirth" type="ees:PseudoDateSearchField" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Date of birth.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Nationalities" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Current nationality or nationalities.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:complexContent>
						<xs:extension base="NationalitiesSearchType">
							<xs:attribute name="Weight" type="vdtc:WeightType" use="optional">
								<xs:annotation>
									<xs:documentation>Description: End-user defined weights</xs:documentation>
								</xs:annotation>
							</xs:attribute>
						</xs:extension>
					</xs:complexContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="Gender" type="ees:CT04_GenderSearchField" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Sex.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DocumentNumber" type="ees:NumberSearchField" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Number of the travel document.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DocumentType" type="ees:CT512_TravelDocumentTypeSearchField" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Type of the travel document.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ValidUntil" type="ees:PseudoDateSearchField" minOccurs="0">
				<xs:annotation>
					<xs:documentation>The date of expiry of the validity of the travel document or documents.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<xs:complexType name="NationalitiesSearchType">
		<xs:sequence>
			<xs:element name="Nationality" type="shared:CT02_CountryOfNationalityType" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<xs:complexType name="VisaApplicationOverviewSearchResultVISType">
		<xs:sequence>
			<xs:element name="ApplicationNumber" type="vdta:ApplicationNumberType">
				<xs:annotation>
					<xs:documentation>Description: ID of the Application.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ApplicantName" type="FullNameGetEESType">
				<xs:annotation>
					<xs:documentation>Description: Name of the Applicant. Composed of the firstname and the surname.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Gender" type="shared:CT04_GenderType" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Description: Gender of the applicant taken from a code table.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DateOfBirth" type="shared:PseudodateType">
				<xs:annotation>
					<xs:documentation>Description: Date of birth.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="BirthName" type="vdta:TransTextType" minOccurs="0"/>
			<xs:element name="FacialImage" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Description: Facial Image used for the application.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="AttachmentID" type="vdta:AttachmentIDType">
							<xs:annotation>
								<xs:documentation>Description: ID of the attachment.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="ApplicationStatus" type="vdta:ST31_ApplicationStatusType">
				<xs:annotation>
					<xs:documentation>Description: Status of the application (code table value). This Status is the outcome of the decision history.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DateOfApplication" type="shared:TimeStampType" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Description: The date of request is taken from the application form.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MemberStatesOfDestination" type="vdta:MemberStatesOfDestinationType" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Description: The member states destination is taken from the application form.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Nationality" type="shared:CT02_CountryOfNationalityType" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Description: This nationality is used for the application.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MainPurposesOfJourney" type="vdta:MainPurposesOfJourneyGetType" minOccurs="0"/>
			<xs:element name="VisaStickerNumber" type="vdta:VisaStickerNumberType" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Description: This attribute uniquely identifies a visa sticker.
						A visa sticker number consists of one to three characters indicating the country and a number that consists of up to 30 digits.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="VisaType" type="vdta:VisaTypeType" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Description: The visa type as printed on the visa sticker.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<xs:complexType name="AuthorityBaseType">
		<xs:annotation>
			<xs:documentation>Authority data - type for the request.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Country" type="shared:CT01_CountryType">
				<xs:annotation>
					<xs:documentation>Description: The country where the authority is located. A code table value.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="AuthorityUniqueId" type="shared:AuthorityUniqueIDType">
				<xs:annotation>
					<xs:documentation>Description: A unique identificator of an Authority.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Location" type="shared:AuthorityLocationType">
				<xs:annotation>
					<xs:documentation>Description: A value referencing a location of an Authority.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Name" type="shared:AuthorityNameTypeExtended">
				<xs:annotation>
					<xs:documentation>Description: Official name of authority.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="AuthorityType" type="shared:CT10_AuthorityTypeType">
				<xs:annotation>
					<xs:documentation>Description: A value referencing a Authority type.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="AddressLine1" type="shared:RawValueType" minOccurs="0"/>
			<xs:element name="AddressLine2" type="shared:RawValueType" minOccurs="0"/>
			<xs:element name="PostCode" type="shared:RawValueType" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Description: PostCode of Authority.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Landline1" type="shared:PhoneNumberType" minOccurs="0"/>
			<xs:element name="Landline2" type="shared:PhoneNumberType" minOccurs="0"/>
			<xs:element name="Landline3" type="shared:PhoneNumberType" minOccurs="0"/>
			<xs:element name="Mobile" type="shared:PhoneNumberType" minOccurs="0"/>
			<xs:element name="Fax" type="shared:PhoneNumberType" minOccurs="0"/>
			<xs:element name="Email1" type="shared:EmailType" minOccurs="0"/>
			<xs:element name="Email2" type="shared:EmailType" minOccurs="0"/>
			<xs:element name="Email3" type="shared:EmailType" minOccurs="0"/>
			<xs:element name="VISMail" type="shared:EmailType" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Description: The Authority's VISMail email address.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ValidStart" type="xs:date"/>
			<xs:element name="ValidEnd" type="xs:date"/>
			<xs:element name="ConsultationFlag" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Description: Only one authority can be designated for consultation and notification purposes, and have this flag set to TRUE</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<xs:complexType name="AuthorityType">
		<xs:annotation>
			<xs:documentation>Authority data - type for the response with metadata.</xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base="AuthorityBaseType">
				<xs:sequence>
					<xs:element name="Owner" type="shared:CT70_UserType"/>
					<xs:element name="CreateDate" type="xs:date" minOccurs="0"/>
					<xs:element name="LastUpdate" type="xs:date" minOccurs="0"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<xs:complexType name="VisaApplicationSearchResultEESType">
		<xs:annotation>
			<xs:documentation>Response for search by travel document and by VSN</xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base="ResultReadApplicationBaseEESType">
				<xs:sequence>
					<xs:element name="ApplicationData">
						<xs:annotation>
							<xs:documentation>Description: This element is a subset of the Application.</xs:documentation>
						</xs:annotation>
						<xs:complexType>
							<xs:complexContent>
								<xs:extension base="ResultReadCoreBorderDataEESType">
									<xs:sequence>
										<xs:element name="ApplicationStatus" type="vdta:ST31_ApplicationStatusType">
											<xs:annotation>
												<xs:documentation>Description: Status of the application (code table value). This Status is the outcome of the decision history.</xs:documentation>
											</xs:annotation>
										</xs:element>
										<xs:element name="ApplicationNumber" type="vdta:ApplicationNumberType">
											<xs:annotation>
												<xs:documentation>Description: ID of the Application.</xs:documentation>
											</xs:annotation>
										</xs:element>
									</xs:sequence>
								</xs:extension>
							</xs:complexContent>
						</xs:complexType>
					</xs:element>
					<xs:element name="Decisions" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Description: Decision history of the application.</xs:documentation>
						</xs:annotation>
						<xs:complexType>
							<xs:sequence>
								<xs:element name="Decision" type="VerificationDecisionHistGetEESType" maxOccurs="unbounded"/>
							</xs:sequence>
						</xs:complexType>
					</xs:element>
					<xs:element name="FacialImages" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Description: The facial images from the Application.</xs:documentation>
						</xs:annotation>
						<xs:complexType>
							<xs:sequence>
								<xs:element name="FacialImage" type="FacialImageGetEESType" maxOccurs="unbounded"/>
							</xs:sequence>
						</xs:complexType>
					</xs:element>
					<xs:element name="GroupSynopses" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Description: List of the groups the Application belongs to.</xs:documentation>
						</xs:annotation>
						<xs:complexType>
							<xs:sequence>
								<xs:element name="GroupSynopsis" type="vdta:GroupSynopsisType" maxOccurs="unbounded"/>
							</xs:sequence>
						</xs:complexType>
					</xs:element>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<xs:complexType name="FullNameGetEESType">
		<xs:annotation>
			<xs:documentation>Decription: Name of a person it means surname and firstname.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="FamilyName" type="vdta:TransTextType">
				<xs:annotation>
					<xs:documentation>Description: This attribute contains all surnames of a person. If a person happens to have more than one surname, the names are concatenated using a comma.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Firstnames" type="vdta:TransTextType" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Description: This attribute contains all first names of a person. If there is more than one first name, the names are concatenated using a comma.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<xs:complexType name="ResultReadApplicationBaseEESType">
		<xs:sequence>
			<xs:element name="DateOfBirth" type="vdtc:PseudodateType">
				<xs:annotation>
					<xs:documentation>Description: Date of birth.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ApplicantName" type="FullNameGetEESType">
				<xs:annotation>
					<xs:documentation>Description: Full name (first name(s) plus surname(s)) of a natural person.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Gender" type="vdta:CT04_GenderType" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Description: Gender of the applicant taken from a code table.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="FormerFamilyNames" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Description: Former surnames of the applicant.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="FormerSurname" type="vdta:TransTextType" maxOccurs="unbounded"/>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="PlaceOfBirth" type="vdta:BirthPlaceGetType" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Description: Place of birth (country from code table plus free text).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="BirthName" type="vdta:TransTextType" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Description: Surname at birth of the applicant.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<xs:complexType name="ResultReadCoreBorderDataEESType">
		<xs:sequence>
			<xs:element name="DateOfApplication" type="vdtc:TimeStampType" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Description: The date of request is taken from the application form.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DurationOfIntendedStayOrTransit" type="vdtc:DurationType" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Description: The duration of intended stay or transit is taken from the application form.
The unit is days. This regards short-term visas, which have a maximum validity of 3 months. Therefore, 3 digits are sufficient.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Host" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Description: HostPerson or HostOrganisation.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:choice>
						<xs:element name="HostPerson" type="HostPersonGetEESType"/>
						<xs:element name="HostOrganisation" type="HostOrganisationGetEESType"/>
					</xs:choice>
				</xs:complexType>
			</xs:element>
			<xs:element name="MemberStateOfFirstEntry" type="vdtc:CT70_UserType" minOccurs="0"/>
			<xs:element name="IntendedDateOfArrival" type="vdtc:PseudodateType" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Description: The date of arrival is taken from the application form.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="IntendedDateOfDeparture" type="vdtc:PseudodateType" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Description: The date of departure is taken from the application form.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MemberStatesOfDestination" type="vdta:MemberStatesOfDestinationType" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Description: The member states destination is taken from the application form.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Occupation" type="vdta:OccupationGetType" minOccurs="0"/>
			<xs:element name="PlaceOfApplication" type="vdta:PlaceGetType" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Description: The place where the application has been lodged. Consists of a country code and the code for the place. This place could differ from the place of the applicationAuthorityLocation.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MainPurposesOfJourney" type="vdta:MainPurposesOfJourneyGetType" minOccurs="0"/>
			<xs:element name="EducationalEstablishment" type="vdta:EducationalEstablishmentGetType" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Description: The educational establishment in case the applicant is a student.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="TravelDocument" type="TravelDocumentGetEESType" minOccurs="0"/>
			<xs:element name="VisaTypeRequested" type="vdta:CT12_VisaTypeRequestedType" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Description: The requested visa type is taken from the application form (code table value).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="FingerprintsNotRequired" type="vdtc:YesNoType" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Description:This pinpoints whether fingerprints are required or not. Fingerprints are required if this boolean is set to FALSE.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="FingerprintsNotApplicable" type="vdtc:YesNoType" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Description: Indicates whether the fingerprints are not applicable. fingerprints are not applicable when the applicant physically cannot provide them (e.g. when the applicant has no hands) or when the fingerprints cannot be taken for a technical reason (e.g. there is no scanner available).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ApplicantData" type="ApplicantReadEESType" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<xs:complexType name="ApplicantReadEESType">
		<xs:sequence>
			<xs:element name="ParentalAuthorityOrLegalGuardian" type="FullNameGetEESType" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Description: Parent's name of the applicant.
This is needed in case the applicant is a minor.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="NationalityAtBirth" type="vdta:CT02_CountryOfNationalityType" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Description: Nationality at birth of the applicant.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Nationality" type="vdta:CT02_CountryOfNationalityType" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Description: This nationality is used for the application.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ApplicantsHomeAddress" type="AddressGetEESType" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Description: The home address of the applicant. It consists of a country street, number, postcode and city.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<xs:complexType name="HostPersonGetEESType">
		<xs:annotation>
			<xs:documentation>Description: Information describing a host person for the application.</xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base="HostGetEESType">
				<xs:sequence>
					<xs:element name="Name" type="FullNameGetEESType">
						<xs:annotation>
							<xs:documentation>Description: name of the contact in the company.</xs:documentation>
						</xs:annotation>
					</xs:element>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<xs:complexType name="HostGetEESType">
		<xs:annotation>
			<xs:documentation>Description: General information for a host.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Address" type="AddressGetEESType" minOccurs="0">
				<xs:annotation>
					<xs:documentation source="Description">Description: Address of contact.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<xs:complexType name="AddressGetEESType">
		<xs:annotation>
			<xs:documentation>Description: Information describing an address.</xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base="vdta:AddressBaseType">
				<xs:sequence>
					<xs:element name="CityTownOrVillage" type="vdta:TransTextType" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Description: City of the applicant's address.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="PostCode" type="vdta:TransTextType" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Description: Postal code of the address.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="Street" type="vdta:TransTextType" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Description: Street of the applicant's address.</xs:documentation>
						</xs:annotation>
					</xs:element>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<xs:complexType name="HostOrganisationGetEESType">
		<xs:annotation>
			<xs:documentation>Description: Information describing a host organisation for the application.</xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base="HostGetEESType">
				<xs:sequence>
					<xs:element name="Name" type="vdta:TransTextType">
						<xs:annotation>
							<xs:documentation>Description: name of the company.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="ContactPerson" type="FullNameGetEESType" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Description: name of the contact in the company.</xs:documentation>
						</xs:annotation>
					</xs:element>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<xs:complexType name="VerificationDecisionHistGetEESType">
		<xs:annotation>
			<xs:documentation>Description: Information describing a decision from the decision history.</xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base="VerificationDecisionHistChoiceEESType">
				<xs:sequence>
					<xs:element name="DecisionID" type="vdta:DecisionIDType">
						<xs:annotation>
							<xs:documentation>Description: ID of the decision.</xs:documentation>
						</xs:annotation>
					</xs:element>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<xs:complexType name="VerificationDecisionHistChoiceEESType">
		<xs:annotation>
			<xs:documentation>Description: Information describing the decision.</xs:documentation>
		</xs:annotation>
		<xs:choice>
			<xs:element name="VisaDecision" type="VisaDecisionGetEESType">
				<xs:annotation>
					<xs:documentation>Description: Information of an VisaDecision.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="VisaCreationDecision" type="VisaCreationDecisionGetEESType">
				<xs:annotation>
					<xs:documentation>Description: Information of an VisaCreationDecision.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:choice>
	</xs:complexType>
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<xs:complexType name="VisaDecisionGetEESType">
		<xs:annotation>
			<xs:documentation>Description: Information describing the decision for a visa.</xs:documentation>
		</xs:annotation>
		<xs:choice>
			<xs:element name="ExtendVisaWithoutNewSticker" type="ExtendVisaWithoutNewStickerGetEESType">
				<xs:annotation>
					<xs:documentation>Description: All information needed for the decision "Extend Visa Without New Sticker".</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="RevokeVisa" type="vdta:RevokeVisaGetType">
				<xs:annotation>
					<xs:documentation>Description: All information needed for the decision "Revoke Visa".</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="AnnulVisa" type="vdta:AnnulVisaGetType">
				<xs:annotation>
					<xs:documentation>Description: All information needed for the decision "Annul Visa".</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ShortenValidityPeriodWithoutNewSticker" type="vdta:ShortenValidityPeriodWithoutNewStickerGetType">
				<xs:annotation>
					<xs:documentation>Description: All information needed for the decision "ShortenValidityPeriodWithoutNewSticker".</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:choice>
	</xs:complexType>
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<xs:complexType name="ExtendVisaWithoutNewStickerGetEESType">
		<xs:annotation>
			<xs:documentation>Description: Information describing the ShortenValidityPeriodWithoutNewSticker decision.</xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base="vdta:DecisionGetType">
				<xs:sequence>
					<xs:element name="AffectedDecisionID" type="vdta:DecisionIDType">
						<xs:annotation>
							<xs:documentation>Description: A VisaDecision refers to an existing visa sticker. As the sticker is part of a VisaCreationDecision, the affectedDecisionID field refers to the VisaCreationDecision containing the visa sticker.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="ExtensionGrounds">
						<xs:annotation>
							<xs:documentation>Description: Grounds for extending the visa as described in [VIS-PEP]. Values are defined within the code table ExtensionGround.</xs:documentation>
						</xs:annotation>
						<xs:complexType>
							<xs:sequence>
								<xs:element name="ExtensionGround" type="vdta:CT51_ExtensionGroundType" maxOccurs="unbounded"/>
							</xs:sequence>
						</xs:complexType>
					</xs:element>
					<xs:element name="VisaType" type="vdta:VisaTypeType">
						<xs:annotation>
							<xs:documentation>Description: The visa type as printed on the visa sticker.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="ExtendedDuration" type="vdtc:DurationType">
						<xs:annotation>
							<xs:documentation>Description: The new extended duration of the visa.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="ExtendedPeriod" type="PeriodEESType">
						<xs:annotation>
							<xs:documentation>Description: The extended period indicates the period (commencement and expiry dates) of the extended period.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="TerritorialValidity" type="vdta:TerritorialValidityType">
						<xs:annotation>
							<xs:documentation>Description: This field reflects the Schengen country (countries) the visa is valid for. If the visa is relevant for all Member States the code table entrance Schengen States has to be selected. If only distinct countries are relevant, those countries should be selected from the code table. In case all code table entries are selected, it is considered as Territorial Validity (not as Schengen States).</xs:documentation>
						</xs:annotation>
					</xs:element>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<xs:complexType name="PeriodEESType">
		<xs:annotation>
			<xs:documentation>Description: this type is used for the period of validity.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="StartDate" type="vdt:TimeStampType"/>
			<xs:element name="EndDate" type="vdt:TimeStampType"/>
		</xs:sequence>
	</xs:complexType>
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<xs:complexType name="TravelDocumentGetEESType">
		<xs:annotation>
			<xs:documentation>Description: Information describing the travel document.</xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base="TravelDocumentBaseEESType">
				<xs:sequence>
					<xs:element name="IssuingAuthorityOfTravelDocument" type="vdta:IssuingAuthorityOfTravelDocumentTransTextType" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Description: The authority which issued the travel document. Can be also a country or an organization such as UN.</xs:documentation>
						</xs:annotation>
					</xs:element>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<xs:complexType name="TravelDocumentBaseEESType">
		<xs:annotation>
			<xs:documentation>Description: group of information describing the travel document.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="DocumentType" type="vdta:CT11_TravelDocumentTypeType">
				<xs:annotation>
					<xs:documentation>Description: Type of the travel document (code table value).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DocumentNumber" type="vdta:TravelDocumentNumberType">
				<xs:annotation>
					<xs:documentation>Description: The number of the travel document including the three letter country code.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DateOfIssue" type="vdtc:DateType" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Description: Date of the issue.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ValidUntil" type="vdtc:DateType" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Description: Validity of the travel document.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="OtherTravelDocument" type="vdta:TravelDocumentOtherType" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Rule: If TravelDocumentType is set to "Other" then a description must be entered here.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<xs:complexType name="VisaCreationDecisionGetEESType">
		<xs:annotation>
			<xs:documentation>Description: Information needed for the creation of a VisaCreationDecision.</xs:documentation>
		</xs:annotation>
		<xs:choice>
			<xs:element name="ExtendVisa" type="ExtendVisaGetEESType">
				<xs:annotation>
					<xs:documentation>Description: All information needed to extend a visa with a new sticker.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="IssueVisa" type="IssueVisaGetEESType">
				<xs:annotation>
					<xs:documentation>Description: All information needed for the decision "Issue Visa".</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ShortenValidityPeriodWithNewSticker" type="ShortenValidityPeriodWithNewStickerGetEESType">
				<xs:annotation>
					<xs:documentation>Description: All information needed for the decision "ShortenValidityPeriodWithNewSticker".</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:choice>
	</xs:complexType>
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<xs:complexType name="ExtendVisaGetEESType">
		<xs:annotation>
			<xs:documentation>Description: All information needed to extend a visa with a new sticker.</xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base="vdta:DecisionGetType">
				<xs:sequence>
					<xs:element name="VisaSticker" type="VisaStickerGetEESType">
						<xs:annotation>
							<xs:documentation>Description: data of the visa sticker.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="IssuanceOnSeparateSheet" type="vdtc:YesNoType">
						<xs:annotation>
							<xs:documentation>Description: Information indicating that the visa has been issued on a separate sheet in accordance with Regulation (EC) No. 333/2002.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="ExtensionGrounds">
						<xs:annotation>
							<xs:documentation>Description: Grounds for extending the visa as described in [VIS-PEP]. Values are defined within the code table ExtensionGround.</xs:documentation>
						</xs:annotation>
						<xs:complexType>
							<xs:sequence>
								<xs:element name="ExtensionGround" type="vdta:CT51_ExtensionGroundType" maxOccurs="unbounded"/>
							</xs:sequence>
						</xs:complexType>
					</xs:element>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<xs:complexType name="VisaStickerGetEESType">
		<xs:annotation>
			<xs:documentation>Description: Information describing a Visa Sticker.</xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base="VisaStickerBaseEESType">
				<xs:sequence>
					<xs:element name="VisaStickerStatus" type="vdta:ST32_VisaStickerStatusType">
						<xs:annotation>
							<xs:documentation>Description: Current status of the visa sticker.</xs:documentation>
						</xs:annotation>
					</xs:element>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<xs:complexType name="VisaStickerBaseEESType">
		<xs:annotation>
			<xs:documentation>Description: Information describing the visa sticker.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="VisaStickerNumber" type="vdta:VisaStickerNumberType">
				<xs:annotation>
					<xs:documentation>Description: ID of the visa sticker.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="VisaType" type="vdta:VisaTypeType">
				<xs:annotation>
					<xs:documentation>Description: The visa type as printed on the visa sticker.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="PeriodOfValidity" type="PeriodEESType">
				<xs:annotation>
					<xs:documentation>Description: The visa's period of validity.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DurationOfStay" type="vdtc:DurationType">
				<xs:annotation>
					<xs:documentation>Description: The duration of stay as printed on the visa sticker.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="NumberOfEntries" type="vdta:CT07_NumberOfEntriesType">
				<xs:annotation>
					<xs:documentation>Description: The number of entries allowed by this visa: one, two or many. The allowed values are defined within the code table NumberOfEntries.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="TerritorialValidity" type="vdta:TerritorialValidityType">
				<xs:annotation>
					<xs:documentation>Description: This field reflects the Schengen country (countries) the visa is valid for.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<xs:complexType name="IssueVisaGetEESType">
		<xs:annotation>
			<xs:documentation>Description: All information needed for the decision "Issue Visa".</xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base="vdta:DecisionGetType">
				<xs:sequence>
					<xs:element name="VisaSticker" type="VisaStickerGetEESType">
						<xs:annotation>
							<xs:documentation>Description: data of the visa sticker.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="IssuanceOnSeparateSheet" type="vdtc:YesNoType">
						<xs:annotation>
							<xs:documentation>Description: Information indicating that the visa has been issued on a separate sheet in accordance with Regulation (EC) No. 333/2002.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="PersonStatus" type="vdta:CT71_PersonStatusType" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Description: Indicates that the TCN is a member of the family of a Union citizen to whom Directive 2004/38/EC of the European Parliament and of the Council applies or of a TCN enjoying the right of free movement equivalent to that of Union citizens under an agreement between the Union and its Member States and a third country.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="VLTVIndicator" type="vdtc:VLTVIndicatorType" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Description: TRUE means that the visa has been issued with limited territorial validity pursuant to Article 25(1)(b) of Regulation (EC) No 810/2009.</xs:documentation>
						</xs:annotation>
					</xs:element>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<xs:complexType name="ShortenValidityPeriodWithNewStickerGetEESType">
		<xs:annotation>
			<xs:documentation>Description: All information needed to shorten validity period of a new sticker.</xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base="vdta:DecisionGetType">
				<xs:sequence>
					<xs:element name="VisaSticker" type="VisaStickerGetEESType">
						<xs:annotation>
							<xs:documentation>Description: data of the visa sticker.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="IssuanceOnSeparateSheet" type="vdtc:YesNoType">
						<xs:annotation>
							<xs:documentation>Description: Information indicating that the visa has been issued on a separate sheet in accordance with Regulation (EC) No. 333/2002.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="ReducedDurationGrounds">
						<xs:annotation>
							<xs:documentation>Description: Optional grounds for the reduction of the period of validity as described in [VIS-PEP]. Values are defined within the code table "ReducedDurationGround".</xs:documentation>
						</xs:annotation>
						<xs:complexType>
							<xs:sequence>
								<xs:element name="ReducedDurationGround" type="vdta:CT53_ReducedDurationGroundType" maxOccurs="unbounded"/>
							</xs:sequence>
						</xs:complexType>
					</xs:element>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<xs:complexType name="FacialImageGetEESType">
		<xs:annotation>
			<xs:documentation>Description: Information describing a facial image.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="AttachmentID" type="vdta:AttachmentIDType">
				<xs:annotation>
					<xs:documentation>Description: ID of the attachment.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<xs:complexType name="VerificationByFPInVISRequestMessageType">
		<xs:sequence>
			<xs:element name="ScopeModifiers" minOccurs="0">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="VisaApplications" type="ees:ST508_VisaApplicationsModifierType">
							<xs:annotation>
								<xs:documentation>If not specified then application overviews are returned.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:choice>
				<xs:element name="VisaStickerNumber" type="shared:VisaStickerNumberType"/>
				<xs:element name="ApplicationNumber" type="vdta:ApplicationNumberType"/>
			</xs:choice>
			<xs:element name="FP">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="NISTFile" type="ees:FPNISTFile"/>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
</xs:schema>
