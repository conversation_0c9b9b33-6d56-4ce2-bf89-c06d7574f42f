<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns="http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:vdta="http://www.europa.eu/schengen/vis/xsd/v3/types/Application" xmlns:ees="http://www.europa.eu/schengen/ees/xsd/v1" targetNamespace="http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1">
	<xs:import namespace="http://www.europa.eu/schengen/ees/xsd/v1" schemaLocation="../../../ees/xsd/DataTypes.xsd"/>
	<xs:include schemaLocation="../../../cs_vis-nui_ees/xsd/Messages.xsd"/>
	<xs:import namespace="http://www.europa.eu/schengen/vis/xsd/v3/types/Application" schemaLocation="../../../vis-ns/xsd/DataTypeDictionary/ApplicationDataTypes.xsd"/>
	<xs:element name="VIS-EES.IdentificationResponse">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="Header" type="HeaderVISBaseType"/>
				<xs:element name="ReturnCodes" type="ReturnCodesVISType" minOccurs="0"/>
				<xs:element name="Response" minOccurs="0">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="Paging" type="ees:PagingResponseType" minOccurs="0"/>
							<xs:choice minOccurs="0">
								<xs:element name="VisaApplicationOverview" maxOccurs="unbounded">
									<xs:complexType>
										<xs:complexContent>
											<xs:extension base="VisaApplicationOverviewSearchResultVISType">
												<xs:sequence>
													<xs:group ref="ees:RankScoreGroup"/>
													<xs:element name="DossierID" type="DossierIDType">
														<xs:annotation>
															<xs:documentation>Description: ID of the Dossier.</xs:documentation>
														</xs:annotation>
													</xs:element>
												</xs:sequence>
											</xs:extension>
										</xs:complexContent>
									</xs:complexType>
								</xs:element>
								<xs:element name="VisaApplication" maxOccurs="unbounded">
									<xs:complexType>
										<xs:complexContent>
											<xs:extension base="VisaApplicationSearchResultEESType">
												<xs:sequence>
													<xs:group ref="ees:RankScoreGroup"/>
													<xs:element name="DossierID" type="DossierIDType">
														<xs:annotation>
															<xs:documentation>Description: ID of the Dossier.</xs:documentation>
														</xs:annotation>
													</xs:element>
												</xs:sequence>
											</xs:extension>
										</xs:complexContent>
									</xs:complexType>
								</xs:element>
							</xs:choice>
							<xs:element name="ProvidedSample" minOccurs="0">
								<xs:complexType>
									<xs:sequence>
										<xs:element name="SampleQuality" minOccurs="0">
											<xs:complexType>
												<xs:group ref="ees:FPQualityGroup"/>
											</xs:complexType>
										</xs:element>
										<xs:element name="SampleExceptions" type="vdta:SampleExceptionsType" minOccurs="0"/>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
						</xs:sequence>
					</xs:complexType>
				</xs:element>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
</xs:schema>
