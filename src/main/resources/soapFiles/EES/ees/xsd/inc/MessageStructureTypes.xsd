<?xml version="1.0" encoding="UTF-8"?>
<xs:schema  xmlns="http://www.europa.eu/schengen/ees/xsd/v1"
            xmlns:xs="http://www.w3.org/2001/XMLSchema"
            xmlns:shared="http://www.europa.eu/schengen/shared/xsd/v1"
            xmlns:ees="http://www.europa.eu/schengen/ees/xsd/v1"
            targetNamespace="http://www.europa.eu/schengen/ees/xsd/v1"
        
        elementFormDefault="qualified" version="1.00">
    
    <xs:include schemaLocation="CodeTableTypes.xsd"/>

    <xs:import namespace="http://www.europa.eu/schengen/shared/xsd/v1" schemaLocation="../../../shared/xsd/DataTypes.xsd"/>

    <xs:simpleType name="TransactionIDType">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="255"/>
        </xs:restriction>
    </xs:simpleType>
    
    <xs:simpleType name="TestCaseIDType">
        <xs:restriction base="xs:string">
            <xs:maxLength value="255"/>
        </xs:restriction>
    </xs:simpleType>
    
    <xs:complexType name="HeaderBaseType">
        <xs:sequence>
            <xs:element name="TransactionID" type="ees:TransactionIDType">
                <xs:annotation>
                    <xs:documentation>The transaction id field used for 2 purposes in EES.
                        It must filled by the NS with a unique identifier for the operation call.
                        First, for write operations and workflow engine operations, this transaction id is used to
                        identify retries of the same transaction and implements the idempotent.  It means that for some
                        technical reason, the NS retries the same exact operation call for a given transaction, it must
                        the use the same transaction id.
                        Additionally, this transaction id is copied in the header of the response sent to the NS which,
                        in the case of asynchronous operations, allows the NS to correlate the response message with its
                        initial request.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="Variant" type="ees:ST504_OperationVariantType"/>
            <xs:element name="SystemID" type="shared:SystemIDType">
                <xs:annotation>
                    <xs:documentation>Identification of the system that sends the message.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="Timestamp" type="xs:dateTime">
                <xs:annotation>
                    <xs:documentation>Message creation timestamp. Field to be populated by the system sending the request and to be logged independently from message reception time.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="EndUserID" type="shared:EndUserIDType" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>The EndUser is the application or officer acting for a User. The EES does not manage EndUsers. It is the responsibility of the User to authenticate its EndUser. An EndUserID is present in the message header for User usage only. It is User responsibility to provide the appropriate EndUserID.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="TestID" type="ees:TestCaseIDType" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>



    <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->

    <xs:complexType name="BaseMessageResponseType">
        <xs:sequence>
            <xs:element name="ReturnCodes">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="ErrorCodes" minOccurs="0">
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="ErrorCode" type="shared:ST14_ErrorCodeType" maxOccurs="unbounded"/>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                        <xs:element name="WarningCodes" minOccurs="0">
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="WarningCode" type="shared:ST15_WarningCodeType" maxOccurs="unbounded"/>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="SampleExceptionsType">
        <xs:sequence>
            <xs:element name="SampleError" type="shared:ST14_ErrorCodeType" minOccurs="0"/>
            <xs:element name="SampleWarning" type="shared:ST15_WarningCodeType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>

</xs:schema>
