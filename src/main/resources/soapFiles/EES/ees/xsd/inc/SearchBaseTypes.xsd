<?xml version="1.0" encoding="utf-8"?>

<xs:schema
		xmlns="http://www.europa.eu/schengen/ees/xsd/v1"
		xmlns:xs="http://www.w3.org/2001/XMLSchema"
		xmlns:shared="http://www.europa.eu/schengen/shared/xsd/v1"
		targetNamespace="http://www.europa.eu/schengen/ees/xsd/v1"
		elementFormDefault="qualified">
                

	<xs:import namespace="http://www.europa.eu/schengen/shared/xsd/v1" schemaLocation="../../../shared/xsd/DataTypes.xsd"/>

	<xs:include schemaLocation="BaseDataTypes.xsd"/>

	<xs:simpleType name="WeightType">
		<xs:restriction base="xs:unsignedByte">
			<xs:minExclusive value="0"/>
			<xs:maxInclusive value="100"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:complexType name="OpenEndPeriodType">
		<xs:sequence>
			<xs:element name="From" type="xs:dateTime" minOccurs="0"/>
			<xs:element name="To" type="xs:dateTime" minOccurs="0">
				<xs:annotation>
					<xs:documentation>If value is not provided then it is assumed to be now.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>

	<!-- scope modifiers -->

	<xs:complexType name="TravelHistoryScopeModifiersType">
		<xs:annotation>
			<xs:documentation>If not specified travel history (Entry/Exit/Refusal records) are not returned.</xs:documentation>
		</xs:annotation>
		<xs:choice>
			<xs:element name="Period" type="OpenEndPeriodType"/>
			<xs:element name="NoOfRecords" type="xs:integer">
				<xs:annotation>
					<xs:documentation>If NoOfRecords is -1 then all records are returned.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:choice>
	</xs:complexType>
	<xs:complexType name="NFPScopeModifiersType">
		<xs:annotation>
			<xs:documentation>If not specified NFP data is not returned.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Period" type="OpenEndPeriodType" minOccurs="0"/>
			<xs:element name="EstablishingMS" type="shared:CT70_UserType" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="UpdatesLogScopeModifiersType">
		<xs:annotation>
			<xs:documentation>If not specified the UpdatesLogs are not returned.</xs:documentation>
		</xs:annotation>
		<xs:choice>
			<xs:element name="Period" type="OpenEndPeriodType"/>
			<xs:element name="NoOfRecords" type="xs:integer">
				<xs:annotation>
					<xs:documentation>If NoOfRecords is -1 then all records are returned.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:choice>
	</xs:complexType>


	<xs:complexType name="BaseScopeModifiersType">
		<xs:sequence>
			<xs:element name="TravelHistory" minOccurs="0" type="TravelHistoryScopeModifiersType"/>
			<xs:element name="NFP" minOccurs="0" type="NFPScopeModifiersType"/>
			<xs:element name="UpdatesLog" minOccurs="0" type="UpdatesLogScopeModifiersType"/>
		</xs:sequence>
	</xs:complexType>

	<!-- search field types -->

	<xs:complexType name="TimestampSearchField">
		<xs:complexContent>
			<xs:extension base="OpenEndPeriodType">
				<xs:attribute name="weight" type="WeightType"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>

	<xs:complexType name="PseudoDateSearchField">
		<xs:choice>
			<xs:element name="Date" type="shared:PseudodateType"/>
			<xs:sequence>
				<xs:element name="From" type="shared:PseudodateType"/>
				<xs:element name="To" type="shared:PseudodateType" minOccurs="0">
					<xs:annotation>
						<xs:documentation>If value is not provided then it is assumed to be today.</xs:documentation>
					</xs:annotation>
				</xs:element>
			</xs:sequence>
		</xs:choice>
		<xs:attribute name="IgnoreInexact" type="xs:boolean" default="false">
			<xs:annotation>
				<xs:documentation>If this boolean is set to "true", records with inexact dates will be ignored and not be considered in the result set.
					Default value: "false"</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="weight" type="WeightType"/>
	</xs:complexType>

	<xs:complexType name="PseudoDateExactSearchField">
		<xs:sequence>
			<xs:element name="Date" type="shared:PseudodateType"/>
		</xs:sequence>
		<xs:attribute name="weight" type="WeightType"/>
	</xs:complexType>

	<xs:complexType name="NonWeightedNameSearchField">
		<xs:simpleContent>
			<xs:extension base="NonWeightedNameSearchFieldType">
				<xs:attribute name="mode" type="shared:ST03_SearchModeType"/>
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>

	<xs:simpleType name="NonWeightedNameSearchFieldType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="255"/>
			<xs:pattern value="[A-Za-zÀ-žẞ ,_\.\-]+"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:complexType name="NameSearchField">
		<xs:simpleContent>
			<xs:extension base="xs:string">
				<xs:attribute name="mode" type="shared:ST03_SearchModeType"/>
				<xs:attribute name="weight" type="WeightType"/>
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>

	<xs:complexType name="AnyNameSearchField">
		<xs:simpleContent>
			<xs:extension base="xs:string">
				<xs:attribute name="anyName" type="xs:boolean" default="false">
					<xs:annotation>
						<xs:documentation>Default value: "false"</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="mode" type="shared:ST03_SearchModeType"/>
				<xs:attribute name="weight" type="WeightType"/>
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>

	<xs:complexType name="BasicNumberSearchField">
		<xs:simpleContent>
			<xs:extension base="xs:string">
				<xs:attribute name="mode" type="shared:ST03_SearchModeType"/>
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>

	<xs:complexType name="NumberSearchField">
		<xs:simpleContent>
			<xs:extension base="xs:string">
				<xs:attribute name="mode" type="shared:ST03_SearchModeType"/>
				<xs:attribute name="weight" type="WeightType"/>
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>

	<xs:complexType name="ExactNumberSearchField">
		<xs:simpleContent>
			<xs:extension base="xs:string">
				<xs:attribute name="weight" type="WeightType"/>
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>

	<xs:complexType name="AuthorityUniqueIDSearchField">
		<xs:simpleContent>
			<xs:extension base="shared:AuthorityUniqueIDType">
				<xs:attribute name="weight" type="WeightType"/>
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>


	<!-- code table search fields -->

	<xs:complexType name="CT02_CountryOfNationalitySearchField">
		<xs:simpleContent>
			<xs:extension base="shared:CT02_CountryOfNationalityType">
				<xs:attribute name="weight" type="WeightType"/>
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>

	<xs:complexType name="CT04_GenderSearchField">
		<xs:simpleContent>
			<xs:extension base="shared:CT04_GenderType">
				<xs:attribute name="weight" type="WeightType"/>
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>

	<xs:complexType name="CT512_TravelDocumentTypeSearchField">
		<xs:simpleContent>
			<xs:extension base="shared:CT512_TravelDocumentTypeType">
				<xs:attribute name="weight" type="WeightType"/>
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>


	<!-- results -->
	<xs:group name="RankScoreGroup">
		<xs:sequence>
			<xs:element name="Rank" type="xs:unsignedInt"/>
			<xs:element name="Score" type="xs:unsignedInt">
				<xs:annotation>
					<xs:documentation>
						Normalised matched score between 0 and 100 for alphanumeric searches. 100 is the highest score for alphanumeric searches.
						Matching score between 0 and 100000 for biometric searches. 100000 is the highest score for biometric searches.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:group>
	<xs:group name="RankScoreGroupWithMatchingInterval">
		<xs:sequence>
			<xs:element name="Rank" type="xs:unsignedInt"/>
			<xs:element name="Score" type="xs:unsignedInt">
				<xs:annotation>
					<xs:documentation>
						Normalised matched score between 0 and 100 for alphanumeric searches. 100 is the highest score for alphanumeric searches.
						Matching score between 0 and 100000 for biometric searches. 100000 is the highest score for biometric searches.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MatchingInterval">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:length value="4"/>
						<xs:pattern value="[0-9]+"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>

		</xs:sequence>
	</xs:group>
	<xs:group name="ResultScoreGroup">
		<xs:sequence>
			<xs:element name="Result" type="xs:boolean"/>
			<xs:element name="Score" type="xs:unsignedInt">
				<xs:annotation>
					<xs:documentation>
						Normalised matched score between 0 and 100 for alphanumeric searches. 100 is the highest score for alphanumeric searches.
						Matching score between 0 and 100000 for biometric searches. 100000 is the highest score for biometric searches.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:group>

	<xs:complexType name="VerificationResultType">
		<xs:sequence>
			<xs:element name="TravellerFileID" type="IdentifierType"/>
			<xs:group ref="ResultScoreGroup"/>
		</xs:sequence>
	</xs:complexType>

	<!-- paging -->

	<xs:complexType name="PagingRequestType">
		<xs:sequence>
			<xs:element name="PageNumber" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:unsignedInt">
						<xs:minInclusive value="1"/>
						<xs:maxInclusive value="999"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="PageSize" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:unsignedInt">
						<xs:minInclusive value="1"/>
						<xs:maxInclusive value="999"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>

	<xs:complexType name="PagingResponseType">
		<xs:sequence>
			<xs:element name="TotalNoOfHits" type="xs:unsignedInt"/>
			<xs:element name="PageNumber">
				<xs:simpleType>
					<xs:restriction base="xs:unsignedInt">
						<xs:minInclusive value="1"/>
						<xs:maxInclusive value="999"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="HitsOnPage" type="xs:unsignedInt"/>
		</xs:sequence>
	</xs:complexType>


	<!-- Travel document search with optional fields -->
	<xs:complexType name="TravelDocumentSearchRequestType">
		<xs:annotation>
			<xs:documentation>Fields of the search operation

				[Article 16(1)(a)] Surname (family name); first name or names (given names); date of birth; nationality or nationalities; sex.
				[Article 16(1)(b)] The type and number of the travel document or documents and three letter code of the issuing country of the travel document or documents.
				[Article 16(1)(c)] The date of expiry of the validity of the travel document or documents.

			</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="FamilyName" type="AnyNameSearchField" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Surname (family name); first name(s) (given names) (with any name modifier).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="FirstName" maxOccurs="unbounded" type="NameSearchField" minOccurs="0">
				<xs:annotation>
					<xs:documentation>First name(s) (given names).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DateOfBirth" type="PseudoDateSearchField" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Date of birth.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Nationality" maxOccurs="unbounded" type="CT02_CountryOfNationalitySearchField" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Current nationality or nationalities.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Gender" type="CT04_GenderSearchField" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Sex.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DocumentNumber" type="NumberSearchField" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Number of the travel document.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DocumentType" type="CT512_TravelDocumentTypeSearchField" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Type of the travel document.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ValidUntil" type="PseudoDateSearchField" minOccurs="0">
				<xs:annotation>
					<xs:documentation>The date of expiry of the validity of the travel document or documents.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>

	<!-- Travel document search with mandatory fields -->
	<xs:complexType name="TravelDocumentSearchWithMandatoryFieldsRequestType">
		<xs:annotation>
			<xs:documentation>Fields of the search operation

				[Article 16(1)(a)] Surname (family name); first name or names (given names); date of birth; nationality or nationalities; sex.
				[Article 16(1)(b)] The type and number of the travel document or documents and three letter code of the issuing country of the travel document or documents.
				[Article 16(1)(c)] The date of expiry of the validity of the travel document or documents.

			</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="FamilyName" type="AnyNameSearchField">
				<xs:annotation>
					<xs:documentation>Surname (family name); first name(s) (given names) (with any name modifier).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="FirstName" minOccurs="0" maxOccurs="unbounded" type="NameSearchField">
				<xs:annotation>
					<xs:documentation>First name(s) (given names).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DateOfBirth" type="PseudoDateSearchField">
				<xs:annotation>
					<xs:documentation>Date of birth.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Nationality" maxOccurs="unbounded" type="CT02_CountryOfNationalitySearchField">
				<xs:annotation>
					<xs:documentation>Current nationality or nationalities.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Gender" type="CT04_GenderSearchField">
				<xs:annotation>
					<xs:documentation>Sex.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DocumentNumber" type="NumberSearchField">
				<xs:annotation>
					<xs:documentation>Number of the travel document.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DocumentType" type="CT512_TravelDocumentTypeSearchField">
				<xs:annotation>
					<xs:documentation>Type of the travel document.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ValidUntil" type="PseudoDateSearchField">
				<xs:annotation>
					<xs:documentation>The date of expiry of the validity of the travel document or documents.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>

</xs:schema>
