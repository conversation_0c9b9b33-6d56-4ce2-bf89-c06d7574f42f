<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns="http://www.europa.eu/schengen/ees/xsd/v1"
		   xmlns:xs="http://www.w3.org/2001/XMLSchema"
		   targetNamespace="http://www.europa.eu/schengen/ees/xsd/v1"
           elementFormDefault="qualified">


	<xs:include schemaLocation="BaseDataTypes.xsd"/>
	<xs:include schemaLocation="CodeTableTypes.xsd"/>

	<!--===== FI =====-->
	<xs:simpleType name="FINISTFile">
		<xs:restriction base="xs:base64Binary"/>
	</xs:simpleType>

	<xs:group name="FIQualityGroup">
		<xs:sequence>
			<xs:element name="ImageQualityValue" type="xs:unsignedByte" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						Image quality score. The available scores are between "0" and "100",
						where "0" means no quality value and "100" is considered the highest quality value.
						A value of "255" indicates that computation of the quality score has failed.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:group>


	<xs:complexType name="FIRequestType">
		<xs:annotation>
			<xs:documentation>Facial image - type for the request.</xs:documentation>
		</xs:annotation>
		<xs:choice>
			<xs:sequence>
				<xs:element name="NISTFile" type="FINISTFile">
					<xs:annotation>
						<xs:documentation>The facial image which shall have sufficient image resolution and quality to be used in automated biometric matching, in accordance with Article 15;
							[Article 16(1)(d), Article 17(1)(b)]</xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="ImageQualityValue" type="xs:unsignedByte">
					<xs:annotation>
						<xs:documentation>
							Image quality score. The available scores are between "0" and "100",
							where "0" means no quality value and "100" is considered the highest quality value.
							A value of "255" indicates that computation of the quality score has failed.
						</xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="Source" type="CT506_FISourceType">
					<xs:annotation>
						<xs:documentation>Code table value indicating whether the image source is passport or image taken live.</xs:documentation>
					</xs:annotation>
				</xs:element>
			</xs:sequence>
			<xs:element name="NotProvidedReason" type="CT507_FIReasonType">
				<xs:annotation>
					<xs:documentation>Code table value indicating a reason why FI was not provided.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:choice>
	</xs:complexType>

	<xs:complexType name="FIResponseType">
		<xs:annotation>
			<xs:documentation>Facial image - type for the request.</xs:documentation>
		</xs:annotation>
		<xs:choice>
			<xs:sequence>
				<xs:element name="NISTFile" type="IdentifierType">
					<xs:annotation>
						<xs:documentation>The facial image which shall have sufficient image resolution and quality to be used in automated biometric matching, in accordance with Article 15;
							[Article 16(1)(d), Article 17(1)(b)]</xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="ImageQualityValue" type="xs:unsignedByte">
					<xs:annotation>
						<xs:documentation>
							Image quality score. The available scores are between "0" and "100",
							where "0" means no quality value and "100" is considered the highest quality value.
							A value of "255" indicates that computation of the quality score has failed.
						</xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="Source" type="CT506_FISourceType">
					<xs:annotation>
						<xs:documentation>Code table value indicating whether the image source is passport or image taken live.</xs:documentation>
					</xs:annotation>
				</xs:element>
			</xs:sequence>
			<xs:element name="NotProvidedReason" type="CT507_FIReasonType">
				<xs:annotation>
					<xs:documentation>Code table value indicating a reason why FI was not provided.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:choice>
	</xs:complexType>

	<xs:complexType name="FIBaseType">
		<xs:annotation>
			<xs:documentation>Facial image - base model.</xs:documentation>
		</xs:annotation>
		<xs:choice>
			<xs:sequence>
				<xs:element name="ImageQualityValue" type="xs:unsignedByte">
					<xs:annotation>
						<xs:documentation>
							Image quality score. The available scores are between “0” and “100”,
							where “0” means the lowest quality value and “100” is considered the highest quality value.
							The quality score is calculated using the shared Biometric Matching Service facial image quality (sFIQ) assessment tool.
						</xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="Source" type="CT506_FISourceType">
					<xs:annotation>
						<xs:documentation>Code table value indicating whether the image source is passport or image taken live.</xs:documentation>
					</xs:annotation>
				</xs:element>
			</xs:sequence>
			<xs:element name="NotProvidedReason" type="CT507_FIReasonType">
				<xs:annotation>
					<xs:documentation>Code table value indicating a reason why FI was not provided.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:choice>
	</xs:complexType>

	<xs:complexType name="FIWithUpdatesLogType">
		<xs:annotation>
			<xs:documentation>Facial image - type for the response.</xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base="FIBaseType">
				<xs:sequence>
					<xs:element name="NISTFile" type="IdentifierType" minOccurs="0">
						<xs:annotation>
							<xs:documentation>This identifier can be used to retrieve the NIST file using the HTTP/GET RetrieveBinary operation</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:group ref="UpdatesLogAuditGroup"/>
					<xs:element name="UpdatesLog" minOccurs="0" maxOccurs="unbounded">
						<xs:complexType>
							<xs:complexContent>
								<xs:extension base="FIBaseType">
									<xs:sequence>
										<xs:group ref="UpdatesLogAuditGroup"/>
									</xs:sequence>
								</xs:extension>
							</xs:complexContent>
						</xs:complexType>
					</xs:element>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>

	</xs:complexType>

	<!--===== FP =====-->
	<xs:complexType name="FPRequestType">
		<xs:annotation>
			<xs:documentation>Fingerprints set - type for the request.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="NISTFile" type="FPNISTFile" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Fingerprint data from the right hand, where present, and otherwise the corresponding fingerprint data from the left hand; Fingerprint data shall have sufficient resolution and quality to be used in automated biometric matching.

						[Article 17(1)(c)]</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ImageQuality" type="FPImageQualityType" minOccurs="0" maxOccurs="4">
				<xs:annotation>
					<xs:documentation>A fingerprint image quality</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="NotProvidedReason" type="CT503_FPReasonType" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Code table value indicating the reason why fingerprints were not provided (the whole set).

						Children under the age of 12 shall be exempt from the requirement to give fingerprints.
						(...)
						Persons for whom fingerprinting is physically impossible shall be exempt from the requirement to give fingerprints.
						However, where the physical impossibility is of a temporary nature, that fact shall be recorded in the EES and the person shall be required to give the fingerprints on exit or at the subsequent entry. This information shall be deleted from the EES once the fingerprints have been given. The border authorities shall be entitled to request further clarification on the grounds for the temporary impossibility to give fingerprints. Member States shall ensure that appropriate procedures guaranteeing the dignity of the person are in place in the event of difficulties encountered in the capturing of fingerprints.
						(...)
						Where the person concerned is exempt from the requirement to give fingerprints pursuant to paragraphs 3 or 4, the specific data field shall be marked as ‘not applicable’.

						[Article 17(3)-(5)]</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>

	<xs:complexType name="FPResponseType">
		<xs:annotation>
			<xs:documentation>Fingerprints set - type for the request.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="NISTFile" type="IdentifierType" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Fingerprint data from the right hand, where present, and otherwise the corresponding fingerprint data from the left hand; Fingerprint data shall have sufficient resolution and quality to be used in automated biometric matching.

						[Article 17(1)(c)]</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ImageQuality" type="FPImageQualityType" minOccurs="0" maxOccurs="4">
				<xs:annotation>
					<xs:documentation>A fingerprint image quality</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="NotProvidedReason" type="CT503_FPReasonType" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Code table value indicating the reason why fingerprints were not provided (the whole set).

						Children under the age of 12 shall be exempt from the requirement to give fingerprints.
						(...)
						Persons for whom fingerprinting is physically impossible shall be exempt from the requirement to give fingerprints.
						However, where the physical impossibility is of a temporary nature, that fact shall be recorded in the EES and the person shall be required to give the fingerprints on exit or at the subsequent entry. This information shall be deleted from the EES once the fingerprints have been given. The border authorities shall be entitled to request further clarification on the grounds for the temporary impossibility to give fingerprints. Member States shall ensure that appropriate procedures guaranteeing the dignity of the person are in place in the event of difficulties encountered in the capturing of fingerprints.
						(...)
						Where the person concerned is exempt from the requirement to give fingerprints pursuant to paragraphs 3 or 4, the specific data field shall be marked as ‘not applicable’.

						[Article 17(3)-(5)]</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>

	<xs:complexType name="FPBaseType">
		<xs:annotation>
			<xs:documentation>Fingerprints set - base model.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:group ref="FPQualityGroup"/>
			<xs:element name="NotProvidedReason" type="CT503_FPReasonType" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Code table value indicating the reason why fingerprints were not provided (the whole set).

						Children under the age of 12 shall be exempt from the requirement to give fingerprints.
						(...)
						Persons for whom fingerprinting is physically impossible shall be exempt from the requirement to give fingerprints.
						However, where the physical impossibility is of a temporary nature, that fact shall be recorded in the EES and the person shall be required to give the fingerprints on exit or at the subsequent entry. This information shall be deleted from the EES once the fingerprints have been given. The border authorities shall be entitled to request further clarification on the grounds for the temporary impossibility to give fingerprints. Member States shall ensure that appropriate procedures guaranteeing the dignity of the person are in place in the event of difficulties encountered in the capturing of fingerprints.
						(...)
						Where the person concerned is exempt from the requirement to give fingerprints pursuant to paragraphs 3 or 4, the specific data field shall be marked as ‘not applicable’.

						[Article 17(3)-(5)]</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>

	<xs:complexType name="FPWithUpdatesLogType">
		<xs:annotation>
			<xs:documentation>Fingerprints set - type for the response.</xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base="FPBaseType">
				<xs:sequence>
					<xs:element name="NISTFile" type="IdentifierType" minOccurs="0">
						<xs:annotation>
							<xs:documentation>This identifier can be used to retrieve the NIST file using the HTTP/GET
								RetrieveBinary operation
							</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:group ref="UpdatesLogAuditGroup"/>
					<xs:element name="UpdatesLog" minOccurs="0" maxOccurs="unbounded">
						<xs:complexType>
							<xs:complexContent>
								<xs:extension base="FPBaseType">
									<xs:sequence>
										<xs:group ref="UpdatesLogAuditGroup"/>
									</xs:sequence>
								</xs:extension>
							</xs:complexContent>
						</xs:complexType>
					</xs:element>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>

	<!--==============-->


	<xs:simpleType name="FPNISTFile">
		<xs:restriction base="xs:base64Binary"/>
	</xs:simpleType>


	<xs:group name="FPQualityGroup">
		<xs:sequence>
			<xs:element name="ImageQuality" type="FPImageQualityType" minOccurs="0" maxOccurs="4">
				<xs:annotation>
					<xs:documentation>A fingerprint image quality</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:group>
	<xs:complexType name="FPImageQualityType">
		<xs:sequence>
			<xs:element name="FingerPositionCode" type="CT509_FingerCodeType">
				<xs:annotation>
					<xs:documentation>A finger position code</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="QualityValue" type="xs:unsignedByte">
				<xs:annotation>
					<xs:documentation>NFIQ 2.0 quality score which is in [0-100], where 0 means no utility value and 100 is the highest utility value. A value of 255 indicates that computation of quality score has failed. A value of 201 indicates that fingerprint is missing due to temporary reason and value 202 indicates that fingerprint is missing due to permanent reason.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>

	<xs:complexType name="FIToBeStoredInTravellerFileType">
		<xs:choice>
			<xs:element name="FIToBeStoredInTravellerFile">
				<xs:annotation>
					<xs:documentation>FI to be stored in the Traveller File</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="IdentifierType">
						<xs:maxLength value="255"/>
						<xs:pattern value="[A-Za-z0-9&amp;\*\+\?\(\)\[\]\.\-~%!#$/:;=@_]+"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="NotProvidedReason" type="CT507_FIReasonType">
				<xs:annotation>
					<xs:documentation>Code table value indicating a reason why FI was not provided.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:choice>
	</xs:complexType>

	<xs:complexType name="FPToBeStoredInTravellerFileType">
		<xs:sequence>
			<xs:element name="FPToBeStoredInTravellerFile" minOccurs="0">
				<xs:annotation>
					<xs:documentation>FP to be stored in the Traveller File</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="IdentifierType">
						<xs:maxLength value="255"/>
						<xs:pattern value="[A-Za-z0-9&amp;\*\+\?\(\)\[\]\.\-~%!#$/:;=@_]+"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="NotProvidedReason" type="CT503_FPReasonType" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Code table value indicating the reason why fingerprints were not provided (the whole set).
						Children under the age of 12 shall be exempt from the requirement to give fingerprints.
						(...)
						Persons for whom fingerprinting is physically impossible shall be exempt from the requirement to give fingerprints.
						However, where the physical impossibility is of a temporary nature, that fact shall be recorded in the EES and the person shall be required to give the fingerprints on exit or at the subsequent entry. This information shall be deleted from the EES once the fingerprints have been given. The border authorities shall be entitled to request further clarification on the grounds for the temporary impossibility to give fingerprints. Member States shall ensure that appropriate procedures guaranteeing the dignity of the person are in place in the event of difficulties encountered in the capturing of fingerprints.
						(...)
						Where the person concerned is exempt from the requirement to give fingerprints pursuant to paragraphs 3 or 4, the specific data field shall be marked as ‘not applicable’.
						[Article 17(3)-(5)]</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="FPToBeDeletedFromTravellerFile" type="IdentifierType" minOccurs="0">
				<xs:annotation>
					<xs:documentation>FP to be deleted from the Traveller File</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>

</xs:schema>
