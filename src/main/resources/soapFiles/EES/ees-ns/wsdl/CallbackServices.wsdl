<?xml version="1.0" encoding="UTF-8" ?>

<definitions targetNamespace="http://www.europa.eu/schengen/ees/webservice/v1"
             xmlns="http://schemas.xmlsoap.org/wsdl/"
             xmlns:tns="http://www.europa.eu/schengen/ees/webservice/v1"
             xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/"
             xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/"
             xmlns:xsd="http://www.w3.org/2001/XMLSchema"
             xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
             xmlns:types="http://www.europa.eu/schengen/ees-ns/xsd/v1">

  <types>
    <xsd:schema targetNamespace="http://www.europa.eu/schengen/ees-ns/xsd/v1" elementFormDefault="qualified">
      <xsd:include schemaLocation="../xsd/Messages.xsd"/>
      <xsd:element name="NotificationHeader" type="types:HeaderResponseType"/>
      <xsd:element name="NotificationAckHeader" type="types:HeaderRequestType"/>
      <xsd:element name="NotificationAckBody" >
        <xsd:simpleType>
          <xsd:restriction base="xsd:string">
            <xsd:maxLength value="0"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
      <xsd:element name="ResponseHeader" type="types:HeaderResponseType"/>
      <xsd:element name="ResponseAckHeader" type="types:HeaderRequestType"/>
      <xsd:element name="ResponseAckBody" >
        <xsd:simpleType>
          <xsd:restriction base="xsd:string">
            <xsd:maxLength value="0"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
    </xsd:schema>
  </types>

  <message name="NotificationHeader" >
    <part name="EES" element="types:NotificationHeader"/>
  </message>
  <message name="NotificationAckHeader" >
    <part name="EES" element="types:NotificationAckHeader"/>
  </message>
  <message name="NotificationAckResponse" >
    <part name="ack" element="types:NotificationAckBody"/>
  </message>
  <message name="ResponseHeader" >
    <part name="EES" element="types:ResponseHeader"/>
  </message>
  <message name="ResponseAckHeader" >
    <part name="EES" element="types:ResponseAckHeader"/>
  </message>
  <message name="ResponseAckResponse" >
    <part name="ack" element="types:ResponseAckBody"/>
  </message>

  
  <!-- === TravellerFile Callback Service messages === -->

    
  <!-- TravellerFile.OverstayersScheduledDeletion messages -->
  <message name="OverstayersScheduledDeletionNotification" >
    <part name="notification" element="types:OverstayersScheduledDeletionNotification"/>
  </message>
    
  <!-- TravellerFile.TravellerFileDeletion messages -->
  <message name="TravellerFileDeletionNotification" >
    <part name="notification" element="types:TravellerFileDeletionNotification"/>
  </message>
    
  

 
  <!-- === VISRelay Async Service messages === -->

    
  <!-- VISRelay.IdentificationInVIS async messages -->
  <message name="IdentificationInVISAsyncResponse">
    <part name="response" element="types:IdentificationInVISResponse"/>
  </message>
    
  
  <!-- === TravellerFile Async Service messages === -->

    
  <!-- TravellerFile.IdentificationInEES async messages -->
  <message name="IdentificationInEESAsyncResponse">
    <part name="response" element="types:IdentificationInEESResponse"/>
  </message>
    
  
  <!-- === BorderControl Async Service messages === -->

    
  <!-- BorderControl.StartBorderControl async messages -->
  <message name="StartBorderControlAsyncResponse">
    <part name="response" element="types:StartBorderControlResponse"/>
  </message>
    
  <!-- BorderControl.AddDataToBorderControl async messages -->
  <message name="AddDataToBorderControlAsyncResponse">
    <part name="response" element="types:AddDataToBorderControlResponse"/>
  </message>
    
  

  
  <!-- === TravellerFile Notification Callback Service portType definition === -->

  <portType name="TravellerFileNotificationService">
    
    <operation name="OverstayersScheduledDeletion">
        <input message="tns:OverstayersScheduledDeletionNotification"/>
        <output message="tns:NotificationAckResponse"/>
    </operation>
    
    <operation name="TravellerFileDeletion">
        <input message="tns:TravellerFileDeletionNotification"/>
        <output message="tns:NotificationAckResponse"/>
    </operation>
    
  </portType>
  

 
  <!-- === VISRelay Async Service portType definition === -->

  <portType name="VISRelayAsyncService">
    
    <operation name="IdentificationInVISCallback">
        <input message="tns:IdentificationInVISAsyncResponse"/>
        <output message="tns:ResponseAckResponse"/>
    </operation>
    
  </portType>
  
  <!-- === TravellerFile Async Service portType definition === -->

  <portType name="TravellerFileAsyncService">
    
    <operation name="IdentificationInEESCallback">
        <input message="tns:IdentificationInEESAsyncResponse"/>
        <output message="tns:ResponseAckResponse"/>
    </operation>
    
  </portType>
  
  <!-- === BorderControl Async Service portType definition === -->

  <portType name="BorderControlAsyncService">
    
    <operation name="StartBorderControlCallback">
        <input message="tns:StartBorderControlAsyncResponse"/>
        <output message="tns:ResponseAckResponse"/>
    </operation>
    
    <operation name="AddDataToBorderControlCallback">
        <input message="tns:AddDataToBorderControlAsyncResponse"/>
        <output message="tns:ResponseAckResponse"/>
    </operation>
    
  </portType>
  

  
  <!-- === TravellerFile Notification Callback Service binding definition === -->

  <binding name="TravellerFileNotificationServiceSOAP12Binding" type="tns:TravellerFileNotificationService">
    <soap12:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
    
    <operation name="OverstayersScheduledDeletion">
      <soap12:operation style="document" soapAction="http://www.europa.eu/schengen/ees/webservice/v1/OverstayersScheduledDeletion"/>
      <input>
        <soap12:header use="literal" message="tns:NotificationHeader" part="EES"/>
        <soap12:body use="literal" parts="notification"/>
      </input>
      <output>
        <soap12:header use="literal" message="tns:NotificationAckHeader" part="EES"/>
        <soap12:body use="literal" parts="ack"/>
      </output>
    </operation>
    
    <operation name="TravellerFileDeletion">
      <soap12:operation style="document" soapAction="http://www.europa.eu/schengen/ees/webservice/v1/TravellerFileDeletion"/>
      <input>
        <soap12:header use="literal" message="tns:NotificationHeader" part="EES"/>
        <soap12:body use="literal" parts="notification"/>
      </input>
      <output>
        <soap12:header use="literal" message="tns:NotificationAckHeader" part="EES"/>
        <soap12:body use="literal" parts="ack"/>
      </output>
    </operation>
    
  </binding>
  

  
  <!-- === VISRelay Async Response Callback Service binding definition === -->

  <binding name="VISRelayAsyncServiceSOAP12Binding" type="tns:VISRelayAsyncService">
    <soap12:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
    
    <operation name="IdentificationInVISCallback">
      <soap12:operation style="document" soapAction="http://www.europa.eu/schengen/ees/webservice/v1/AsyncIdentificationInVISCallback"/>
      <input>
        <soap12:header use="literal" message="tns:ResponseHeader" part="EES"/>
        <soap12:body use="literal" parts="response"/>
      </input>
      <output>
        <soap12:header use="literal" message="tns:ResponseAckHeader" part="EES"/>
        <soap12:body use="literal" parts="ack"/>
      </output>
    </operation>
    
  </binding>
  
  <!-- === TravellerFile Async Response Callback Service binding definition === -->

  <binding name="TravellerFileAsyncServiceSOAP12Binding" type="tns:TravellerFileAsyncService">
    <soap12:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
    
    <operation name="IdentificationInEESCallback">
      <soap12:operation style="document" soapAction="http://www.europa.eu/schengen/ees/webservice/v1/AsyncIdentificationInEESCallback"/>
      <input>
        <soap12:header use="literal" message="tns:ResponseHeader" part="EES"/>
        <soap12:body use="literal" parts="response"/>
      </input>
      <output>
        <soap12:header use="literal" message="tns:ResponseAckHeader" part="EES"/>
        <soap12:body use="literal" parts="ack"/>
      </output>
    </operation>
    
  </binding>
  
  <!-- === BorderControl Async Response Callback Service binding definition === -->

  <binding name="BorderControlAsyncServiceSOAP12Binding" type="tns:BorderControlAsyncService">
    <soap12:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
    
    <operation name="StartBorderControlCallback">
      <soap12:operation style="document" soapAction="http://www.europa.eu/schengen/ees/webservice/v1/AsyncStartBorderControlCallback"/>
      <input>
        <soap12:header use="literal" message="tns:ResponseHeader" part="EES"/>
        <soap12:body use="literal" parts="response"/>
      </input>
      <output>
        <soap12:header use="literal" message="tns:ResponseAckHeader" part="EES"/>
        <soap12:body use="literal" parts="ack"/>
      </output>
    </operation>
    
    <operation name="AddDataToBorderControlCallback">
      <soap12:operation style="document" soapAction="http://www.europa.eu/schengen/ees/webservice/v1/AsyncAddDataToBorderControlCallback"/>
      <input>
        <soap12:header use="literal" message="tns:ResponseHeader" part="EES"/>
        <soap12:body use="literal" parts="response"/>
      </input>
      <output>
        <soap12:header use="literal" message="tns:ResponseAckHeader" part="EES"/>
        <soap12:body use="literal" parts="ack"/>
      </output>
    </operation>
    
  </binding>
  

  <service name="NSEESCallbackService">
  
    
    <port name="VISRelayAsyncServiceSOAP12" binding="tns:VISRelayAsyncServiceSOAP12Binding">
      <soap12:address location="http://www.example.com"/>
      <!--The actual URL will be provided by configuration on the NUI.-->
    </port>
    
  
    
    <port name="TravellerFileAsyncServiceSOAP12" binding="tns:TravellerFileAsyncServiceSOAP12Binding">
      <soap12:address location="http://www.example.com"/>
      <!--The actual URL will be provided by configuration on the NUI.-->
    </port>
    


    <port name="BorderControlAsyncServiceSOAP12" binding="tns:BorderControlAsyncServiceSOAP12Binding">
      <soap12:address location="http://www.example.com"/>
      <!--The actual URL will be provided by configuration on the NUI.-->
    </port>



    <port name="TravellerFileServiceSOAP12" binding="tns:TravellerFileNotificationServiceSOAP12Binding">
      <soap12:address location="http://www.example.com"/>
      <!--The actual URL will be provided by configuration on the NUI.-->
    </port>
  
  </service>

</definitions>








