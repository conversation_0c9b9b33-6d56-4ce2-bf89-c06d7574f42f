

syntax = "proto3";

package eu.europa.eulisa.http://www.europa.eu/schengen/ees-ns/xsd/v1;

option java_multiple_files = true;
option java_package = "eu.europa.eulisa.http://www.europa.eu/schengen/ees-ns/xsd/v1";



    // === TravellerFile Service === -->
    service TravellerFileService {

      rpc DataEntry (DataEntryRequest) returns (DataEntryResponse);

      rpc DataAmendment (DataAmendmentRequest) returns (DataAmendmentResponse);

      rpc AdvanceDataDeletion (AdvanceDataDeletionRequest) returns (AdvanceDataDeletionResponse);

      rpc Rebuttal (RebuttalRequest) returns (RebuttalResponse);

      rpc ChangeAuthorisation (ChangeAuthorisationRequest) returns (ChangeAuthorisationResponse);

      rpc SearchByTravelDocument (SearchByTravelDocumentRequest) returns (SearchByTravelDocumentResponse);

      rpc SearchByVSN (SearchByVSNRequest) returns (SearchByVSNResponse);

      rpc SearchByPersonalDataInEES (SearchByPersonalDataInEESRequest) returns (SearchByPersonalDataInEESResponse);

      rpc SearchForTravelHistory (SearchForTravelHistoryRequest) returns (SearchForTravelHistoryResponse);

      rpc RetrieveTravellerFile (RetrieveTravellerFileRequest) returns (RetrieveTravellerFileResponse);

      rpc VerificationByFIInEES (VerificationByFIInEESRequest) returns (VerificationByFIInEESResponse);

      rpc VerificationByFPInEES (VerificationByFPInEESRequest) returns (VerificationByFPInEESResponse);

      rpc OverstayersReport (OverstayersReportRequest) returns (OverstayersReportResponse);

      rpc Calculator (CalculatorRequest) returns (CalculatorResponse);

      rpc IdentificationInEES (IdentificationInEESRequest) returns (IdentificationInEESResponse);

    }

    // === VISRelay Service === -->
    service VISRelayService {

      rpc SearchByPersonalDataInVIS (SearchByPersonalDataInVISRequest) returns (SearchByPersonalDataInVISResponse);

      rpc VerificationByFPInVIS (VerificationByFPInVISRequest) returns (VerificationByFPInVISResponse);

      rpc IdentificationInVIS (IdentificationInVISRequest) returns (IdentificationInVISResponse);

    }

    // === PreEnrolment Service === -->
    service PreEnrolmentService {

      rpc DataPreEnrolment (DataPreEnrolmentRequest) returns (DataPreEnrolmentResponse);

      rpc RetrievePreEnrolledData (RetrievePreEnrolledDataRequest) returns (RetrievePreEnrolledDataResponse);

    }

    // === Authority Service === -->
    service AuthorityService {

      rpc UpdateAuthority (UpdateAuthorityRequest) returns (UpdateAuthorityResponse);

      rpc SearchAuthority (SearchAuthorityRequest) returns (SearchAuthorityResponse);

    }

    // === VEVHMatrix Service === -->
    service VEVHMatrixService {

      rpc RetrieveVHVEMatrix (RetrieveVHVEMatrixRequest) returns (RetrieveVHVEMatrixResponse);

      rpc UpdateVHVEMatrix (UpdateVHVEMatrixRequest) returns (UpdateVHVEMatrixResponse);

    }

    // === BorderControl Service === -->
    service BorderControlService {

      rpc EndBorderControl (EndBorderControlRequest) returns (EndBorderControlResponse);

      rpc AbortBorderControl (AbortBorderControlRequest) returns (AbortBorderControlResponse);

      rpc IdentificationResult (IdentificationResultRequest) returns (IdentificationResultResponse);

      rpc SearchOngoingBorderControlTransactions (SearchOngoingBorderControlTransactionsRequest) returns (SearchOngoingBorderControlTransactionsResponse);

      rpc StartBorderControl (StartBorderControlRequest) returns (StartBorderControlResponse);

      rpc AddDataToBorderControl (AddDataToBorderControlRequest) returns (AddDataToBorderControlResponse);

    }



