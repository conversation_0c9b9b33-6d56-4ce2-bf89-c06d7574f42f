<?xml version="1.0" encoding="utf-8"?>
<!--
This xsd contains all the messages defined as part of the ICD
-->
<xs:schema xmlns="http://www.europa.eu/schengen/ees-ns/xsd/v1" xmlns:xs="http://www.w3.org/2001/XMLSchema"
           targetNamespace="http://www.europa.eu/schengen/ees-ns/xsd/v1">

    <xs:include schemaLocation="inc/MessageStructureTypes.xsd"/>


    <!-- === TravellerFile Service request/response operations === -->

    <!-- TravellerFile.DataEntry messages -->
    <xs:include schemaLocation="messages/DataEntryRequest.xsd"/>
    <xs:include schemaLocation="messages/DataEntryResponse.xsd"/>

    <!-- TravellerFile.DataAmendment messages -->
    <xs:include schemaLocation="messages/DataAmendmentRequest.xsd"/>
    <xs:include schemaLocation="messages/DataAmendmentResponse.xsd"/>

    <!-- TravellerFile.AdvanceDataDeletion messages -->
    <xs:include schemaLocation="messages/AdvanceDataDeletionRequest.xsd"/>
    <xs:include schemaLocation="messages/AdvanceDataDeletionResponse.xsd"/>

    <!-- TravellerFile.Rebuttal messages -->
    <xs:include schemaLocation="messages/RebuttalRequest.xsd"/>
    <xs:include schemaLocation="messages/RebuttalResponse.xsd"/>

    <!-- TravellerFile.ChangeAuthorisation messages -->
    <xs:include schemaLocation="messages/ChangeAuthorisationRequest.xsd"/>
    <xs:include schemaLocation="messages/ChangeAuthorisationResponse.xsd"/>

    <!-- TravellerFile.SearchByTravelDocument messages -->
    <xs:include schemaLocation="messages/SearchByTravelDocumentRequest.xsd"/>
    <xs:include schemaLocation="messages/SearchByTravelDocumentResponse.xsd"/>

    <!-- TravellerFile.SearchByVSN messages -->
    <xs:include schemaLocation="messages/SearchByVSNRequest.xsd"/>
    <xs:include schemaLocation="messages/SearchByVSNResponse.xsd"/>

    <!-- TravellerFile.SearchByPersonalDataInEES messages -->
    <xs:include schemaLocation="messages/SearchByPersonalDataInEESRequest.xsd"/>
    <xs:include schemaLocation="messages/SearchByPersonalDataInEESResponse.xsd"/>

    <!-- TravellerFile.SearchForTravelHistory messages -->
    <xs:include schemaLocation="messages/SearchForTravelHistoryRequest.xsd"/>
    <xs:include schemaLocation="messages/SearchForTravelHistoryResponse.xsd"/>

    <!-- TravellerFile.RetrieveTravellerFile messages -->
    <xs:include schemaLocation="messages/RetrieveTravellerFileRequest.xsd"/>
    <xs:include schemaLocation="messages/RetrieveTravellerFileResponse.xsd"/>

    <!-- TravellerFile.VerificationByFIInEES messages -->
    <xs:include schemaLocation="messages/VerificationByFIInEESRequest.xsd"/>
    <xs:include schemaLocation="messages/VerificationByFIInEESResponse.xsd"/>

    <!-- TravellerFile.VerificationByFPInEES messages -->
    <xs:include schemaLocation="messages/VerificationByFPInEESRequest.xsd"/>
    <xs:include schemaLocation="messages/VerificationByFPInEESResponse.xsd"/>

    <!-- TravellerFile.OverstayersReport messages -->
    <xs:include schemaLocation="messages/OverstayersReportRequest.xsd"/>
    <xs:include schemaLocation="messages/OverstayersReportResponse.xsd"/>

    <!-- TravellerFile.Calculator messages -->
    <xs:include schemaLocation="messages/CalculatorRequest.xsd"/>
    <xs:include schemaLocation="messages/CalculatorResponse.xsd"/>

    <!-- TravellerFile.IdentificationInEES messages -->
    <xs:include schemaLocation="messages/IdentificationInEESRequest.xsd"/>
    <xs:include schemaLocation="messages/IdentificationInEESResponse.xsd"/>


    <!-- === VISRelay Service request/response operations === -->

    <!-- VISRelay.SearchByPersonalDataInVIS messages -->
    <xs:include schemaLocation="messages/SearchByPersonalDataInVISRequest.xsd"/>
    <xs:include schemaLocation="messages/SearchByPersonalDataInVISResponse.xsd"/>

    <!-- VISRelay.VerificationByFPInVIS messages -->
    <xs:include schemaLocation="messages/VerificationByFPInVISRequest.xsd"/>
    <xs:include schemaLocation="messages/VerificationByFPInVISResponse.xsd"/>

    <!-- VISRelay.IdentificationInVIS messages -->
    <xs:include schemaLocation="messages/IdentificationInVISRequest.xsd"/>
    <xs:include schemaLocation="messages/IdentificationInVISResponse.xsd"/>


    <!-- === PreEnrolment Service request/response operations === -->

    <!-- PreEnrolment.DataPreEnrolment messages -->
    <xs:include schemaLocation="messages/DataPreEnrolmentRequest.xsd"/>
    <xs:include schemaLocation="messages/DataPreEnrolmentResponse.xsd"/>

    <!-- PreEnrolment.RetrievePreEnrolledData messages -->
    <xs:include schemaLocation="messages/RetrievePreEnrolledDataRequest.xsd"/>
    <xs:include schemaLocation="messages/RetrievePreEnrolledDataResponse.xsd"/>

    <xs:include schemaLocation="messages/SSSBiometricsComparisonRequest.xsd"/>
    <xs:include schemaLocation="messages/SSSBiometricsComparisonResponse.xsd"/>

    <!-- === Authority Service request/response operations === -->

    <!-- Authority.UpdateAuthority messages -->
    <xs:include schemaLocation="messages/UpdateAuthorityRequest.xsd"/>
    <xs:include schemaLocation="messages/UpdateAuthorityResponse.xsd"/>

    <!-- Authority.SearchAuthority messages -->
    <xs:include schemaLocation="messages/SearchAuthorityRequest.xsd"/>
    <xs:include schemaLocation="messages/SearchAuthorityResponse.xsd"/>

    <!-- === BorderControl Service request/response operations === -->

    <!-- BorderControl.EndBorderControl messages -->
    <xs:include schemaLocation="messages/EndBorderControlRequest.xsd"/>
    <xs:include schemaLocation="messages/EndBorderControlResponse.xsd"/>

    <!-- BorderControl.AbortBorderControl messages -->
    <xs:include schemaLocation="messages/AbortBorderControlRequest.xsd"/>
    <xs:include schemaLocation="messages/AbortBorderControlResponse.xsd"/>

    <!-- BorderControl.IdentificationResult messages -->
    <xs:include schemaLocation="messages/IdentificationResultRequest.xsd"/>
    <xs:include schemaLocation="messages/IdentificationResultResponse.xsd"/>

    <!-- BorderControl.SearchOngoingBorderControlTransactions messages -->
    <xs:include schemaLocation="messages/SearchOngoingBorderControlTransactionsRequest.xsd"/>
    <xs:include schemaLocation="messages/SearchOngoingBorderControlTransactionsResponse.xsd"/>

    <!-- BorderControl.StartBorderControl messages -->
    <xs:include schemaLocation="messages/StartBorderControlRequest.xsd"/>
    <xs:include schemaLocation="messages/StartBorderControlResponse.xsd"/>

    <!-- BorderControl.AddDataToBorderControl messages -->
    <xs:include schemaLocation="messages/AddDataToBorderControlRequest.xsd"/>
    <xs:include schemaLocation="messages/AddDataToBorderControlResponse.xsd"/>



    <!-- === TravellerFile Service notification operations === -->

    <!-- TravellerFile.OverstayersScheduledDeletion notification message -->
    <xs:include schemaLocation="messages/OverstayersScheduledDeletionNotification.xsd"/>

    <!-- TravellerFile.TravellerFileDeletion notification message -->
    <xs:include schemaLocation="messages/TravellerFileDeletionNotification.xsd"/>



</xs:schema>
