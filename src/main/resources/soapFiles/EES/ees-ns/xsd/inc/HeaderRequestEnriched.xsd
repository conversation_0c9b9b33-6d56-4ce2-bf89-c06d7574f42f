<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<xs:schema version="1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:ns1="http://www.europa.eu/schengen/ees-ns/xsd/v1">

  <xs:import namespace="http://www.europa.eu/schengen/ees-ns/xsd/v1" schemaLocation="www.europa.eu_schengen_ees-ns_xsd_v1.xsd"/>
  <xs:import namespace="http://www.europa.eu/schengen/ees-ns/xsd/v1" schemaLocation="./EnumStructureType.xsd"/>

  <xs:element name="HeaderRequestEnriched" type="headerRequestTypeEnriched"/>

  <xs:element name="SIFHeader">
    <xs:complexType>
      <xs:sequence>
        <xs:element name="idUser" type="xs:string" minOccurs="1"/>
        <xs:element name="idChiamante" type="xs:string" minOccurs="1"/>
        <xs:element name="icd" type="xs:string" minOccurs="1"/>
        <xs:element name="offline" type="xs:boolean" minOccurs="0" />
        <xs:element name="numeroDocumento" type="xs:string" minOccurs="1"/>
        <xs:element name="fi" type="xs:integer" minOccurs="0" maxOccurs="1"/>
        <xs:element name="fp" type="xs:boolean" minOccurs="0"/>
        <xs:element name="stato" type="ns1:statoType" minOccurs="0" maxOccurs="unbounded"/>
        <xs:element name="operazione" type="xs:string" minOccurs="0"/>
        <xs:element name="tipoOperazione" type="xs:string" minOccurs="0"/>
        <xs:element name="securityToken" type="xs:string" minOccurs="0" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>

  <xs:complexType name="headerRequestTypeEnriched">
    <xs:complexContent>
      <xs:extension base="ns1:HeaderRequestType">
        <xs:sequence>
          <xs:element ref="SIFHeader" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
</xs:schema>

