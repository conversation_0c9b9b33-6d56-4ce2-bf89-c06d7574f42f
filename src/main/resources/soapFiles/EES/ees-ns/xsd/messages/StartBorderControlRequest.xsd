<?xml version="1.0" encoding="utf-8"?>
<xs:schema
        xmlns="http://www.europa.eu/schengen/ees-ns/xsd/v1"
        xmlns:xs="http://www.w3.org/2001/XMLSchema"
        xmlns:ees="http://www.europa.eu/schengen/ees/xsd/v1"
        xmlns:shared="http://www.europa.eu/schengen/shared/xsd/v1"
        xmlns:etias="http://www.europa.eu/schengen/etias/xsd/v1"
        xmlns:vdta="http://www.europa.eu/schengen/vis/xsd/v3/types/Application"
        targetNamespace="http://www.europa.eu/schengen/ees-ns/xsd/v1">

    <xs:import namespace="http://www.europa.eu/schengen/ees/xsd/v1"
               schemaLocation="../../../ees/xsd/DataTypes.xsd"/>
    <xs:import namespace="http://www.europa.eu/schengen/shared/xsd/v1"
               schemaLocation="../../../shared/xsd/DataTypes.xsd"/>
    <xs:import namespace="http://www.europa.eu/schengen/etias/xsd/v1"
               schemaLocation="../../../etias/xsd/DataTypes.xsd"/>
    <xs:import namespace="http://www.europa.eu/schengen/vis/xsd/v3/types/Application"
               schemaLocation="../../../vis-ns/xsd/DataTypeDictionary/ApplicationDataTypes.xsd"/>

    <xs:include schemaLocation="../inc/MessageStructureTypes.xsd"/>
    <xs:include schemaLocation="../inc/WFETypes.xsd"/>


    <xs:element name="StartBorderControlRequest" type="StartBorderControlRequestMessageType"/>

    <xs:complexType name="StartBorderControlRequestMessageType">
        <xs:sequence>
            <xs:element name="ScopeModifiers" type="WFEScopeModifiersType" minOccurs="0"/>
            <xs:element name="TravelDocument" type="WFETravelDocumentSearchRequestType"/>
            <xs:choice minOccurs="0">
                <xs:element name="CollectedData" type="CollectedDataRequestType"/>
                <xs:element name="SelectedResponseData">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="EESSearchAndVerification" minOccurs="0">
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="TravellerFile">
                                            <xs:complexType>
                                                <xs:sequence>
                                                    <xs:element name="TravellerFileID" type="ees:IdentifierType"
                                                                minOccurs="0"/>
                                                </xs:sequence>
                                            </xs:complexType>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="VISSearchAndVerification" minOccurs="0">
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="VisaApplication">
                                            <xs:complexType>
                                                <xs:choice minOccurs="0">
                                                    <xs:element name="VisaStickerNumber"
                                                                type="shared:VisaStickerNumberType"/>
                                                    <xs:element name="VisaApplicationNumber"
                                                                type="vdta:ApplicationNumberType"/>
                                                </xs:choice>
                                            </xs:complexType>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="ETIASSearch" minOccurs="0">
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="TravelAuthorization">
                                            <xs:complexType>
                                                <xs:sequence>
                                                    <xs:element name="ApplicationNumber" type="etias:IdentifierType" minOccurs="0"/>
                                                </xs:sequence>
                                            </xs:complexType>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:choice>
            <xs:element name="BorderCrossingPoint" type="shared:AuthorityUniqueIDType"/>
        </xs:sequence>
    </xs:complexType>

</xs:schema>
