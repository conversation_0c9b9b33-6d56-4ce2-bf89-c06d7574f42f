<?xml version="1.0" encoding="utf-8"?>
<xs:schema
        xmlns="http://www.europa.eu/schengen/ees-ns/xsd/v1"
        xmlns:xs="http://www.w3.org/2001/XMLSchema"
        xmlns:ees="http://www.europa.eu/schengen/ees/xsd/v1"
        targetNamespace="http://www.europa.eu/schengen/ees-ns/xsd/v1">

    <xs:import namespace="http://www.europa.eu/schengen/ees/xsd/v1"
               schemaLocation="../../../ees/xsd/DataTypes.xsd"/>

    <xs:include schemaLocation="../inc/MessageStructureTypes.xsd"/>
    <xs:include schemaLocation="../inc/WFETypes.xsd"/>

    <xs:element name="SearchOngoingBorderControlTransactionsResponse"
                type="SearchOngoingBorderControlTransactionsResponseMessageType"/>
    <xs:complexType name="SearchOngoingBorderControlTransactionsResponseMessageType">
        <xs:complexContent>
            <xs:extension base="MessageResponseType">
                <xs:sequence>
                    <xs:element name="Response" minOccurs="0">
                        <xs:complexType>
                            <xs:sequence>
                                <xs:element name="Paging" type="ees:PagingResponseType"/>
                                <xs:element name="BorderControlTransaction" type="WFEResponseWithoutCalculatorType" minOccurs="0"
                                            maxOccurs="unbounded"/>
                            </xs:sequence>
                        </xs:complexType>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

</xs:schema>
