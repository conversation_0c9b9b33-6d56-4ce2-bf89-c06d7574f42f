<?xml version="1.0" encoding="utf-8"?>
<xs:schema
        xmlns="http://www.europa.eu/schengen/ees-ns/xsd/v1"
        xmlns:xs="http://www.w3.org/2001/XMLSchema"
        xmlns:ees="http://www.europa.eu/schengen/ees/xsd/v1"
        xmlns:shared="http://www.europa.eu/schengen/shared/xsd/v1"
        targetNamespace="http://www.europa.eu/schengen/ees-ns/xsd/v1">

    <xs:import namespace="http://www.europa.eu/schengen/shared/xsd/v1"
               schemaLocation="../../../shared/xsd/DataTypes.xsd"/>
    <xs:import namespace="http://www.europa.eu/schengen/ees/xsd/v1"
               schemaLocation="../../../ees/xsd/DataTypes.xsd"/>

    <xs:include schemaLocation="../inc/MessageStructureTypes.xsd"/>
    <xs:include schemaLocation="../inc/TravellerFileTypes.xsd"/>

    <xs:element name="ChangeAuthorisationRequest" type="ChangeAuthorisationRequestMessageType"/>

    <xs:complexType name="ChangeAuthorisationRequestMessageType">
        <xs:choice>
            <xs:element name="Update">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="TravellerFile">
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="EntryRecord">
                                        <xs:complexType>
                                            <xs:sequence>
                                                <xs:element name="AuthorisedStayUntil" type="xs:dateTime" minOccurs="0"/>
                                                <xs:element name="AuthorisationChange"
                                                            type="ees:AuthorisationChangeWithoutVisaType"/>
                                                <xs:element name="EntryRecordID" type="ees:IdentifierType" minOccurs="0"/>
                                            </xs:sequence>
                                        </xs:complexType>
                                    </xs:element>
                                    <xs:element name="ExitRecord" type="ees:ExitRecordBaseType" minOccurs="0"/>
                                    <xs:element name="TravellerFileID" type="ees:IdentifierType"/>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="Offline">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="TravellerFile">
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="TravelDocument" type="shared:TravelDocumentType"/>
                                    <xs:element name="FI" type="ees:FIRequestType"/>
                                    <xs:element name="FP" type="ees:FPRequestType"/>
                                    <xs:element name="NFP" type="ees:NFPType" minOccurs="0"/>
                                    <xs:element name="EntryRecord" type="ees:EntryRecordWithoutDurationOfStayAndVisaInformationType"/>
                                    <xs:element name="ExitRecord" type="ees:ExitRecordBaseType" minOccurs="0"/>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
        </xs:choice>
    </xs:complexType>

</xs:schema>
