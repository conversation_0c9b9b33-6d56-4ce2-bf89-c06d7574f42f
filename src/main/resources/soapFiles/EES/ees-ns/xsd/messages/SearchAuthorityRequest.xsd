<?xml version="1.0" encoding="utf-8"?>
<xs:schema
        xmlns="http://www.europa.eu/schengen/ees-ns/xsd/v1"
        xmlns:xs="http://www.w3.org/2001/XMLSchema"
        xmlns:shared="http://www.europa.eu/schengen/shared/xsd/v1"
        xmlns:ees="http://www.europa.eu/schengen/ees/xsd/v1"
        targetNamespace="http://www.europa.eu/schengen/ees-ns/xsd/v1">

    <xs:import namespace="http://www.europa.eu/schengen/shared/xsd/v1"
               schemaLocation="../../../shared/xsd/DataTypes.xsd"/>
    <xs:import namespace="http://www.europa.eu/schengen/ees/xsd/v1"
               schemaLocation="../../../ees/xsd/DataTypes.xsd"/>

    <xs:include schemaLocation="../inc/MessageStructureTypes.xsd"/>

    <xs:element name="SearchAuthorityRequest" type="SearchAuthorityRequestMessageType"/>

    <xs:complexType name="SearchAuthorityRequestMessageType">
        <xs:sequence>
            <xs:element name="Paging" type="ees:PagingRequestType" minOccurs="0"/>
            <xs:element name="SearchData">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="Authority">
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="AuthorityUniqueId" type="shared:AuthorityUniqueIDType"
                                                minOccurs="0">
                                        <xs:annotation>
                                            <xs:documentation>Description: A unique identificator of an Authority.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="Owner" type="shared:CT70_UserType" minOccurs="0">
                                        <xs:annotation>
                                            <xs:documentation>Description: The Member State this AuthorityName
                                                refers to.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="Country" type="shared:CT01_CountryType" minOccurs="0">
                                        <xs:annotation>
                                            <xs:documentation>Description: The country where the authority is
                                                located. A code table value.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="Location" type="ees:NonWeightedNameSearchField" minOccurs="0">
                                        <xs:annotation>
                                            <xs:documentation>Description: A value referencing a location of an
                                                Authority.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="AuthorityType" type="shared:CT10_AuthorityTypeType"
                                                minOccurs="0">
                                        <xs:annotation>
                                            <xs:documentation>Description: A value referencing a Authority type.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="ModifiedAfter" type="xs:date" minOccurs="0"/>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>


</xs:schema>
