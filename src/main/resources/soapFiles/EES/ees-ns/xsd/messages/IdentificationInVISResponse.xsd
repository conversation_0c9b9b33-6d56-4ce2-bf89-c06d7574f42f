<?xml version="1.0" encoding="utf-8"?>
<xs:schema
        xmlns="http://www.europa.eu/schengen/ees-ns/xsd/v1"
        xmlns:xs="http://www.w3.org/2001/XMLSchema"
        xmlns:ees="http://www.europa.eu/schengen/ees/xsd/v1"
        xmlns:cs_vis-nui_ees="http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1"
        targetNamespace="http://www.europa.eu/schengen/ees-ns/xsd/v1">

    <xs:import namespace="http://www.europa.eu/schengen/ees/xsd/v1"
               schemaLocation="../../../ees/xsd/DataTypes.xsd"/>
    <xs:import namespace="http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1"
               schemaLocation="../../../cs_vis-nui_ees/xsd/Messages.xsd"/>

    <xs:include schemaLocation="../inc/MessageStructureTypes.xsd"/>

    <xs:element name="IdentificationInVISResponse" type="IdentificationInVISResponseMessageType"/>
    <xs:complexType name="IdentificationInVISResponseMessageType">
        <xs:complexContent>
            <xs:extension base="MessageResponseType">
                <xs:sequence>
                    <xs:element name="Response" minOccurs="0">
                        <xs:complexType>
                            <xs:sequence>
                                <xs:element name="Paging" type="ees:PagingResponseType"/>
                                <xs:choice minOccurs="0">
                                    <xs:element name="VisaApplicationOverview" maxOccurs="unbounded">
                                        <xs:complexType>
                                            <xs:complexContent>
                                                <xs:extension base="cs_vis-nui_ees:VisaApplicationOverviewSearchResultVISType">
                                                    <xs:sequence>
                                                        <xs:group ref="ees:RankScoreGroup"/>
                                                        <xs:element name="DossierID" type="cs_vis-nui_ees:DossierIDType">
                                                            <xs:annotation>
                                                                <xs:documentation>Description: ID of the Dossier.</xs:documentation>
                                                            </xs:annotation>
                                                        </xs:element>
                                                    </xs:sequence>
                                                </xs:extension>
                                            </xs:complexContent>
                                        </xs:complexType>
                                    </xs:element>
                                    <xs:element name="VisaApplication" maxOccurs="unbounded">
                                        <xs:complexType>
                                            <xs:complexContent>
                                                <xs:extension base="cs_vis-nui_ees:VisaApplicationSearchResultEESType">
                                                    <xs:sequence>
                                                        <xs:group ref="ees:RankScoreGroup"/>
                                                        <xs:element name="DossierID" type="cs_vis-nui_ees:DossierIDType">
                                                            <xs:annotation>
                                                                <xs:documentation>Description: ID of the Dossier.</xs:documentation>
                                                            </xs:annotation>
                                                        </xs:element>
                                                    </xs:sequence>
                                                </xs:extension>
                                            </xs:complexContent>
                                        </xs:complexType>
                                    </xs:element>
                                </xs:choice>
                                <xs:element name="BiometricsQuality" minOccurs="0">
                                    <xs:complexType>
                                        <xs:sequence>
                                            <xs:element name="FP">
                                                <xs:complexType>
                                                    <xs:group ref="ees:FPQualityGroup"/>
                                                </xs:complexType>
                                            </xs:element>
                                        </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                            </xs:sequence>
                        </xs:complexType>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

</xs:schema>
