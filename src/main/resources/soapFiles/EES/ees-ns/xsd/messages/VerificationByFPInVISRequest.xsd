<?xml version="1.0" encoding="utf-8"?>
<xs:schema
        xmlns="http://www.europa.eu/schengen/ees-ns/xsd/v1"
        xmlns:xs="http://www.w3.org/2001/XMLSchema"
        xmlns:shared="http://www.europa.eu/schengen/shared/xsd/v1"
        xmlns:ees="http://www.europa.eu/schengen/ees/xsd/v1"
        xmlns:vdta="http://www.europa.eu/schengen/vis/xsd/v3/types/Application"
        targetNamespace="http://www.europa.eu/schengen/ees-ns/xsd/v1">

    <xs:import namespace="http://www.europa.eu/schengen/shared/xsd/v1"
               schemaLocation="../../../shared/xsd/DataTypes.xsd"/>
    <xs:import namespace="http://www.europa.eu/schengen/ees/xsd/v1"
               schemaLocation="../../../ees/xsd/DataTypes.xsd"/>
    <xs:import namespace="http://www.europa.eu/schengen/vis/xsd/v3/types/Application"
               schemaLocation="../../../vis-ns/xsd/DataTypeDictionary/ApplicationDataTypes.xsd"/>


    <xs:include schemaLocation="../inc/MessageStructureTypes.xsd"/>


    <xs:element name="VerificationByFPInVISRequest" type="VerificationByFPInVISRequestMessageType"/>

    <xs:complexType name="VerificationByFPInVISRequestMessageType">
        <xs:sequence>
            <xs:element name="ScopeModifiers" minOccurs="0">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="OperationModifier" type="ees:ST506_OperationModifierType"
                                    minOccurs="0">
                        </xs:element>
                        <xs:element name="VisaApplications" type="ees:ST508_VisaApplicationsModifierType"
                                    minOccurs="0">
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:choice>
                <xs:element name="VisaStickerNumber" type="shared:VisaStickerNumberType"/>
                <xs:element name="ApplicationNumber" type="vdta:ApplicationNumberType"/>
            </xs:choice>
            <xs:element name="FP">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="NISTFile" type="ees:FPNISTFile"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>


</xs:schema>
