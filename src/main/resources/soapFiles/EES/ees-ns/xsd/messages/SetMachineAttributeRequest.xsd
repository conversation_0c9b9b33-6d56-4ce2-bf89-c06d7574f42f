<xs:schema
        xmlns="http://www.europa.eu/schengen/ees-ns/xsd/v1"
        xmlns:xs="http://www.w3.org/2001/XMLSchema"
        xmlns:ees="http://www.europa.eu/schengen/ees/xsd/v1"
        xmlns:ns1="http://www.europa.eu/schengen/ees-ns/xsd/v1"
        targetNamespace="http://www.europa.eu/schengen/ees-ns/xsd/v1">
    
    <xs:include schemaLocation="../inc/EnumStructureType.xsd"/>    
        
    <xs:element name="SetMachineAttributeRequest" type="SetMachineAttributeRequestMessageType"/>

    <xs:complexType name="SetMachineAttributeRequestMessageType">	
    	<xs:sequence>
    		  <xs:element minOccurs="1" maxOccurs="1" name="IdUfficio" type="xs:string"/>
    		  <xs:element minOccurs="0" maxOccurs="1" name="IdClient" nillable="false" type="xs:string"/>
    		  <xs:element minOccurs="1" maxOccurs="1" name="IdUtente" type="xs:string"/>
    		  <xs:element name="MachineAttributes">
    		  	<xs:complexType>
    		  		<xs:sequence>
    		  			<xs:element minOccurs="1" maxOccurs="unbounded" name="MachineAttribute" type="ns1:MachineAttributeRequestType"/>
    		  		</xs:sequence>
    		  	</xs:complexType>
    		  </xs:element>
    	</xs:sequence>
    </xs:complexType>
        
  </xs:schema>