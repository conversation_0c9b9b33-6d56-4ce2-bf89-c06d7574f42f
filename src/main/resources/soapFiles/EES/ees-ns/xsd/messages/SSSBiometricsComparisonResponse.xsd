<?xml version="1.0" encoding="utf-8"?>
<xs:schema
        xmlns="http://www.europa.eu/schengen/ees-ns/xsd/v1"
        xmlns:xs="http://www.w3.org/2001/XMLSchema"
        xmlns:ees="http://www.europa.eu/schengen/ees/xsd/v1"
        xmlns:shared="http://www.europa.eu/schengen/shared/xsd/v1"
        targetNamespace="http://www.europa.eu/schengen/ees-ns/xsd/v1">

    <xs:import namespace="http://www.europa.eu/schengen/shared/xsd/v1" schemaLocation="../../../shared/xsd/DataTypes.xsd"/>
    <xs:import namespace="http://www.europa.eu/schengen/ees/xsd/v1" schemaLocation="../../../ees/xsd/DataTypes.xsd"/>

    <xs:include schemaLocation="../inc/MessageStructureTypes.xsd"/>

    <xs:element name="SSSBiometricsComparisonResponse" type="SSSBiometricsComparisonResponseMessageType"/>

    <xs:complexType name="SSSBiometricsComparisonResponseMessageType">
        <xs:complexContent>
            <xs:extension base="MessageResponseType">
                <xs:sequence>
                    <xs:element name="Response" minOccurs="0">
                        <xs:complexType>
                            <xs:sequence>
                                <xs:element name="TravelDocument">
                                    <xs:complexType>
                                        <xs:sequence>
                                            <xs:element name="DocumentNumber" type="shared:TravelDocumentNumberType"/>
                                            <xs:element name="ComparisonResults">
                                                <xs:complexType>
                                                    <xs:sequence>
                                                        <xs:element name="FI" minOccurs="0">
                                                            <xs:complexType>
                                                                    <xs:choice>
                                                                        <xs:group ref="ees:ResultScoreGroup"/>
                                                                        <xs:element name="BiometricVerificationError" type="shared:ST14_ErrorCodeType"/>
                                                                    </xs:choice>
                                                            </xs:complexType>
                                                        </xs:element>
                                                        <xs:element name="FP" minOccurs="0">
                                                            <xs:complexType>
                                                                    <xs:choice>
                                                                        <xs:group ref="ees:ResultScoreGroup"/>
                                                                        <xs:element name="BiometricVerificationError" type="shared:ST14_ErrorCodeType"/>
                                                                    </xs:choice>
                                                            </xs:complexType>
                                                        </xs:element>
                                                    </xs:sequence>
                                                </xs:complexType>
                                            </xs:element>
                                            <xs:element name="PreEnrolledBiometricsData">
                                                <xs:complexType>
                                                    <xs:sequence>
                                                        <xs:element name="ProvidedSample" type="ProvidedSample" maxOccurs="2"/>
                                                    </xs:sequence>
                                                </xs:complexType>
                                            </xs:element>
                                            <xs:element name="ComparedBiometricsData">
                                                <xs:complexType>
                                                    <xs:sequence>
                                                        <xs:element name="ProvidedSample" type="ProvidedSample" maxOccurs="2"/>
                                                    </xs:sequence>
                                                </xs:complexType>
                                            </xs:element>
                                        </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                            </xs:sequence>
                        </xs:complexType>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="ProvidedSample">
        <xs:sequence>
            <xs:element name="BiometricType" type="shared:CT603_BiometricType"/>
            <xs:element name="SampleQuality">
                <xs:complexType>
                    <xs:choice>
                        <xs:element name="FI">
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:group ref="ees:FIQualityGroup"/>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                        <xs:element name="FP">
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:group ref="ees:FPQualityGroup"/>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                    </xs:choice>
                </xs:complexType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
</xs:schema>
