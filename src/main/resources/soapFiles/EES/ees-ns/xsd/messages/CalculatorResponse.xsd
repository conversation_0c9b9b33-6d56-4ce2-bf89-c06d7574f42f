<?xml version="1.0" encoding="utf-8"?>
<xs:schema
        xmlns="http://www.europa.eu/schengen/ees-ns/xsd/v1"
        xmlns:xs="http://www.w3.org/2001/XMLSchema"
        xmlns:ees="http://www.europa.eu/schengen/ees/xsd/v1"
        xmlns:shared="http://www.europa.eu/schengen/shared/xsd/v1"
        targetNamespace="http://www.europa.eu/schengen/ees-ns/xsd/v1">

    <xs:import namespace="http://www.europa.eu/schengen/ees/xsd/v1"
               schemaLocation="../../../ees/xsd/DataTypes.xsd"/>

    <xs:import namespace="http://www.europa.eu/schengen/shared/xsd/v1"
               schemaLocation="../../../shared/xsd/DataTypes.xsd"/>

    <xs:include schemaLocation="../inc/MessageStructureTypes.xsd"/>


    <xs:element name="CalculatorResponse" type="CalculatorResponseMessageType"/>

    <xs:complexType name="CalculatorResponseMessageType">
        <xs:complexContent>
            <xs:extension base="MessageResponseType">
                <xs:sequence>
                    <xs:element name="Response" minOccurs="0">
                        <xs:complexType>
                            <xs:sequence>
                                <xs:element name="Calculator">
                                    <xs:complexType>
                                        <xs:complexContent>
                                            <xs:extension base="ees:ExtendedCalculatorResultType">
                                                <xs:choice>
                                                    <xs:element name="TravellerFileID" type="ees:IdentifierType"/>
                                                    <xs:element name="TravelDocument">
                                                        <xs:complexType>
                                                            <xs:sequence>
                                                                <xs:element name="DocumentNumber" type="shared:TravelDocumentNumberType"/>
                                                            </xs:sequence>
                                                        </xs:complexType>
                                                    </xs:element>
                                                </xs:choice>
                                            </xs:extension>
                                        </xs:complexContent>
                                    </xs:complexType>
                                </xs:element>
                            </xs:sequence>
                        </xs:complexType>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

</xs:schema>
