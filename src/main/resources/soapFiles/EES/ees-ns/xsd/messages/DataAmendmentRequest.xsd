<?xml version="1.0" encoding="utf-8"?>
<xs:schema
        xmlns="http://www.europa.eu/schengen/ees-ns/xsd/v1"
        xmlns:xs="http://www.w3.org/2001/XMLSchema"
        xmlns:ees="http://www.europa.eu/schengen/ees/xsd/v1"
        xmlns:shared="http://www.europa.eu/schengen/shared/xsd/v1"
        targetNamespace="http://www.europa.eu/schengen/ees-ns/xsd/v1">

    <xs:import namespace="http://www.europa.eu/schengen/ees/xsd/v1"
               schemaLocation="../../../ees/xsd/DataTypes.xsd"/>
    <xs:import namespace="http://www.europa.eu/schengen/shared/xsd/v1"
               schemaLocation="../../../shared/xsd/DataTypes.xsd"/>

    <xs:include schemaLocation="../inc/MessageStructureTypes.xsd"/>
    <xs:include schemaLocation="../inc/TravellerFileTypes.xsd"/>

    <xs:element name="DataAmendmentRequest" type="DataAmendmentRequestMessageType"/>

    <xs:complexType name="DataAmendmentRequestMessageType">
        <xs:choice>
            <xs:element name="Create">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="TravellerFile" type="DataAmendmentCreateRequestType"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="Update">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="TravellerFile" type="DataAmendmentUpdateRequestType"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="Delete">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="TravellerFile" type="DataAmendmentDeleteRequestType"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="Merge">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="TravellerFile" type="DataAmendmentMergeRequestType" minOccurs="2"
                                    maxOccurs="2"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
        </xs:choice>
    </xs:complexType>


    <xs:complexType name="DataAmendmentCreateRequestType">
        <xs:annotation>
            <xs:documentation>TravellerFile grouping all TCN data - type for the create request.</xs:documentation>
        </xs:annotation>
            <xs:sequence>
                <xs:group ref="TravellerFileCreateBase"/>
                <xs:element name="EntryRecord" type="ees:EntryRecordBaseType" minOccurs="0" maxOccurs="unbounded"/>
                <xs:element name="ExitRecord" type="ees:ExitRecordBaseType" minOccurs="0" maxOccurs="unbounded"/>
                <xs:element name="RefusalRecord" type="ees:RefusalRecordBaseType" minOccurs="0" maxOccurs="unbounded"/>
            </xs:sequence>
    </xs:complexType>


    <xs:complexType name="DataAmendmentUpdateRequestType">
        <xs:annotation>
            <xs:documentation>TravellerFile grouping all TCN data - type for the update request.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:group ref="TravellerFileUpdateBase"/>
            <xs:element name="EntryRecord" type="ees:EntryRecordUpdateRequestType" minOccurs="0"
                        maxOccurs="unbounded"/>
            <xs:element name="ExitRecord" type="ees:ExitRecordUpdateRequestType" minOccurs="0"
                        maxOccurs="unbounded"/>
            <xs:element name="RefusalRecord" type="ees:RefusalRecordUpdateRequestType" minOccurs="0"
                        maxOccurs="unbounded"/>
            <xs:element name="TravellerFileID" type="ees:IdentifierType"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="DataAmendmentDeleteRequestType">
        <xs:sequence>
            <xs:sequence>
                <xs:element name="TravelDocument" minOccurs="0" maxOccurs="unbounded">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="DocumentNumber" type="shared:TravelDocumentNumberType"/>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element name="NFP" minOccurs="0">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="EstablishingMS" type="shared:CT70_UserType"/>
                            <xs:element name="Name" type="shared:RawValueType" minOccurs="0"/>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element name="EntryRecord" minOccurs="0" maxOccurs="unbounded">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="EntryRecordID" type="ees:IdentifierType"/>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element name="ExitRecord" minOccurs="0" maxOccurs="unbounded">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="ExitRecordID" type="ees:IdentifierType"/>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element name="RefusalRecord" minOccurs="0" maxOccurs="unbounded">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="RefusalRecordID" type="ees:IdentifierType"/>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
            <xs:element name="TravellerFileID" type="ees:IdentifierType"/>
        </xs:sequence>
    </xs:complexType>


    <xs:complexType name="DataAmendmentMergeRequestType">
        <xs:sequence>
            <xs:element name="TravellerFileID" type="ees:IdentifierType"/>
            <xs:element name="FI" type="shared:EmptyType" minOccurs="0"/>
            <xs:element name="FP" type="shared:EmptyType" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>
</xs:schema>


