<?xml version="1.0" encoding="utf-8"?>
<xs:schema
        xmlns="http://www.europa.eu/schengen/ees-ns/xsd/v1"
        xmlns:xs="http://www.w3.org/2001/XMLSchema"
        xmlns:shared="http://www.europa.eu/schengen/shared/xsd/v1"
        xmlns:ees="http://www.europa.eu/schengen/ees/xsd/v1"
        targetNamespace="http://www.europa.eu/schengen/ees-ns/xsd/v1">


    <xs:include schemaLocation="../inc/MessageStructureTypes.xsd"/>
    <xs:include schemaLocation="../inc/WFETypes.xsd"/>

    <xs:import namespace="http://www.europa.eu/schengen/shared/xsd/v1"
               schemaLocation="../../../shared/xsd/DataTypes.xsd"/>
    <xs:import namespace="http://www.europa.eu/schengen/ees/xsd/v1"
        schemaLocation="../../../ees/xsd/DataTypes.xsd"/>

    <xs:element name="EndBorderControlRequest" type="EndBorderControlRequestMessageType"/>

    <xs:complexType name="EndBorderControlRequestMessageType">
        <xs:sequence>
            <xs:element name="TravellerFile">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="TravelDocument">
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="DocumentNumber" type="shared:TravelDocumentNumberType"/>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                        <xs:choice>
                            <xs:element name="EntryRecord" type="ees:EntryRecordCreatePreEnrolmentRequestNPSType"/>
                            <xs:element name="ExitRecord">
                                <xs:complexType>
                                    <xs:complexContent>
                                        <xs:extension base="ees:ExitRecordBaseType">
                                            <xs:sequence>
                                                <xs:element name="EntryRecordData" minOccurs="0">
                                                    <xs:complexType>
                                                        <xs:choice>
                                                            <xs:sequence>
                                                                <xs:element name="AuthorisationChange" type="ees:AuthorisationChangeWithoutVisaType"/>
                                                                <xs:element name="AuthorisedStayUntil" type="xs:dateTime" minOccurs="0"/>
                                                            </xs:sequence>
                                                            <xs:element name="CorrespondingEntryRecord" type="ees:EntryRecordCreatePreEnrolmentRequestType"/>
                                                        </xs:choice>
                                                    </xs:complexType>
                                                </xs:element>
                                            </xs:sequence>
                                        </xs:extension>
                                    </xs:complexContent>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="RefusalRecord" type="ees:RefusalRecordCreatePreEnrolmentRequestType"/>
                        </xs:choice>
                        <xs:element name="FIToBeStoredInTravellerFile" type="ees:FIToBeStoredInTravellerFileType" minOccurs="0"/>
                        <xs:element name="FPToBeStoredInTravellerFile" type="ees:FPToBeStoredInTravellerFileType" minOccurs="0"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="BorderCrossingPoint" type="shared:AuthorityUniqueIDType"/>
        </xs:sequence>
    </xs:complexType>


</xs:schema>
