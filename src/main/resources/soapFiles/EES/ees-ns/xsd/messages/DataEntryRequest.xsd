<?xml version="1.0" encoding="utf-8"?>
<xs:schema
        xmlns="http://www.europa.eu/schengen/ees-ns/xsd/v1"
        xmlns:xs="http://www.w3.org/2001/XMLSchema"
        xmlns:ees="http://www.europa.eu/schengen/ees/xsd/v1"
        xmlns:shared="http://www.europa.eu/schengen/shared/xsd/v1"
        targetNamespace="http://www.europa.eu/schengen/ees-ns/xsd/v1">

    <xs:import namespace="http://www.europa.eu/schengen/shared/xsd/v1"
               schemaLocation="../../../shared/xsd/DataTypes.xsd"/>
    <xs:import namespace="http://www.europa.eu/schengen/ees/xsd/v1"
               schemaLocation="../../../ees/xsd/DataTypes.xsd"/>

    <xs:include schemaLocation="../inc/MessageStructureTypes.xsd"/>
    <xs:include schemaLocation="../inc/TravellerFileTypes.xsd"/>

    <xs:element name="DataEntryRequest" type="DataEntryRequestMessageType"/>

    <xs:complexType name="DataEntryRequestMessageType">
        <xs:choice>
            <xs:element name="Create">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="TravellerFile" type="DataEntryCreateRequestType"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="Update">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="TravellerFile" type="DataEntryUpdateRequestType"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="Offline">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="TravellerFile" type="DataEntryOfflineRequestType"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
        </xs:choice>
    </xs:complexType>


    <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->

    <xs:complexType name="DataEntryCreateRequestType">
        <xs:annotation>
            <xs:documentation>TravellerFile grouping all TCN data - type for the create request with the use of
                pre-enrolled data.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:choice>
                <xs:group ref="TravellerFileCreateBase"/>
                <xs:element name="PreEnrolment">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="TravelDocument">
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="DocumentNumber" type="shared:TravelDocumentNumberType"/>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:choice>
            <xs:element name="EntryRecord" type="ees:EntryRecordBaseType" minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="ExitRecord" type="ees:ExitRecordBaseType" minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="RefusalRecord" type="ees:RefusalRecordBaseType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="DataEntryUpdateRequestType">
        <xs:annotation>
            <xs:documentation>TravellerFile grouping all TCN data - type for the update request with the use of
                pre-enrolled data.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:choice>
                <xs:group ref="TravellerFileUpdateBase"/>
                <xs:element name="PreEnrolment">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="TravelDocument">
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="DocumentNumber" type="shared:TravelDocumentNumberType"/>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:choice>
            <xs:element name="EntryRecord" type="ees:EntryRecordUpdateRequestType" minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="ExitRecord" type="ees:ExitRecordUpdateRequestType" minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="RefusalRecord" type="ees:RefusalRecordUpdateRequestType" minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="TravellerFileID" type="ees:IdentifierType"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="DataEntryOfflineRequestType">
        <xs:annotation>
            <xs:documentation>TravellerFile grouping all TCN data - type for the create request in the offline mode.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:group ref="TravellerFileSingleTravelDocumentCreateBase"/>
            <xs:choice>
                <xs:element name="EntryRecord" type="ees:EntryRecordBaseType"/>
                <xs:element name="ExitRecord">
                    <xs:complexType>
                        <xs:complexContent>
                            <xs:extension base="ees:ExitRecordBaseType">
                                <xs:sequence>
                                    <xs:element name="CorrespondingEntryRecord" minOccurs="0" type="ees:EntryRecordBaseType"/>
                                </xs:sequence>
                            </xs:extension>
                        </xs:complexContent>
                    </xs:complexType>
                </xs:element>
                <xs:element name="RefusalRecord" type="ees:RefusalRecordBaseType"/>
            </xs:choice>
        </xs:sequence>
    </xs:complexType>

</xs:schema>

