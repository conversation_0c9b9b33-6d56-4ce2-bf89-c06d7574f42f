<?xml version="1.0" encoding="utf-8"?>
<xs:schema
        xmlns="http://www.europa.eu/schengen/ees-ns/xsd/v1"
        xmlns:xs="http://www.w3.org/2001/XMLSchema"
        xmlns:shared="http://www.europa.eu/schengen/shared/xsd/v1"
        xmlns:ees="http://www.europa.eu/schengen/ees/xsd/v1"
        targetNamespace="http://www.europa.eu/schengen/ees-ns/xsd/v1">

    <xs:import namespace="http://www.europa.eu/schengen/shared/xsd/v1"
               schemaLocation="../../../shared/xsd/DataTypes.xsd"/>
    <xs:import namespace="http://www.europa.eu/schengen/ees/xsd/v1"
               schemaLocation="../../../ees/xsd/DataTypes.xsd"/>

    <xs:include schemaLocation="../inc/MessageStructureTypes.xsd"/>
    <xs:include schemaLocation="../inc/WFETypes.xsd"/>

    <xs:element name="SearchOngoingBorderControlTransactionsRequest"
                type="SearchOngoingBorderControlTransactionsRequestMessageType"/>

    <xs:complexType name="SearchOngoingBorderControlTransactionsRequestMessageType">
        <xs:sequence>
            <xs:element name="Paging" type="ees:PagingRequestType" minOccurs="0"/>
            <xs:element name="ScopeModifiers" type="WFEScopeModifiersWithoutCalculatorType" minOccurs="0"/>
            <xs:element name="SearchData">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="BorderControlTransaction">
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="BorderCrossingPoint" type="shared:AuthorityUniqueIDType"/>
                                    <xs:element name="Authority" type="shared:AuthorityUniqueIDType" minOccurs="0">
                                        <xs:annotation>
                                            <xs:documentation>Authority which entered/updated the data, authorised
                                                entry, etc. (it is assumed that the same authority enters the data
                                                as authorised entry).
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="EndUserRole" type="shared:ST12_EndUserRoleType" minOccurs="0">
                                        <xs:annotation>
                                            <xs:documentation>Role of the End User who entered/updated the data.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="TransactionTimestamp" minOccurs="0" type="ees:OpenEndPeriodType"/>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>


</xs:schema>
