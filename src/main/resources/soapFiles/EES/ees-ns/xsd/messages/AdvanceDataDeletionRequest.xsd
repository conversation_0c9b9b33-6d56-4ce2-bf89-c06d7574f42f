<?xml version="1.0" encoding="utf-8"?>
<!-- edited by <PERSON><PERSON><PERSON> & <PERSON><PERSON><PERSON><PERSON><PERSON> -->
<xs:schema
        xmlns="http://www.europa.eu/schengen/ees-ns/xsd/v1"
        xmlns:xs="http://www.w3.org/2001/XMLSchema"
        xmlns:ees="http://www.europa.eu/schengen/ees/xsd/v1"
        targetNamespace="http://www.europa.eu/schengen/ees-ns/xsd/v1">

    <xs:import namespace="http://www.europa.eu/schengen/ees/xsd/v1"
               schemaLocation="../../../ees/xsd/DataTypes.xsd"/>

    <xs:include schemaLocation="../inc/MessageStructureTypes.xsd"/>

    <xs:element name="AdvanceDataDeletionRequest" type="AdvanceDataDeletionRequestMessageType"/>

    <xs:complexType name="AdvanceDataDeletionRequestMessageType">
        <xs:sequence>
            <xs:element name="Delete">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="TravellerFile">
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="TravellerFileID" type="ees:IdentifierType"/>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>


</xs:schema>
