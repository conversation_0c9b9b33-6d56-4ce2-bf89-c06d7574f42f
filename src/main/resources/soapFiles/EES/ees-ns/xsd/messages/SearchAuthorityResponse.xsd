<?xml version="1.0" encoding="utf-8"?>
<xs:schema
        xmlns="http://www.europa.eu/schengen/ees-ns/xsd/v1"
        xmlns:xs="http://www.w3.org/2001/XMLSchema"
        xmlns:shared="http://www.europa.eu/schengen/shared/xsd/v1"
        xmlns:ees="http://www.europa.eu/schengen/ees/xsd/v1"
        targetNamespace="http://www.europa.eu/schengen/ees-ns/xsd/v1">

    <xs:import namespace="http://www.europa.eu/schengen/shared/xsd/v1"
               schemaLocation="../../../shared/xsd/DataTypes.xsd"/>
    <xs:import namespace="http://www.europa.eu/schengen/ees/xsd/v1"
               schemaLocation="../../../ees/xsd/DataTypes.xsd"/>

    <xs:include schemaLocation="../inc/MessageStructureTypes.xsd"/>


    <xs:element name="SearchAuthorityResponse" type="SearchAuthorityResponseMessageType"/>
    <xs:complexType name="SearchAuthorityResponseMessageType">
        <xs:complexContent>
            <xs:extension base="MessageResponseType">
                <xs:sequence>
                    <xs:element name="Response" minOccurs="0">
                        <xs:complexType>
                            <xs:sequence>
                                <xs:element name="Paging" type="ees:PagingResponseType"/>
                                <xs:element name="Authority" maxOccurs="unbounded" minOccurs="0">
                                    <xs:complexType>
                                        <xs:complexContent>
                                            <xs:extension base="shared:AuthorityType">
                                                <xs:sequence>
                                                    <xs:group ref="ees:RankScoreGroup"/>
                                                </xs:sequence>
                                            </xs:extension>
                                        </xs:complexContent>
                                    </xs:complexType>
                                </xs:element>
                            </xs:sequence>
                        </xs:complexType>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

</xs:schema>
