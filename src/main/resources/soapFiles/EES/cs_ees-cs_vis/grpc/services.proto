

syntax = "proto3";

package eu.europa.eulisa.EES;

option java_multiple_files = true;
option java_package = "eu.europa.eulisa.EES";


// === TravellerFile Service === -->
service TravellerFileService {

  rpc EES_VIS_Calculator (EES_VIS_CalculatorRequest) returns (EES_VIS_CalculatorResponse);

  rpc EES_VIS_SearchByPersonalData (EES_VIS_SearchByPersonalDataRequest) returns (EES_VIS_SearchByPersonalDataResponse);

  rpc EES_VIS_SearchByVSN (EES_VIS_SearchByVSNRequest) returns (EES_VIS_SearchByVSNResponse);

  rpc EES_VIS_SearchByBiometrics (EES_VIS_SearchByBiometricsRequest) returns (EES_VIS_SearchByBiometricsResponse);

  rpc EES_VIS_RetrieveTravellerFile (EES_VIS_RetrieveTravellerFileRequest) returns (EES_VIS_RetrieveTravellerFileResponse);

}



