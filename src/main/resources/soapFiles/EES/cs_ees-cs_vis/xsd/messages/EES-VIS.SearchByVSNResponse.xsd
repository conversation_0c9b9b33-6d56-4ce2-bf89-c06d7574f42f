<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns="http://www.europa.eu/schengen/cs_ees-cs_vis/xsd/v1" xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:ees="http://www.europa.eu/schengen/ees/xsd/v1"
           targetNamespace="http://www.europa.eu/schengen/cs_ees-cs_vis/xsd/v1">

    <xs:import schemaLocation="../../../ees/xsd/DataTypes.xsd" namespace="http://www.europa.eu/schengen/ees/xsd/v1"/>

    <xs:element name="EES-VIS.SearchByVSNResponse" type="SearchByVSNResponseMessageType"/>

    <xs:complexType name="SearchByVSNResponseMessageType">
        <xs:complexContent>
            <xs:extension base="ees:BaseMessageResponseType">
                <xs:sequence>
                    <xs:element name="Response">
                        <xs:complexType>
                            <xs:sequence>
                                <xs:element name="Paging" type="ees:PagingResponseType"/>
                                <xs:element name="TravellerFile" type="ees:TravellerFileSearchResponseType" minOccurs="0" maxOccurs="unbounded"/>
                            </xs:sequence>
                        </xs:complexType>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

</xs:schema>