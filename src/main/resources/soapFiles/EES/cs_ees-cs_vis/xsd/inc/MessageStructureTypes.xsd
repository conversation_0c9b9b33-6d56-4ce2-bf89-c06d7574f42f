<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns="http://www.europa.eu/schengen/cs_ees-cs_vis/xsd/v1" xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:ees="http://www.europa.eu/schengen/ees/xsd/v1"
           xmlns:shared="http://www.europa.eu/schengen/shared/xsd/v1"
           targetNamespace="http://www.europa.eu/schengen/cs_ees-cs_vis/xsd/v1">

    <xs:import schemaLocation="../../../ees/xsd/DataTypes.xsd"
               namespace="http://www.europa.eu/schengen/ees/xsd/v1"/>
    <xs:import schemaLocation="../../../shared/xsd/DataTypes.xsd"
               namespace="http://www.europa.eu/schengen/shared/xsd/v1"/>


    <xs:complexType name="HeaderRequestType">
        <xs:complexContent>
            <xs:extension base="ees:HeaderBaseType">
                <xs:sequence>
                    <xs:element name="User" type="ees:ResponsibleType"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="HeaderResponseType">
        <xs:complexContent>
            <xs:extension base="ees:HeaderBaseType">
                <xs:sequence>
                    <xs:element name="User" type="shared:CT70_UserType"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->

    <xs:complexType name="MessageResponseType">
        <xs:sequence>
            <xs:element name="ReturnCodes">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="ErrorCodes" minOccurs="0">
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="ErrorCode" type="shared:ST14_ErrorCodeType" maxOccurs="unbounded"/>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                        <xs:element name="WarningCodes" minOccurs="0">
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="WarningCode" type="shared:ST15_WarningCodeType" maxOccurs="unbounded"/>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

</xs:schema>
