<?xml version="1.0" encoding="UTF-8"?>
<!--
==========================================================================================
|| File Name       :  shared/xsd/common/CodeTableTypes.xsd (initial version from vis-ns v2 ICD)
|| Version         :  2.00
|| Description     :  List of types related to Authorities shared by several ICD
==========================================================================================
-->
<xsd:schema xmlns="http://www.europa.eu/schengen/shared/xsd/v1"
            xmlns:shared="http://www.europa.eu/schengen/shared/xsd/v1"
            xmlns:xsd="http://www.w3.org/2001/XMLSchema"
            targetNamespace="http://www.europa.eu/schengen/shared/xsd/v1" elementFormDefault="qualified"
            version="1.00">

    <xsd:include schemaLocation="BaseTypes.xsd"/>
    <xsd:include schemaLocation="CodeTableTypes.xsd"/>


    <xsd:complexType name="TravelDocumentType">
        <xsd:annotation>
            <xsd:documentation>Biographical data and travel document data - type for the request.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="FamilyName" type="shared:NameType">
                <xsd:annotation>
                    <xsd:documentation>Surname (family name); first name(s) (given names).

                        Surname (family name); first name or names (given names); date of birth; nationality or nationalities; sex.

                        [Article 16(1)(a)]</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="FirstName" type="shared:NameWithoutSpacesType" minOccurs="0" maxOccurs="unbounded">
                <xsd:annotation>
                    <xsd:documentation>Surname (family name); first name(s) (given names).

                        Surname (family name); first name or names (given names); date of birth; nationality or nationalities; sex.

                        [Article 16(1)(a)]</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="DateOfBirth" type="shared:PseudodateType">
                <xsd:annotation>
                    <xsd:documentation>Date of birth.

                        Surname (family name); first name or names (given names); date of birth; nationality or nationalities; sex.

                        [Article 16(1)(a)]</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="Nationality" type="shared:CT02_CountryOfNationalityType"
                maxOccurs="unbounded">
                <xsd:annotation>
                    <xsd:documentation>Current nationality or nationalities.

                        Surname (family name); first name or names (given names); date of birth; nationality or nationalities; sex.

                        [Article 16(1)(a)]</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="Gender" type="shared:CT04_GenderType">
                <xsd:annotation>
                    <xsd:documentation>Sex.

                        Surname (family name); first name or names (given names); date of birth; nationality or nationalities; sex.

                        [Article 16(1)(a)]</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="DocumentNumber" type="shared:TravelDocumentNumberType">
                <xsd:annotation>
                    <xsd:documentation>Travel document unique key</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="DocumentType" type="shared:CT512_TravelDocumentTypeType">
                <xsd:annotation>
                    <xsd:documentation>Type of the travel document.

                        The type and number of the travel document or documents and three letter code of the issuing country of the travel document or documents.

                        [Article 16(1)(b)]</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="ValidUntil" type="xsd:date">
                <xsd:annotation>
                    <xsd:documentation>
                        The date of expiry of the validity of the travel document or documents as per [Article 16(1)(c)].
                        If the Travel Document does not contain an expiry date, then value 01.01.3000 needs to be provided.
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>


    <xsd:simpleType name="VisaStickerNumberType">
        <xsd:annotation>
            <xsd:documentation source="Description">
                Description: The short stay visa sticker number, including the three-letter code of the issuing Member State,
                which must belong to an EES User. Validated against CT517.
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:pattern value="[A-Z]{1,3}.{1,30}"/>
        </xsd:restriction>
    </xsd:simpleType>

    <!-- TravelDocumentNumberType -->
    <xsd:simpleType name="TravelDocumentNumberType">
        <xsd:annotation>
            <xsd:documentation>Number of the travel document.

                The type and number of the travel document or documents and three letter code of the issuing country of the travel document or documents.

                [Article 16(1)(b)]

                This is compatible with VIS XSD but more restricted by regexp
            </xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
            <xsd:pattern value="[A-Z]([A-Z][A-Z])?.{1,30}"/>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:complexType name="NameType">
        <xsd:sequence>
            <xsd:element name="PrimaryValue">
                <xsd:annotation>
                    <xsd:documentation>Taken from the MRZ</xsd:documentation>
                </xsd:annotation>
                <xsd:simpleType>
                    <xsd:restriction base="xsd:string">
                        <xsd:pattern value="[A-Z 0-9]+"/>
                    </xsd:restriction>
                </xsd:simpleType>
            </xsd:element>
            <xsd:element name="AlternativeSpelling" minOccurs="0" maxOccurs="5">
                <xsd:annotation>
                    <xsd:documentation>Taken from other sources, e.g. vizual inspection zone (VIZ) of the travel document, etc.</xsd:documentation>
                </xsd:annotation>
                <xsd:complexType>
                    <xsd:simpleContent>
                        <xsd:extension base="xsd:string">
                            <xsd:attribute name="Source" type="shared:CT508_NameDataSourceType"
                                use="required"/>
                        </xsd:extension>
                    </xsd:simpleContent>
                </xsd:complexType>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="NameWithoutSpacesType">
        <xsd:sequence>
            <xsd:element name="PrimaryValue">
                <xsd:annotation>
                    <xsd:documentation>Taken from the MRZ</xsd:documentation>
                </xsd:annotation>
                <xsd:simpleType>
                    <xsd:restriction base="xsd:string">
                        <xsd:pattern value="[A-Z0-9]+"/>
                    </xsd:restriction>
                </xsd:simpleType>
            </xsd:element>
            <xsd:element name="AlternativeSpelling" minOccurs="0" maxOccurs="5">
                <xsd:annotation>
                    <xsd:documentation>Taken from other sources, e.g. vizual inspection zone (VIZ) of the travel document, etc.</xsd:documentation>
                </xsd:annotation>
                <xsd:complexType>
                    <xsd:simpleContent>
                        <xsd:extension base="xsd:string">
                            <xsd:attribute name="Source" type="shared:CT508_NameDataSourceType"
                                           use="required"/>
                        </xsd:extension>
                    </xsd:simpleContent>
                </xsd:complexType>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>
</xsd:schema>
