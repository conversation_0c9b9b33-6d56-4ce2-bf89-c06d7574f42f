<?xml version="1.0" encoding="UTF-8"?>
<!--
==========================================================================================
|| File Name       :  shared/xsd/common/MessageStructureTypes.xsd (initial version from vis-ns v2 ICD)
|| Version         :  1.00
|| Description     :  Common types related to the structure of the messages shared by several ICD
==========================================================================================
-->
<xsd:schema xmlns="http://www.europa.eu/schengen/shared/xsd/v1"
            xmlns:shared="http://www.europa.eu/schengen/shared/xsd/v1"
            xmlns:xsd="http://www.w3.org/2001/XMLSchema"
            targetNamespace="http://www.europa.eu/schengen/shared/xsd/v1"
            elementFormDefault="qualified" version="1.00">

    <xsd:include schemaLocation="BaseTypes.xsd"/>
    <xsd:include schemaLocation="CodeTableTypes.xsd"/>

    <xsd:simpleType name="SystemIDType">
        <xsd:annotation>
            <xsd:documentation>
                Description: User allowed to access the VIS application. System Identifiers are used to
                implement authentication mechanism.
                .</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="shared:TextRD">
            <xsd:maxLength value="50"/>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:simpleType name="EndUserIDType">
        <xsd:annotation>
            <xsd:documentation>
                Description: The EndUser is the application or officer acting for a USER.
                The VIS application do not manage the End-Users. It is the
                responsibility of the USER to authenticate its EndUser. An EndUser-ID may
                be present in the message header for USER usage only. The USER has
                the responsibility to provide the appropriate EndUser-ID.
                .</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="shared:TextRD">
            <xsd:maxLength value="255"/>
        </xsd:restriction>
    </xsd:simpleType>

</xsd:schema>
