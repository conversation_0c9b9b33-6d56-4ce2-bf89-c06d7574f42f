<?xml version="1.0" encoding="UTF-8"?>
<!--version=201-->
<!-- =====================================================================
      || File Name       :  $Id: NSUpdateApplication.xsd
      || Version          :  3.00 || Description    :  VIS Specification messages sent by the National System to the Central System 
      || Copyright European Commission 2019
========================================================================= -->
<xsd:schema xmlns:vnsmsg="http://www.europa.eu/schengen/vis/xsd/v3/nsmessages" xmlns:vdtc="http://www.europa.eu/schengen/vis/xsd/v3/types/Common" xmlns:vdta="http://www.europa.eu/schengen/vis/xsd/v3/types/Application" xmlns:xsd="http://www.w3.org/2001/XMLSchema" targetNamespace="http://www.europa.eu/schengen/vis/xsd/v3/nsmessages" elementFormDefault="qualified" attributeFormDefault="unqualified" version="3.00">
	<!--==============================================================-->
	<!--simpleelement-->
	<!--==============================================================-->
	<!--VIS Type-->
	<xsd:import namespace="http://www.europa.eu/schengen/vis/xsd/v3/types/Application" schemaLocation="../DataTypeDictionary/ApplicationDataTypes.xsd"/>
	<xsd:import namespace="http://www.europa.eu/schengen/vis/xsd/v3/types/Common" schemaLocation="../DataTypeDictionary/CommonDataTypes.xsd"/>
	<!---->
	<xsd:element name="NSUpdateApplication">
		<xsd:annotation>
			<xsd:documentation source="Description">Description: VIS Messages issued by NS to CS.</xsd:documentation>
		</xsd:annotation>
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="Header" type="vdtc:HeaderType"/>
				<xsd:element name="Request">
					<xsd:annotation>
						<xsd:documentation source="Description">Description: NSUpdateApplicationRequest.</xsd:documentation>
					</xsd:annotation>
					<xsd:complexType>
						<xsd:sequence>
							<xsd:element name="ApplicationNumber" type="vdta:ApplicationNumberType">
								<xsd:annotation>
									<xsd:documentation>Description: ID of the Application to update.</xsd:documentation>
								</xsd:annotation>
							</xsd:element>
							<xsd:element name="ApplicationData" type="vdta:ApplicationNewType">
								<xsd:annotation>
									<xsd:documentation>Description: Information of the Application.</xsd:documentation>
								</xsd:annotation>
							</xsd:element>
						</xsd:sequence>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
</xsd:schema>
