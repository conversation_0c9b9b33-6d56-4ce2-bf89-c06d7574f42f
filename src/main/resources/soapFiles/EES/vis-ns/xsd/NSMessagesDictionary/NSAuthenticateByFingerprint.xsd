<?xml version="1.0" encoding="UTF-8"?>
<!--version=201-->
<!-- =====================================================================
      || File Name       :  $Id: NSAuthenticateByFingerprint.xsd
      || Version          :  3.00 || Description    :  VIS Specification messages sent by the National System to the Central System 
      || Copyright European Commission 2019
========================================================================= -->
<xsd:schema xmlns:vnsmsg="http://www.europa.eu/schengen/vis/xsd/v3/nsmessages" xmlns:shared="http://www.europa.eu/schengen/shared/xsd/v1" xmlns:vdtc="http://www.europa.eu/schengen/vis/xsd/v3/types/Common" xmlns:vdta="http://www.europa.eu/schengen/vis/xsd/v3/types/Application" xmlns:xsd="http://www.w3.org/2001/XMLSchema" targetNamespace="http://www.europa.eu/schengen/vis/xsd/v3/nsmessages" elementFormDefault="qualified" attributeFormDefault="unqualified" version="3.00">
	<!--==============================================================-->
	<!--simpleelement-->
	<!--==============================================================-->
	<!--VIS Type-->
	<xsd:import namespace="http://www.europa.eu/schengen/vis/xsd/v3/types/Application" schemaLocation="../DataTypeDictionary/ApplicationDataTypes.xsd"/>
	<xsd:import namespace="http://www.europa.eu/schengen/vis/xsd/v3/types/Common" schemaLocation="../DataTypeDictionary/CommonDataTypes.xsd"/>
	<xsd:import namespace="http://www.europa.eu/schengen/shared/xsd/v1" schemaLocation="../../../shared/xsd/DataTypes.xsd"/>
	<!---->
	<xsd:element name="NSAuthenticateByFingerprint">
		<xsd:annotation>
			<xsd:documentation source="Description">Description: VIS Messages issued by NS to CS.</xsd:documentation>
		</xsd:annotation>
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="Header" type="vdtc:HeaderType"/>
				<xsd:element name="Request">
					<xsd:annotation>
						<xsd:documentation>Description: NSAuthenticateByFingerprintRequest.</xsd:documentation>
					</xsd:annotation>
					<xsd:complexType>
						<xsd:sequence>
							<xsd:element name="Authority" type="vdtc:AuthorityType">
								<xsd:annotation>
									<xsd:documentation>Description: The authority which requests the operation.</xsd:documentation>
								</xsd:annotation>
							</xsd:element>
							<xsd:element name="Action">
								<xsd:annotation>
									<xsd:documentation source="Description">Description: NSAuthenticateByFingerprint.</xsd:documentation>
								</xsd:annotation>
								<xsd:complexType>
									<xsd:choice>
										<xsd:element name="ApplicationExamination">
											<xsd:annotation>
												<xsd:documentation source="Description">Type: ApplicationExamination.</xsd:documentation>
											</xsd:annotation>
											<xsd:complexType>
												<xsd:complexContent>
													<xsd:extension base="vdta:ApplicationExaminationType">
														<xsd:sequence>
															<xsd:element name="FreshlyScannedFingerprintSet" type="vdta:DataSourceType">
																<xsd:annotation>
																	<xsd:documentation>Description: This attribute represents the NIST file of the freshly scanned fingerprintsset to be used for authenticating the fingerprint set registered with the visa sticker.</xsd:documentation>
																</xsd:annotation>
															</xsd:element>
														</xsd:sequence>
													</xsd:extension>
												</xsd:complexContent>
											</xsd:complexType>
										</xsd:element>
										<xsd:element name="LawEnforcement">
											<xsd:annotation>
												<xsd:documentation>Type: LawEnforcement.</xsd:documentation>
											</xsd:annotation>
											<xsd:complexType>
												<xsd:complexContent>
													<xsd:extension base="vdta:LawEnforcementType">
														<xsd:sequence>
															<xsd:element name="FreshlyScannedFingerprintSet" type="vdta:DataSourceType">
																<xsd:annotation>
																	<xsd:documentation>Description: This attribute represents the NIST file of the freshly scanned fingerprintsset to be used for authenticating the fingerprint set registered with the visa sticker.</xsd:documentation>
																</xsd:annotation>
															</xsd:element>
														</xsd:sequence>
													</xsd:extension>
												</xsd:complexContent>
											</xsd:complexType>
										</xsd:element>
										<xsd:element name="VerificationBorder">
											<xsd:annotation>
												<xsd:documentation>Type: VerificationBorder.</xsd:documentation>
											</xsd:annotation>
											<xsd:complexType>
												<xsd:complexContent>
													<xsd:extension base="vdta:ListType">
														<xsd:sequence>
															<xsd:choice>
																<xsd:element name="ApplicationNumber" type="vdta:ApplicationNumberType">
																	<xsd:annotation>
																		<xsd:documentation>Description: ID of the Application.</xsd:documentation>
																	</xsd:annotation>
																</xsd:element>
																<xsd:element name="VisaStickerNumber" type="shared:VisaStickerNumberType">
																	<xsd:annotation>
																		<xsd:documentation>Description: ID of the VisaStickerNumber.</xsd:documentation>
																	</xsd:annotation>
																</xsd:element>
															</xsd:choice>
															<xsd:element name="FreshlyScannedFingerprintSet" type="vdta:DataSourceType">
																<xsd:annotation>
																	<xsd:documentation>Description: This attribute represents the NIST file of the freshly scanned fingerprintsset to be used for authenticating the fingerprint set registered with the visa sticker.</xsd:documentation>
																</xsd:annotation>
															</xsd:element>
														</xsd:sequence>
													</xsd:extension>
												</xsd:complexContent>
											</xsd:complexType>
										</xsd:element>
										<xsd:element name="VerificationTerritory">
											<xsd:annotation>
												<xsd:documentation>Type: Verification within territory.</xsd:documentation>
											</xsd:annotation>
											<xsd:complexType>
												<xsd:complexContent>
													<xsd:extension base="vdta:ListType">
														<xsd:sequence>
															<xsd:element name="VisaStickerNumber" type="shared:VisaStickerNumberType">
																<xsd:annotation>
																	<xsd:documentation>Description: ID of the VisaStickerNumber.</xsd:documentation>
																</xsd:annotation>
															</xsd:element>
															<xsd:element name="FreshlyScannedFingerprintSet" type="vdta:DataSourceType">
																<xsd:annotation>
																	<xsd:documentation>Description: This attribute represents the NIST file of the freshly scanned fingerprintsset to be used for authenticating the fingerprint set registered with the visa sticker.</xsd:documentation>
																</xsd:annotation>
															</xsd:element>
														</xsd:sequence>
													</xsd:extension>
												</xsd:complexContent>
											</xsd:complexType>
										</xsd:element>
										<xsd:element name="RegistrationEES">
											<xsd:annotation>
												<xsd:documentation>Type: Registration in EES.</xsd:documentation>
											</xsd:annotation>
											<xsd:complexType>
												<xsd:complexContent>
													<xsd:extension base="vdta:ListType">
														<xsd:sequence>
															<xsd:element name="ApplicationNumber" type="vdta:ApplicationNumberType">
																<xsd:annotation>
																	<xsd:documentation>Description: ID of the Application.</xsd:documentation>
																</xsd:annotation>
															</xsd:element>
															<xsd:element name="FreshlyScannedFingerprintSet" type="vdta:DataSourceType">
																<xsd:annotation>
																	<xsd:documentation>Description: This attribute represents the NIST file of the freshly scanned fingerprintsset to be used for authenticating the fingerprint set registered with the visa sticker.</xsd:documentation>
																</xsd:annotation>
															</xsd:element>
														</xsd:sequence>
													</xsd:extension>
												</xsd:complexContent>
											</xsd:complexType>
										</xsd:element>
									</xsd:choice>
								</xsd:complexType>
							</xsd:element>
						</xsd:sequence>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
</xsd:schema>
