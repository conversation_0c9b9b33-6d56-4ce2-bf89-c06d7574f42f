<?xml version="1.0" encoding="UTF-8"?>
<!--version=201-->
<!-- =====================================================================
      || File Name       :  $Id: NSListApplicationsInDossier.xsd
      || Version          :  3.00 || Description    :  VIS Specification messages sent by the National System to the Central System 
      || Copyright European Commission 2014
========================================================================= -->
<xsd:schema xmlns:vnsmsg="http://www.europa.eu/schengen/vis/xsd/v3/nsmessages" xmlns:vdtc="http://www.europa.eu/schengen/vis/xsd/v3/types/Common" xmlns:vdta="http://www.europa.eu/schengen/vis/xsd/v3/types/Application" xmlns:xsd="http://www.w3.org/2001/XMLSchema" targetNamespace="http://www.europa.eu/schengen/vis/xsd/v3/nsmessages" elementFormDefault="qualified" attributeFormDefault="unqualified" version="3.00">
	<!--==============================================================-->
	<!--simpleelement-->
	<!--==============================================================-->
	<!--VIS Type-->
	<xsd:import namespace="http://www.europa.eu/schengen/vis/xsd/v3/types/Application" schemaLocation="../DataTypeDictionary/ApplicationDataTypes.xsd"/>
	<xsd:import namespace="http://www.europa.eu/schengen/vis/xsd/v3/types/Common" schemaLocation="../DataTypeDictionary/CommonDataTypes.xsd"/>
	<!---->
	<xsd:element name="NSListApplicationsInDossier">
		<xsd:annotation>
			<xsd:documentation source="Description">Description: VIS Messages issued by NS to CS.</xsd:documentation>
		</xsd:annotation>
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="Header" type="vdtc:HeaderType"/>
				<xsd:element name="Request">
					<xsd:annotation>
						<xsd:documentation source="Description">Description: NSListApplicationsInDossierRequest.</xsd:documentation>
					</xsd:annotation>
					<xsd:complexType>
						<xsd:sequence>
							<xsd:element name="Authority" type="vdtc:AuthorityType">
								<xsd:annotation>
									<xsd:documentation>Description: The authority which requests the operation.</xsd:documentation>
								</xsd:annotation>
							</xsd:element>
							<xsd:element name="Action">
								<xsd:annotation>
									<xsd:documentation>Description: Choice between the different purposes of a ListApplicationsInDossier.</xsd:documentation>
								</xsd:annotation>
								<xsd:complexType>
									<xsd:choice>
										<xsd:element name="ApplicationExamination" type="vdta:IdentifierChoiceType">
											<xsd:annotation>
												<xsd:documentation>Type: ApplicationExamination.</xsd:documentation>
											</xsd:annotation>
										</xsd:element>
										<xsd:element name="LawEnforcement" type="vdta:IdentifierChoiceType">
											<xsd:annotation>
												<xsd:documentation>Type: LawEnforcement.</xsd:documentation>
											</xsd:annotation>
										</xsd:element>
										<xsd:element name="IdentificationBorder" type="vdta:IdentifierChoiceType">
											<xsd:annotation>
												<xsd:documentation>Type: IdentificationBorder.</xsd:documentation>
											</xsd:annotation>
										</xsd:element>
										<xsd:element name="IdentificationTerritory" type="vdta:IdentifierChoiceType">
											<xsd:annotation>
												<xsd:documentation>Type: IdentificationTerritory.</xsd:documentation>
											</xsd:annotation>
										</xsd:element>
										<xsd:element name="AsylumResponsibility" type="vdta:IdentifierChoiceType">
											<xsd:annotation>
												<xsd:documentation source="Description">Type: AsylumResponsibility</xsd:documentation>
											</xsd:annotation>
										</xsd:element>
										<xsd:element name="AsylumExamination" type="vdta:IdentifierChoiceType">
											<xsd:annotation>
												<xsd:documentation source="Description">Type: AsylumExamination.</xsd:documentation>
											</xsd:annotation>
										</xsd:element>
									</xsd:choice>
								</xsd:complexType>
							</xsd:element>
						</xsd:sequence>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
</xsd:schema>
