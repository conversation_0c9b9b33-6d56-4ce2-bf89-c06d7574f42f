<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:vdtct="http://www.europa.eu/schengen/vis/xsd/v3/codetables" xmlns:xsd="http://www.w3.org/2001/XMLSchema" targetNamespace="http://www.europa.eu/schengen/vis/xsd/v3/codetables" elementFormDefault="qualified" attributeFormDefault="unqualified" version="3.00">
	<xsd:include schemaLocation="EnumerationDataTypes.xsd"/>
	<xsd:element name="ST21Export">
		<xsd:annotation>
			<xsd:documentation>Description: Export issued by the Central System.</xsd:documentation>
		</xsd:annotation>
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="Header" type="vdtct:EnumerationHeader"/>
				<xsd:element name="Data">
					<xsd:complexType>
						<xsd:sequence>
							<xsd:element name="ST21_Variant" type="vdtct:EnumerationEntry" minOccurs="0" maxOccurs="unbounded"/>
						</xsd:sequence>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
</xsd:schema>
