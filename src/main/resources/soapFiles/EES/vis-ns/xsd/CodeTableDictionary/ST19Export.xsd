<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:vdtct="http://www.europa.eu/schengen/vis/xsd/v3/codetables" xmlns:vdtc="http://www.europa.eu/schengen/vis/xsd/v3/types/Common" xmlns:xsd="http://www.w3.org/2001/XMLSchema" targetNamespace="http://www.europa.eu/schengen/vis/xsd/v3/codetables" elementFormDefault="qualified" attributeFormDefault="unqualified" version="3.00">
	<xsd:include schemaLocation="EnumerationDataTypes.xsd"/>
	<xsd:import namespace="http://www.europa.eu/schengen/vis/xsd/v3/types/Common" schemaLocation="../DataTypeDictionary/CommonDataTypes.xsd"/>
	<xsd:element name="ST19Export">
		<xsd:annotation>
			<xsd:documentation>Description: Export issued by the Central System.</xsd:documentation>
		</xsd:annotation>
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="Header" type="vdtct:EnumerationHeader"/>
				<xsd:element name="Data">
					<xsd:complexType>
						<xsd:sequence>
							<xsd:element name="ST19_Operation" minOccurs="0" maxOccurs="unbounded">
								<xsd:complexType>
									<xsd:complexContent>
										<xsd:extension base="vdtct:EnumerationEntry">
											<xsd:sequence>
												<xsd:element name="Contract" type="vdtc:ST18_ContractType"/>
											</xsd:sequence>
										</xsd:extension>
									</xsd:complexContent>
								</xsd:complexType>
							</xsd:element>
						</xsd:sequence>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
</xsd:schema>
