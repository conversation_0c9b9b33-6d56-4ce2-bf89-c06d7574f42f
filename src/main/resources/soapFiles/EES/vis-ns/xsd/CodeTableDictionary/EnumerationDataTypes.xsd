<?xml version="1.0" encoding="UTF-8"?>
<!-- ========================================================================= -->
<!-- File Name : EnumerationDataTypes.xsd -->
<!-- VIS Element Enumeration Data Types-->
<!-- Release: 3.00-->
<!-- Copyright European Commission 2014-->
<!-- ========================================================================= -->
<xsd:schema xmlns:visctdt="http://www.europa.eu/schengen/vis/xsd/v3/codetables" xmlns:xsd="http://www.w3.org/2001/XMLSchema" targetNamespace="http://www.europa.eu/schengen/vis/xsd/v3/codetables" elementFormDefault="qualified" attributeFormDefault="unqualified" version="3.00">
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<xsd:include schemaLocation="CommonTableDataTypes.xsd"/>
	<xsd:complexType name="EnumerationHeader">
		<xsd:annotation>
			<xsd:documentation>Description: The header of all exports of code table.</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="ExportDate" type="visctdt:DateTimeRD"/>
		</xsd:sequence>
	</xsd:complexType>
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
	<xsd:complexType name="EnumerationEntry">
		<xsd:annotation>
			<xsd:documentation>Description: This type is used for deletion notes.</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="Code" type="visctdt:CodeType"/>
			<xsd:element name="Label" type="visctdt:LabelType"/>
		</xsd:sequence>
	</xsd:complexType>
	<!-- ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
</xsd:schema>
