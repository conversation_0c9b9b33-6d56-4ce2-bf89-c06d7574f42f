<?xml version="1.0" encoding="UTF-8"?>
<!--version=201-->
<!-- =====================================================================
      || File Name       :  $Id: CSRetrieval.xsd
      || Version          :  3.00 || Description    :  VIS Specification messages sent by the Central System to the National System 
      || Copyright European Commission 2019
 ========================================================================= -->
<xsd:schema xmlns:vcsmsg="http://www.europa.eu/schengen/vis/xsd/v3/csmessages" xmlns:vdtc="http://www.europa.eu/schengen/vis/xsd/v3/types/Common" xmlns:vdta="http://www.europa.eu/schengen/vis/xsd/v3/types/Application" xmlns:xsd="http://www.w3.org/2001/XMLSchema" targetNamespace="http://www.europa.eu/schengen/vis/xsd/v3/csmessages" elementFormDefault="qualified" attributeFormDefault="unqualified" version="3.00">
	<!--==============================================================-->
	<!--simpleelement-->
	<!--==============================================================-->
	<!--VIS Type-->
	<xsd:import namespace="http://www.europa.eu/schengen/vis/xsd/v3/types/Application" schemaLocation="../DataTypeDictionary/ApplicationDataTypes.xsd"/>
	<xsd:import namespace="http://www.europa.eu/schengen/vis/xsd/v3/types/Common" schemaLocation="../DataTypeDictionary/CommonDataTypes.xsd"/>
	<!---->
	<xsd:element name="CSRetrieval">
		<xsd:annotation>
			<xsd:documentation source="Description">Description: VIS Messages issued by CS to NS.</xsd:documentation>
		</xsd:annotation>
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="Header" type="vdtc:HeaderType"/>
				<xsd:element name="Response">
					<xsd:annotation>
						<xsd:documentation source="Description">Description: CSRetrievalResponse.</xsd:documentation>
					</xsd:annotation>
					<xsd:complexType>
						<xsd:sequence>
							<xsd:element name="RCI" type="vdtc:RCIType"/>
							<xsd:element name="ActionResult" minOccurs="0">
								<xsd:annotation>
									<xsd:documentation>Rule: Present if succes.</xsd:documentation>
								</xsd:annotation>
								<xsd:complexType>
									<xsd:choice>
										<xsd:element name="ApplicationExamination" type="vdta:ResultApplicationExaminationReadType">
											<xsd:annotation>
												<xsd:documentation source="Description">Type: ApplicationExamination.</xsd:documentation>
											</xsd:annotation>
										</xsd:element>
										<xsd:element name="AsylumExamination" type="vdta:ResultAsylumExaminationReadType">
											<xsd:annotation>
												<xsd:documentation source="Description">Type: AsylumExamination.</xsd:documentation>
											</xsd:annotation>
										</xsd:element>
										<xsd:element name="AsylumResponsibility" type="vdta:ResultAsylumResponsibilityUsageOfDataReadType">
											<xsd:annotation>
												<xsd:documentation source="Description">Type: AsylumResponsibility.</xsd:documentation>
											</xsd:annotation>
										</xsd:element>
										<xsd:element name="IdentificationBorder" type="vdta:ResultIdentificationReadType">
											<xsd:annotation>
												<xsd:documentation source="Description">Type: IdentificationBorder.</xsd:documentation>
											</xsd:annotation>
										</xsd:element>
										<xsd:element name="IdentificationTerritory" type="vdta:ResultIdentificationReadType">
											<xsd:annotation>
												<xsd:documentation source="Description">Type: IdentificationTerritory.</xsd:documentation>
											</xsd:annotation>
										</xsd:element>
										<xsd:element name="LawEnforcement" type="vdta:ResultLawEnforcementReadType">
											<xsd:annotation>
												<xsd:documentation>Type: LawEnforcement.</xsd:documentation>
											</xsd:annotation>
										</xsd:element>
										<xsd:element name="VerificationBorder">
											<xsd:annotation>
												<xsd:documentation source="Description">Type: VerificationBorder.</xsd:documentation>
											</xsd:annotation>
											<xsd:complexType>
												<xsd:complexContent>
													<xsd:extension base="vdta:ResultVerificationBorderReadType">
														<xsd:sequence>
															<xsd:element name="HasFingerprints" type="vdtc:YesNoType">
																<xsd:annotation>
																	<xsd:documentation>Description: Indicates whether or not a FingerprintSet is present in the central repository for the found Application.</xsd:documentation>
																</xsd:annotation>
															</xsd:element>
														</xsd:sequence>
													</xsd:extension>
												</xsd:complexContent>
											</xsd:complexType>
										</xsd:element>
										<xsd:element name="VerificationTerritory" type="vdta:ResultVerificationReadType">
											<xsd:annotation>
												<xsd:documentation source="Description">Type: VerificationTerritory.</xsd:documentation>
											</xsd:annotation>
										</xsd:element>
										<xsd:element name="IdentificationApplication" type="vdta:ResultIdentificationReadType">
											<xsd:annotation>
												<xsd:documentation source="Description">Type: IdentificationApplication.</xsd:documentation>
											</xsd:annotation>
										</xsd:element>
										<xsd:element name="IdentificationRegistrationEES" type="vdta:ResultIdentificationReadType">
											<xsd:annotation>
												<xsd:documentation source="Description">Type: IdentificationRegistrationEES.</xsd:documentation>
											</xsd:annotation>
										</xsd:element>
									</xsd:choice>
								</xsd:complexType>
							</xsd:element>
						</xsd:sequence>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
</xsd:schema>
