<?xml version="1.0" encoding="UTF-8"?>
<!--version=201-->
<!-- =====================================================================
      || File Name       :  $Id: CSScheduleReportExecution.xsd
      || Version          :  3.00 || Description    :  VIS Specification messages sent by the Central System to the National System 
      || Copyright European Commission 2014
 ========================================================================= -->
<xsd:schema xmlns:vcsmsg="http://www.europa.eu/schengen/vis/xsd/v3/csmessages" xmlns:vdtc="http://www.europa.eu/schengen/vis/xsd/v3/types/Common" xmlns:vdtr="http://www.europa.eu/schengen/vis/xsd/v3/types/Report" xmlns:xsd="http://www.w3.org/2001/XMLSchema" targetNamespace="http://www.europa.eu/schengen/vis/xsd/v3/csmessages" elementFormDefault="qualified" attributeFormDefault="unqualified" version="3.00">
	<!--==============================================================-->
	<!--simpleelement-->
	<!--==============================================================-->
	<!--VIS Type-->
	<xsd:import namespace="http://www.europa.eu/schengen/vis/xsd/v3/types/Report" schemaLocation="../DataTypeDictionary/ReportDataTypes.xsd"/>
	<xsd:import namespace="http://www.europa.eu/schengen/vis/xsd/v3/types/Common" schemaLocation="../DataTypeDictionary/CommonDataTypes.xsd"/>
	<!---->
	<xsd:element name="CSScheduleReportExecution">
		<xsd:annotation>
			<xsd:documentation source="Description">Description: VIS Messages issued by CS to NS.</xsd:documentation>
		</xsd:annotation>
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="Header" type="vdtc:HeaderType"/>
				<xsd:choice>
					<xsd:element name="Response">
						<xsd:annotation>
							<xsd:documentation source="Description">Description: CSScheduleReportExecution.</xsd:documentation>
						</xsd:annotation>
						<xsd:complexType>
							<xsd:sequence>
								<xsd:element name="RCI" type="vdtc:RCIType"/>
								<xsd:element name="ExecutionID" type="vdtr:ExecutionIDType" minOccurs="0">
									<xsd:annotation>
										<xsd:documentation>Rule: Present if success
Description: ID of the report execution.</xsd:documentation>
									</xsd:annotation>
								</xsd:element>
							</xsd:sequence>
						</xsd:complexType>
					</xsd:element>
					<xsd:element name="Report" type="vdtr:ReportResultType">
						<xsd:annotation>
							<xsd:documentation source="Description">Description: CSScheduledReportExecution.</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
				</xsd:choice>
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
</xsd:schema>
