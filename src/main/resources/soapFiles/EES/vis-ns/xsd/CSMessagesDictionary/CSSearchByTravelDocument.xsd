<?xml version="1.0" encoding="UTF-8"?>
<!--version=201-->
<!-- =====================================================================
      || File Name       :  $Id: CSSearchByTravelDocument.xsd
      || Version          :  3.00 || Description    :  VIS Specification messages sent by the Central System to the EES
      || Copyright European Commission 2019
 ========================================================================= -->
<xsd:schema xmlns:vcsmsg="http://www.europa.eu/schengen/vis/xsd/v3/csmessages" xmlns:vdtc="http://www.europa.eu/schengen/vis/xsd/v3/types/Common" xmlns:vdta="http://www.europa.eu/schengen/vis/xsd/v3/types/Application" xmlns:xsd="http://www.w3.org/2001/XMLSchema" targetNamespace="http://www.europa.eu/schengen/vis/xsd/v3/csmessages" elementFormDefault="qualified" attributeFormDefault="unqualified" version="3.00">
	<!--==============================================================-->
	<!--simpleelement-->
	<!--==============================================================-->
	<!--VIS Type-->
	<xsd:import namespace="http://www.europa.eu/schengen/vis/xsd/v3/types/Application" schemaLocation="../DataTypeDictionary/ApplicationDataTypes.xsd"/>
	<xsd:import namespace="http://www.europa.eu/schengen/vis/xsd/v3/types/Common" schemaLocation="../DataTypeDictionary/CommonDataTypes.xsd"/>
	<!---->
	<xsd:element name="CSSearchByTravelDocument">
		<xsd:annotation>
			<xsd:documentation source="Description">Description: VIS Messages issued by CS to EES.</xsd:documentation>
		</xsd:annotation>
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="Header" type="vdtc:HeaderType"/>
				<xsd:element name="Response">
					<xsd:annotation>
						<xsd:documentation source="Description">Description: CSSearchByTravelDocument.</xsd:documentation>
					</xsd:annotation>
					<xsd:complexType>
						<xsd:sequence>
							<xsd:element name="RCI" type="vdtc:RCIType"/>
							<xsd:element name="ActionResult" minOccurs="0">
								<xsd:annotation>
									<xsd:documentation>Rule: Present if success.</xsd:documentation>
								</xsd:annotation>
								<xsd:complexType>
									<xsd:choice>
										<xsd:element name="VerificationBorder">
											<xsd:annotation>
												<xsd:documentation source="Description">Type: VerificationBorder.</xsd:documentation>
											</xsd:annotation>
											<xsd:complexType>
												<xsd:complexContent>
													<xsd:extension base="vdta:FoundDossierCountType">
														<xsd:sequence>
															<xsd:element name="Result">
																<xsd:annotation>
																	<xsd:documentation>Description: Found data.</xsd:documentation>
																</xsd:annotation>
																<xsd:complexType>
																	<xsd:choice>
																		<xsd:element name="Dossiers">
																			<xsd:annotation>
																				<xsd:documentation>Rule: If NumberOfFoundApplications > 1.</xsd:documentation>
																			</xsd:annotation>
																			<xsd:complexType>
																				<xsd:sequence>
																					<xsd:element name="Dossier" type="vdta:ResultVerificationSearchType" maxOccurs="unbounded"/>
																				</xsd:sequence>
																			</xsd:complexType>
																		</xsd:element>
																		<xsd:element name="Verification" type="vdta:ResultVerificationBorderReadType">
																			<xsd:annotation>
																				<xsd:documentation>Rule: If NumberOfFoundApplications = 1.</xsd:documentation>
																			</xsd:annotation>
																		</xsd:element>
																	</xsd:choice>
																</xsd:complexType>
															</xsd:element>
														</xsd:sequence>
													</xsd:extension>
												</xsd:complexContent>
											</xsd:complexType>
										</xsd:element>
										<xsd:element name="RegistrationEES">
											<xsd:annotation>
												<xsd:documentation source="Description">Type: RegistrationEES.</xsd:documentation>
											</xsd:annotation>
											<xsd:complexType>
												<xsd:complexContent>
													<xsd:extension base="vdta:FoundDossierCountType">
														<xsd:sequence>
															<xsd:element name="Result">
																<xsd:annotation>
																	<xsd:documentation>Description: The result contains only application number therefore there is no distinction between one and more than one applicatition.</xsd:documentation>
																</xsd:annotation>
																<xsd:complexType>
																	<xsd:sequence>
																		<xsd:element name="Dossiers">
																			<xsd:annotation>
																				<xsd:documentation>NumberOfFoundApplications >= 1.</xsd:documentation>
																			</xsd:annotation>
																			<xsd:complexType>
																				<xsd:sequence>
																					<xsd:element name="Dossier" type="vdta:ResultApplicationSearchType" maxOccurs="unbounded"/>
																				</xsd:sequence>
																			</xsd:complexType>
																		</xsd:element>
																	</xsd:sequence>
																</xsd:complexType>
															</xsd:element>
														</xsd:sequence>
													</xsd:extension>
												</xsd:complexContent>
											</xsd:complexType>
										</xsd:element>
									</xsd:choice>
								</xsd:complexType>
							</xsd:element>
						</xsd:sequence>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
</xsd:schema>
