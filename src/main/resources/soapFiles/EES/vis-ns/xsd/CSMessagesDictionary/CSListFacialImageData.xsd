<?xml version="1.0" encoding="UTF-8"?>
<!--version=201-->
<!-- =====================================================================
      || File Name       :  $Id: CSListFacialImageData.xsd
      || Version          :  3.00 || Description    :  VIS Specification messages sent by the Central System to the National System 
      || Copyright European Commission 2014
 ========================================================================= -->
<xsd:schema xmlns:vcsmsg="http://www.europa.eu/schengen/vis/xsd/v3/csmessages" xmlns:vdtc="http://www.europa.eu/schengen/vis/xsd/v3/types/Common" xmlns:vdta="http://www.europa.eu/schengen/vis/xsd/v3/types/Application" xmlns:xsd="http://www.w3.org/2001/XMLSchema" targetNamespace="http://www.europa.eu/schengen/vis/xsd/v3/csmessages" elementFormDefault="qualified" attributeFormDefault="unqualified" version="3.00">
	<!--==============================================================-->
	<!--simpleelement-->
	<!--==============================================================-->
	<!--VIS Type-->
	<xsd:import namespace="http://www.europa.eu/schengen/vis/xsd/v3/types/Application" schemaLocation="../DataTypeDictionary/ApplicationDataTypes.xsd"/>
	<xsd:import namespace="http://www.europa.eu/schengen/vis/xsd/v3/types/Common" schemaLocation="../DataTypeDictionary/CommonDataTypes.xsd"/>
	<!---->
	<xsd:element name="CSListFacialImageData">
		<xsd:annotation>
			<xsd:documentation source="Description">Description: VIS Messages issued by CS to NS.</xsd:documentation>
		</xsd:annotation>
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="Header" type="vdtc:HeaderType"/>
				<xsd:element name="Response">
					<xsd:annotation>
						<xsd:documentation source="Description">Description: CSListFacialImageDataResponse.</xsd:documentation>
					</xsd:annotation>
					<xsd:complexType>
						<xsd:sequence>
							<xsd:element name="RCI" type="vdtc:RCIType"/>
							<xsd:element name="NumberOfElements" type="vdtc:ReturnNumberType">
								<xsd:annotation>
									<xsd:documentation>Description: Description: This field has been added to allow the MS to identify if the result contains 0 reccord or if it is empty due to an error.</xsd:documentation>
								</xsd:annotation>
							</xsd:element>
							<xsd:element name="FacialImageReferences" minOccurs="0">
								<xsd:annotation>
									<xsd:documentation>Description: Reference of facial images.</xsd:documentation>
								</xsd:annotation>
								<xsd:complexType>
									<xsd:sequence>
										<xsd:element name="FacialImageReference" type="vdta:FacialImageReferenceGetType" maxOccurs="unbounded"/>
									</xsd:sequence>
								</xsd:complexType>
							</xsd:element>
						</xsd:sequence>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
</xsd:schema>
