<?xml version="1.0" encoding="UTF-8"?>
<!--version=201-->
<!-- =====================================================================
      || File Name       :  $Id: CSAutomatedDeletionReport.xsd
      || Version          :  3.00 || Description    :  VIS Specification messages sent by the Central System to the National System 
      || Copyright European Commission 2014
 ========================================================================= -->
<xsd:schema xmlns:vcsmsg="http://www.europa.eu/schengen/vis/xsd/v3/csmessages" xmlns:vdtc="http://www.europa.eu/schengen/vis/xsd/v3/types/Common" xmlns:vdta="http://www.europa.eu/schengen/vis/xsd/v3/types/Application" xmlns:vdtr="http://www.europa.eu/schengen/vis/xsd/v3/types/Report" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ns1="http://www.europa.eu/schengen/vis/xsd/v3/types/Application" targetNamespace="http://www.europa.eu/schengen/vis/xsd/v3/csmessages" elementFormDefault="qualified" attributeFormDefault="unqualified" version="3.00">
	<!--==============================================================-->
	<!--simpleelement-->
	<!--==============================================================-->
	<!--VIS Type-->
	<xsd:import namespace="http://www.europa.eu/schengen/vis/xsd/v3/types/Application" schemaLocation="../DataTypeDictionary/ApplicationDataTypes.xsd"/>
	<xsd:import namespace="http://www.europa.eu/schengen/vis/xsd/v3/types/Report" schemaLocation="../DataTypeDictionary/ReportDataTypes.xsd"/>
	<xsd:import namespace="http://www.europa.eu/schengen/vis/xsd/v3/types/Common" schemaLocation="../DataTypeDictionary/CommonDataTypes.xsd"/>
	<!---->
	<xsd:element name="CSAutomatedDeletionReport">
		<xsd:annotation>
			<xsd:documentation source="Description">Description: VIS Messages issued by CS to NS.</xsd:documentation>
		</xsd:annotation>
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="Header" type="vdtc:HeaderType"/>
				<xsd:element name="Report">
					<xsd:annotation>
						<xsd:documentation source="Description">Description: This report is generated when an automated deletion of the applications occurs.</xsd:documentation>
					</xsd:annotation>
					<xsd:complexType>
						<xsd:sequence>
							<xsd:element name="DateOfDeletion" type="vdtc:DateType"/>
							<xsd:element name="ApplicationNumbers">
								<xsd:annotation>
									<xsd:documentation>Description: ApplicationNumbers of the deleted elements.</xsd:documentation>
								</xsd:annotation>
								<xsd:complexType>
									<xsd:sequence>
										<xsd:element name="ApplicationNumber" type="vdta:ApplicationNumberType" maxOccurs="unbounded"/>
									</xsd:sequence>
								</xsd:complexType>
							</xsd:element>
						</xsd:sequence>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
</xsd:schema>
