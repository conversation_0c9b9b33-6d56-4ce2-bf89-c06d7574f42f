<?xml version="1.0" encoding="UTF-8"?>
<!--version=201-->
<!-- =====================================================================
      || File Name       :  $Id: CSSearchEESByVisaStickerNumber.xsd
      || Version          :  3.00 || Description    :  VIS Specification messages sent by the Central System to the EES
      || Copyright European Commission 2019
 ========================================================================= -->
<xsd:schema xmlns:vcsmsg="http://www.europa.eu/schengen/vis/xsd/v3/csmessages" xmlns:vdtc="http://www.europa.eu/schengen/vis/xsd/v3/types/Common" xmlns:vdta="http://www.europa.eu/schengen/vis/xsd/v3/types/Application" xmlns:ees="http://www.europa.eu/schengen/ees/xsd/v1" xmlns:xsd="http://www.w3.org/2001/XMLSchema" targetNamespace="http://www.europa.eu/schengen/vis/xsd/v3/csmessages" elementFormDefault="qualified" attributeFormDefault="unqualified" version="3.00">
	<!--==============================================================-->
	<!--simpleelement-->
	<!--==============================================================-->
	<!--VIS Type-->
	<xsd:import namespace="http://www.europa.eu/schengen/vis/xsd/v3/types/Application" schemaLocation="../DataTypeDictionary/ApplicationDataTypes.xsd"/>
	<xsd:import namespace="http://www.europa.eu/schengen/vis/xsd/v3/types/Common" schemaLocation="../DataTypeDictionary/CommonDataTypes.xsd"/>
	<xsd:import namespace="http://www.europa.eu/schengen/ees/xsd/v1" schemaLocation="../../../ees/xsd/DataTypes.xsd"/>
	<!---->
	<xsd:element name="CSSearchEESByVisaStickerNumber">
		<xsd:annotation>
			<xsd:documentation source="Description">Description: CSSearchEESByVisaStickerNumber.</xsd:documentation>
		</xsd:annotation>
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="Header" type="vdtc:HeaderType"/>
				<xsd:element name="Response">
					<xsd:annotation>
						<xsd:documentation source="Description">Description: CSSearchEESByVisaStickerNumber.</xsd:documentation>
					</xsd:annotation>
					<xsd:complexType>
						<xsd:sequence>
							<xsd:element name="RCI" type="vdtc:RCIType"/>
							<xsd:element name="ActionResult" minOccurs="0">
								<xsd:annotation>
									<xsd:documentation>Rule: Present if success.</xsd:documentation>
								</xsd:annotation>
								<xsd:complexType>
									<xsd:sequence>
										<xsd:element name="Paging" type="ees:PagingResponseType"/>
										<xsd:element name="TravellerFile" minOccurs="0" maxOccurs="unbounded">
											<xsd:complexType>
												<xsd:complexContent>
													<xsd:extension base="ees:TravellerFileSearchResponseType">
														<xsd:sequence>
															<xsd:element name="Attachments" type="vdta:BiometricTFGetType" minOccurs="0"/>
														</xsd:sequence>
													</xsd:extension>
												</xsd:complexContent>
											</xsd:complexType>
										</xsd:element>
									</xsd:sequence>
								</xsd:complexType>
							</xsd:element>
						</xsd:sequence>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
</xsd:schema>
