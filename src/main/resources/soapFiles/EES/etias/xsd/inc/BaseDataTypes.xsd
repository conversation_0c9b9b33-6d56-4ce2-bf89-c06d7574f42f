<?xml version="1.0" encoding="UTF-8"?>
<!--
==========================================================================================
|| File Name       :  etias/xsd/common/BaseDataTypes.xsd
|| Version         :  1.00
|| Description     :  List of base types shared by several ICD implying ETIAS
==========================================================================================
-->
<xs:schema xmlns:etias="http://www.europa.eu/schengen/etias/xsd/v1"
            xmlns:xs="http://www.w3.org/2001/XMLSchema"
            xmlns:shared="http://www.europa.eu/schengen/shared/xsd/v1"
            targetNamespace="http://www.europa.eu/schengen/etias/xsd/v1"
            elementFormDefault="qualified" version="1.00">

    <xs:import namespace="http://www.europa.eu/schengen/shared/xsd/v1" schemaLocation="../../../shared/xsd/DataTypes.xsd"/>

    <xs:include schemaLocation="CodeTableTypes.xsd"/>

    <xs:simpleType name="IdentifierType">
        <xs:restriction base="xs:anyURI"/>
    </xs:simpleType>


    <!-- application data types -->

    <xs:complexType name="AddressType">
        <xs:annotation>
            <xs:documentation>For minors, surname and first name(s), home address, email address and, if available, phone number of the person exercising parental authority or of the applicant's legal guardian.

                [ETIAS Regulation] Article 17(2)(k)

                Where he or she claims the status of family member (...) the surname, first name(s), (...) home address, email address and, if available, phone number of the family member with whom the applicant has family ties.

                [ETIAS Regulation] Article 17(2)(l)</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="FirstName" type="etias:FirstNameType" minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="FamilyName" type="etias:FamilyNameType"/>
            <xs:element name="Street" type="etias:StreetType" minOccurs="0"/>
            <xs:element name="StreetNumber" type="etias:StreetNumberType" minOccurs="0"/>
            <xs:element name="ApartmentNumber" type="etias:ApartmentNumberType" minOccurs="0"/>
            <xs:element name="SecondLineAddress" type="etias:SecondLineAddressType" minOccurs="0"/>
            <xs:element name="City" type="etias:CityType"/>
            <xs:element name="PostCode" type="etias:PostCodeType"/>
            <xs:element name="Country" type="shared:CT01_CountryType">
                <xs:annotation>
                    <xs:documentation>The country where the authority is located. A code table value.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="PhoneNumber" type="shared:PhoneNumberType"/>
            <xs:element name="E-mail" type="shared:EmailType" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>



    <xs:complexType name="FamilyMemberType">
        <xs:annotation>
            <xs:documentation>Family member referred to in point (c) of Article 2(1) [ETIAS Regulation].</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="etias:AddressType">
                <xs:sequence>
                    <xs:element name="CountryOfBirth" type="shared:CT01_CountryType">
                        <xs:annotation>
                            <xs:documentation>VIS Description: The country where the authority is located. A code table value.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="DateOfBirth" type="shared:PseudodateType" minOccurs="0"/>
                    <xs:element name="PlaceOfBirth" type="etias:PlaceOfBirthType"/>
                    <xs:element name="Nationality" type="shared:CT02_CountryOfNationalityType">
                        <xs:annotation>
                            <xs:documentation>Current nationality.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="FamilyTies" type="etias:CTX14_FamilyTiesType">
                        <xs:annotation>
                            <xs:documentation>Family ties with that family member in accordance with Article 2(2) of Directive 2004/38/EC.

                                [ETIAS Regulation] Article 17(2)(l).</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="ParentType">
        <xs:annotation>
            <xs:documentation>For minors, surname and first name(s), home address, email address and, if available, phone number of the person exercising parental authority or of the applicant's legal guardian.

                [ETIAS Regulation] Artcile 17(2)(k)</xs:documentation>
        </xs:annotation>
        <xs:complexContent>
            <xs:extension base="etias:AddressType"/>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="TravelAuthorisationRequestType">
        <xs:annotation>
            <xs:documentation>Travel authorisation - a decision issued in accordance with ETIAS Regulation which is required for third-country nationals to fulfil the entry condition.

                Flag is present only: whether the travel authorisation will expire within the next 90 days and the remaining validity period;

                [ETIAS Regulation] Article 47(2)(c)</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="ApplicationNumber" type="etias:IdentifierType">
                <xs:annotation>
                    <xs:documentation>Application number of the application file recorded by the ETIAS Central System.

                        [EES Regulation]  Article 17(2)
                        [ETIAS Regulation] Article 11a</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="Valid" type="xs:boolean">
                <xs:annotation>
                    <xs:documentation>Whether or not the person has a valid travel authorisation.

                        [Article 47(2)(a)]</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="ValidUntil" type="xs:date" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>End of validity period of an ETIAS travel authorisation.

                        [EES Regulation]  Article 17(2)
                        [ETIAS Regulation] Article 11a</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="TerritorialValidity" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>In the case of a travel authorisation with limited territorial validity issued under Article 44, the Member State(s) for which it is valid

                        [ETIAS Regulation] Article 47(2)(a)

                        In case of a travel authorisation with limited territorial validity, the Member State(s) for which it is valid.

                        [Article 17(2)(c)]</xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="TravelTerritory" type="shared:CT06_TerritoryType" maxOccurs="unbounded"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TravelAuthorisationResponseType">
        <xs:complexContent>
            <xs:extension base="etias:TravelAuthorisationRequestType">
                <xs:sequence>
                    <xs:element name="TravelDocument" type="shared:TravelDocumentType"/>
                    <xs:element name="Parent" type="etias:ParentType" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>For minors, surname and first name(s), home address, email address and, if available, phone number of the person exercising parental authority or of the applicant's legal guardian

                                [ETIAS Regulation] Article 17(2)(k)

                                Note: This data is not stored in the EES database.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="FamilyMember" type="etias:FamilyMemberType" minOccurs="0">
                        <xs:annotation>
                            <xs:documentation>Where he or she claims the status of family member referred to in point (c) of Article 2(1):
                                (i) his or her status of family member;
                                (ii) the surname, first name(s), date of birth, place of birth, country of birth, current nationality, home address, email address and, if available, phone number of the family member with whom the applicant has family ties;
                                (iii) his or her family ties with that family member in accordance with Article 2(2) of Directive 2004/38/EC

                                [ETIAS Regulation] Article 17(2)(l)

                                Note: This data is not stored in the EES database.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                    <xs:element name="ETIASFlag" type="etias:CTX13_ETIASFlagType" minOccurs="0" maxOccurs="unbounded">
                        <xs:annotation>
                            <xs:documentation>Any flag attached to the travel authorisation under Article 36(2) and (3);

                                [ETIAS Regulation] Article 47(2)(b)

                                In cases where there is doubt as to whether sufficient reasons to refuse the travel authorisation exist, the ETIAS National Unit of the Member State responsible shall have the possibility, including after an interview, to issue a travel authorisation with a flag recommending to border authorities to proceed with a second line check.
                                (...)
                                The flag shall be removed automatically once the border authorities have carried out the check and have entered the entry record in the EES.

                                [ETIAS Regulation] Article 36(2)

                                The ETIAS National Unit of the Member State responsible shall have the possibility to add a flag indicating to border authorities and other authorities with access to the data in the ETIAS Central System that a specific hit triggered during the processing of the application has been assessed and that it has been verified that the hit constituted a false hit or that the manual processing has shown that there were no grounds for the refusal of the travel authorisation.

                                [ETIAS Regulation] Article 36(3)

                                Note: This data is not stored in the EES database.</xs:documentation>
                        </xs:annotation>
                    </xs:element>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:simpleType name="PostCodeType">
        <xs:annotation>
            <xs:documentation>Postcode/zip code.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:maxLength value="30"/>
            <xs:pattern value="[A-Za-zÀ-žẞ ,_\.\-]+"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="CityType">
        <xs:annotation>
            <xs:documentation>City/town with state/region/province when relevant.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:maxLength value="255"/>
            <xs:pattern value="[A-Za-zÀ-žẞ ,_\.\-]+"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="SecondLineAddressType">
        <xs:restriction base="xs:string">
            <xs:maxLength value="255"/>
            <xs:pattern value="[A-Za-zÀ-žẞ ,_\.\-]+"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="ApartmentNumberType">
        <xs:restriction base="xs:string">
            <xs:maxLength value="30"/>
            <xs:pattern value="[A-Za-zÀ-žẞ ,_\.\-]+"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="StreetNumberType">
        <xs:restriction base="xs:string">
            <xs:maxLength value="30"/>
            <xs:pattern value="[A-Za-zÀ-žẞ ,_\.\-]+"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="StreetType">
        <xs:annotation>
            <xs:documentation>Street name.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:maxLength value="255"/>
            <xs:pattern value="[A-Za-zÀ-žẞ ,_\.\-]+"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="FamilyNameType">
        <xs:annotation>
            <xs:documentation>Surname (family name).</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:maxLength value="255"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="FirstNameType">
        <xs:annotation>
            <xs:documentation>First name(s) (given names).</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:maxLength value="255"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="PlaceOfBirthType">
        <xs:restriction base="xs:string">
            <xs:maxLength value="255"/>
            <xs:pattern value="[A-Za-zÀ-žẞ ,_\.\-]+"/>
        </xs:restriction>
    </xs:simpleType>

</xs:schema>
