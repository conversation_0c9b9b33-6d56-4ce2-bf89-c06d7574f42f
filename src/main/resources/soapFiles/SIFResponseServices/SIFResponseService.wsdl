<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<wsdl:definitions xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
             xmlns:tns="http://sifresponseservice.sif.it/"
             xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
             xmlns:xsd="http://www.w3.org/2001/XMLSchema"
             name="SIFResponseService"
             targetNamespace="http://sifresponseservice.sif.it/">
  <wsdl:types>
   <xsd:schema targetNamespace="http://sifresponseservice.sif.it/">
     <xsd:include schemaLocation="RetrieveEESA.xsd"/>
     <xsd:element name="SifHeader" type="tns:SifHeaderType" />
   </xsd:schema>
  </wsdl:types>

  <wsdl:message name="NewOperationRequest">
    <wsdl:part element="tns:RetrieveEESAsyncRequest" name="parameters"/>
  </wsdl:message>
  
  <wsdl:message name="NewOperationResponse">
    <wsdl:part element="tns:RetrieveEESAsyncResponse" name="parameters"/>
  </wsdl:message>

  <wsdl:message name="SifHeader" >
    <wsdl:part name="header" element="tns:SifHeader"/>
  </wsdl:message>
  
  <wsdl:portType name="SIFResponseService">
    <wsdl:operation name="GetResponse">
      <wsdl:input message="tns:NewOperationRequest"/>
      <wsdl:output message="tns:NewOperationResponse"/>
    </wsdl:operation>
  </wsdl:portType>
  
  <wsdl:binding name="SIFResponseServiceSOAP" type="tns:SIFResponseService">
    <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
    <wsdl:operation name="GetResponse">
      <soap:operation soapAction="http://sifresponseservice.sif.it/NewOperation"/>
      <wsdl:input>
        <soap:header  use="literal" message="tns:SifHeader" part="header"/>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="SIFResponseService">
    <wsdl:port binding="tns:SIFResponseServiceSOAP" name="SIFResponseServiceSOAP">
      <soap:address location="https://sifee-coll.cen.poliziadistato.it/ct/sif-async"/>
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>
