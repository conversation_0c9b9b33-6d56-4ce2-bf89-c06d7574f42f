<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:s="http://www.w3.org/2001/XMLSchema" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/"
                  xmlns:http="http://schemas.xmlsoap.org/wsdl/http/"
                  xmlns:tns="http://tempuri.org/" xmlns:s1="http://schemas.sif.bcs.sita.it/"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  targetNamespace="http://tempuri.org/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <s:schema elementFormDefault="qualified" targetNamespace="http://tempuri.org/">
      <s:import namespace="http://schemas.sif.bcs.sita.it/" />
      <s:element name="SIFServizioBCS">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ric" type="tns:BCSAlertRequest" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="BCSAlertRequest">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="header" type="tns:Header" />
          <s:element minOccurs="0" maxOccurs="1" name="traveller" type="s1:Traveller" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="Header">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="idApplicazioneChiamante" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="serviceUrl" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="idTransazione" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="timeOutClient" type="s:int" />
        </s:sequence>
      </s:complexType>
      <s:element name="SIFServizioBCSResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="SIFServizioBCSResult" type="s1:BCSAlertResponse" />
          </s:sequence>
        </s:complexType>
      </s:element>
    </s:schema>
    <s:schema elementFormDefault="qualified" targetNamespace="http://schemas.sif.bcs.sita.it/">
      <s:complexType name="Traveller">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" form="unqualified" name="airportCode" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" form="unqualified" name="countryOfIssue" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" form="unqualified" name="dateOfBirth" type="s:dateTime" />
          <s:element minOccurs="0" maxOccurs="1" form="unqualified" name="docNumber" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" form="unqualified" name="docType" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" form="unqualified" name="firstName" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" form="unqualified" name="lastName" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" form="unqualified" name="nationality" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="BCSAlertResponse">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" form="unqualified" name="descrizione" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" form="unqualified" name="esito" type="s:int" />
          <s:element minOccurs="1" maxOccurs="1" form="unqualified" name="noSegnalazioni" type="s:int" />
          <s:element minOccurs="0" maxOccurs="unbounded" form="unqualified" name="positivitaTipo" nillable="true" type="s1:positivitaTipo" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="positivitaTipo">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" form="unqualified" name="fonte" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" form="unqualified" name="soggetto" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" form="unqualified" name="stato" type="s:string" />
        </s:sequence>
      </s:complexType>
    </s:schema>
  </wsdl:types>
  <wsdl:message name="SIFServizioBCSSoapIn">
    <wsdl:part name="parameters" element="tns:SIFServizioBCS" />
  </wsdl:message>
  <wsdl:message name="SIFServizioBCSSoapOut">
    <wsdl:part name="parameters" element="tns:SIFServizioBCSResponse" />
  </wsdl:message>
  <wsdl:portType name="wsSIFBCSProxySoap">
    <wsdl:operation name="SIFServizioBCS">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Richiesta Servizio BCS OnDemand</wsdl:documentation>
      <wsdl:input message="tns:SIFServizioBCSSoapIn" />
      <wsdl:output message="tns:SIFServizioBCSSoapOut" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="wsSIFBCSProxySoap" type="tns:wsSIFBCSProxySoap">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="SIFServizioBCS">
      <soap:operation soapAction="http://tempuri.org/SIFServizioBCS" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="wsSIFBCSProxySoap12" type="tns:wsSIFBCSProxySoap">
    <soap12:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="SIFServizioBCS">
      <soap12:operation soapAction="http://tempuri.org/SIFServizioBCS" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="wsSIFBCSProxy">
    <wsdl:port name="wsSIFBCSProxySoap" binding="tns:wsSIFBCSProxySoap">
      <soap:address location="https://sifext-egate-coll.cen.poliziadistato.it/Sif-External-Service/sif/wsSIFServicesSoap" />
    </wsdl:port>
    <wsdl:port name="wsSIFBCSProxySoap12" binding="tns:wsSIFBCSProxySoap12">
      <soap12:address location="https://sifext-egate-coll.cen.poliziadistato.it/Sif-External-Service/sif/wsSIFServicesSoap" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>