<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:s="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/"
                  xmlns:http="http://schemas.xmlsoap.org/wsdl/http/"
                  xmlns:tns="http://tempuri.org/"
                  xmlns:s2="http://common.business.sdi.ibm.it"
                  xmlns:s1="http://interrogazione.iae.sdi.ibm.it"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  targetNamespace="http://tempuri.org/"
                  xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <s:schema elementFormDefault="qualified" targetNamespace="http://tempuri.org/">
      <s:import namespace="http://common.business.sdi.ibm.it"  schemaLocation="wsSIFProxyMsg.xsd">
      </s:import>
      
      <s:import namespace="http://common.business.sdi.ibm.it"  schemaLocation="wsSIFProxySifHeader.xsd"/>
      
      <s:element name="SifHeader">
        <s:complexType>
          <s:sequence>
           <s:element name="versione" nillable="false" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      
      <s:element name="SIFServizioS004">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ric" type="tns:IAERequestS004" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="IAERequestS004">
        <s:complexContent mixed="false">
          <s:extension base="s1:RichiestaS004">
            <s:sequence>
              <s:element minOccurs="0" maxOccurs="1" name="idTransazione" type="s:string" />
              <s:element minOccurs="1" maxOccurs="1" name="timeOutClient" type="s:int" />
              <s:element minOccurs="0" maxOccurs="1" name="password" type="s:string" />
              <s:element minOccurs="0" maxOccurs="1" name="userName" type="s:string" />
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:element name="SIFServizioS004Response">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="SIFServizioS004Result" type="s1:RispostaS004" />
          </s:sequence>
        </s:complexType>
      </s:element>
    </s:schema>
    <s:schema elementFormDefault="qualified" targetNamespace="http://interrogazione.iae.sdi.ibm.it">
      <s:import namespace="http://interrogazione.iae.sdi.ibm.it"  schemaLocation="wsSIFProxy.xsd">
      </s:import>
      <s:complexType name="RichiestaS004">
        <s:complexContent mixed="false">
          <s:extension base="s2:WSRichiesta">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="timeStampRichiesta" nillable="true" type="s:string" />
              <s:element minOccurs="0" maxOccurs="1" name="cognome" type="s:string" />
              <s:element minOccurs="0" maxOccurs="1" name="nome" type="s:string" />
              <s:element minOccurs="1" maxOccurs="1" name="dataNas" type="s:dateTime" />
              <s:element minOccurs="0" maxOccurs="1" name="indicatoreDataNas" type="s:string" />
              <s:element minOccurs="0" maxOccurs="1" nillable="true" name="codiceLuogoNas" type="s:int" />
              <s:element name="codIso3LuogoNas" minOccurs="0" maxOccurs="1">
                <s:simpleType>
                  <s:restriction base="s:string">
                    <s:maxLength value="3" />
                  </s:restriction>
                </s:simpleType>
              </s:element>
              <s:element minOccurs="0" maxOccurs="1" name="luogoNas" type="s:string" />
              <s:element minOccurs="1" maxOccurs="1" name="siglaProvinciaNas" nillable="true" type="s:string" />
              <s:element minOccurs="0" maxOccurs="1" name="indicatoreInformativa" type="s:string" />
              <s:element minOccurs="0" maxOccurs="1" name="indicatoreAnagraficaCollegata" type="s:string" />
              <s:element minOccurs="0" maxOccurs="1" name="indicatorePermessoSoggiorno" type="s:string" />
              <s:element minOccurs="0" maxOccurs="1" name="indicatoreScomparso" type="s:string" />
              <s:element minOccurs="1" maxOccurs="1" name="codiceTipoInformativa" nillable="true" type="s:string" />
              <s:element minOccurs="1" maxOccurs="1" name="dataInizioRicInformativa" nillable="true" type="s:dateTime" />
              <s:element minOccurs="1" maxOccurs="1" name="dataFineRicInformativa" nillable="true" type="s:dateTime" />
              <s:element minOccurs="1" maxOccurs="1" name="indicatoreAggravanti" nillable="true" type="s:string" />
              <s:element minOccurs="1" maxOccurs="1" name="flagSchengen" nillable="true" type="s:string" />
              <s:element minOccurs="1" maxOccurs="1" name="flagAsf" nillable="true" type="s:string" />
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
    </s:schema>
  </wsdl:types>
  <wsdl:message name="sifHeader">
		<wsdl:part name="SifHeader" element="tns:SifHeader"/>
	</wsdl:message>
  <wsdl:message name="SIFServizioS004SoapIn">
    <wsdl:part name="parameters" element="tns:SIFServizioS004" />
  </wsdl:message>
  <wsdl:message name="SIFServizioS004SoapOut">
    <wsdl:part name="parameters" element="tns:SIFServizioS004Response" />
  </wsdl:message>
  <wsdl:portType name="wsSIFS004ProxySoap">
    <wsdl:operation name="SIFServizioS004">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Richiesta Servizio IAE S004</wsdl:documentation>
      <wsdl:input message="tns:SIFServizioS004SoapIn" />
      <wsdl:output message="tns:SIFServizioS004SoapOut" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="wsSIFS004ProxySoap" type="tns:wsSIFS004ProxySoap">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="SIFServizioS004">
      <soap:operation soapAction="http://tempuri.org/SIFServizioS004" style="document" />
      <wsdl:input>
      	<soap:header use="literal" part="SifHeader" message="tns:sifHeader"/>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="wsSIFS004ProxySoap12" type="tns:wsSIFS004ProxySoap">
    <soap12:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="SIFServizioS004">
      <soap12:operation soapAction="http://tempuri.org/SIFServizioS004" style="document" />
      <wsdl:input>
        <soap:header use="literal" part="SifHeader" message="tns:sifHeader"/>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="wsSIFS004Proxy">
    <wsdl:port name="wsSIFS004ProxySoap" binding="tns:wsSIFS004ProxySoap">
      <soap:address location="https://sifext-egate-coll.cen.poliziadistato.it/Sif-External-Service/sif/wsSIFServicesSoap" />
    </wsdl:port>
    <wsdl:port name="wsSIFS004ProxySoap12" binding="tns:wsSIFS004ProxySoap12">
      <soap12:address location="https://sifext-egate-coll.cen.poliziadistato.it/Sif-External-Service/sif/wsSIFServicesSoap" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>