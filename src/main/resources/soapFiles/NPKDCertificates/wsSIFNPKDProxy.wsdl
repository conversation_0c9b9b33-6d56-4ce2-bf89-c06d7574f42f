<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:s="http://www.w3.org/2001/XMLSchema" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/"
                  xmlns:http="http://schemas.xmlsoap.org/wsdl/http/"
                  xmlns:tns="http://tempuri.org/" xmlns:s1="http://npkd.cen.it/NPKD/"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  targetNamespace="http://tempuri.org/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <s:schema elementFormDefault="qualified" targetNamespace="http://tempuri.org/">
      <s:import namespace="http://npkd.cen.it/NPKD/" />
      <s:element name="GetISCert">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="attore" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="idMachine" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetISCertResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetISCertResult" type="s1:Esito" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetMasterListCSCA">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="attore" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetMasterListCSCAResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetMasterListCSCAResult" type="s1:Esito" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetMasterListaCVCA">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="attore" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetMasterListaCVCAResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetMasterListaCVCAResult" type="s1:Esito" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetDocSignerAndRevocationList">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="attore" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="blocco" type="s:int" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetDocSignerAndRevocationListResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetDocSignerAndRevocationListResult" type="s1:Esito" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetDVAndRevocationList">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="attore" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetDVAndRevocationListResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetDVAndRevocationListResult" type="s1:Esito" />
          </s:sequence>
        </s:complexType>
      </s:element>
    </s:schema>
    <s:schema elementFormDefault="qualified" targetNamespace="http://npkd.cen.it/NPKD/">
      <s:complexType name="Esito">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" form="unqualified" name="ldif" type="s:base64Binary" />
          <s:element minOccurs="0" maxOccurs="1" form="unqualified" name="codError" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" form="unqualified" name="descError" type="s:string" />
        </s:sequence>
      </s:complexType>
    </s:schema>
  </wsdl:types>
  <wsdl:message name="GetISCertSoapIn">
    <wsdl:part name="parameters" element="tns:GetISCert" />
  </wsdl:message>
  <wsdl:message name="GetISCertSoapOut">
    <wsdl:part name="parameters" element="tns:GetISCertResponse" />
  </wsdl:message>
  <wsdl:message name="GetMasterListCSCASoapIn">
    <wsdl:part name="parameters" element="tns:GetMasterListCSCA" />
  </wsdl:message>
  <wsdl:message name="GetMasterListCSCASoapOut">
    <wsdl:part name="parameters" element="tns:GetMasterListCSCAResponse" />
  </wsdl:message>
  <wsdl:message name="GetMasterListaCVCASoapIn">
    <wsdl:part name="parameters" element="tns:GetMasterListaCVCA" />
  </wsdl:message>
  <wsdl:message name="GetMasterListaCVCASoapOut">
    <wsdl:part name="parameters" element="tns:GetMasterListaCVCAResponse" />
  </wsdl:message>
  <wsdl:message name="GetDocSignerAndRevocationListSoapIn">
    <wsdl:part name="parameters" element="tns:GetDocSignerAndRevocationList" />
  </wsdl:message>
  <wsdl:message name="GetDocSignerAndRevocationListSoapOut">
    <wsdl:part name="parameters" element="tns:GetDocSignerAndRevocationListResponse" />
  </wsdl:message>
  <wsdl:message name="GetDVAndRevocationListSoapIn">
    <wsdl:part name="parameters" element="tns:GetDVAndRevocationList" />
  </wsdl:message>
  <wsdl:message name="GetDVAndRevocationListSoapOut">
    <wsdl:part name="parameters" element="tns:GetDVAndRevocationListResponse" />
  </wsdl:message>
  <wsdl:portType name="wsSIFServiceProxySoap">
    <wsdl:operation name="GetISCert">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">RichiestaListISCert</wsdl:documentation>
      <wsdl:input message="tns:GetISCertSoapIn" />
      <wsdl:output message="tns:GetISCertSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetMasterListCSCA">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">RichiestaMasterListCSCA</wsdl:documentation>
      <wsdl:input message="tns:GetMasterListCSCASoapIn" />
      <wsdl:output message="tns:GetMasterListCSCASoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetMasterListaCVCA">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">RichiestaMasterListCVCA</wsdl:documentation>
      <wsdl:input message="tns:GetMasterListaCVCASoapIn" />
      <wsdl:output message="tns:GetMasterListaCVCASoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetDocSignerAndRevocationList">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">RichiestaDocSignerAndRevocationList</wsdl:documentation>
      <wsdl:input message="tns:GetDocSignerAndRevocationListSoapIn" />
      <wsdl:output message="tns:GetDocSignerAndRevocationListSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetDVAndRevocationList">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">GetDVAndRevocationList</wsdl:documentation>
      <wsdl:input message="tns:GetDVAndRevocationListSoapIn" />
      <wsdl:output message="tns:GetDVAndRevocationListSoapOut" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="wsSIFServiceProxySoap" type="tns:wsSIFServiceProxySoap">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="GetISCert">
      <soap:operation soapAction="http://tempuri.org/GetISCert" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetMasterListCSCA">
      <soap:operation soapAction="http://tempuri.org/GetMasterListCSCA" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetMasterListaCVCA">
      <soap:operation soapAction="http://tempuri.org/GetMasterListaCVCA" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetDocSignerAndRevocationList">
      <soap:operation soapAction="http://tempuri.org/GetDocSignerAndRevocationList" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetDVAndRevocationList">
      <soap:operation soapAction="http://tempuri.org/GetDVAndRevocationList" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="wsSIFServiceProxySoap12" type="tns:wsSIFServiceProxySoap">
    <soap12:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="GetISCert">
      <soap12:operation soapAction="http://tempuri.org/GetISCert" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetMasterListCSCA">
      <soap12:operation soapAction="http://tempuri.org/GetMasterListCSCA" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetMasterListaCVCA">
      <soap12:operation soapAction="http://tempuri.org/GetMasterListaCVCA" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetDocSignerAndRevocationList">
      <soap12:operation soapAction="http://tempuri.org/GetDocSignerAndRevocationList" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetDVAndRevocationList">
      <soap12:operation soapAction="http://tempuri.org/GetDVAndRevocationList" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="wsSIFServiceProxy">
    <wsdl:port name="wsSIFServiceProxySoap" binding="tns:wsSIFServiceProxySoap">
      <soap:address location="https://sifext-egate-coll.cen.poliziadistato.it/Sif-External-Service/sif/wsSIFServicesSoap" />
    </wsdl:port>
    <wsdl:port name="wsSIFServiceProxySoap12" binding="tns:wsSIFServiceProxySoap12">
      <soap12:address location="https://sifext-egate-coll.cen.poliziadistato.it/Sif-External-Service/sif/wsSIFServicesSoap" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>