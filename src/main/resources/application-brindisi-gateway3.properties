spring.datasource.url=*******************************************************************************************
#exTODO: consiglio di aggiornare Postgres alla versione piu recente (17) - per ora migliorato passando da postgres 9.5 a postgres 14.8 alpine
spring.datasource.username=postgres
spring.datasource.password=RJSTpP&+
#spring.datasource.url=********************************************
#spring.datasource.username=postgres
#spring.datasource.password=password
#spring.datasource.password=docker

spring.datasource.hikari.pool-name=gateway4ees
spring.datasource.hikari.max-lifetime=120000
#spring.datasource.hikari.max-lifetime=1800000 pari a 30 minuti
#se hai problemi in prod, allora disabilita hibernate L2 caching (con caffeine e jcache), quindi rimuovi spring.datasource.hikari.keepalive-time=30000
spring.datasource.hikari.keepalive-time=30000
#spring.datasource.hikari.maximum-pool-size=20
#spring.datasource.hikari.maximum-pool-size=10
#spring.datasource.hikari.minimum-idle=5
#spring.datasource.hikari.minimum-idle=10
#spring.datasource.hikari.idle-timeout=30000
#spring.datasource.hikari.idle-timeout=600000
#spring.datasource.hikari.leak-detection-threshold=2000
#spring.datasource.hikari.shutdown-timeout=5000
#spring.datasource.hikari.connection-timeout=30000

spring.jpa.hibernate.ddl-auto=update
## ATTENZIONE: Se hai problemi con DB (prestazioni o errori di LazyInitialization), allora imposta spring.jpa.open-in-view=true
spring.jpa.open-in-view=false
#spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.datasource.driver-class-name=org.postgresql.Driver
#spring.jpa.show-sql=true
logging.level.sql=off
#logging.level.sql=trace per loggare su file anche tutte le query sql eseguite
#spring.jpa.properties.hibernate.format_sql=true
#spring.jpa.properties.hibernate.use_sql_comments=true
logging.level.com.zaxxer.hikari.HikariConfig=DEBUG
logging.level.com.zaxxer.hikari=INFO

server.servlet.context-path=/ees_gateway

management.endpoints.web.exposure.include=*
management.endpoint.health.show-details=when_authorized
#management.endpoint.health.show-details=always
management.endpoints.web.base-path=/actuator
management.endpoint.prometheus.access=unrestricted
management.prometheus.metrics.export.enabled=true

#management.metrics.export.graphite.host=127.0.0.1
#management.metrics.export.graphite.port=2004
#management.endpoints.web.exposure.include=*

zabbix.network.monitor.enabled=false
zabbix.network.monitor.frequency.ms=60000
zabbix.monitor.ip.addresses=*************:443,*************:443,*************:443,************:443,************:443
zabbix.network.monitor.use.external.zabbix.sender=false
zabbix.server.host=************3
zabbix.server.port=10051
zabbix.host.name=eesgateway

endpoint.endBorderControl.withTooMuchData.enabled=false

server.shutdown=graceful
spring.lifecycle.timeout-per-shutdown-phase=60s
spring.task.scheduling.shutdown.await-termination=true
spring.threads.virtual.enabled=true
spring.threads.type=virtual

spring.devtools.restart.enabled=false

logging.level.org.springframework.security=WARN
#logging.level.org.springframework.security=INFO
logging.level.org.springframework.web=WARN
#logging.level.org.springframework.web=INFO
logging.level.com.reco=INFO

using.heartbeat=false
#if using.heartbeat=true then set the following properties, otherwise it is not necessary because the app is expected to be always up with a single instance that can health-checked by actuator and docker while swarm is managing the restarts on failure on other nodes
heartbeat.mode=ACTUATOR
#heartbeat.mode=DB or heartbeat.mode=ACTUATOR ##MAYBE IN THE FUTURE IF NEEDED: or heartbeat.mode=KAFKA or heartbeat.mode=REDIS
broken.instance.timeout.seconds=30
#broken.instance.timeout.seconds: PREFERIRE VALORE INFERIORE AL TEMPO DI AVVIO DELL'APP (MEGLIO MINORE DELLA META')
autoscheduled.job.locks.offset.seconds=21
#autoscheduled.job.locks.offset.seconds idealmente dovrebbe essere pari a 1 per un'istanza, 10 per un'altra e 21 per l'altra ancora (perche' a volte lo moltiplico per numero casuale tra 1 e 2)

kafka.bootstrapAddress=*************:19092,*************:29092,**************:39092
kafka.topics.common.replication.factor=3
kafka.topics.common.partitions=3
#BRINDISI: kafka.bootstrapAddress=*************:19092,*************:29092,**************:39092
#**************:19092,**************:29092,**************:39092
#*************:19092
#************:19092,************:29092,************:39092

#kafka.passport.data.topicName=nodes.passport.data
#kafka.passport.data.groupId=nodes_passport_data_vn_2

kafka.registration.topicName=nodes.registration
kafka.registration.groupId=nodes_registration_vn_2
#nodes_registration_gt_1
#gateway_registration-3

kafka.sdi.download.topicName=gateway.sdi.downloads
kafka.sdi.download.groupId=gateway_sdi_downloads_vn_2

kafka.certificates.topicName=gateway.certificates
kafka.certificates.groupId=gateway_certificates_vn_2
#gateway_certificates_gt_1
#gateway_certificates-3

kafka.node.abc.process.topicPattern=nodes.abc.process.*
kafka.node.abc.process.groupId=abc.process_gateway_dev
kafka.node.abc.response.topicPattern=nodes.abc.response.process.*
kafka.node.abc.response.groupId=abc.response_gateway_dev

max.soap.response.xml.megabytes.on.db=1

generated.npkd.certificates.classes=com.reco.ees.gateway.generated.npkd.certificates
generated.npkd.certificates.default.uri=https://sifext-egate.cen.poliziadistato.it/Sif-External-Service/sif/wsSIFServicesSoap
generated.npkd.certificates.soap.version=1.2
#test: https://sifext-egate-coll.cen.poliziadistato.it/Sif-External-Service/sif/wsSIFServicesSoap
#prod: https://sifext-egate.cen.poliziadistato.it/Sif-External-Service/sif/wsSIFServicesSoap
#http://localhost:8081/
#http://localhost:8090/mockwsSIFServiceProxySoap
#http://localhost:8090/mockwsSIFServiceProxySoap12

generated.sif.auth.services.classes=com.reco.ees.gateway.generated.sif.services
generated.sif.auth.services.default.uri=https://sifint-coll.cen.poliziadistato.it/Sif-WS-Service/sif/wsSIFServicesSoap
generated.sif.auth.services.soap.version=1.2
#http://localhost:8082/
#https://sifint-coll.cen.poliziadistato.it/Sif-WS-Service/sif/wsSIFServicesSoap
#http://localhost:8091/mockwsSIFServicesSoap12

generated.sif.wfe.classes=eu.europa.schengen.ees_ns.xsd.v1
generated.sif.wfe.default.uri=https://sifee-coll.cen.poliziadistato.it/pgd/sif-ees
generated.sif.wfe.soap.version=1.2
#https://sifee-coll.cen.poliziadistato.it/pgd/sif-ees
#http://localhost:8082/
#http://localhost:8083/
#http://localhost:8093/mockBorderControlAsyncServiceSOAP12Binding

generated.sif.survey.classes=eu.europa.schengen.ees_ns.xsd.v1
generated.sif.survey.default.uri=https://sifee-coll.cen.poliziadistato.it/pgd/sif-ees

generated.sif.async.classes=com.reco.ees.gateway.generated.sif.response
generated.sif.async.default.uri=https://sifee-coll.cen.poliziadistato.it/pgd/sif-async
generated.sif.async.soap.version=1.1
#https://sifee-coll.cen.poliziadistato.it/pgd/sif-async
#http://localhost:8082/
#http://localhost:8084/
#http://localhost:8094/mockSIFResponseServiceSOAP

generated.sdi.s000.uri=http://localhost:8095/mockSDIServiceSOAP12Binding
generated.sdi.s004.uri=http://localhost:8095/mockSDIServiceSOAP12Binding
generated.sdi.s016.uri=http://localhost:8095/mockSDIServiceSOAP12Binding
generated.sdi.bcs.uri=http://localhost:8095/mockSDIServiceSOAP12Binding
generated.sdi.luoghi.documenti.uri=http://localhost:8095/mockSDIServiceSOAP12Binding

generated.sif.bcs.classes=com.reco.ees.gateway.generated.sif.iae
generated.sif.bcs.soap.version=1.2

generated.sif.iae.classes=com.reco.ees.gateway.generated.sif.iae
generated.sif.iae.soap.version=1.2

generated.sif.support.soap.version=1.2
generated.sif.support.classes=com.reco.ees.gateway.generated.sif.iae

header.tags=HeaderAsyncRequestType,HeaderAsyncResponseType,RequestAckHeader

sif.truststore.path=securityCerts/truststore.jks
sif.keystore.path=securityCerts/keystore.jks
sif.mutual.authentication.certificate.password=SlS|5nN59k!D
sif.https.certificates.password=password

sif.auth.user.kiosk=eesbrindisi
#sif.auth.password=ZGI2ZTQ1ZGNiNGFhYmFkMjBlMjQ0ODMwYWRiMzRkMmI=
sif.auth.password.kiosk=MDY4YTU5NTAyYjE3YTQ2MWQwYjkwNGZiNTI4N2ZkZjY=
#sif.auth.password "ZGI2ZTQ1ZGNiNGFhYmFkMjBlMjQ0ODMwYWRiMzRkMmI=" e' la base64 dell'hash MD5 di "Init0000.2". Solo su DB e' salvata criptata ulteriormente (considerare di rimuoverla da qui e quindi anche dal ParameterService oppure scriverla anche qui criptata?)
#sif.auth.password "MDY4YTU5NTAyYjE3YTQ2MWQwYjkwNGZiNTI4N2ZkZjY=" e' la base64 dell'hash MD5 di "Init0000.4". Solo su DB e' salvata criptata ulteriormente (considerare di rimuoverla da qui e quindi anche dal ParameterService oppure scriverla anche qui criptata (pari a AAECAwQFBgcICQoLM7GuuZ54jgHdeulDC16ma58m9kapZ2T/01nWHmX190BkYKiVzopl50n0Qm6OsWoqBQDVGcCeul2Gvc91 se attribute.encryptor.key=0123456789abcdef0123456789abcdef))
sif.auth.user.egate=abcbrindisi
sif.auth.password.egate=ZGI2ZTQ1ZGNiNGFhYmFkMjBlMjQ0ODMwYWRiMzRkMmI=

sif.auth.refresh.frequency.hours=23
sif.auth.job.reschedule.parameter.check.frequency.milliseconds=21600000
sif.untraceable.frequency.seconds=5
sif.untraceable.max.retries=5

fake.kafka.registration.producer=false
fake.kafka.registration.quantity=1
fake.kafka.registration.traveller.random.details=true

fake.clients.returns=false
fake.clients.returns.random.response=false
fake.random.response.delay.milliseconds=200
fake.random.response.ldif.max.megabytes=3
print.soap.messages=true

simulate.random.shutdown.or.crash=false
simulate.random.shutdown.or.crash.delay.seconds=600

graceful.auto.restart.enabled=true
graceful.auto.restart.time=01:10:15

CSCA.LDIF.filename=MasterListaCSCA.ldif
DS_CRL.LDIF.filename=DSandCRL.ldif
CVCA.LDIF.filename=MasterListaCVCA.ldif
IS.LDIF.filename=ISCert.ldif
DV_RL.LDIF.filename=DVandRL.ldif
save.LDIF.files=false

kafka.sent.message.show=true
kafka.received.message.show=true

airport.code=BDS

#certificate.download.seconds=0
#certificate.download.minutes=1
certificate.download.start.hour=03:00
certificate.download.stop.hour=04:00
#certificate.download.day-of-month=*
#certificate.download.months=*
#certificate.download.day-of-week=*
certificate.download.time.retry.minute.interval=5
certificate.download.time.retries.limit=3
certificate.download.actor=PS-KIOSK-0900403001S
#BRINDISI: certificate.download.actor=PS-KIOSK-0900403001S (gateway sif id)
#PS-KIOSK-0900403002A
#PS-KIOSK-0900403001A
#certificate.job.completion.timeout.minutes=5
certificate.refresh.frequency.hours=24
certificates.job.reschedule.parameter.check.frequency.milliseconds=21600000

sdi.download.enabled=true
sdi.download.start.hour=04:00
sdi.download.stop.hour=05:00
sdi.download.time.retry.minute.interval=5
sdi.download.time.retries.limit=3
sdi.download.actor=PS-GATE-G0900403002A
sdi.download.refresh.frequency.hours=24
sdi.download.job.reschedule.parameter.check.frequency.milliseconds=21600000

cleaners.max.execution.time.hours=11
cleaners.batch.size=1000
#Set days.from.today.for.event.log.cleaning=-1 to avoid event log cleaning
days.from.today.for.event.log.cleaning=7
clean.log.event.seconds=0
clean.log.event.minutes=0
clean.log.event.hours=12
clean.log.event.day-of-month=*
clean.log.event.months=*
clean.log.event.day-of-week=*

#Set days.from.today.for.dossiers.cleaning=-1 to avoid dossiers cleaning
days.from.today.for.dossiers.cleaning=7
clean.dossiers.seconds=0
clean.dossiers.minutes=0
clean.dossiers.hours=12
clean.dossiers.day-of-month=*
clean.dossiers.months=*
clean.dossiers.day-of-week=*

dossier.transaction.check.frequency.seconds=5
dossier.transaction.check.max.retries=3
#dossier.transaction.timeout.seconds=600

all.done.and.sleep.time.ms=1001

to.be.processed.record.limit=8
#valori <= 0 per to.be.processed.record.limit evitano di limitare il numero di record da processare

id.chiamante.sif=ITAeur22b

application.name=gateway4ees
#ees_gateway-3
#certificates.concurrency.request=certificates

server.port = 8443

server.ssl.key-store=classpath:/securityCerts/autosigned.p12
server.ssl.key-store-password=password
server.ssl.key-store-type=PKCS12
server.ssl.key-alias=alias

secure.transport=true
accept.insecure.certificate=true

spring.output.ansi.enabled=ALWAYS

spring.banner.location=classpath:/customSpringBanner/banner.txt

predestroy.timeout.seconds=60

# abilita/disabilita decifratura degli attributi sensibili del registration message ricevuto dai kiosk
is.encryption.active=true
key.encryption=Reco326@@@LE20191239874560963456
iv.encryption=1239874560963456

# abilita/disabilita cifratura degli attributi sensibili della tabella dossier
is.attribute.encryptor.active=true
attribute.encryptor.key=0123456789abcdef0123456789abcdef
not.all.saved.records.encrypted=false

# abilita/disabilita eliminazione dei dati sensibili del dossier
is.delete.sensitive.data.active=true

# abilita/disabilita esecuzione della abort border control -> utile in fase di test
is.dossier.abort.active=false

email.airport.name=Aeroporto di Brindisi
email.sdi.download.subject=XXX - Report download SDI Luoghi e Documenti
email.certificates.subject=XXX - Report download certificati NPKD
email.sdi.download.success.content=SDI Luoghi e Documenti scaricati correttamente il XXX
email.certificates.success.content=Certificati scaricati correttamente il XXX
email.sdi.download.failed.content=Non e' stato possibile scaricare i SDI Luoghi e Documenti
email.certificates.failed.content=Non e' stato possibile scaricare i certificati
email.sif.password.check.subject=XXX - Controllo scadenza password utenza SIF
email.sif.password.not.expired.content=La password dell'utenza XXX e' in scadenza il YYY (tra ZZZ giorni)
email.sif.password.expired.content=La password dell'utenza XXX e' scaduta il YYY (ZZZ giorni fa)
email.days.to.password.expiration=7

#spring.task.scheduling.pool.size=10


# --- Hibernate-Caffeine L2 Caching: ---
spring.jpa.properties.hibernate.cache.use_second_level_cache=true
spring.jpa.properties.hibernate.cache.use_query_cache=true
spring.jpa.properties.hibernate.generate_statistics=false
# Configurazione statistiche per tutte le cache regions
spring.jpa.properties.hibernate.javax.cache.properties.caffeine.jcache.default.statisticsEnabled=false
spring.jpa.properties.hibernate.javax.cache.properties.caffeine.jcache.default-update-timestamps-region.statisticsEnabled=false
spring.jpa.properties.hibernate.javax.cache.properties.caffeine.jcache.default-query-results-region.statisticsEnabled=false
# Entita
spring.jpa.properties.hibernate.javax.cache.properties.caffeine.jcache.com.reco.ees.gateway.repository.model.SifParameter.statisticsEnabled=false
spring.jpa.properties.hibernate.javax.cache.properties.caffeine.jcache.com.reco.ees.gateway.repository.model.SifCountryCode.statisticsEnabled=false
spring.jpa.properties.hibernate.javax.cache.properties.caffeine.jcache.kioskCache.statisticsEnabled=false
spring.jpa.properties.hibernate.javax.cache.properties.caffeine.jcache.sifAuthCache.statisticsEnabled=false
spring.jpa.properties.hibernate.javax.cache.properties.caffeine.jcache.com.reco.ees.gateway.repository.model.SifAddDataRequest.statisticsEnabled=false
spring.jpa.properties.hibernate.javax.cache.properties.caffeine.jcache.com.reco.ees.gateway.repository.model.SifGenericCode.statisticsEnabled=false
spring.jpa.properties.hibernate.javax.cache.properties.caffeine.jcache.com.reco.ees.gateway.repository.model.Parameter.statisticsEnabled=false
# LuoghiSDI e DocumentiSDI non sono piu cachati L2
# Query
spring.jpa.properties.hibernate.javax.cache.properties.caffeine.jcache.sifParameterByType.statisticsEnabled=false
spring.jpa.properties.hibernate.javax.cache.properties.caffeine.jcache.sifCountryCodeByKioskValue.statisticsEnabled=false
spring.jpa.properties.hibernate.javax.cache.properties.caffeine.jcache.kioskQueries.statisticsEnabled=false
spring.jpa.properties.hibernate.javax.cache.properties.caffeine.jcache.sifAuthQueries.statisticsEnabled=false
spring.jpa.properties.hibernate.javax.cache.properties.caffeine.jcache.sifAddDataRequestQueries.statisticsEnabled=false
spring.jpa.properties.hibernate.javax.cache.properties.caffeine.jcache.sifGenericCodeQueries.statisticsEnabled=false
spring.jpa.properties.hibernate.javax.cache.properties.caffeine.jcache.sifParametersFindAllQueries.statisticsEnabled=false
spring.jpa.properties.hibernate.javax.cache.properties.caffeine.jcache.sifCountryCodeFindAllQueries.statisticsEnabled=false
spring.jpa.properties.hibernate.javax.cache.properties.caffeine.jcache.parameterFindAllQueries.statisticsEnabled=false

# Proprieta' di logging per HibernateCacheStatsLogger
logging.cache.stats.enabled=false
logging.cache.stats.fixedDelay=3600000
logging.cache.stats.initialDelay=60000
cache.reset.fixedDelay=21600000
cache.reset.initialDelay=21600000
logging.cache.contents.fixedDelay=14400000
# Logging per la cache di Hibernate
logging.level.org.hibernate.cache=WARN
logging.level.org.hibernate.cache.jcache=WARN
logging.level.org.hibernate.stat=WARN
logging.level.com.github.benmanes.caffeine=WARN
logging.level.com.reco.ees.gateway.config.HibernateCacheConfig=INFO

logging.level.org.hibernate.cache.spi.support.AbstractReadWriteAccess=ERROR
