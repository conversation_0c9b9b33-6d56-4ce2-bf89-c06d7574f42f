<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="OFF">

    <Properties>
        <Property name="LOG_LOC">${env:logFilename:-logs}</Property>
        <Property name="MAX">5</Property>
        <Property name="LOG_PATTERN">%highlight{%d{yyyy.MM.dd HH:mm:ss.SSS} [%p] %c: %m%n}{STYLE=DEFAULT}</Property>
        <Property name="APP_NAME">${spring:application.name:-defaultAppName}</Property>
    </Properties>

    <Appenders>
        <RollingFile name="FILE" fileName="${LOG_LOC}/${APP_NAME}.log"
                     filePattern="${LOG_LOC}/${APP_NAME}.%i.log">
            <PatternLayout>
                <Pattern>${LOG_PATTERN}</Pattern>
            </PatternLayout>

            <Policies>
                <OnStartupTriggeringPolicy />
                <SizeBasedTriggeringPolicy size="1GB" />
            </Policies>

            <DefaultRolloverStrategy max="${MAX}" />
        </RollingFile>

        <Console name="STDOUT" target="SYSTEM_OUT" follow="true">
            <!--<PatternLayout pattern="${LOG_PATTERN}" />-->
            <PatternLayout pattern="${LOG_PATTERN}" disableAnsi="false" />
            <!--<PatternLayout pattern="%highlight{%d{ISO8601} [%t] %-5level: %msg%n%throwable}" disableAnsi="false" />-->
        </Console>
    </Appenders>

    <Loggers>
        <Logger name="com.reco" level="debug" />

        <Logger name="org.springframework.web" level="info"
                additivity="false" />

        <!--<Logger name="org.hibernate.SQL" level="debug" additivity="false">
            <AppenderRef ref="STDOUT" />
            <AppenderRef ref="FILE" />
        </Logger>
        <Logger name="org.hibernate.type.descriptor.sql.BasicBinder" level="trace" additivity="false">
            <AppenderRef ref="STDOUT" />
            <AppenderRef ref="FILE" />
        </Logger>-->

        <Logger name="file" level="debug" additivity="false">
            <AppenderRef ref="FILE" />
        </Logger>

        <Root level="warn">
            <AppenderRef ref="FILE" />
            <AppenderRef ref="STDOUT" />
        </Root>
    </Loggers>

</Configuration>