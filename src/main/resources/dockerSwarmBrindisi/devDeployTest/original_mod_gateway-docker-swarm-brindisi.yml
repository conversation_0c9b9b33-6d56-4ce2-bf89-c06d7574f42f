#OK PER USO GATEWAY CON HEARTBEAT IN MODALITA' ACTUATOR (dopo assegnazione label ai vari nodi docker con:
  #docker node update --label-add app=gateway_1 node1
  #docker node update --label-add app=gateway_2 node2
  #docker node update --label-add app=gateway_3 node3)
version: "3.9"

services:
  openssh-server_1:
    image: linuxserver/openssh-server
    logging:
      driver: "json-file"
      options:
        max-file: "1"
        max-size: "1g"
    environment:
      - PUID=1000
      - PGID=1000
      - TZ=Europe/Rome
      - PASSWORD_ACCESS=true
      - USER_PASSWORD=S@c802022!#
      - USER_NAME=eesadmin
    volumes:
      - firmware:/app
      - ssh-config:/config
    ports:
      - target: 2222
        published: 2222
        protocol: tcp
        mode: host
    deploy:
      replicas: 1
      placement:
        max_replicas_per_node: 1
        constraints:
          - node.labels.type == gateway_1

  gateway_1:
    image: openjdk:21
    logging:
      driver: "json-file"
      options:
        max-file: "1"
        max-size: "1g"
    environment:
      - TZ=Europe/Rome
      - SPRING_CONFIG_LOCATION=/app/gateway/application.properties
    working_dir: /app/gateway
    command:
      - java
      - -jar
      - gateway-1.0.1.jar
    stop_grace_period: 60s
    ports:
      - target: 8443
        published: 8441
        protocol: tcp
        mode: host
    volumes:
      - firmware:/app
    deploy:
      replicas: 1
      placement:
        max_replicas_per_node: 1
        constraints:
          - node.labels.type == gateway_1


  openssh-server_2:
    image: linuxserver/openssh-server
    logging:
      driver: "json-file"
      options:
        max-file: "1"
        max-size: "1g"
    environment:
      - PUID=1000
      - PGID=1000
      - TZ=Europe/Rome
      - PASSWORD_ACCESS=true
      - USER_PASSWORD=S@c802022!#
      - USER_NAME=eesadmin
    volumes:
      - firmware:/app
      - ssh-config:/config
    ports:
      - target: 2222
        published: 2222
        protocol: tcp
        mode: host
    deploy:
      replicas: 1
      placement:
        max_replicas_per_node: 1
        constraints:
          - node.labels.type == gateway_2

  gateway_2:
    image: openjdk:21
    logging:
      driver: "json-file"
      options:
        max-file: "1"
        max-size: "1g"
    environment:
      - TZ=Europe/Rome
      - SPRING_CONFIG_LOCATION=/app/gateway/application.properties
    working_dir: /app/gateway
    command:
      - java
      - -jar
      - gateway-1.0.1.jar
    stop_grace_period: 60s
    ports:
      - target: 8443
        published: 8442
        protocol: tcp
        mode: host
    volumes:
      - firmware:/app
    deploy:
      replicas: 1
      placement:
        max_replicas_per_node: 1
        constraints:
          - node.labels.type == gateway_2

  openssh-server_3:
    image: linuxserver/openssh-server
    logging:
      driver: "json-file"
      options:
        max-file: "1"
        max-size: "1g"
    environment:
      - PUID=1000
      - PGID=1000
      - TZ=Europe/Rome
      - PASSWORD_ACCESS=true
      - USER_PASSWORD=S@c802022!#
      - USER_NAME=eesadmin
    volumes:
      - firmware:/app
      - ssh-config:/config
    ports:
      - target: 2222
        published: 2222
        protocol: tcp
        mode: host
    deploy:
      replicas: 1
      placement:
        max_replicas_per_node: 1
        constraints:
          - node.labels.type == gateway_3

  gateway_3:
    image: openjdk:21
    logging:
      driver: "json-file"
      options:
        max-file: "1"
        max-size: "1g"
    environment:
      - TZ=Europe/Rome
      - SPRING_CONFIG_LOCATION=/app/gateway/application.properties
    working_dir: /app/gateway
    command:
      - java
      - -jar
      - gateway-1.0.1.jar
    stop_grace_period: 60s
    ports:
      - target: 8443
        published: 8444
        protocol: tcp
        mode: host
    volumes:
      - firmware:/app
    deploy:
      replicas: 1
      placement:
        max_replicas_per_node: 1
        constraints:
          - node.labels.type == gateway_3

volumes:
  ssh-config:
  firmware:
