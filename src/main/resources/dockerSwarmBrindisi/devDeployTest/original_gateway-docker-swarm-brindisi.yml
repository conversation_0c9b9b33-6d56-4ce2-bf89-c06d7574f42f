#OK PER USO GATEWAY CON HEARTBEAT IN MODALITA' DB
version: "3.9"

#secrets:
#  ees-webapp-certificate:
#    name: eeswebapp.p12
#    external: true

services:
  openssh-server:
    image: linuxserver/openssh-server
    logging:
      driver: "json-file"
      options:
        max-file: "1"
        max-size: "1g"
    environment:
      - PUID=1000
      - PGID=1000
      - TZ=Europe/Rome
      - PASSWORD_ACCESS=true
      - USER_PASSWORD=S@c802022!#
      - USER_NAME=eesadmin
    volumes:
      - firmware:/app
      - ssh-config:/config
    ports:
      - target: 2222
        published: 2222
        protocol: tcp
        mode: host
    deploy:
      replicas: 3
      placement:
        max_replicas_per_node: 1
        constraints:
          - node.labels.type == gateway


  gateway:
    image: openjdk:21
    logging:
      driver: "json-file"
      options:
        max-file: "1"
        max-size: "1g"
    environment:
      - TZ=Europe/Rome
      - SPRING_CONFIG_LOCATION=/app/gateway/application.properties
    working_dir: /app/gateway
    command:
      - java
      - -jar
      - gateway-1.0.1.jar
      #- sh
      #- -c
      #- |
      #  keytool -import -trustcacerts -keystore $JAVA_HOME/lib/security/cacerts -storepass changeit -noprompt -alias CARootCen -file /certs/CARootCen.cer &&
      #  keytool -import -trustcacerts -keystore $JAVA_HOME/lib/security/cacerts -storepass changeit -noprompt -alias IntermediateCACen -file /certs/IntermediateCACen.cer &&
      #  java -jar gateway-1.0.0.jar
    stop_grace_period: 60s
    ports:
      - target: 8443
        published: 8443
        protocol: tcp
        mode: host
    volumes:
      - firmware:/app
      #- /path/to/certs:/certs
    #secrets:
    # - source: CARootCen.cer
    #   target: /run/secrets/CARootCen.cer
    # - source: IntermediateCACen.cer
    #   target: /run/secrets/IntermediateCACen.cer
    #  - source: ees-webapp-certificate
    #    target: /app/dispatcher/ees-webapp-certificate.p12
    #    uid: '1000'
    #    gid: '1000'
    #    mode: 0440
    #healthcheck: #magari usa spring actuator (health)
    #  test: curl --fail -k https://localhost:8443/ees/web/login || exit 1
    #  interval: 60s
    #  retries: 5
    #  start_period: 30s
    #  timeout: 10s
    deploy:
      replicas: 3
      placement:
        max_replicas_per_node: 1
        constraints:
          - node.labels.type == gateway

volumes:
  ssh-config:
  firmware:


#TODO: prova a usare una di queste base img
# ALTERNATIVE BASE IMAGEs for CERTIFICATES (ovviamente evita command in gateway-docker-swarm-brindisi.yml riguardante utilizzo keytool):
##1
  #FROM amazoncorretto:21-alpine3.20

  #ARG KEYSTORE_PASSWORD=changeit

  #RUN apk update && apk add --no-cache ca-certificates

  #COPY CARootCen.cer /usr/local/share/ca-certificates/CARootCen.cer
  #COPY IntermedieCACen.cer /usr/local/share/ca-certificates/IntermedieCACen.cer

  #RUN update-ca-certificates

  #RUN keytool -import -trustcacerts -cacerts -storepass ${KEYSTORE_PASSWORD} -noprompt -alias CARootCen -file /usr/local/share/ca-certificates/CARootCen.cer
  #RUN keytool -import -trustcacerts -keystore $JAVA_HOME/lib/security/cacerts -storepass ${KEYSTORE_PASSWORD} -noprompt -alias IntermedieCACen -file /usr/local/share/ca-certificates/IntermedieCACen.cer

  #WORKDIR /app/gateway

  ##COPY gateway-1.0.0.jar /app/gateway/gateway-1.0.0.jar
  ##CMD ["java", "-jar", "gateway-1.0.0.jar"]


##2
  #FROM openjdk:21-jdk-slim

  #ARG KEYSTORE_PASSWORD=changeit

  #RUN apt-get update && apt-get install -y ca-certificates

  #COPY CARootCen.cer /usr/local/share/ca-certificates/CARootCen.cer
  #COPY IntermedieCACen.cer /usr/local/share/ca-certificates/IntermedieCACen.cer

  #RUN update-ca-certificates

  #RUN keytool -import -trustcacerts -cacerts -storepass ${KEYSTORE_PASSWORD} -noprompt -alias CARootCen -file /usr/local/share/ca-certificates/CARootCen.cer
  #RUN keytool -import -trustcacerts -keystore $JAVA_HOME/lib/security/cacerts -storepass ${KEYSTORE_PASSWORD} -noprompt -alias IntermedieCACen -file /usr/local/share/ca-certificates/IntermedieCACen.cer

  #WORKDIR /app/gateway

  ##COPY gateway-1.0.0.jar /app/gateway/gateway-1.0.0.jar
  ##CMD ["java", "-jar", "gateway-1.0.0.jar"]