version: "3.9"
services:
  grafana:
    image: grafana/grafana:11.4.0
    logging:
      driver: "json-file"
      options:
        max-file: "1"
        max-size: "1g"
    networks:
      - postgresql-network
    ports:
      - target: 3000
        published: 3000
        protocol: tcp
        mode: host
    volumes:
      - grafana_data:/var/lib/grafana
    deploy:
      replicas: 1
      placement:
        constraints: [node.hostname == vm-prod-postgresql-1]

  prometheus:
    image: prom/prometheus:v3.0.1
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--storage.tsdb.retention.time=30d'
    logging:
      driver: "json-file"
      options:
        max-file: "1"
        max-size: "1g"
    networks:
      - postgresql-network
    ports:
      - target: 9090
        published: 9090
        protocol: tcp
        mode: host
    configs:
      - source: prometheus.yml
        target: /etc/prometheus/prometheus.yml
        uid: '1000'
        gid: '1000'
        mode: 0664
    volumes:
      - prometheus_data:/prometheus
    #environment:
    #  - STORAGE_TSDB_RETENTION_TIME=30d
    deploy:
      replicas: 1
      placement:
        constraints: [node.hostname == vm-prod-postgresql-1]

  postgres-exporter-1:
    image: prometheuscommunity/postgres-exporter:v0.16.0
    logging:
      driver: "json-file"
      options:
        max-file: "1"
        max-size: "1g"
    networks:
      - postgresql-network
    #ports:
    #  - target: 9187
    #    published: 9187
    #    protocol: tcp
    #    mode: host
    environment:
      DATA_SOURCE_URI: "pg-1:5432/abc?sslmode=disable"
      DATA_SOURCE_USER: "postgres"
      DATA_SOURCE_PASS_FILE: "/run/secrets/postgres_password"
    secrets:
      - postgres_password
    links:
      - prometheus
    deploy:
      replicas: 1
      placement:
        constraints: [node.hostname == vm-prod-postgresql-1]

  postgres-exporter-2:
    image: prometheuscommunity/postgres-exporter:v0.16.0
    logging:
      driver: "json-file"
      options:
        max-file: "1"
        max-size: "1g"
    networks:
      - postgresql-network
    #ports:
    #  - target: 9187
    #    published: 9187
    #    protocol: tcp
    #    mode: host
    environment:
      DATA_SOURCE_URI: "pg-2:5432/abc?sslmode=disable"
      DATA_SOURCE_USER: "postgres"
      DATA_SOURCE_PASS_FILE: "/run/secrets/postgres_password"
    secrets:
      - postgres_password
    links:
      - prometheus
    deploy:
      replicas: 1
      placement:
        constraints: [node.hostname == vm-prod-postgresql-1]

  postgres-exporter-3:
    image: prometheuscommunity/postgres-exporter:v0.16.0
    logging:
      driver: "json-file"
      options:
        max-file: "1"
        max-size: "1g"
    networks:
      - postgresql-network
    #ports:
    #  - target: 9187
    #    published: 9187
    #    protocol: tcp
    #    mode: host
    environment:
      DATA_SOURCE_URI: "pg-3:5432/abc?sslmode=disable"
      DATA_SOURCE_USER: "postgres"
      DATA_SOURCE_PASS_FILE: "/run/secrets/postgres_password"
    secrets:
      - postgres_password
    links:
      - prometheus
    deploy:
      replicas: 1
      placement:
        constraints: [node.hostname == vm-prod-postgresql-1]

  pgpool2_exporter-1:
    image: pgpool/pgpool2_exporter:1.2.2
    #ports:
    #  - 9719:9719
    logging:
      driver: "json-file"
      options:
        max-file: "1"
        max-size: "1g"
    environment:
      - POSTGRES_USERNAME=postgres
      #- POSTGRES_PASSWORD_FILE=/run/secrets/postgres_password
      - POSTGRES_PASSWORD=RJSTpP&+
      - POSTGRES_DATABASE=abc
      - PGPOOL_SERVICE=pgpool
      - PGPOOL_SERVICE_PORT=5432
    #secrets:
    #  - postgres_password
    networks:
      - postgresql-network
    deploy:
      replicas: 1
      placement:
        constraints: [node.hostname == vm-prod-postgresql-1]

networks:
  postgresql-network:
    external: true

secrets:
  postgres_password:
    external: true

configs:
  prometheus.yml:
    external: true

volumes:
  grafana_data:
  prometheus_data: