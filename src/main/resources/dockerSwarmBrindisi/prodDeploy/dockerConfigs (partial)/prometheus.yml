global:
  scrape_interval: 15s
  evaluation_interval: 15s
 
scrape_configs:
  - job_name: prometheus
    static_configs:
      - targets: ["localhost:9090"]
  - job_name: postgres-exporter
    static_configs:
      - targets: ["postgres-exporter-1:9187", "postgres-exporter-2:9187", "postgres-exporter-3:9187"]
  - job_name: pgpool-exporter
    static_configs:
      - targets: ["pgpool2_exporter-1:9719"]
  - job_name: gateway-actuator
    metrics_path: '/ees_gateway/actuator/prometheus'
    scheme: https
    static_configs:
      - targets: ["gateway:8443"]
    basic_auth:
      username: 'admin'
      password: 'admin'
    tls_config:
      insecure_skip_verify: true