<?xml version="1.0" encoding="UTF-8"?>
<con:soapui-project id="e29b056c-73f3-403b-b1eb-67321068e302" activeEnvironment="Default" name="wsSIFServices" resourceRoot="" soapui-version="5.7.2" abortOnError="false" runType="SEQUENTIAL" xmlns:con="http://eviware.com/soapui/config"><con:settings/><con:interface xsi:type="con:WsdlInterface" id="e829cded-ea7f-4f26-ad87-d48e2fa1eefc" wsaVersion="NONE" name="wsSIFServicesSoap" type="wsdl" bindingName="{http://tempuri.org/}wsSIFServicesSoap" soapVersion="1_1" anonymous="optional" definition="file:/C:/Users/<USER>/Desktop/GIT%20REPO/gateway_ees/src/main/resources/soapFiles/SIFServices/wsSIFServices.wsdl" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:settings/><con:definitionCache/><con:endpoints><con:endpoint>https://sifint-coll.cen.poliziadistato.it/Sif-WS-Service/sif/wsSIFServicesSoap</con:endpoint></con:endpoints><con:operation id="53b20f0d-7e95-4ccd-aadb-c27ebf9d8044" isOneWay="false" action="http://tempuri.org/AddAlert" name="AddAlert" bindingOperationName="AddAlert" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="6723f96d-4022-4077-9869-08db5798de5a" isOneWay="false" action="http://tempuri.org/AddAlertBySpec" name="AddAlertBySpec" bindingOperationName="AddAlertBySpec" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="49ee10f5-5d93-4a1a-a069-ba12adf1cc97" isOneWay="false" action="http://tempuri.org/AddApplicationRole" name="AddApplicationRole" bindingOperationName="AddApplicationRole" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="e234d96e-638b-41fd-964f-cb6affd375ae" isOneWay="false" action="http://tempuri.org/AddDomain" name="AddDomain" bindingOperationName="AddDomain" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="02b7f73d-e877-4b73-a938-6f4ce38ab121" isOneWay="false" action="http://tempuri.org/AddMachine" name="AddMachine" bindingOperationName="AddMachine" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="86ea7b59-c25b-44d9-b82e-27dad654e1f2" isOneWay="false" action="http://tempuri.org/AddOffice" name="AddOffice" bindingOperationName="AddOffice" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="5717fc65-7702-41ca-bb5b-218ef391a850" isOneWay="false" action="http://tempuri.org/AddOfficeAdministrator" name="AddOfficeAdministrator" bindingOperationName="AddOfficeAdministrator" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="2cedea44-e043-4d7a-a881-2f12ec2ace3a" isOneWay="false" action="http://tempuri.org/AddUser" name="AddUser" bindingOperationName="AddUser" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="59fc92d4-0e52-41bb-a8cc-81d6be071387" isOneWay="false" action="http://tempuri.org/AddUserSecurityContext" name="AddUserSecurityContext" bindingOperationName="AddUserSecurityContext" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="06f26f58-a876-4a00-b81f-c8e185b66bb9" isOneWay="false" action="http://tempuri.org/CheckDbReachability" name="CheckDbReachability" bindingOperationName="CheckDbReachability" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="675be2d9-5a95-4d27-b2d3-8f10f46a3397" isOneWay="false" action="http://tempuri.org/CheckDigitalCertificate" name="CheckDigitalCertificate" bindingOperationName="CheckDigitalCertificate" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="72c6ca31-acda-44ff-b7b1-7221d4e1b747" isOneWay="false" action="http://tempuri.org/CheckExternalServices" name="CheckExternalServices" bindingOperationName="CheckExternalServices" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="2e5a4117-3314-457d-8636-82aedd58f978" isOneWay="false" action="http://tempuri.org/DeleteOffice" name="DeleteOffice" bindingOperationName="DeleteOffice" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="74f1a3d1-386a-4901-9424-19d8c56b5405" isOneWay="false" action="http://tempuri.org/DeleteUserSecurityContext" name="DeleteUserSecurityContext" bindingOperationName="DeleteUserSecurityContext" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="479f79b1-db6d-42a6-849d-12d33f42d501" isOneWay="false" action="http://tempuri.org/DelMachine" name="DelMachine" bindingOperationName="DelMachine" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="62f0d803-68f5-4d5e-8587-b50d4d7b4923" isOneWay="false" action="http://tempuri.org/GetAccountCurrentStatus" name="GetAccountCurrentStatus" bindingOperationName="GetAccountCurrentStatus" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="a0a88953-304e-4653-9a48-1db5d784d0ff" isOneWay="false" action="http://tempuri.org/GetAccountInfo" name="GetAccountInfo" bindingOperationName="GetAccountInfo" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="96a89cba-4d67-459b-be97-9a17d2fe9d93" isOneWay="false" action="http://tempuri.org/GetAccountRoles" name="GetAccountRoles" bindingOperationName="GetAccountRoles" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="bc1085df-38c5-4d19-a101-821a4a2d2835" isOneWay="false" action="http://tempuri.org/GetAccountRolesActivationList" name="GetAccountRolesActivationList" bindingOperationName="GetAccountRolesActivationList" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="e4216537-9647-4714-ad87-37411826be32" isOneWay="false" action="http://tempuri.org/GetAccountSecurityContextId" name="GetAccountSecurityContextId" bindingOperationName="GetAccountSecurityContextId" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="4db479c1-6725-4887-bb17-4cc691b9bbe7" isOneWay="false" action="http://tempuri.org/GetAccountServices" name="GetAccountServices" bindingOperationName="GetAccountServices" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="a0f3a17e-a04f-4d0e-90fc-e583666138bd" isOneWay="false" action="http://tempuri.org/GetActiveAccounts" name="GetActiveAccounts" bindingOperationName="GetActiveAccounts" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="94728fbc-0116-47fa-9ca5-243f6e5813ea" isOneWay="false" action="http://tempuri.org/GetAdministeredChildOffices" name="GetAdministeredChildOffices" bindingOperationName="GetAdministeredChildOffices" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="81dd6ae2-d057-4937-bc6d-00552d504185" isOneWay="false" action="http://tempuri.org/GetAdministeredMachineByOffice" name="GetAdministeredMachineByOffice" bindingOperationName="GetAdministeredMachineByOffice" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="41cfbfe8-dc8a-44c6-b49f-dcf1b2189fd5" isOneWay="false" action="http://tempuri.org/GetAdministeredMachines" name="GetAdministeredMachines" bindingOperationName="GetAdministeredMachines" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="cfb6542c-a58d-418e-b5f4-a9e7cb260b0c" isOneWay="false" action="http://tempuri.org/GetAdministeredOffices" name="GetAdministeredOffices" bindingOperationName="GetAdministeredOffices" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="d5d51c9b-0ccd-4dc1-9aa7-bb193bd240e9" isOneWay="false" action="http://tempuri.org/GetAdministeredOfficesByType" name="GetAdministeredOfficesByType" bindingOperationName="GetAdministeredOfficesByType" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="8c419f34-ce13-49bd-8595-0bf143e455d4" isOneWay="false" action="http://tempuri.org/GetAdministeredUserByOffice" name="GetAdministeredUserByOffice" bindingOperationName="GetAdministeredUserByOffice" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="ca29acb4-78bf-4f2a-8266-f3d44dc09b1f" isOneWay="false" action="http://tempuri.org/GetAllAccountSecurityContext" name="GetAllAccountSecurityContext" bindingOperationName="GetAllAccountSecurityContext" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="65eddfca-e85f-422f-99e9-d15b914e1b42" isOneWay="false" action="http://tempuri.org/GetAllActiveOffices" name="GetAllActiveOffices" bindingOperationName="GetAllActiveOffices" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="f7379451-8379-42e8-9279-6fdb94ef7b56" isOneWay="false" action="http://tempuri.org/GetAllApplicationRoles" name="GetAllApplicationRoles" bindingOperationName="GetAllApplicationRoles" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="89c4008d-379e-41e0-a7c5-31a07482b5b8" isOneWay="false" action="http://tempuri.org/GetAllApplications" name="GetAllApplications" bindingOperationName="GetAllApplications" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="2730c2d7-33b2-4e9e-8c62-c1eb00af9971" isOneWay="false" action="http://tempuri.org/GetAllApplicationServices" name="GetAllApplicationServices" bindingOperationName="GetAllApplicationServices" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="f969de9f-d460-44e4-8db8-b998b9e4dd8b" isOneWay="false" action="http://tempuri.org/GetAllDocumentAlertsCertified" name="GetAllDocumentAlertsCertified" bindingOperationName="GetAllDocumentAlertsCertified" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="9f6e13ff-1fd9-49e0-a9d1-9c6b4b14b4a2" isOneWay="false" action="http://tempuri.org/GetAllDomains" name="GetAllDomains" bindingOperationName="GetAllDomains" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="43d53261-66bf-47e9-9dfb-47ecdd734504" isOneWay="false" action="http://tempuri.org/GetAllUncertifiedAlert" name="GetAllUncertifiedAlert" bindingOperationName="GetAllUncertifiedAlert" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="a84505c8-bac9-45c9-ab8d-a46fe4dcb963" isOneWay="false" action="http://tempuri.org/GetAllUsers" name="GetAllUsers" bindingOperationName="GetAllUsers" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="c87f9e96-f98f-497c-a9f8-4f7eea4a2d7e" isOneWay="false" action="http://tempuri.org/GetAppInfo" name="GetAppInfo" bindingOperationName="GetAppInfo" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="fb679caa-1975-426e-8824-88638dcea735" isOneWay="false" action="http://tempuri.org/GetApplicationAccounts" name="GetApplicationAccounts" bindingOperationName="GetApplicationAccounts" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="f8756bcf-9163-431d-abed-9ff4e889e866" isOneWay="false" action="http://tempuri.org/GetCertifiedAlertImages" name="GetCertifiedAlertImages" bindingOperationName="GetCertifiedAlertImages" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="d799b51f-a710-4698-85a7-1e5c33443fec" isOneWay="false" action="http://tempuri.org/GetDetailsByDatiSidafId" name="GetDetailsByDatiSidafId" bindingOperationName="GetDetailsByDatiSidafId" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="ff325d89-e78b-4220-8a65-7a1ec74d59f7" isOneWay="false" action="http://tempuri.org/GetDockType" name="GetDockType" bindingOperationName="GetDockType" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="fe7a952d-320e-42a5-953a-27a459ebea13" isOneWay="false" action="http://tempuri.org/GetDocumentAlerts" name="GetDocumentAlerts" bindingOperationName="GetDocumentAlerts" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="1082e555-d0b7-4a86-ab82-163c82f69b1d" isOneWay="false" action="http://tempuri.org/GetDocumentAlertsCertified" name="GetDocumentAlertsCertified" bindingOperationName="GetDocumentAlertsCertified" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="8c5fb7ba-fd2f-435d-9c98-b26cd19d4b2c" isOneWay="false" action="http://tempuri.org/GetDocumentAlertsDetailInfo" name="GetDocumentAlertsDetailInfo" bindingOperationName="GetDocumentAlertsDetailInfo" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="db35f5fe-1d0c-4467-b4d3-744007f7b35f" isOneWay="false" action="http://tempuri.org/GetDomainInfo" name="GetDomainInfo" bindingOperationName="GetDomainInfo" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="92dd233c-03de-436b-b68c-eecb103caffc" isOneWay="false" action="http://tempuri.org/GetKnowledgeAttachmentFile" name="GetKnowledgeAttachmentFile" bindingOperationName="GetKnowledgeAttachmentFile" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="38cd1da3-1ff3-468a-9b1e-a150e3fee05f" isOneWay="false" action="http://tempuri.org/GetLoginAuthentication" name="GetLoginAuthentication" bindingOperationName="GetLoginAuthentication" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="c3cc9985-1d7c-4194-b266-556ae1770808" isOneWay="false" action="http://tempuri.org/GetLoginAuthenticationApp" name="GetLoginAuthenticationApp" bindingOperationName="GetLoginAuthenticationApp" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="d3c82ceb-a15d-48fd-b2b4-b2d37f2dec32" isOneWay="false" action="http://tempuri.org/GetLoginAuthenticationClient" name="GetLoginAuthenticationClient" bindingOperationName="GetLoginAuthenticationClient" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="34b7e2a0-2632-4e6c-ac83-643fb2a8a107" isOneWay="false" action="http://tempuri.org/GetMachineInfo" name="GetMachineInfo" bindingOperationName="GetMachineInfo" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="05db1177-ede1-4454-af7e-9178b41b3b5f" isOneWay="false" action="http://tempuri.org/GetMachineInfoApp" name="GetMachineInfoApp" bindingOperationName="GetMachineInfoApp" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="ba460d36-4dba-44bf-bd4e-9f43df3b571c" isOneWay="false" action="http://tempuri.org/GetNotifyKnowledge" name="GetNotifyKnowledge" bindingOperationName="GetNotifyKnowledge" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="96b48fe7-3f10-4874-8e99-73687b9a4929" isOneWay="false" action="http://tempuri.org/GetNotifyKnowledgeAttachment" name="GetNotifyKnowledgeAttachment" bindingOperationName="GetNotifyKnowledgeAttachment" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="c20e2cc0-5c1b-4ba1-87e5-342ccaf19802" isOneWay="false" action="http://tempuri.org/GetOfficeInfo" name="GetOfficeInfo" bindingOperationName="GetOfficeInfo" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="5ae02d91-ae77-48fd-9c23-3bd068b543f7" isOneWay="false" action="http://tempuri.org/GetOfficeMessages" name="GetOfficeMessages" bindingOperationName="GetOfficeMessages" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="13635cdb-e0a5-4ab2-b277-97d05c708c73" isOneWay="false" action="http://tempuri.org/GetRoleServices" name="GetRoleServices" bindingOperationName="GetRoleServices" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="183c68cb-7f5c-49e1-b49e-c6a5c40ade91" isOneWay="false" action="http://tempuri.org/GetRoleServicesAuthorizationList" name="GetRoleServicesAuthorizationList" bindingOperationName="GetRoleServicesAuthorizationList" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="ccab6d4c-d8a9-413e-a5ba-82a856271512" isOneWay="false" action="http://tempuri.org/GetServiceRolesAuthorizationList" name="GetServiceRolesAuthorizationList" bindingOperationName="GetServiceRolesAuthorizationList" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="08dcd5a9-b3d7-47f7-8139-72dc1f05778d" isOneWay="false" action="http://tempuri.org/GetSupervisedMachines" name="GetSupervisedMachines" bindingOperationName="GetSupervisedMachines" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="8baae56e-7a56-463f-8f35-fd2826eececa" isOneWay="false" action="http://tempuri.org/GetSupervisedOffices" name="GetSupervisedOffices" bindingOperationName="GetSupervisedOffices" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="63b4d098-9945-4f61-aad5-bb2f6f1dfb1d" isOneWay="false" action="http://tempuri.org/GetSystemInfo" name="GetSystemInfo" bindingOperationName="GetSystemInfo" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="83430e5b-09d4-443c-93fd-fcff2798dafc" isOneWay="false" action="http://tempuri.org/IsSysAdminInContext" name="IsSysAdminInContext" bindingOperationName="IsSysAdminInContext" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="15b7f78d-7a73-4153-bb67-7315a3f2c2a4" isOneWay="false" action="http://tempuri.org/LoadDocumentLayoutDatabase" name="LoadDocumentLayoutDatabase" bindingOperationName="LoadDocumentLayoutDatabase" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="daf9d822-5dc6-494a-807b-28d6850a6858" isOneWay="false" action="http://tempuri.org/LogoutClient" name="LogoutClient" bindingOperationName="LogoutClient" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="19e6383f-597a-4168-a02e-7bd529619f84" isOneWay="false" action="http://tempuri.org/ResetUserPassword" name="ResetUserPassword" bindingOperationName="ResetUserPassword" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="4316463b-9669-40a1-9b56-222b435f61af" isOneWay="false" action="http://tempuri.org/SearchUserInfo" name="SearchUserInfo" bindingOperationName="SearchUserInfo" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="f7432898-2727-4f01-a762-d7c8beccaedf" isOneWay="false" action="http://tempuri.org/SendAttachmentFile" name="SendAttachmentFile" bindingOperationName="SendAttachmentFile" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="f8bd56b4-9484-45cb-a55e-502775cb7742" isOneWay="false" action="http://tempuri.org/SendBroadcastMessage" name="SendBroadcastMessage" bindingOperationName="SendBroadcastMessage" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="cd9c723b-2d89-4104-b5e1-53c2ed24159a" isOneWay="false" action="http://tempuri.org/TestIvis2" name="TestIvis2" bindingOperationName="TestIvis2" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="cfdb30c8-7c7e-483e-ac76-dda80c802054" isOneWay="false" action="http://tempuri.org/UpdAlertStatus" name="UpdAlertStatus" bindingOperationName="UpdAlertStatus" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="aa1a3fed-eefe-4999-977b-24a582863b58" isOneWay="false" action="http://tempuri.org/UpdateAccountRoles" name="UpdateAccountRoles" bindingOperationName="UpdateAccountRoles" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="c7434f58-f01b-4af1-b251-d75cca26f43a" isOneWay="false" action="http://tempuri.org/UpdateAccountStatus" name="UpdateAccountStatus" bindingOperationName="UpdateAccountStatus" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="75e877be-c78d-445e-9bab-48727df3bc84" isOneWay="false" action="http://tempuri.org/UpdateApplicationRole" name="UpdateApplicationRole" bindingOperationName="UpdateApplicationRole" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="ab438c7f-d95f-4766-968e-4e36e7c57c54" isOneWay="false" action="http://tempuri.org/UpdateClientMachine" name="UpdateClientMachine" bindingOperationName="UpdateClientMachine" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="3ed69020-b7ff-4828-ad56-3109f40e6740" isOneWay="false" action="http://tempuri.org/UpdateOfficeData" name="UpdateOfficeData" bindingOperationName="UpdateOfficeData" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="79a5eb81-1876-48d5-a650-6ae3089edd67" isOneWay="false" action="http://tempuri.org/UpdateRoleServices" name="UpdateRoleServices" bindingOperationName="UpdateRoleServices" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="d913d071-37b7-4128-a733-d46582cbfc14" isOneWay="false" action="http://tempuri.org/UpdateUserAccount" name="UpdateUserAccount" bindingOperationName="UpdateUserAccount" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="e0c8e45c-e9ec-4441-85a0-fad0913ae888" isOneWay="false" action="http://tempuri.org/UpdateUserPassword" name="UpdateUserPassword" bindingOperationName="UpdateUserPassword" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="988fa4cf-ec3e-4831-98c1-f88cbbf9fe40" isOneWay="false" action="http://tempuri.org/ValidatePassword" name="ValidatePassword" bindingOperationName="ValidatePassword" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="f2e8240a-65d7-4bd4-a978-2d3288cb4c75" isOneWay="false" action="http://tempuri.org/ViewLog" name="ViewLog" bindingOperationName="ViewLog" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation></con:interface><con:interface xsi:type="con:WsdlInterface" id="6e1f234b-d5b8-4b17-a588-17cc31899a02" wsaVersion="NONE" name="wsSIFServicesSoap12" type="wsdl" bindingName="{http://tempuri.org/}wsSIFServicesSoap12" soapVersion="1_2" anonymous="optional" definition="file:/C:/Users/<USER>/Desktop/GIT%20REPO/gateway_ees/src/main/resources/soapFiles/SIFServices/wsSIFServices.wsdl" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:settings/><con:definitionCache type="TEXT" rootPart="file:\C:\Users\<USER>\Desktop\GIT%20REPO\gateway_ees\src\main\resources\soapFiles\SIFServices\wsSIFServices.wsdl"><con:part><con:url>file:\C:\Users\<USER>\Desktop\GIT%20REPO\gateway_ees\src\main\resources\soapFiles\SIFServices\wsSIFServices.wsdl</con:url><con:content><![CDATA[<wsdl:definitions targetNamespace="http://tempuri.org/" xmlns:s="http://www.w3.org/2001/XMLSchema" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" xmlns:http="http://schemas.xmlsoap.org/wsdl/http/" xmlns:tns="http://tempuri.org/" xmlns:s1="http://microsoft.com/wsdl/types/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <s:schema elementFormDefault="qualified" targetNamespace="http://tempuri.org/">
      <s:import namespace="http://microsoft.com/wsdl/types/"/>
      <s:import namespace="http://www.w3.org/2001/XMLSchema"/>
      <s:element name="IsSysAdminInContext">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="username" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="password" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="application" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="context" type="s1:guid"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="IsSysAdminInContextResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="IsSysAdminInContextResult">
              <s:complexType>
                <s:sequence>
                  <!--s:element ref="s:schema" /-->
                  <s:any/>
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ValidatePassword">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="password" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ValidatePasswordResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="ValidatePasswordResult" type="s:boolean"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetAccountRolesActivationList">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="accountName" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="applicationName" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="officeId" type="s:int"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetAccountRolesActivationListResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetAccountRolesActivationListResult">
              <s:complexType>
                <s:sequence>
                  <!--s:element ref="s:schema" /-->
                  <s:any/>
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="UpdateUserPassword">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="userName" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="oldPassword" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="newPassword" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="UpdateUserPasswordResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="UpdateUserPasswordResult" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ResetUserPassword">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="userName" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ResetUserPasswordResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ResetUserPasswordResult" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetAdministeredOffices">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="user" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="application" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetAdministeredOfficesResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetAdministeredOfficesResult">
              <s:complexType>
                <s:sequence>
                  <!--s:element ref="s:schema" /-->
                  <s:any/>
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetSupervisedOffices">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="user" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="application" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetSupervisedOfficesResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetSupervisedOfficesResult">
              <s:complexType>
                <s:sequence>
                  <!--s:element ref="s:schema" /-->
                  <s:any/>
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetAllUsers">
        <s:complexType/>
      </s:element>
      <s:element name="GetAllUsersResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetAllUsersResult">
              <s:complexType>
                <s:sequence>
                  <!--s:element ref="s:schema" /-->
                  <s:any/>
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="AddOffice">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="idParentOffice" type="s:int"/>
            <s:element minOccurs="0" maxOccurs="1" name="officeType" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="officeStatus" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="idDepartment" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="idOfficeInternal" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="name" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="description" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="idLocale" type="s:int"/>
            <s:element minOccurs="0" maxOccurs="1" name="codCountryRegion" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="address" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="cap" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="telephone" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="fax" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="email" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="idUser" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="AddOfficeResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="AddOfficeResult" type="s:int"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="AddDomain">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="dsDomainName" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="AddDomainResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="AddDomainResult" type="s:boolean"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetAllDomains">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="withLocal" type="s:boolean"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetAllDomainsResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetAllDomainsResult">
              <s:complexType>
                <s:sequence>
                  <!--s:element ref="s:schema" /-->
                  <s:any/>
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetAdministeredMachines">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="user" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="application" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetAdministeredMachinesResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetAdministeredMachinesResult">
              <s:complexType>
                <s:sequence>
                  <!--s:element ref="s:schema" /-->
                  <s:any/>
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetSupervisedMachines">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="user" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="application" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetSupervisedMachinesResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetSupervisedMachinesResult">
              <s:complexType>
                <s:sequence>
                  <!--s:element ref="s:schema" /-->
                  <s:any/>
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="UpdateAccountStatus">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="username" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="application" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="status" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="UpdateAccountStatusResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="UpdateAccountStatusResult" type="s:boolean"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetApplicationAccounts">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="idApplication" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetApplicationAccountsResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetApplicationAccountsResult">
              <s:complexType>
                <s:sequence>
                  <!--s:element ref="s:schema" /-->
                  <s:any/>
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="UpdateAccountRoles">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="idAccount" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="application" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="idContext" type="s1:guid"/>
            <s:element minOccurs="0" maxOccurs="1" name="newRoles" type="tns:ArrayOfAuthorizationRole"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ArrayOfAuthorizationRole">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="AuthorizationRole" nillable="true" type="tns:AuthorizationRole"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="AuthorizationRole">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="Description" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="RoleName" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="RoleStatus" type="s:string"/>
        </s:sequence>
      </s:complexType>
      <s:element name="UpdateAccountRolesResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="UpdateAccountRolesResult" type="s:boolean"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="UpdateRoleServices">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="dsRole" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="services" type="tns:ArrayOfAnyType"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ArrayOfAnyType">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="anyType" nillable="true"/>
        </s:sequence>
      </s:complexType>
      <s:element name="UpdateRoleServicesResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="UpdateRoleServicesResult" type="s:boolean"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="AddApplicationRole">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="dsName" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="idRole" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="flgState" type="s:int"/>
            <s:element minOccurs="0" maxOccurs="1" name="dsDescription" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="AddApplicationRoleResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="AddApplicationRoleResult" type="s:boolean"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetAdministeredOfficesByType">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="dsUserName" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="dsApplication" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="idType" type="s:int"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetAdministeredOfficesByTypeResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetAdministeredOfficesByTypeResult">
              <s:complexType>
                <s:sequence>
                  <!--s:element ref="s:schema" /-->
                  <s:any/>
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetAllApplicationRoles">
        <s:complexType/>
      </s:element>
      <s:element name="GetAllApplicationRolesResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetAllApplicationRolesResult">
              <s:complexType>
                <s:sequence>
                  <!--s:element ref="s:schema" /-->
                  <s:any/>
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetRoleServicesAuthorizationList">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="dsRole" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetRoleServicesAuthorizationListResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetRoleServicesAuthorizationListResult">
              <s:complexType>
                <s:sequence>
                  <!--s:element ref="s:schema" /-->
                  <s:any/>
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetAccountCurrentStatus">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="idUser" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="idApplication" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="dsUserName" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetAccountCurrentStatusResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetAccountCurrentStatusResult">
              <s:complexType>
                <s:sequence>
                  <!--s:element ref="s:schema" /-->
                  <s:any/>
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetAccountInfo">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="idUser" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetAccountInfoResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetAccountInfoResult">
              <s:complexType>
                <s:sequence>
                  <!--s:element ref="s:schema" /-->
                  <s:any/>
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetAdministeredUserByOffice">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="user" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="office" type="s:int"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetAdministeredUserByOfficeResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetAdministeredUserByOfficeResult">
              <s:complexType>
                <s:sequence>
                  <!--s:element ref="s:schema" /-->
                  <s:any/>
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetAdministeredMachineByOffice">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="user" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="office" type="s:int"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetAdministeredMachineByOfficeResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetAdministeredMachineByOfficeResult">
              <s:complexType>
                <s:sequence>
                  <!--s:element ref="s:schema" /-->
                  <s:any/>
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetOfficeInfo">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="idOffice" type="s:int"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetOfficeInfoResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetOfficeInfoResult">
              <s:complexType>
                <s:sequence>
                  <!--s:element ref="s:schema" /-->
                  <s:any/>
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetDomainInfo">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="idDomain" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetDomainInfoResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetDomainInfoResult">
              <s:complexType>
                <s:sequence>
                  <!--s:element ref="s:schema" /-->
                  <s:any/>
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="UpdateUserAccount">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="username" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="firstName" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="secondName" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="surname" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="sex" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="perId" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="application" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="accountStatus" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="accountExpirationDate" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="UpdateUserAccountResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="UpdateUserAccountResult" type="s:boolean"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetAdministeredChildOffices">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="user" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="application" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="parent" type="s:int"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetAdministeredChildOfficesResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetAdministeredChildOfficesResult">
              <s:complexType>
                <s:sequence>
                  <!--s:element ref="s:schema" /-->
                  <s:any/>
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="UpdateOfficeData">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="idOffice" type="s:int"/>
            <s:element minOccurs="1" maxOccurs="1" name="idParentOffice" type="s:int"/>
            <s:element minOccurs="0" maxOccurs="1" name="officeType" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="officeStatus" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="department" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="name" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="description" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="address" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="locale" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="cap" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="telephone" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="fax" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="email" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="UpdateOfficeDataResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="UpdateOfficeDataResult" type="s:boolean"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="AddUser">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="firstName" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="secondName" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="surname" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="sex" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="idPer" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="application" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="userName" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="accountExpDate" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="idOffice" type="s:int"/>
            <s:element minOccurs="0" maxOccurs="1" name="OwnerUserName" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="AddUserResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="AddUserResult" type="s:int"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetAllApplicationServices">
        <s:complexType/>
      </s:element>
      <s:element name="GetAllApplicationServicesResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetAllApplicationServicesResult">
              <s:complexType>
                <s:sequence>
                  <!--s:element ref="s:schema" /-->
                  <s:any/>
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetRoleServices">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="role" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetRoleServicesResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetRoleServicesResult">
              <s:complexType>
                <s:sequence>
                  <!--s:element ref="s:schema" /-->
                  <s:any/>
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="UpdateApplicationRole">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="role" type="s1:guid"/>
            <s:element minOccurs="0" maxOccurs="1" name="name" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="description" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="state" type="s:boolean"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="UpdateApplicationRoleResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="UpdateApplicationRoleResult" type="s:boolean"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetAllActiveOffices">
        <s:complexType/>
      </s:element>
      <s:element name="GetAllActiveOfficesResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetAllActiveOfficesResult">
              <s:complexType>
                <s:sequence>
                  <!--s:element ref="s:schema" /-->
                  <s:any/>
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DeleteUserSecurityContext">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="idUser" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="idApplication" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="officeId" type="s:int"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DeleteUserSecurityContextResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="DeleteUserSecurityContextResult" type="s:boolean"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DeleteOffice">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="officeId" type="s:int"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DeleteOfficeResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="DeleteOfficeResult" type="s:boolean"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DelMachine">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="idMachine" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="idDomain" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DelMachineResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="DelMachineResult" type="s:boolean"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="AddUserSecurityContext">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="userName" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="application" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="idOffice" type="s:int"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="AddUserSecurityContextResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="AddUserSecurityContextResult" type="s:int"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SearchUserInfo">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="searchText" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SearchUserInfoResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="SearchUserInfoResult">
              <s:complexType>
                <s:sequence>
                  <!--s:element ref="s:schema" /-->
                  <s:any/>
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetActiveAccounts">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="office" type="s:int"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetActiveAccountsResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetActiveAccountsResult">
              <s:complexType>
                <s:sequence>
                  <!--s:element ref="s:schema" /-->
                  <s:any/>
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetAccountSecurityContextId">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="userName" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="applicationName" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="officeId" type="s:int"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetAccountSecurityContextIdResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="GetAccountSecurityContextIdResult" type="s1:guid"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetAllAccountSecurityContext">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="userName" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetAllAccountSecurityContextResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetAllAccountSecurityContextResult">
              <s:complexType>
                <s:sequence>
                  <!--s:element ref="s:schema" /-->
                  <s:any/>
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="AddOfficeAdministrator">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="officeId" type="s:int"/>
            <s:element minOccurs="0" maxOccurs="1" name="userName" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="applicationName" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="AddOfficeAdministratorResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="AddOfficeAdministratorResult" type="s:int"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="UpdateClientMachine">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="idMachine" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="idDomain" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="idOffice" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="dsName" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="dsIPAddress" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="dsDescription" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="dsFullName" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="dsSystem" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="dsType" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="UpdateClientMachineResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="UpdateClientMachineResult" type="s:boolean"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetDockType">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="idOfficeInternal" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetDockTypeResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetDockTypeResult">
              <s:complexType>
                <s:sequence>
                  <!--s:element ref="s:schema" /-->
                  <s:any/>
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="LoadDocumentLayoutDatabase">
        <s:complexType/>
      </s:element>
      <s:element name="LoadDocumentLayoutDatabaseResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="LoadDocumentLayoutDatabaseResult" type="s:boolean"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetLoginAuthentication">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="accountName" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="password" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="application" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="idMachine" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="securityContextId" type="s1:guid"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetLoginAuthenticationApp">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="accountName" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="password" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="application" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="idOffice" type="s:int"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetLoginAuthenticationClient">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="accountName" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="password" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="application" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="unbounded" name="idMachine" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="LogoutClient">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="SecurityToken" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="FieldMapping">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="SourceField" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="DestinationField" type="s:string"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="ApplicationService">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="Address" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="ServiceType" type="tns:ApplicationServiceTypes"/>
          <s:element minOccurs="0" maxOccurs="1" name="Namespace" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="Class" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="Assembly" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="Name" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="Method" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="ConnectedMode" type="tns:ApplicationServiceModes"/>
          <s:element minOccurs="0" maxOccurs="1" name="ExecutingIdentity" type="tns:ServiceIdentity"/>
          <s:element minOccurs="0" maxOccurs="1" name="ServiceExecutionRules" type="tns:ArrayOfApplicationServiceRule"/>
        </s:sequence>
      </s:complexType>
      <s:simpleType name="ApplicationServiceTypes">
        <s:restriction base="s:string">
          <s:enumeration value="WebService"/>
          <s:enumeration value="Method"/>
          <s:enumeration value="Abstract"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="ApplicationServiceModes">
        <s:restriction base="s:string">
          <s:enumeration value="Connected"/>
          <s:enumeration value="Disconnected"/>
          <s:enumeration value="Both"/>
        </s:restriction>
      </s:simpleType>
      <s:complexType name="ServiceIdentity" abstract="true">
        <s:complexContent mixed="false">
          <s:extension base="tns:AuthorizationIdentityBase">
            <s:sequence>
              <s:element minOccurs="0" maxOccurs="1" name="AuthorizedServices" type="tns:ArrayOfApplicationService"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="ServiceIdentityClient" abstract="true">
        <s:complexContent mixed="false">
          <s:extension base="tns:AuthorizationIdentityBaseClient"></s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="AuthorizationIdentityBase" abstract="true">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Status" type="tns:ApplicationIdentityStatus"/>
          <s:element minOccurs="1" maxOccurs="1" name="DateOfExpire" type="s:dateTime"/>
          <s:element minOccurs="1" maxOccurs="1" name="PasswordExpirationDate" type="s:dateTime"/>
          <s:element minOccurs="0" maxOccurs="1" name="InitialPassword" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="IsAuthenticated" type="s:boolean"/>
          <s:element minOccurs="0" maxOccurs="1" name="Password" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="Roles" type="tns:ArrayOfAuthorizationRole"/>
          <s:element minOccurs="0" maxOccurs="1" name="User" type="tns:User"/>
          <s:element minOccurs="0" maxOccurs="1" name="UserName" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="ApplicationName" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="CanResetPassword" type="s:boolean"/>
          <s:element minOccurs="1" maxOccurs="1" name="PasswordNewerExpire" type="s:boolean"/>
          <s:element minOccurs="1" maxOccurs="1" name="MaxInvalidPasswordAttempts" type="s:int"/>
          <s:element minOccurs="1" maxOccurs="1" name="CreationDate" type="s:dateTime"/>
          <s:element minOccurs="1" maxOccurs="1" name="IsLogged" type="s:boolean"/>
          <s:element minOccurs="1" maxOccurs="1" name="LastActivityDateTime" type="s:dateTime"/>
          <s:element minOccurs="1" maxOccurs="1" name="LastLoginDateTime" type="s:dateTime"/>
          <s:element minOccurs="1" maxOccurs="1" name="LastPasswordChangedDate" type="s:dateTime"/>
          <s:element minOccurs="1" maxOccurs="1" name="IsExpired" type="s:boolean"/>
          <s:element minOccurs="1" maxOccurs="1" name="IsLocked" type="s:boolean"/>
          <s:element minOccurs="1" maxOccurs="1" name="PasswordChangeNeeded" type="s:boolean"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="AuthorizationIdentityBaseClient" abstract="true">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Status" type="tns:ApplicationIdentityStatus"/>
          <s:element minOccurs="1" maxOccurs="1" name="DateOfExpire" type="s:dateTime"/>
          <s:element minOccurs="1" maxOccurs="1" name="PasswordExpirationDate" type="s:dateTime"/>
          <s:element minOccurs="1" maxOccurs="1" name="IsAuthenticated" type="s:boolean"/>
          <s:element minOccurs="0" maxOccurs="1" name="Message" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="UserName" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="MaxInvalidPasswordAttempts" type="s:int"/>
          <s:element minOccurs="1" maxOccurs="1" name="LastLoginDateTime" type="s:dateTime"/>
        </s:sequence>
      </s:complexType>
      <s:simpleType name="ApplicationIdentityStatus">
        <s:restriction base="s:string">
          <s:enumeration value="Active"/>
          <s:enumeration value="Locked"/>
          <s:enumeration value="Suspended"/>
          <s:enumeration value="Expired"/>
          <s:enumeration value="Ended"/>
          <s:enumeration value="Waiting"/>
        </s:restriction>
      </s:simpleType>
      <s:complexType name="User">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="FirstName" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="SecondName" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="Surname" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="Sex" type="tns:UserSex"/>
          <s:element minOccurs="0" maxOccurs="1" name="Addresses" type="tns:ArrayOfAnyType"/>
          <s:element minOccurs="0" maxOccurs="1" name="Id"/>
        </s:sequence>
      </s:complexType>
      <s:simpleType name="UserSex">
        <s:restriction base="s:string">
          <s:enumeration value="M"/>
          <s:enumeration value="F"/>
        </s:restriction>
      </s:simpleType>
      <s:complexType name="ArrayOfApplicationService">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="ApplicationService" nillable="true" type="tns:ApplicationService"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="SIFIdentity">
        <s:complexContent mixed="false">
          <s:extension base="tns:ServiceIdentity">
            <s:sequence>
              <s:element minOccurs="0" maxOccurs="1" name="Name" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="UserIAE" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="PswIAE" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="SelectCommand" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="InsertCommand" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="DeleteCommand" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="UpdateCommand" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="FieldMappings" type="tns:ArrayOfFieldMapping"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="SIFIdentityClient">
        <s:complexContent mixed="false">
          <s:extension base="tns:ServiceIdentityClient">
            <s:sequence>
              <s:element minOccurs="0" maxOccurs="unbounded" name="Security" type="tns:SecurityType"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="SecurityType">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="idMachine" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="SecurityToken" type="s:string"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArrayOfFieldMapping">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="FieldMapping" nillable="true" type="tns:FieldMapping"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArrayOfApplicationServiceRule">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="ApplicationServiceRule" nillable="true" type="tns:ApplicationServiceRule"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="ApplicationServiceRule">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="Name" type="s:string"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="SecurityContextBase" abstract="true">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="Items" type="tns:ArrayOfSecurityContextItem"/>
          <s:element minOccurs="1" maxOccurs="1" name="SecurityToken" type="s1:guid"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArrayOfSecurityContextItem">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="SecurityContextItem" nillable="true" type="tns:SecurityContextItem"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="SecurityContextItem"/>
      <s:complexType name="SIFSecurityContext">
        <s:complexContent mixed="false">
          <s:extension base="tns:SecurityContextBase"/>
        </s:complexContent>
      </s:complexType>
      <s:element name="GetLoginAuthenticationResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetLoginAuthenticationResult" type="tns:SIFIdentity"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetLoginAuthenticationClientResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetLoginAuthenticationClientResult" type="tns:SIFIdentityClient"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="LogoutClientResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Esito" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="Messaggio" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetServiceRolesAuthorizationList">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="serviceName" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetServiceRolesAuthorizationListResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetServiceRolesAuthorizationListResult">
              <s:complexType>
                <s:sequence>
                  <!--s:element ref="s:schema" /-->
                  <s:any/>
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetAccountServices">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="identity" type="tns:SIFIdentity"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetAccountServicesResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetAccountServicesResult" type="tns:ArrayOfApplicationService"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetOfficeMessages">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="officeInternalId" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetOfficeMessagesResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetOfficeMessagesResult">
              <s:complexType>
                <s:sequence>
                  <!--s:element ref="s:schema" /-->
                  <s:any/>
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SendBroadcastMessage">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="idKnowledge" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="dstype" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="dsstate" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="dsScope" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="subject" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="messageBody" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="dateExpire" type="s:dateTime"/>
            <s:element minOccurs="0" maxOccurs="1" name="dsReceiverOffice" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="flgbroadcast" type="s:boolean"/>
            <s:element minOccurs="0" maxOccurs="1" name="extraData" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="notes" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="attachmentType" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="attachmentExt" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="attachment" type="s:base64Binary"/>
            <s:element minOccurs="0" maxOccurs="1" name="insertingOffice" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="insertingUser" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="insertingApp" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="domain" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="insertingMachine" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SendBroadcastMessageResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="SendBroadcastMessageResult" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SendAttachmentFile">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="idKnowledge" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="attachmentype" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="attachmentext" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="attachment" type="s:base64Binary"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SendAttachmentFileResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="SendAttachmentFileResult" type="s:boolean"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetAccountRoles">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="accountName" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="password" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="application" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="securityContextId" type="s1:guid"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetAccountRolesResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetAccountRolesResult" type="tns:ArrayOfAuthorizationRole"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetSystemInfo">
        <s:complexType/>
      </s:element>
      <s:element name="GetSystemInfoResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetSystemInfoResult" type="tns:ArrayOfAnyType"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetAppInfo">
        <s:complexType/>
      </s:element>
      <s:element name="GetAppInfoResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetAppInfoResult" type="tns:ArrayOfAnyType"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetKnowledgeAttachmentFile">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="idKnowledge" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetKnowledgeAttachmentFileResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetKnowledgeAttachmentFileResult" type="s:base64Binary"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="AddMachine">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="dsName" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="dsDomainName" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="dsType" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="dsIPAddress" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="dsFullName" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="dsDescription" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="dsSystem" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="dsOfficeName" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="dsOfficeInternal" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="AddMachineResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="AddMachineResult" type="s:int"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetAllApplications">
        <s:complexType/>
      </s:element>
      <s:element name="GetAllApplicationsResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetAllApplicationsResult">
              <s:complexType>
                <s:sequence>
                  <!--s:element ref="s:schema" /-->
                  <s:any/>
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetAllUncertifiedAlert">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="idState" type="s:int"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetAllUncertifiedAlertResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetAllUncertifiedAlertResult">
              <s:complexType>
                <s:sequence>
                  <!--s:element ref="s:schema" /-->
                  <s:any/>
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetNotifyKnowledgeAttachment">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="idKnowledge" type="s1:guid"/>
            <s:element minOccurs="1" maxOccurs="1" name="idAttach" type="s1:guid"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetNotifyKnowledgeAttachmentResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetNotifyKnowledgeAttachmentResult">
              <s:complexType>
                <s:sequence>
                  <!--s:element ref="s:schema" /-->
                  <s:any/>
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetNotifyKnowledge">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="idKnowledge" type="s1:guid"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetNotifyKnowledgeResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetNotifyKnowledgeResult">
              <s:complexType>
                <s:sequence>
                  <!--s:element ref="s:schema" /-->
                  <s:any/>
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetCertifiedAlertImages">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="idKnowledge" type="s1:guid"/>
            <s:element minOccurs="0" maxOccurs="1" name="idAttaches" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="newWidth" type="s:int"/>
            <s:element minOccurs="1" maxOccurs="1" name="maxHeight" type="s:int"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetCertifiedAlertImagesResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetCertifiedAlertImagesResult">
              <s:complexType>
                <s:sequence>
                  <!--s:element ref="s:schema" /-->
                  <s:any/>
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CheckDigitalCertificate">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="certificate" type="s:base64Binary"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CheckDigitalCertificateResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="CheckDigitalCertificateResult" type="s:boolean"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CheckExternalServices">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="serverAddress" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="porta" type="s:int"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CheckExternalServicesResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="CheckExternalServicesResult" type="s:boolean"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetMachineInfo">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="machineName" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="domain" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetMachineInfoResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetMachineInfoResult">
              <s:complexType>
                <s:sequence>
                  <!--s:element ref="s:schema" /-->
                  <s:any/>
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetMachineInfoApp">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="machineName" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="domain" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetMachineInfoAppResponse">
        <s:complexType>
          <s:sequence>
            <s:element name="GetMachineInfoAppResult">
              <s:complexType>
                <s:sequence>
                  <s:element minOccurs="0" maxOccurs="1" name="idMachine" type="s:string"/>
                  <s:element minOccurs="0" maxOccurs="1" name="dsName" type="s:string"/>
                  <s:element minOccurs="0" maxOccurs="1" name="dsIPAddress" type="s:string"/>
                  <s:element minOccurs="0" maxOccurs="1" name="dsDescription" type="s:string"/>
                  <s:element minOccurs="0" maxOccurs="1" name="dsSystem" type="s:string"/>
                  <s:element minOccurs="0" maxOccurs="1" name="dsType" type="s:string"/>
                  <s:element minOccurs="0" maxOccurs="1" name="dsDomain" type="s:string"/>
                  <s:element minOccurs="0" maxOccurs="1" name="idDomain" type="s:string"/>
                  <s:element minOccurs="0" maxOccurs="1" name="idOffice" type="s:string"/>
                  <s:element minOccurs="0" maxOccurs="1" name="dsOffice" type="s:string"/>
                  <s:element minOccurs="0" maxOccurs="1" name="dsOfficeInternal" type="s:string"/>
                  <s:element minOccurs="0" maxOccurs="1" name="dsApplicationName" type="s:string"/>
                  <s:element minOccurs="0" maxOccurs="1" name="esito" type="s:int"/>
                  <s:element minOccurs="0" maxOccurs="1" name="errorMessage" type="s:string"/>
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="AddAlert">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="dsName" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="docType" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="codIssuer" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="dsDocumentVersion" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="attachment" type="s:base64Binary"/>
            <s:element minOccurs="0" maxOccurs="1" name="dsInsertingOffice" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="dsInsertingUser" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="dsApplication" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="dsDomain" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="dsMachine" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="dsNote" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="AddAlertResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="AddAlertResult" type="s:boolean"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="AddAlertBySpec">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="dsName" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="docType" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="codIssuer" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="dsDocumentVersion" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="attachment" type="s:base64Binary"/>
            <s:element minOccurs="0" maxOccurs="1" name="dsInsertingOffice" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="dsInsertingUser" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="dsApplication" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="dsDomain" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="dsMachine" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="dsNote" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="idSpecimenSidaf" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="AddAlertBySpecResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="AddAlertBySpecResult" type="s:boolean"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetDocumentAlerts">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="name" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="sub" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetDocumentAlertsResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetDocumentAlertsResult">
              <s:complexType>
                <s:sequence>
                  <!--s:element ref="s:schema" /-->
                  <s:any/>
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetDocumentAlertsDetailInfo">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="name" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="sub" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="DocumentCode" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="IssuerCode" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="Version" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetDocumentAlertsDetailInfoResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetDocumentAlertsDetailInfoResult">
              <s:complexType>
                <s:sequence>
                  <!--s:element ref="s:schema" /-->
                  <s:any/>
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetDocumentAlertsCertified">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="issuer" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="type" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetDocumentAlertsCertifiedResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetDocumentAlertsCertifiedResult">
              <s:complexType>
                <s:sequence>
                  <!--s:element ref="s:schema" /-->
                  <s:any/>
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CheckDbReachability">
        <s:complexType/>
      </s:element>
      <s:element name="CheckDbReachabilityResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="CheckDbReachabilityResult" type="s:boolean"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ViewLog">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="fromDate" type="s:dateTime"/>
            <s:element minOccurs="1" maxOccurs="1" name="toDate" type="s:dateTime"/>
            <s:element minOccurs="1" maxOccurs="1" name="fromRow" type="s:int"/>
            <s:element minOccurs="1" maxOccurs="1" name="toRow" type="s:int"/>
            <s:element minOccurs="1" maxOccurs="1" name="idOffice" nillable="true" type="s:int"/>
            <s:element minOccurs="1" maxOccurs="1" name="idType" nillable="true" type="s:int"/>
            <s:element minOccurs="1" maxOccurs="1" name="idSeverity" nillable="true" type="s:int"/>
            <s:element minOccurs="0" maxOccurs="1" name="idMachine" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="idDomain" nillable="true" type="s1:guid"/>
            <s:element minOccurs="0" maxOccurs="1" name="userName" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="application" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ViewLogResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ViewLogResult">
              <s:complexType>
                <s:sequence>
                  <!--s:element ref="s:schema" /-->
                  <s:any/>
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="UpdAlertStatus">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="idKnowledge" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="idState" type="s:int"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="UpdAlertStatusResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="UpdAlertStatusResult" type="s:boolean"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetAllDocumentAlertsCertified">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="idState" type="s:int"/>
            <s:element minOccurs="0" maxOccurs="1" name="idOffice" type="s:int"/>
            <s:element minOccurs="0" maxOccurs="1" name="codIssuer" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="codType" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="note" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="dataIn" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="dataFin" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetAllDocumentAlertsCertifiedResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetAllDocumentAlertsCertifiedResult">
              <s:complexType>
                <s:sequence>
                  <!--s:element ref="s:schema" /-->
                  <s:any/>
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="TestIvis2">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="visaNumber" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="TestIvis2Response">
        <s:complexType/>
      </s:element>
      <s:element name="GetDetailsByDatiSidafId">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="datiSidafId" type="s1:guid"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetDetailsByDatiSidafIdResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetDetailsByDatiSidafIdResult">
              <s:complexType>
                <s:sequence>
                  <!--s:element ref="s:schema" /-->
                  <s:any/>
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
    </s:schema>
    <s:schema elementFormDefault="qualified" targetNamespace="http://microsoft.com/wsdl/types/">
      <s:simpleType name="guid">
        <s:restriction base="s:string">
          <s:pattern value="[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}"/>
        </s:restriction>
      </s:simpleType>
    </s:schema>
  </wsdl:types>
  <wsdl:message name="IsSysAdminInContextSoapIn">
    <wsdl:part name="parameters" element="tns:IsSysAdminInContext"/>
  </wsdl:message>
  <wsdl:message name="IsSysAdminInContextSoapOut">
    <wsdl:part name="parameters" element="tns:IsSysAdminInContextResponse"/>
  </wsdl:message>
  <wsdl:message name="ValidatePasswordSoapIn">
    <wsdl:part name="parameters" element="tns:ValidatePassword"/>
  </wsdl:message>
  <wsdl:message name="ValidatePasswordSoapOut">
    <wsdl:part name="parameters" element="tns:ValidatePasswordResponse"/>
  </wsdl:message>
  <wsdl:message name="GetAccountRolesActivationListSoapIn">
    <wsdl:part name="parameters" element="tns:GetAccountRolesActivationList"/>
  </wsdl:message>
  <wsdl:message name="GetAccountRolesActivationListSoapOut">
    <wsdl:part name="parameters" element="tns:GetAccountRolesActivationListResponse"/>
  </wsdl:message>
  <wsdl:message name="UpdateUserPasswordSoapIn">
    <wsdl:part name="parameters" element="tns:UpdateUserPassword"/>
  </wsdl:message>
  <wsdl:message name="UpdateUserPasswordSoapOut">
    <wsdl:part name="parameters" element="tns:UpdateUserPasswordResponse"/>
  </wsdl:message>
  <wsdl:message name="ResetUserPasswordSoapIn">
    <wsdl:part name="parameters" element="tns:ResetUserPassword"/>
  </wsdl:message>
  <wsdl:message name="ResetUserPasswordSoapOut">
    <wsdl:part name="parameters" element="tns:ResetUserPasswordResponse"/>
  </wsdl:message>
  <wsdl:message name="GetAdministeredOfficesSoapIn">
    <wsdl:part name="parameters" element="tns:GetAdministeredOffices"/>
  </wsdl:message>
  <wsdl:message name="GetAdministeredOfficesSoapOut">
    <wsdl:part name="parameters" element="tns:GetAdministeredOfficesResponse"/>
  </wsdl:message>
  <wsdl:message name="GetSupervisedOfficesSoapIn">
    <wsdl:part name="parameters" element="tns:GetSupervisedOffices"/>
  </wsdl:message>
  <wsdl:message name="GetSupervisedOfficesSoapOut">
    <wsdl:part name="parameters" element="tns:GetSupervisedOfficesResponse"/>
  </wsdl:message>
  <wsdl:message name="GetAllUsersSoapIn">
    <wsdl:part name="parameters" element="tns:GetAllUsers"/>
  </wsdl:message>
  <wsdl:message name="GetAllUsersSoapOut">
    <wsdl:part name="parameters" element="tns:GetAllUsersResponse"/>
  </wsdl:message>
  <wsdl:message name="AddOfficeSoapIn">
    <wsdl:part name="parameters" element="tns:AddOffice"/>
  </wsdl:message>
  <wsdl:message name="AddOfficeSoapOut">
    <wsdl:part name="parameters" element="tns:AddOfficeResponse"/>
  </wsdl:message>
  <wsdl:message name="AddDomainSoapIn">
    <wsdl:part name="parameters" element="tns:AddDomain"/>
  </wsdl:message>
  <wsdl:message name="AddDomainSoapOut">
    <wsdl:part name="parameters" element="tns:AddDomainResponse"/>
  </wsdl:message>
  <wsdl:message name="GetAllDomainsSoapIn">
    <wsdl:part name="parameters" element="tns:GetAllDomains"/>
  </wsdl:message>
  <wsdl:message name="GetAllDomainsSoapOut">
    <wsdl:part name="parameters" element="tns:GetAllDomainsResponse"/>
  </wsdl:message>
  <wsdl:message name="GetAdministeredMachinesSoapIn">
    <wsdl:part name="parameters" element="tns:GetAdministeredMachines"/>
  </wsdl:message>
  <wsdl:message name="GetAdministeredMachinesSoapOut">
    <wsdl:part name="parameters" element="tns:GetAdministeredMachinesResponse"/>
  </wsdl:message>
  <wsdl:message name="GetSupervisedMachinesSoapIn">
    <wsdl:part name="parameters" element="tns:GetSupervisedMachines"/>
  </wsdl:message>
  <wsdl:message name="GetSupervisedMachinesSoapOut">
    <wsdl:part name="parameters" element="tns:GetSupervisedMachinesResponse"/>
  </wsdl:message>
  <wsdl:message name="UpdateAccountStatusSoapIn">
    <wsdl:part name="parameters" element="tns:UpdateAccountStatus"/>
  </wsdl:message>
  <wsdl:message name="UpdateAccountStatusSoapOut">
    <wsdl:part name="parameters" element="tns:UpdateAccountStatusResponse"/>
  </wsdl:message>
  <wsdl:message name="GetApplicationAccountsSoapIn">
    <wsdl:part name="parameters" element="tns:GetApplicationAccounts"/>
  </wsdl:message>
  <wsdl:message name="GetApplicationAccountsSoapOut">
    <wsdl:part name="parameters" element="tns:GetApplicationAccountsResponse"/>
  </wsdl:message>
  <wsdl:message name="UpdateAccountRolesSoapIn">
    <wsdl:part name="parameters" element="tns:UpdateAccountRoles"/>
  </wsdl:message>
  <wsdl:message name="UpdateAccountRolesSoapOut">
    <wsdl:part name="parameters" element="tns:UpdateAccountRolesResponse"/>
  </wsdl:message>
  <wsdl:message name="UpdateRoleServicesSoapIn">
    <wsdl:part name="parameters" element="tns:UpdateRoleServices"/>
  </wsdl:message>
  <wsdl:message name="UpdateRoleServicesSoapOut">
    <wsdl:part name="parameters" element="tns:UpdateRoleServicesResponse"/>
  </wsdl:message>
  <wsdl:message name="AddApplicationRoleSoapIn">
    <wsdl:part name="parameters" element="tns:AddApplicationRole"/>
  </wsdl:message>
  <wsdl:message name="AddApplicationRoleSoapOut">
    <wsdl:part name="parameters" element="tns:AddApplicationRoleResponse"/>
  </wsdl:message>
  <wsdl:message name="GetAdministeredOfficesByTypeSoapIn">
    <wsdl:part name="parameters" element="tns:GetAdministeredOfficesByType"/>
  </wsdl:message>
  <wsdl:message name="GetAdministeredOfficesByTypeSoapOut">
    <wsdl:part name="parameters" element="tns:GetAdministeredOfficesByTypeResponse"/>
  </wsdl:message>
  <wsdl:message name="GetAllApplicationRolesSoapIn">
    <wsdl:part name="parameters" element="tns:GetAllApplicationRoles"/>
  </wsdl:message>
  <wsdl:message name="GetAllApplicationRolesSoapOut">
    <wsdl:part name="parameters" element="tns:GetAllApplicationRolesResponse"/>
  </wsdl:message>
  <wsdl:message name="GetRoleServicesAuthorizationListSoapIn">
    <wsdl:part name="parameters" element="tns:GetRoleServicesAuthorizationList"/>
  </wsdl:message>
  <wsdl:message name="GetRoleServicesAuthorizationListSoapOut">
    <wsdl:part name="parameters" element="tns:GetRoleServicesAuthorizationListResponse"/>
  </wsdl:message>
  <wsdl:message name="GetAccountCurrentStatusSoapIn">
    <wsdl:part name="parameters" element="tns:GetAccountCurrentStatus"/>
  </wsdl:message>
  <wsdl:message name="GetAccountCurrentStatusSoapOut">
    <wsdl:part name="parameters" element="tns:GetAccountCurrentStatusResponse"/>
  </wsdl:message>
  <wsdl:message name="GetAccountInfoSoapIn">
    <wsdl:part name="parameters" element="tns:GetAccountInfo"/>
  </wsdl:message>
  <wsdl:message name="GetAccountInfoSoapOut">
    <wsdl:part name="parameters" element="tns:GetAccountInfoResponse"/>
  </wsdl:message>
  <wsdl:message name="GetAdministeredUserByOfficeSoapIn">
    <wsdl:part name="parameters" element="tns:GetAdministeredUserByOffice"/>
  </wsdl:message>
  <wsdl:message name="GetAdministeredUserByOfficeSoapOut">
    <wsdl:part name="parameters" element="tns:GetAdministeredUserByOfficeResponse"/>
  </wsdl:message>
  <wsdl:message name="GetAdministeredMachineByOfficeSoapIn">
    <wsdl:part name="parameters" element="tns:GetAdministeredMachineByOffice"/>
  </wsdl:message>
  <wsdl:message name="GetAdministeredMachineByOfficeSoapOut">
    <wsdl:part name="parameters" element="tns:GetAdministeredMachineByOfficeResponse"/>
  </wsdl:message>
  <wsdl:message name="GetOfficeInfoSoapIn">
    <wsdl:part name="parameters" element="tns:GetOfficeInfo"/>
  </wsdl:message>
  <wsdl:message name="GetOfficeInfoSoapOut">
    <wsdl:part name="parameters" element="tns:GetOfficeInfoResponse"/>
  </wsdl:message>
  <wsdl:message name="GetDomainInfoSoapIn">
    <wsdl:part name="parameters" element="tns:GetDomainInfo"/>
  </wsdl:message>
  <wsdl:message name="GetDomainInfoSoapOut">
    <wsdl:part name="parameters" element="tns:GetDomainInfoResponse"/>
  </wsdl:message>
  <wsdl:message name="UpdateUserAccountSoapIn">
    <wsdl:part name="parameters" element="tns:UpdateUserAccount"/>
  </wsdl:message>
  <wsdl:message name="UpdateUserAccountSoapOut">
    <wsdl:part name="parameters" element="tns:UpdateUserAccountResponse"/>
  </wsdl:message>
  <wsdl:message name="GetAdministeredChildOfficesSoapIn">
    <wsdl:part name="parameters" element="tns:GetAdministeredChildOffices"/>
  </wsdl:message>
  <wsdl:message name="GetAdministeredChildOfficesSoapOut">
    <wsdl:part name="parameters" element="tns:GetAdministeredChildOfficesResponse"/>
  </wsdl:message>
  <wsdl:message name="UpdateOfficeDataSoapIn">
    <wsdl:part name="parameters" element="tns:UpdateOfficeData"/>
  </wsdl:message>
  <wsdl:message name="UpdateOfficeDataSoapOut">
    <wsdl:part name="parameters" element="tns:UpdateOfficeDataResponse"/>
  </wsdl:message>
  <wsdl:message name="AddUserSoapIn">
    <wsdl:part name="parameters" element="tns:AddUser"/>
  </wsdl:message>
  <wsdl:message name="AddUserSoapOut">
    <wsdl:part name="parameters" element="tns:AddUserResponse"/>
  </wsdl:message>
  <wsdl:message name="GetAllApplicationServicesSoapIn">
    <wsdl:part name="parameters" element="tns:GetAllApplicationServices"/>
  </wsdl:message>
  <wsdl:message name="GetAllApplicationServicesSoapOut">
    <wsdl:part name="parameters" element="tns:GetAllApplicationServicesResponse"/>
  </wsdl:message>
  <wsdl:message name="GetRoleServicesSoapIn">
    <wsdl:part name="parameters" element="tns:GetRoleServices"/>
  </wsdl:message>
  <wsdl:message name="GetRoleServicesSoapOut">
    <wsdl:part name="parameters" element="tns:GetRoleServicesResponse"/>
  </wsdl:message>
  <wsdl:message name="UpdateApplicationRoleSoapIn">
    <wsdl:part name="parameters" element="tns:UpdateApplicationRole"/>
  </wsdl:message>
  <wsdl:message name="UpdateApplicationRoleSoapOut">
    <wsdl:part name="parameters" element="tns:UpdateApplicationRoleResponse"/>
  </wsdl:message>
  <wsdl:message name="GetAllActiveOfficesSoapIn">
    <wsdl:part name="parameters" element="tns:GetAllActiveOffices"/>
  </wsdl:message>
  <wsdl:message name="GetAllActiveOfficesSoapOut">
    <wsdl:part name="parameters" element="tns:GetAllActiveOfficesResponse"/>
  </wsdl:message>
  <wsdl:message name="DeleteUserSecurityContextSoapIn">
    <wsdl:part name="parameters" element="tns:DeleteUserSecurityContext"/>
  </wsdl:message>
  <wsdl:message name="DeleteUserSecurityContextSoapOut">
    <wsdl:part name="parameters" element="tns:DeleteUserSecurityContextResponse"/>
  </wsdl:message>
  <wsdl:message name="DeleteOfficeSoapIn">
    <wsdl:part name="parameters" element="tns:DeleteOffice"/>
  </wsdl:message>
  <wsdl:message name="DeleteOfficeSoapOut">
    <wsdl:part name="parameters" element="tns:DeleteOfficeResponse"/>
  </wsdl:message>
  <wsdl:message name="DelMachineSoapIn">
    <wsdl:part name="parameters" element="tns:DelMachine"/>
  </wsdl:message>
  <wsdl:message name="DelMachineSoapOut">
    <wsdl:part name="parameters" element="tns:DelMachineResponse"/>
  </wsdl:message>
  <wsdl:message name="AddUserSecurityContextSoapIn">
    <wsdl:part name="parameters" element="tns:AddUserSecurityContext"/>
  </wsdl:message>
  <wsdl:message name="AddUserSecurityContextSoapOut">
    <wsdl:part name="parameters" element="tns:AddUserSecurityContextResponse"/>
  </wsdl:message>
  <wsdl:message name="SearchUserInfoSoapIn">
    <wsdl:part name="parameters" element="tns:SearchUserInfo"/>
  </wsdl:message>
  <wsdl:message name="SearchUserInfoSoapOut">
    <wsdl:part name="parameters" element="tns:SearchUserInfoResponse"/>
  </wsdl:message>
  <wsdl:message name="GetActiveAccountsSoapIn">
    <wsdl:part name="parameters" element="tns:GetActiveAccounts"/>
  </wsdl:message>
  <wsdl:message name="GetActiveAccountsSoapOut">
    <wsdl:part name="parameters" element="tns:GetActiveAccountsResponse"/>
  </wsdl:message>
  <wsdl:message name="GetAccountSecurityContextIdSoapIn">
    <wsdl:part name="parameters" element="tns:GetAccountSecurityContextId"/>
  </wsdl:message>
  <wsdl:message name="GetAccountSecurityContextIdSoapOut">
    <wsdl:part name="parameters" element="tns:GetAccountSecurityContextIdResponse"/>
  </wsdl:message>
  <wsdl:message name="GetAllAccountSecurityContextSoapIn">
    <wsdl:part name="parameters" element="tns:GetAllAccountSecurityContext"/>
  </wsdl:message>
  <wsdl:message name="GetAllAccountSecurityContextSoapOut">
    <wsdl:part name="parameters" element="tns:GetAllAccountSecurityContextResponse"/>
  </wsdl:message>
  <wsdl:message name="AddOfficeAdministratorSoapIn">
    <wsdl:part name="parameters" element="tns:AddOfficeAdministrator"/>
  </wsdl:message>
  <wsdl:message name="AddOfficeAdministratorSoapOut">
    <wsdl:part name="parameters" element="tns:AddOfficeAdministratorResponse"/>
  </wsdl:message>
  <wsdl:message name="UpdateClientMachineSoapIn">
    <wsdl:part name="parameters" element="tns:UpdateClientMachine"/>
  </wsdl:message>
  <wsdl:message name="UpdateClientMachineSoapOut">
    <wsdl:part name="parameters" element="tns:UpdateClientMachineResponse"/>
  </wsdl:message>
  <wsdl:message name="GetDockTypeSoapIn">
    <wsdl:part name="parameters" element="tns:GetDockType"/>
  </wsdl:message>
  <wsdl:message name="GetDockTypeSoapOut">
    <wsdl:part name="parameters" element="tns:GetDockTypeResponse"/>
  </wsdl:message>
  <wsdl:message name="LoadDocumentLayoutDatabaseSoapIn">
    <wsdl:part name="parameters" element="tns:LoadDocumentLayoutDatabase"/>
  </wsdl:message>
  <wsdl:message name="LoadDocumentLayoutDatabaseSoapOut">
    <wsdl:part name="parameters" element="tns:LoadDocumentLayoutDatabaseResponse"/>
  </wsdl:message>
  <wsdl:message name="GetLoginAuthenticationSoapIn">
    <wsdl:part name="parameters" element="tns:GetLoginAuthentication"/>
  </wsdl:message>
  <wsdl:message name="GetLoginAuthenticationAppSoapIn">
    <wsdl:part name="parameters" element="tns:GetLoginAuthenticationApp"/>
  </wsdl:message>
  <wsdl:message name="GetLoginAuthenticationClientSoapIn">
    <wsdl:part name="parameters" element="tns:GetLoginAuthenticationClient"/>
  </wsdl:message>
  <wsdl:message name="LogoutClientSoapIn">
    <wsdl:part name="parameters" element="tns:LogoutClient"/>
  </wsdl:message>
  <wsdl:message name="GetLoginAuthenticationSoapOut">
    <wsdl:part name="parameters" element="tns:GetLoginAuthenticationResponse"/>
  </wsdl:message>
  <wsdl:message name="GetLoginAuthenticationClientSoapOut">
    <wsdl:part name="parameters" element="tns:GetLoginAuthenticationClientResponse"/>
  </wsdl:message>
  <wsdl:message name="LogoutClientSoapOut">
    <wsdl:part name="parameters" element="tns:LogoutClientResponse"/>
  </wsdl:message>
  <wsdl:message name="GetServiceRolesAuthorizationListSoapIn">
    <wsdl:part name="parameters" element="tns:GetServiceRolesAuthorizationList"/>
  </wsdl:message>
  <wsdl:message name="GetServiceRolesAuthorizationListSoapOut">
    <wsdl:part name="parameters" element="tns:GetServiceRolesAuthorizationListResponse"/>
  </wsdl:message>
  <wsdl:message name="GetAccountServicesSoapIn">
    <wsdl:part name="parameters" element="tns:GetAccountServices"/>
  </wsdl:message>
  <wsdl:message name="GetAccountServicesSoapOut">
    <wsdl:part name="parameters" element="tns:GetAccountServicesResponse"/>
  </wsdl:message>
  <wsdl:message name="GetOfficeMessagesSoapIn">
    <wsdl:part name="parameters" element="tns:GetOfficeMessages"/>
  </wsdl:message>
  <wsdl:message name="GetOfficeMessagesSoapOut">
    <wsdl:part name="parameters" element="tns:GetOfficeMessagesResponse"/>
  </wsdl:message>
  <wsdl:message name="SendBroadcastMessageSoapIn">
    <wsdl:part name="parameters" element="tns:SendBroadcastMessage"/>
  </wsdl:message>
  <wsdl:message name="SendBroadcastMessageSoapOut">
    <wsdl:part name="parameters" element="tns:SendBroadcastMessageResponse"/>
  </wsdl:message>
  <wsdl:message name="SendAttachmentFileSoapIn">
    <wsdl:part name="parameters" element="tns:SendAttachmentFile"/>
  </wsdl:message>
  <wsdl:message name="SendAttachmentFileSoapOut">
    <wsdl:part name="parameters" element="tns:SendAttachmentFileResponse"/>
  </wsdl:message>
  <wsdl:message name="GetAccountRolesSoapIn">
    <wsdl:part name="parameters" element="tns:GetAccountRoles"/>
  </wsdl:message>
  <wsdl:message name="GetAccountRolesSoapOut">
    <wsdl:part name="parameters" element="tns:GetAccountRolesResponse"/>
  </wsdl:message>
  <wsdl:message name="GetSystemInfoSoapIn">
    <wsdl:part name="parameters" element="tns:GetSystemInfo"/>
  </wsdl:message>
  <wsdl:message name="GetSystemInfoSoapOut">
    <wsdl:part name="parameters" element="tns:GetSystemInfoResponse"/>
  </wsdl:message>
  <wsdl:message name="GetAppInfoSoapIn">
    <wsdl:part name="parameters" element="tns:GetAppInfo"/>
  </wsdl:message>
  <wsdl:message name="GetAppInfoSoapOut">
    <wsdl:part name="parameters" element="tns:GetAppInfoResponse"/>
  </wsdl:message>
  <wsdl:message name="GetKnowledgeAttachmentFileSoapIn">
    <wsdl:part name="parameters" element="tns:GetKnowledgeAttachmentFile"/>
  </wsdl:message>
  <wsdl:message name="GetKnowledgeAttachmentFileSoapOut">
    <wsdl:part name="parameters" element="tns:GetKnowledgeAttachmentFileResponse"/>
  </wsdl:message>
  <wsdl:message name="AddMachineSoapIn">
    <wsdl:part name="parameters" element="tns:AddMachine"/>
  </wsdl:message>
  <wsdl:message name="AddMachineSoapOut">
    <wsdl:part name="parameters" element="tns:AddMachineResponse"/>
  </wsdl:message>
  <wsdl:message name="GetAllApplicationsSoapIn">
    <wsdl:part name="parameters" element="tns:GetAllApplications"/>
  </wsdl:message>
  <wsdl:message name="GetAllApplicationsSoapOut">
    <wsdl:part name="parameters" element="tns:GetAllApplicationsResponse"/>
  </wsdl:message>
  <wsdl:message name="GetAllUncertifiedAlertSoapIn">
    <wsdl:part name="parameters" element="tns:GetAllUncertifiedAlert"/>
  </wsdl:message>
  <wsdl:message name="GetAllUncertifiedAlertSoapOut">
    <wsdl:part name="parameters" element="tns:GetAllUncertifiedAlertResponse"/>
  </wsdl:message>
  <wsdl:message name="GetNotifyKnowledgeAttachmentSoapIn">
    <wsdl:part name="parameters" element="tns:GetNotifyKnowledgeAttachment"/>
  </wsdl:message>
  <wsdl:message name="GetNotifyKnowledgeAttachmentSoapOut">
    <wsdl:part name="parameters" element="tns:GetNotifyKnowledgeAttachmentResponse"/>
  </wsdl:message>
  <wsdl:message name="GetNotifyKnowledgeSoapIn">
    <wsdl:part name="parameters" element="tns:GetNotifyKnowledge"/>
  </wsdl:message>
  <wsdl:message name="GetNotifyKnowledgeSoapOut">
    <wsdl:part name="parameters" element="tns:GetNotifyKnowledgeResponse"/>
  </wsdl:message>
  <wsdl:message name="GetCertifiedAlertImagesSoapIn">
    <wsdl:part name="parameters" element="tns:GetCertifiedAlertImages"/>
  </wsdl:message>
  <wsdl:message name="GetCertifiedAlertImagesSoapOut">
    <wsdl:part name="parameters" element="tns:GetCertifiedAlertImagesResponse"/>
  </wsdl:message>
  <wsdl:message name="CheckDigitalCertificateSoapIn">
    <wsdl:part name="parameters" element="tns:CheckDigitalCertificate"/>
  </wsdl:message>
  <wsdl:message name="CheckDigitalCertificateSoapOut">
    <wsdl:part name="parameters" element="tns:CheckDigitalCertificateResponse"/>
  </wsdl:message>
  <wsdl:message name="CheckExternalServicesSoapIn">
    <wsdl:part name="parameters" element="tns:CheckExternalServices"/>
  </wsdl:message>
  <wsdl:message name="CheckExternalServicesSoapOut">
    <wsdl:part name="parameters" element="tns:CheckExternalServicesResponse"/>
  </wsdl:message>
  <wsdl:message name="GetMachineInfoSoapIn">
    <wsdl:part name="parameters" element="tns:GetMachineInfo"/>
  </wsdl:message>
  <wsdl:message name="GetMachineInfoSoapOut">
    <wsdl:part name="parameters" element="tns:GetMachineInfoResponse"/>
  </wsdl:message>
  <wsdl:message name="GetMachineInfoAppSoapIn">
    <wsdl:part name="parameters" element="tns:GetMachineInfoApp"/>
  </wsdl:message>
  <wsdl:message name="MachineInfoSoapOut">
    <wsdl:part name="parameters" element="tns:GetMachineInfoAppResponse"/>
  </wsdl:message>
  <wsdl:message name="AddAlertSoapIn">
    <wsdl:part name="parameters" element="tns:AddAlert"/>
  </wsdl:message>
  <wsdl:message name="AddAlertSoapOut">
    <wsdl:part name="parameters" element="tns:AddAlertResponse"/>
  </wsdl:message>
  <wsdl:message name="AddAlertBySpecSoapIn">
    <wsdl:part name="parameters" element="tns:AddAlertBySpec"/>
  </wsdl:message>
  <wsdl:message name="AddAlertBySpecSoapOut">
    <wsdl:part name="parameters" element="tns:AddAlertBySpecResponse"/>
  </wsdl:message>
  <wsdl:message name="GetDocumentAlertsSoapIn">
    <wsdl:part name="parameters" element="tns:GetDocumentAlerts"/>
  </wsdl:message>
  <wsdl:message name="GetDocumentAlertsSoapOut">
    <wsdl:part name="parameters" element="tns:GetDocumentAlertsResponse"/>
  </wsdl:message>
  <wsdl:message name="GetDocumentAlertsDetailInfoSoapIn">
    <wsdl:part name="parameters" element="tns:GetDocumentAlertsDetailInfo"/>
  </wsdl:message>
  <wsdl:message name="GetDocumentAlertsDetailInfoSoapOut">
    <wsdl:part name="parameters" element="tns:GetDocumentAlertsDetailInfoResponse"/>
  </wsdl:message>
  <wsdl:message name="GetDocumentAlertsCertifiedSoapIn">
    <wsdl:part name="parameters" element="tns:GetDocumentAlertsCertified"/>
  </wsdl:message>
  <wsdl:message name="GetDocumentAlertsCertifiedSoapOut">
    <wsdl:part name="parameters" element="tns:GetDocumentAlertsCertifiedResponse"/>
  </wsdl:message>
  <wsdl:message name="CheckDbReachabilitySoapIn">
    <wsdl:part name="parameters" element="tns:CheckDbReachability"/>
  </wsdl:message>
  <wsdl:message name="CheckDbReachabilitySoapOut">
    <wsdl:part name="parameters" element="tns:CheckDbReachabilityResponse"/>
  </wsdl:message>
  <wsdl:message name="ViewLogSoapIn">
    <wsdl:part name="parameters" element="tns:ViewLog"/>
  </wsdl:message>
  <wsdl:message name="ViewLogSoapOut">
    <wsdl:part name="parameters" element="tns:ViewLogResponse"/>
  </wsdl:message>
  <wsdl:message name="UpdAlertStatusSoapIn">
    <wsdl:part name="parameters" element="tns:UpdAlertStatus"/>
  </wsdl:message>
  <wsdl:message name="UpdAlertStatusSoapOut">
    <wsdl:part name="parameters" element="tns:UpdAlertStatusResponse"/>
  </wsdl:message>
  <wsdl:message name="GetAllDocumentAlertsCertifiedSoapIn">
    <wsdl:part name="parameters" element="tns:GetAllDocumentAlertsCertified"/>
  </wsdl:message>
  <wsdl:message name="GetAllDocumentAlertsCertifiedSoapOut">
    <wsdl:part name="parameters" element="tns:GetAllDocumentAlertsCertifiedResponse"/>
  </wsdl:message>
  <wsdl:message name="TestIvis2SoapIn">
    <wsdl:part name="parameters" element="tns:TestIvis2"/>
  </wsdl:message>
  <wsdl:message name="TestIvis2SoapOut">
    <wsdl:part name="parameters" element="tns:TestIvis2Response"/>
  </wsdl:message>
  <wsdl:message name="GetDetailsByDatiSidafIdSoapIn">
    <wsdl:part name="parameters" element="tns:GetDetailsByDatiSidafId"/>
  </wsdl:message>
  <wsdl:message name="GetDetailsByDatiSidafIdSoapOut">
    <wsdl:part name="parameters" element="tns:GetDetailsByDatiSidafIdResponse"/>
  </wsdl:message>
  <wsdl:portType name="wsSIFServicesSoap">
    <wsdl:operation name="IsSysAdminInContext">
      <wsdl:documentation>Verifica se l'account è SysAdmin nel suo
                context</wsdl:documentation>
      <wsdl:input message="tns:IsSysAdminInContextSoapIn"/>
      <wsdl:output message="tns:IsSysAdminInContextSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="ValidatePassword">
      <wsdl:documentation>Convalida la password utente in modo che
                rispecchi le regole di validità</wsdl:documentation>
      <wsdl:input message="tns:ValidatePasswordSoapIn"/>
      <wsdl:output message="tns:ValidatePasswordSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="GetAccountRolesActivationList">
      <wsdl:documentation>Temp</wsdl:documentation>
      <wsdl:input message="tns:GetAccountRolesActivationListSoapIn"/>
      <wsdl:output message="tns:GetAccountRolesActivationListSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="UpdateUserPassword">
      <wsdl:documentation>Modifica la password dell'utente
                loggato.</wsdl:documentation>
      <wsdl:input message="tns:UpdateUserPasswordSoapIn"/>
      <wsdl:output message="tns:UpdateUserPasswordSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="ResetUserPassword">
      <wsdl:documentation>Resetta la password dell'utente loggato.</wsdl:documentation>
      <wsdl:input message="tns:ResetUserPasswordSoapIn"/>
      <wsdl:output message="tns:ResetUserPasswordSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="GetAdministeredOffices">
      <wsdl:documentation>Seleziona Uffici</wsdl:documentation>
      <wsdl:input message="tns:GetAdministeredOfficesSoapIn"/>
      <wsdl:output message="tns:GetAdministeredOfficesSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="GetSupervisedOffices">
      <wsdl:documentation>Seleziona Uffici</wsdl:documentation>
      <wsdl:input message="tns:GetSupervisedOfficesSoapIn"/>
      <wsdl:output message="tns:GetSupervisedOfficesSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="GetAllUsers">
      <wsdl:documentation>Riporta gli utenti censiti su SIF</wsdl:documentation>
      <wsdl:input message="tns:GetAllUsersSoapIn"/>
      <wsdl:output message="tns:GetAllUsersSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="AddOffice">
      <wsdl:documentation>Inserimento nuovo ufficio</wsdl:documentation>
      <wsdl:input message="tns:AddOfficeSoapIn"/>
      <wsdl:output message="tns:AddOfficeSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="AddDomain">
      <wsdl:documentation>Inserisce un Nuovo Dominio</wsdl:documentation>
      <wsdl:input message="tns:AddDomainSoapIn"/>
      <wsdl:output message="tns:AddDomainSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="GetAllDomains">
      <wsdl:documentation>Riporta i Domini disponibili</wsdl:documentation>
      <wsdl:input message="tns:GetAllDomainsSoapIn"/>
      <wsdl:output message="tns:GetAllDomainsSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="GetAdministeredMachines">
      <wsdl:documentation>Seleziona Postazioni negli uffici
                amministrati dall'utente</wsdl:documentation>
      <wsdl:input message="tns:GetAdministeredMachinesSoapIn"/>
      <wsdl:output message="tns:GetAdministeredMachinesSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="GetSupervisedMachines">
      <wsdl:documentation>Seleziona Postazioni negli uffici
                amministrati dall'utente</wsdl:documentation>
      <wsdl:input message="tns:GetSupervisedMachinesSoapIn"/>
      <wsdl:output message="tns:GetSupervisedMachinesSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="UpdateAccountStatus">
      <wsdl:documentation>Aggiorna lo Stato di un Account</wsdl:documentation>
      <wsdl:input message="tns:UpdateAccountStatusSoapIn"/>
      <wsdl:output message="tns:UpdateAccountStatusSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="GetApplicationAccounts">
      <wsdl:documentation>Seleziona gli account di una
                applicazione</wsdl:documentation>
      <wsdl:input message="tns:GetApplicationAccountsSoapIn"/>
      <wsdl:output message="tns:GetApplicationAccountsSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="UpdateAccountRoles">
      <wsdl:documentation>Aggiorna i ruoli di un account</wsdl:documentation>
      <wsdl:input message="tns:UpdateAccountRolesSoapIn"/>
      <wsdl:output message="tns:UpdateAccountRolesSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="UpdateRoleServices">
      <wsdl:documentation>Aggiorna i servizi associati ad un ruolo</wsdl:documentation>
      <wsdl:input message="tns:UpdateRoleServicesSoapIn"/>
      <wsdl:output message="tns:UpdateRoleServicesSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="AddApplicationRole">
      <wsdl:documentation>Crea un nuovo ruolo applicativo</wsdl:documentation>
      <wsdl:input message="tns:AddApplicationRoleSoapIn"/>
      <wsdl:output message="tns:AddApplicationRoleSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="GetAdministeredOfficesByType">
      <wsdl:documentation>Riporta gli uffici di un certo tipo
                amministrati dall utente</wsdl:documentation>
      <wsdl:input message="tns:GetAdministeredOfficesByTypeSoapIn"/>
      <wsdl:output message="tns:GetAdministeredOfficesByTypeSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="GetAllApplicationRoles">
      <wsdl:documentation>Riporta tutti i ruoli applicativi</wsdl:documentation>
      <wsdl:input message="tns:GetAllApplicationRolesSoapIn"/>
      <wsdl:output message="tns:GetAllApplicationRolesSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="GetRoleServicesAuthorizationList">
      <wsdl:documentation>I servici autorizzati per un dato ruolo</wsdl:documentation>
      <wsdl:input message="tns:GetRoleServicesAuthorizationListSoapIn"/>
      <wsdl:output message="tns:GetRoleServicesAuthorizationListSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="GetAccountCurrentStatus">
      <wsdl:documentation>Riporta lo stato di un determianto
                account</wsdl:documentation>
      <wsdl:input message="tns:GetAccountCurrentStatusSoapIn"/>
      <wsdl:output message="tns:GetAccountCurrentStatusSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="GetAccountInfo">
      <wsdl:documentation>Estrae le info di un account</wsdl:documentation>
      <wsdl:input message="tns:GetAccountInfoSoapIn"/>
      <wsdl:output message="tns:GetAccountInfoSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="GetAdministeredUserByOffice">
      <wsdl:documentation>Seleziona Utenti Amministrati in base
                all'ufficio di appartenenz</wsdl:documentation>
      <wsdl:input message="tns:GetAdministeredUserByOfficeSoapIn"/>
      <wsdl:output message="tns:GetAdministeredUserByOfficeSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="GetAdministeredMachineByOffice">
      <wsdl:documentation>Seleziona Macchine Amministrati in base
                all'ufficio di appartenenza</wsdl:documentation>
      <wsdl:input message="tns:GetAdministeredMachineByOfficeSoapIn"/>
      <wsdl:output message="tns:GetAdministeredMachineByOfficeSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="GetOfficeInfo">
      <wsdl:documentation>Estrae le info di un ufficio</wsdl:documentation>
      <wsdl:input message="tns:GetOfficeInfoSoapIn"/>
      <wsdl:output message="tns:GetOfficeInfoSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="GetDomainInfo">
      <wsdl:documentation>Ricava le Informazioni su un Dominio</wsdl:documentation>
      <wsdl:input message="tns:GetDomainInfoSoapIn"/>
      <wsdl:output message="tns:GetDomainInfoSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="UpdateUserAccount">
      <wsdl:documentation>Aggiorna un Utente dal SIF</wsdl:documentation>
      <wsdl:input message="tns:UpdateUserAccountSoapIn"/>
      <wsdl:output message="tns:UpdateUserAccountSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="GetAdministeredChildOffices">
      <wsdl:documentation>Seleziona Uffici</wsdl:documentation>
      <wsdl:input message="tns:GetAdministeredChildOfficesSoapIn"/>
      <wsdl:output message="tns:GetAdministeredChildOfficesSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="UpdateOfficeData">
      <wsdl:documentation>Aggiornamento dati ufficio</wsdl:documentation>
      <wsdl:input message="tns:UpdateOfficeDataSoapIn"/>
      <wsdl:output message="tns:UpdateOfficeDataSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="AddUser">
      <wsdl:documentation>Inserimento nuovo utente</wsdl:documentation>
      <wsdl:input message="tns:AddUserSoapIn"/>
      <wsdl:output message="tns:AddUserSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="GetAllApplicationServices">
      <wsdl:documentation>Seleziona servizi</wsdl:documentation>
      <wsdl:input message="tns:GetAllApplicationServicesSoapIn"/>
      <wsdl:output message="tns:GetAllApplicationServicesSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="GetRoleServices">
      <wsdl:documentation>Seleziona servizi di un ruolo</wsdl:documentation>
      <wsdl:input message="tns:GetRoleServicesSoapIn"/>
      <wsdl:output message="tns:GetRoleServicesSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="UpdateApplicationRole">
      <wsdl:documentation>Cancella logicamente un ruolo</wsdl:documentation>
      <wsdl:input message="tns:UpdateApplicationRoleSoapIn"/>
      <wsdl:output message="tns:UpdateApplicationRoleSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="GetAllActiveOffices">
      <wsdl:documentation>Seleziona Uffici</wsdl:documentation>
      <wsdl:input message="tns:GetAllActiveOfficesSoapIn"/>
      <wsdl:output message="tns:GetAllActiveOfficesSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="DeleteUserSecurityContext">
      <wsdl:documentation>Cancella il security context dell'utente</wsdl:documentation>
      <wsdl:input message="tns:DeleteUserSecurityContextSoapIn"/>
      <wsdl:output message="tns:DeleteUserSecurityContextSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="DeleteOffice">
      <wsdl:documentation>Cancella logicamente un ufficio</wsdl:documentation>
      <wsdl:input message="tns:DeleteOfficeSoapIn"/>
      <wsdl:output message="tns:DeleteOfficeSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="DelMachine">
      <wsdl:documentation>Cancella una macchina</wsdl:documentation>
      <wsdl:input message="tns:DelMachineSoapIn"/>
      <wsdl:output message="tns:DelMachineSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="AddUserSecurityContext">
      <wsdl:documentation>Inserimento utente esistente</wsdl:documentation>
      <wsdl:input message="tns:AddUserSecurityContextSoapIn"/>
      <wsdl:output message="tns:AddUserSecurityContextSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="SearchUserInfo">
      <wsdl:documentation>Seleziona le info di uno user</wsdl:documentation>
      <wsdl:input message="tns:SearchUserInfoSoapIn"/>
      <wsdl:output message="tns:SearchUserInfoSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="GetActiveAccounts">
      <wsdl:documentation>Seleziona le info di uno user</wsdl:documentation>
      <wsdl:input message="tns:GetActiveAccountsSoapIn"/>
      <wsdl:output message="tns:GetActiveAccountsSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="GetAccountSecurityContextId">
      <wsdl:documentation>Legge il security context di un account</wsdl:documentation>
      <wsdl:input message="tns:GetAccountSecurityContextIdSoapIn"/>
      <wsdl:output message="tns:GetAccountSecurityContextIdSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="GetAllAccountSecurityContext">
      <wsdl:input message="tns:GetAllAccountSecurityContextSoapIn"/>
      <wsdl:output message="tns:GetAllAccountSecurityContextSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="AddOfficeAdministrator">
      <wsdl:input message="tns:AddOfficeAdministratorSoapIn"/>
      <wsdl:output message="tns:AddOfficeAdministratorSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="UpdateClientMachine">
      <wsdl:documentation>Aggiorna una Macchina SIF</wsdl:documentation>
      <wsdl:input message="tns:UpdateClientMachineSoapIn"/>
      <wsdl:output message="tns:UpdateClientMachineSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="GetDockType">
      <wsdl:input message="tns:GetDockTypeSoapIn"/>
      <wsdl:output message="tns:GetDockTypeSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="LoadDocumentLayoutDatabase">
      <wsdl:documentation>Carica il nuovo document layout database
                3M</wsdl:documentation>
      <wsdl:input message="tns:LoadDocumentLayoutDatabaseSoapIn"/>
      <wsdl:output message="tns:LoadDocumentLayoutDatabaseSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="GetLoginAuthentication">
      <wsdl:documentation>Recupera i dati dell'utente.</wsdl:documentation>
      <wsdl:input message="tns:GetLoginAuthenticationSoapIn"/>
      <wsdl:output message="tns:GetLoginAuthenticationSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="GetLoginAuthenticationApp">
      <wsdl:documentation>Recupera i dati dell'utente.</wsdl:documentation>
      <wsdl:input message="tns:GetLoginAuthenticationAppSoapIn"/>
      <wsdl:output message="tns:GetLoginAuthenticationSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="GetLoginAuthenticationClient">
      <wsdl:documentation>Recupera i dati dell'utente.</wsdl:documentation>
      <wsdl:input message="tns:GetLoginAuthenticationClientSoapIn"/>
      <wsdl:output message="tns:GetLoginAuthenticationClientSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="LogoutClient">
      <wsdl:documentation>Fa il logout dell'utente.</wsdl:documentation>
      <wsdl:input message="tns:LogoutClientSoapIn"/>
      <wsdl:output message="tns:LogoutClientSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="GetServiceRolesAuthorizationList">
      <wsdl:documentation>Temp</wsdl:documentation>
      <wsdl:input message="tns:GetServiceRolesAuthorizationListSoapIn"/>
      <wsdl:output message="tns:GetServiceRolesAuthorizationListSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="GetAccountServices">
      <wsdl:documentation>Recupera i ruoli per i Servizi.</wsdl:documentation>
      <wsdl:input message="tns:GetAccountServicesSoapIn"/>
      <wsdl:output message="tns:GetAccountServicesSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="GetOfficeMessages">
      <wsdl:documentation>Ritorna la tabella dei messaggi
                broadcast</wsdl:documentation>
      <wsdl:input message="tns:GetOfficeMessagesSoapIn"/>
      <wsdl:output message="tns:GetOfficeMessagesSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="SendBroadcastMessage">
      <wsdl:documentation>Inserisce Un Messaggo Broadcast</wsdl:documentation>
      <wsdl:input message="tns:SendBroadcastMessageSoapIn"/>
      <wsdl:output message="tns:SendBroadcastMessageSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="SendAttachmentFile">
      <wsdl:documentation>Inserisce Un Allegato al Messaggo</wsdl:documentation>
      <wsdl:input message="tns:SendAttachmentFileSoapIn"/>
      <wsdl:output message="tns:SendAttachmentFileSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="GetAccountRoles">
      <wsdl:documentation>Recupera tutti i ruoli per account.</wsdl:documentation>
      <wsdl:input message="tns:GetAccountRolesSoapIn"/>
      <wsdl:output message="tns:GetAccountRolesSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="GetSystemInfo">
      <wsdl:documentation>Recupera le informazioni sullo stato del
                sistema</wsdl:documentation>
      <wsdl:input message="tns:GetSystemInfoSoapIn"/>
      <wsdl:output message="tns:GetSystemInfoSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="GetAppInfo">
      <wsdl:documentation>Recupera le informazioni sullo stato
                dell'applicazione</wsdl:documentation>
      <wsdl:input message="tns:GetAppInfoSoapIn"/>
      <wsdl:output message="tns:GetAppInfoSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="GetKnowledgeAttachmentFile">
      <wsdl:documentation>Estrae il file allegato ad un idKnoledge</wsdl:documentation>
      <wsdl:input message="tns:GetKnowledgeAttachmentFileSoapIn"/>
      <wsdl:output message="tns:GetKnowledgeAttachmentFileSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="AddMachine">
      <wsdl:documentation>Inserimento nuova postazione</wsdl:documentation>
      <wsdl:input message="tns:AddMachineSoapIn"/>
      <wsdl:output message="tns:AddMachineSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="GetAllApplications">
      <wsdl:documentation>Seleziona Applicazioni</wsdl:documentation>
      <wsdl:input message="tns:GetAllApplicationsSoapIn"/>
      <wsdl:output message="tns:GetAllApplicationsSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="GetAllUncertifiedAlert">
      <wsdl:documentation>Ritorna elenco alert non certificati</wsdl:documentation>
      <wsdl:input message="tns:GetAllUncertifiedAlertSoapIn"/>
      <wsdl:output message="tns:GetAllUncertifiedAlertSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="GetNotifyKnowledgeAttachment">
      <wsdl:documentation>Ritorna l'attach del knonowledge</wsdl:documentation>
      <wsdl:input message="tns:GetNotifyKnowledgeAttachmentSoapIn"/>
      <wsdl:output message="tns:GetNotifyKnowledgeAttachmentSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="GetNotifyKnowledge">
      <wsdl:documentation>Ritorna il knonowledge</wsdl:documentation>
      <wsdl:input message="tns:GetNotifyKnowledgeSoapIn"/>
      <wsdl:output message="tns:GetNotifyKnowledgeSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="GetCertifiedAlertImages">
      <wsdl:documentation>Ritorna le immagini dell'alert certificato
                in formato thumbnail.
                idAttaches è una string di guid idAttach separati da virgola.</wsdl:documentation>
      <wsdl:input message="tns:GetCertifiedAlertImagesSoapIn"/>
      <wsdl:output message="tns:GetCertifiedAlertImagesSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="CheckDigitalCertificate">
      <wsdl:documentation>Verifica certificato elettronico</wsdl:documentation>
      <wsdl:input message="tns:CheckDigitalCertificateSoapIn"/>
      <wsdl:output message="tns:CheckDigitalCertificateSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="CheckExternalServices">
      <wsdl:documentation>Controlla lo stato dei servizi esterni</wsdl:documentation>
      <wsdl:input message="tns:CheckExternalServicesSoapIn"/>
      <wsdl:output message="tns:CheckExternalServicesSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="GetMachineInfo">
      <wsdl:documentation>Legge le informazioni della macchina</wsdl:documentation>
      <wsdl:input message="tns:GetMachineInfoSoapIn"/>
      <wsdl:output message="tns:GetMachineInfoSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="GetMachineInfoApp">
      <wsdl:documentation>Legge le informazioni della macchina</wsdl:documentation>
      <wsdl:input message="tns:GetMachineInfoAppSoapIn"/>
      <wsdl:output message="tns:MachineInfoSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="AddAlert">
      <wsdl:documentation>Aggiunge SIDAF Alert non certificati</wsdl:documentation>
      <wsdl:input message="tns:AddAlertSoapIn"/>
      <wsdl:output message="tns:AddAlertSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="AddAlertBySpec">
      <wsdl:documentation>Aggiunge SIDAF Alert non certificati
                associandoli ad uno specimen Sidaf</wsdl:documentation>
      <wsdl:input message="tns:AddAlertBySpecSoapIn"/>
      <wsdl:output message="tns:AddAlertBySpecSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="GetDocumentAlerts">
      <wsdl:documentation>Estrae elenco alert</wsdl:documentation>
      <wsdl:input message="tns:GetDocumentAlertsSoapIn"/>
      <wsdl:output message="tns:GetDocumentAlertsSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="GetDocumentAlertsDetailInfo">
      <wsdl:documentation>Estrae elenco alert Dettagliato</wsdl:documentation>
      <wsdl:input message="tns:GetDocumentAlertsDetailInfoSoapIn"/>
      <wsdl:output message="tns:GetDocumentAlertsDetailInfoSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="GetDocumentAlertsCertified">
      <wsdl:documentation>Estrae elenco Alert Certificati</wsdl:documentation>
      <wsdl:input message="tns:GetDocumentAlertsCertifiedSoapIn"/>
      <wsdl:output message="tns:GetDocumentAlertsCertifiedSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="CheckDbReachability">
      <wsdl:documentation>Controlla lo stato della connessione al
                DB</wsdl:documentation>
      <wsdl:input message="tns:CheckDbReachabilitySoapIn"/>
      <wsdl:output message="tns:CheckDbReachabilitySoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="ViewLog">
      <wsdl:documentation>Visualizza il log degli eventi del SIF</wsdl:documentation>
      <wsdl:input message="tns:ViewLogSoapIn"/>
      <wsdl:output message="tns:ViewLogSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="UpdAlertStatus">
      <wsdl:documentation>UpdAlertStatus</wsdl:documentation>
      <wsdl:input message="tns:UpdAlertStatusSoapIn"/>
      <wsdl:output message="tns:UpdAlertStatusSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="GetAllDocumentAlertsCertified">
      <wsdl:documentation>Estrae elenco Tutti Alert Certificati</wsdl:documentation>
      <wsdl:input message="tns:GetAllDocumentAlertsCertifiedSoapIn"/>
      <wsdl:output message="tns:GetAllDocumentAlertsCertifiedSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="TestIvis2">
      <wsdl:documentation>Test</wsdl:documentation>
      <wsdl:input message="tns:TestIvis2SoapIn"/>
      <wsdl:output message="tns:TestIvis2SoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="GetDetailsByDatiSidafId">
      <wsdl:documentation>Test</wsdl:documentation>
      <wsdl:input message="tns:GetDetailsByDatiSidafIdSoapIn"/>
      <wsdl:output message="tns:GetDetailsByDatiSidafIdSoapOut"/>
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="wsSIFServicesSoap" type="tns:wsSIFServicesSoap">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http"/>
    <wsdl:operation name="IsSysAdminInContext">
      <soap:operation soapAction="http://tempuri.org/IsSysAdminInContext" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ValidatePassword">
      <soap:operation soapAction="http://tempuri.org/ValidatePassword" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAccountRolesActivationList">
      <soap:operation soapAction="http://tempuri.org/GetAccountRolesActivationList" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdateUserPassword">
      <soap:operation soapAction="http://tempuri.org/UpdateUserPassword" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ResetUserPassword">
      <soap:operation soapAction="http://tempuri.org/ResetUserPassword" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAdministeredOffices">
      <soap:operation soapAction="http://tempuri.org/GetAdministeredOffices" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetSupervisedOffices">
      <soap:operation soapAction="http://tempuri.org/GetSupervisedOffices" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAllUsers">
      <soap:operation soapAction="http://tempuri.org/GetAllUsers" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AddOffice">
      <soap:operation soapAction="http://tempuri.org/AddOffice" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AddDomain">
      <soap:operation soapAction="http://tempuri.org/AddDomain" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAllDomains">
      <soap:operation soapAction="http://tempuri.org/GetAllDomains" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAdministeredMachines">
      <soap:operation soapAction="http://tempuri.org/GetAdministeredMachines" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetSupervisedMachines">
      <soap:operation soapAction="http://tempuri.org/GetSupervisedMachines" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdateAccountStatus">
      <soap:operation soapAction="http://tempuri.org/UpdateAccountStatus" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetApplicationAccounts">
      <soap:operation soapAction="http://tempuri.org/GetApplicationAccounts" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdateAccountRoles">
      <soap:operation soapAction="http://tempuri.org/UpdateAccountRoles" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdateRoleServices">
      <soap:operation soapAction="http://tempuri.org/UpdateRoleServices" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AddApplicationRole">
      <soap:operation soapAction="http://tempuri.org/AddApplicationRole" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAdministeredOfficesByType">
      <soap:operation soapAction="http://tempuri.org/GetAdministeredOfficesByType" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAllApplicationRoles">
      <soap:operation soapAction="http://tempuri.org/GetAllApplicationRoles" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetRoleServicesAuthorizationList">
      <soap:operation soapAction="http://tempuri.org/GetRoleServicesAuthorizationList" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAccountCurrentStatus">
      <soap:operation soapAction="http://tempuri.org/GetAccountCurrentStatus" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAccountInfo">
      <soap:operation soapAction="http://tempuri.org/GetAccountInfo" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAdministeredUserByOffice">
      <soap:operation soapAction="http://tempuri.org/GetAdministeredUserByOffice" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAdministeredMachineByOffice">
      <soap:operation soapAction="http://tempuri.org/GetAdministeredMachineByOffice" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetOfficeInfo">
      <soap:operation soapAction="http://tempuri.org/GetOfficeInfo" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetDomainInfo">
      <soap:operation soapAction="http://tempuri.org/GetDomainInfo" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdateUserAccount">
      <soap:operation soapAction="http://tempuri.org/UpdateUserAccount" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAdministeredChildOffices">
      <soap:operation soapAction="http://tempuri.org/GetAdministeredChildOffices" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdateOfficeData">
      <soap:operation soapAction="http://tempuri.org/UpdateOfficeData" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AddUser">
      <soap:operation soapAction="http://tempuri.org/AddUser" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAllApplicationServices">
      <soap:operation soapAction="http://tempuri.org/GetAllApplicationServices" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetRoleServices">
      <soap:operation soapAction="http://tempuri.org/GetRoleServices" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdateApplicationRole">
      <soap:operation soapAction="http://tempuri.org/UpdateApplicationRole" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAllActiveOffices">
      <soap:operation soapAction="http://tempuri.org/GetAllActiveOffices" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteUserSecurityContext">
      <soap:operation soapAction="http://tempuri.org/DeleteUserSecurityContext" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteOffice">
      <soap:operation soapAction="http://tempuri.org/DeleteOffice" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DelMachine">
      <soap:operation soapAction="http://tempuri.org/DelMachine" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AddUserSecurityContext">
      <soap:operation soapAction="http://tempuri.org/AddUserSecurityContext" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SearchUserInfo">
      <soap:operation soapAction="http://tempuri.org/SearchUserInfo" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetActiveAccounts">
      <soap:operation soapAction="http://tempuri.org/GetActiveAccounts" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAccountSecurityContextId">
      <soap:operation soapAction="http://tempuri.org/GetAccountSecurityContextId" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAllAccountSecurityContext">
      <soap:operation soapAction="http://tempuri.org/GetAllAccountSecurityContext" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AddOfficeAdministrator">
      <soap:operation soapAction="http://tempuri.org/AddOfficeAdministrator" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdateClientMachine">
      <soap:operation soapAction="http://tempuri.org/UpdateClientMachine" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetDockType">
      <soap:operation soapAction="http://tempuri.org/GetDockType" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LoadDocumentLayoutDatabase">
      <soap:operation soapAction="http://tempuri.org/LoadDocumentLayoutDatabase" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetLoginAuthentication">
      <soap:operation soapAction="http://tempuri.org/GetLoginAuthentication" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetLoginAuthenticationApp">
      <soap:operation soapAction="http://tempuri.org/GetLoginAuthenticationApp" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetLoginAuthenticationClient">
      <soap:operation soapAction="http://tempuri.org/GetLoginAuthenticationClient" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LogoutClient">
      <soap:operation soapAction="http://tempuri.org/LogoutClient" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetServiceRolesAuthorizationList">
      <soap:operation soapAction="http://tempuri.org/GetServiceRolesAuthorizationList" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAccountServices">
      <soap:operation soapAction="http://tempuri.org/GetAccountServices" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetOfficeMessages">
      <soap:operation soapAction="http://tempuri.org/GetOfficeMessages" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SendBroadcastMessage">
      <soap:operation soapAction="http://tempuri.org/SendBroadcastMessage" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SendAttachmentFile">
      <soap:operation soapAction="http://tempuri.org/SendAttachmentFile" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAccountRoles">
      <soap:operation soapAction="http://tempuri.org/GetAccountRoles" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetSystemInfo">
      <soap:operation soapAction="http://tempuri.org/GetSystemInfo" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAppInfo">
      <soap:operation soapAction="http://tempuri.org/GetAppInfo" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetKnowledgeAttachmentFile">
      <soap:operation soapAction="http://tempuri.org/GetKnowledgeAttachmentFile" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AddMachine">
      <soap:operation soapAction="http://tempuri.org/AddMachine" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAllApplications">
      <soap:operation soapAction="http://tempuri.org/GetAllApplications" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAllUncertifiedAlert">
      <soap:operation soapAction="http://tempuri.org/GetAllUncertifiedAlert" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetNotifyKnowledgeAttachment">
      <soap:operation soapAction="http://tempuri.org/GetNotifyKnowledgeAttachment" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetNotifyKnowledge">
      <soap:operation soapAction="http://tempuri.org/GetNotifyKnowledge" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetCertifiedAlertImages">
      <soap:operation soapAction="http://tempuri.org/GetCertifiedAlertImages" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CheckDigitalCertificate">
      <soap:operation soapAction="http://tempuri.org/CheckDigitalCertificate" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CheckExternalServices">
      <soap:operation soapAction="http://tempuri.org/CheckExternalServices" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetMachineInfo">
      <soap:operation soapAction="http://tempuri.org/GetMachineInfo" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetMachineInfoApp">
      <soap:operation soapAction="http://tempuri.org/GetMachineInfoApp" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AddAlert">
      <soap:operation soapAction="http://tempuri.org/AddAlert" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AddAlertBySpec">
      <soap:operation soapAction="http://tempuri.org/AddAlertBySpec" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetDocumentAlerts">
      <soap:operation soapAction="http://tempuri.org/GetDocumentAlerts" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetDocumentAlertsDetailInfo">
      <soap:operation soapAction="http://tempuri.org/GetDocumentAlertsDetailInfo" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetDocumentAlertsCertified">
      <soap:operation soapAction="http://tempuri.org/GetDocumentAlertsCertified" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CheckDbReachability">
      <soap:operation soapAction="http://tempuri.org/CheckDbReachability" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ViewLog">
      <soap:operation soapAction="http://tempuri.org/ViewLog" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdAlertStatus">
      <soap:operation soapAction="http://tempuri.org/UpdAlertStatus" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAllDocumentAlertsCertified">
      <soap:operation soapAction="http://tempuri.org/GetAllDocumentAlertsCertified" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TestIvis2">
      <soap:operation soapAction="http://tempuri.org/TestIvis2" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetDetailsByDatiSidafId">
      <soap:operation soapAction="http://tempuri.org/GetDetailsByDatiSidafId" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="wsSIFServicesSoap12" type="tns:wsSIFServicesSoap">
    <soap12:binding transport="http://schemas.xmlsoap.org/soap/http"/>
    <wsdl:operation name="IsSysAdminInContext">
      <soap12:operation soapAction="http://tempuri.org/IsSysAdminInContext" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ValidatePassword">
      <soap12:operation soapAction="http://tempuri.org/ValidatePassword" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAccountRolesActivationList">
      <soap12:operation soapAction="http://tempuri.org/GetAccountRolesActivationList" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdateUserPassword">
      <soap12:operation soapAction="http://tempuri.org/UpdateUserPassword" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ResetUserPassword">
      <soap12:operation soapAction="http://tempuri.org/ResetUserPassword" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAdministeredOffices">
      <soap12:operation soapAction="http://tempuri.org/GetAdministeredOffices" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetSupervisedOffices">
      <soap12:operation soapAction="http://tempuri.org/GetSupervisedOffices" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAllUsers">
      <soap12:operation soapAction="http://tempuri.org/GetAllUsers" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AddOffice">
      <soap12:operation soapAction="http://tempuri.org/AddOffice" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AddDomain">
      <soap12:operation soapAction="http://tempuri.org/AddDomain" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAllDomains">
      <soap12:operation soapAction="http://tempuri.org/GetAllDomains" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAdministeredMachines">
      <soap12:operation soapAction="http://tempuri.org/GetAdministeredMachines" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetSupervisedMachines">
      <soap12:operation soapAction="http://tempuri.org/GetSupervisedMachines" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdateAccountStatus">
      <soap12:operation soapAction="http://tempuri.org/UpdateAccountStatus" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetApplicationAccounts">
      <soap12:operation soapAction="http://tempuri.org/GetApplicationAccounts" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdateAccountRoles">
      <soap12:operation soapAction="http://tempuri.org/UpdateAccountRoles" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdateRoleServices">
      <soap12:operation soapAction="http://tempuri.org/UpdateRoleServices" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AddApplicationRole">
      <soap12:operation soapAction="http://tempuri.org/AddApplicationRole" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAdministeredOfficesByType">
      <soap12:operation soapAction="http://tempuri.org/GetAdministeredOfficesByType" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAllApplicationRoles">
      <soap12:operation soapAction="http://tempuri.org/GetAllApplicationRoles" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetRoleServicesAuthorizationList">
      <soap12:operation soapAction="http://tempuri.org/GetRoleServicesAuthorizationList" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAccountCurrentStatus">
      <soap12:operation soapAction="http://tempuri.org/GetAccountCurrentStatus" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAccountInfo">
      <soap12:operation soapAction="http://tempuri.org/GetAccountInfo" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAdministeredUserByOffice">
      <soap12:operation soapAction="http://tempuri.org/GetAdministeredUserByOffice" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAdministeredMachineByOffice">
      <soap12:operation soapAction="http://tempuri.org/GetAdministeredMachineByOffice" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetOfficeInfo">
      <soap12:operation soapAction="http://tempuri.org/GetOfficeInfo" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetDomainInfo">
      <soap12:operation soapAction="http://tempuri.org/GetDomainInfo" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdateUserAccount">
      <soap12:operation soapAction="http://tempuri.org/UpdateUserAccount" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAdministeredChildOffices">
      <soap12:operation soapAction="http://tempuri.org/GetAdministeredChildOffices" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdateOfficeData">
      <soap12:operation soapAction="http://tempuri.org/UpdateOfficeData" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AddUser">
      <soap12:operation soapAction="http://tempuri.org/AddUser" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAllApplicationServices">
      <soap12:operation soapAction="http://tempuri.org/GetAllApplicationServices" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetRoleServices">
      <soap12:operation soapAction="http://tempuri.org/GetRoleServices" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdateApplicationRole">
      <soap12:operation soapAction="http://tempuri.org/UpdateApplicationRole" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAllActiveOffices">
      <soap12:operation soapAction="http://tempuri.org/GetAllActiveOffices" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteUserSecurityContext">
      <soap12:operation soapAction="http://tempuri.org/DeleteUserSecurityContext" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteOffice">
      <soap12:operation soapAction="http://tempuri.org/DeleteOffice" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DelMachine">
      <soap12:operation soapAction="http://tempuri.org/DelMachine" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AddUserSecurityContext">
      <soap12:operation soapAction="http://tempuri.org/AddUserSecurityContext" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SearchUserInfo">
      <soap12:operation soapAction="http://tempuri.org/SearchUserInfo" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetActiveAccounts">
      <soap12:operation soapAction="http://tempuri.org/GetActiveAccounts" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAccountSecurityContextId">
      <soap12:operation soapAction="http://tempuri.org/GetAccountSecurityContextId" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAllAccountSecurityContext">
      <soap12:operation soapAction="http://tempuri.org/GetAllAccountSecurityContext" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AddOfficeAdministrator">
      <soap12:operation soapAction="http://tempuri.org/AddOfficeAdministrator" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdateClientMachine">
      <soap12:operation soapAction="http://tempuri.org/UpdateClientMachine" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetDockType">
      <soap12:operation soapAction="http://tempuri.org/GetDockType" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LoadDocumentLayoutDatabase">
      <soap12:operation soapAction="http://tempuri.org/LoadDocumentLayoutDatabase" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetLoginAuthentication">
      <soap12:operation soapAction="http://tempuri.org/GetLoginAuthentication" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetLoginAuthenticationApp">
      <soap:operation soapAction="http://tempuri.org/GetLoginAuthenticationApp" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetLoginAuthenticationClient">
      <soap:operation soapAction="http://tempuri.org/GetLoginAuthenticationClient" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LogoutClient">
      <soap:operation soapAction="http://tempuri.org/LogoutClient" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetServiceRolesAuthorizationList">
      <soap12:operation soapAction="http://tempuri.org/GetServiceRolesAuthorizationList" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAccountServices">
      <soap12:operation soapAction="http://tempuri.org/GetAccountServices" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetOfficeMessages">
      <soap12:operation soapAction="http://tempuri.org/GetOfficeMessages" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SendBroadcastMessage">
      <soap12:operation soapAction="http://tempuri.org/SendBroadcastMessage" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SendAttachmentFile">
      <soap12:operation soapAction="http://tempuri.org/SendAttachmentFile" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAccountRoles">
      <soap12:operation soapAction="http://tempuri.org/GetAccountRoles" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetSystemInfo">
      <soap12:operation soapAction="http://tempuri.org/GetSystemInfo" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAppInfo">
      <soap12:operation soapAction="http://tempuri.org/GetAppInfo" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetKnowledgeAttachmentFile">
      <soap12:operation soapAction="http://tempuri.org/GetKnowledgeAttachmentFile" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AddMachine">
      <soap12:operation soapAction="http://tempuri.org/AddMachine" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAllApplications">
      <soap12:operation soapAction="http://tempuri.org/GetAllApplications" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAllUncertifiedAlert">
      <soap12:operation soapAction="http://tempuri.org/GetAllUncertifiedAlert" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetNotifyKnowledgeAttachment">
      <soap12:operation soapAction="http://tempuri.org/GetNotifyKnowledgeAttachment" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetNotifyKnowledge">
      <soap12:operation soapAction="http://tempuri.org/GetNotifyKnowledge" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetCertifiedAlertImages">
      <soap12:operation soapAction="http://tempuri.org/GetCertifiedAlertImages" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CheckDigitalCertificate">
      <soap12:operation soapAction="http://tempuri.org/CheckDigitalCertificate" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CheckExternalServices">
      <soap12:operation soapAction="http://tempuri.org/CheckExternalServices" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetMachineInfo">
      <soap12:operation soapAction="http://tempuri.org/GetMachineInfo" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetMachineInfoApp">
      <soap:operation soapAction="http://tempuri.org/GetMachineInfoApp" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AddAlert">
      <soap12:operation soapAction="http://tempuri.org/AddAlert" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AddAlertBySpec">
      <soap12:operation soapAction="http://tempuri.org/AddAlertBySpec" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetDocumentAlerts">
      <soap12:operation soapAction="http://tempuri.org/GetDocumentAlerts" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetDocumentAlertsDetailInfo">
      <soap12:operation soapAction="http://tempuri.org/GetDocumentAlertsDetailInfo" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetDocumentAlertsCertified">
      <soap12:operation soapAction="http://tempuri.org/GetDocumentAlertsCertified" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CheckDbReachability">
      <soap12:operation soapAction="http://tempuri.org/CheckDbReachability" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ViewLog">
      <soap12:operation soapAction="http://tempuri.org/ViewLog" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdAlertStatus">
      <soap12:operation soapAction="http://tempuri.org/UpdAlertStatus" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAllDocumentAlertsCertified">
      <soap12:operation soapAction="http://tempuri.org/GetAllDocumentAlertsCertified" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TestIvis2">
      <soap12:operation soapAction="http://tempuri.org/TestIvis2" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetDetailsByDatiSidafId">
      <soap12:operation soapAction="http://tempuri.org/GetDetailsByDatiSidafId" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="wsSIFServices">
    <wsdl:port name="wsSIFServicesSoap" binding="tns:wsSIFServicesSoap">
      <soap:address location="https://sifint-coll.cen.poliziadistato.it/Sif-WS-Service/sif/wsSIFServicesSoap"/>
    </wsdl:port>
    <wsdl:port name="wsSIFServicesSoap12" binding="tns:wsSIFServicesSoap12">
      <soap12:address location="https://sifint-coll.cen.poliziadistato.it/Sif-WS-Service/sif/wsSIFServicesSoap\"/>
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>]]></con:content><con:type>http://schemas.xmlsoap.org/wsdl/</con:type></con:part></con:definitionCache><con:endpoints><con:endpoint>http://Manuel:8091/</con:endpoint><con:endpoint>https://sifint-coll.cen.poliziadistato.it/Sif-WS-Service/sif/wsSIFServicesSoap\</con:endpoint></con:endpoints><con:operation id="fbb824c0-3020-4807-9d20-333d74ee5fde" isOneWay="false" action="http://tempuri.org/AddAlert" name="AddAlert" bindingOperationName="AddAlert" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="b06a126c-2342-47f4-8fd1-e85c32379045" isOneWay="false" action="http://tempuri.org/AddAlertBySpec" name="AddAlertBySpec" bindingOperationName="AddAlertBySpec" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="f54a464b-422b-415f-b6c7-b8b67b96ec80" isOneWay="false" action="http://tempuri.org/AddApplicationRole" name="AddApplicationRole" bindingOperationName="AddApplicationRole" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="f3a61516-e0f3-4856-bff0-bc30c2cf31e3" isOneWay="false" action="http://tempuri.org/AddDomain" name="AddDomain" bindingOperationName="AddDomain" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="1861679d-a6a1-4fbb-b65d-9921fbb01646" isOneWay="false" action="http://tempuri.org/AddMachine" name="AddMachine" bindingOperationName="AddMachine" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="46bd5cf9-7858-4da6-a2fa-15e537d321bb" isOneWay="false" action="http://tempuri.org/AddOffice" name="AddOffice" bindingOperationName="AddOffice" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="211cd852-89b3-41f9-bc16-d7325784632d" isOneWay="false" action="http://tempuri.org/AddOfficeAdministrator" name="AddOfficeAdministrator" bindingOperationName="AddOfficeAdministrator" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="b5bbe08c-4ad2-46c7-99eb-48042dc1c3d5" isOneWay="false" action="http://tempuri.org/AddUser" name="AddUser" bindingOperationName="AddUser" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="c3cead79-2013-4e82-a2bc-802933a6fe9d" isOneWay="false" action="http://tempuri.org/AddUserSecurityContext" name="AddUserSecurityContext" bindingOperationName="AddUserSecurityContext" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="8f8b7ded-33d3-466b-8748-e4385cfe02e8" isOneWay="false" action="http://tempuri.org/CheckDbReachability" name="CheckDbReachability" bindingOperationName="CheckDbReachability" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="f73fd788-825d-4fc6-8460-836ec3c831de" isOneWay="false" action="http://tempuri.org/CheckDigitalCertificate" name="CheckDigitalCertificate" bindingOperationName="CheckDigitalCertificate" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="87e8ca0c-3462-45ff-b33f-e4eee9ed1df7" isOneWay="false" action="http://tempuri.org/CheckExternalServices" name="CheckExternalServices" bindingOperationName="CheckExternalServices" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="be598f68-3b61-45ec-985b-0a0831e900fc" isOneWay="false" action="http://tempuri.org/DeleteOffice" name="DeleteOffice" bindingOperationName="DeleteOffice" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="4e856a56-5c29-4a32-8423-5a16b1452d00" isOneWay="false" action="http://tempuri.org/DeleteUserSecurityContext" name="DeleteUserSecurityContext" bindingOperationName="DeleteUserSecurityContext" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="c49ebcdd-19e6-4df0-a37c-e4d3497a4a53" isOneWay="false" action="http://tempuri.org/DelMachine" name="DelMachine" bindingOperationName="DelMachine" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="ed4a1486-9250-4f48-b748-14b927645b78" isOneWay="false" action="http://tempuri.org/GetAccountCurrentStatus" name="GetAccountCurrentStatus" bindingOperationName="GetAccountCurrentStatus" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="93faa6d4-da67-4db4-a40c-ab10d3c5f996" isOneWay="false" action="http://tempuri.org/GetAccountInfo" name="GetAccountInfo" bindingOperationName="GetAccountInfo" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="4720a9e7-dd7e-4570-9836-f1d9a0a8e805" isOneWay="false" action="http://tempuri.org/GetAccountRoles" name="GetAccountRoles" bindingOperationName="GetAccountRoles" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="a51c9df3-73e4-431d-9d87-240cf6167e10" isOneWay="false" action="http://tempuri.org/GetAccountRolesActivationList" name="GetAccountRolesActivationList" bindingOperationName="GetAccountRolesActivationList" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="0fcfd244-c543-4426-866a-a67d29145ed2" isOneWay="false" action="http://tempuri.org/GetAccountSecurityContextId" name="GetAccountSecurityContextId" bindingOperationName="GetAccountSecurityContextId" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="572e57e8-ef8a-4e15-be56-f1c7438a130d" isOneWay="false" action="http://tempuri.org/GetAccountServices" name="GetAccountServices" bindingOperationName="GetAccountServices" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="085ca261-2cf5-4979-9723-d35647bbc7d3" isOneWay="false" action="http://tempuri.org/GetActiveAccounts" name="GetActiveAccounts" bindingOperationName="GetActiveAccounts" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="756cd973-2942-4e85-8fec-605a021c2cc9" isOneWay="false" action="http://tempuri.org/GetAdministeredChildOffices" name="GetAdministeredChildOffices" bindingOperationName="GetAdministeredChildOffices" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="23374d86-e9e3-499c-a11b-5480ece77323" isOneWay="false" action="http://tempuri.org/GetAdministeredMachineByOffice" name="GetAdministeredMachineByOffice" bindingOperationName="GetAdministeredMachineByOffice" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="5ec120fe-4f0e-419d-9e47-a0e1ce654917" isOneWay="false" action="http://tempuri.org/GetAdministeredMachines" name="GetAdministeredMachines" bindingOperationName="GetAdministeredMachines" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="5f70fc39-ee77-4bac-9d57-b83daaf2636d" isOneWay="false" action="http://tempuri.org/GetAdministeredOffices" name="GetAdministeredOffices" bindingOperationName="GetAdministeredOffices" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="1351bb7a-5b8f-4245-a5c6-7334307b6bcb" isOneWay="false" action="http://tempuri.org/GetAdministeredOfficesByType" name="GetAdministeredOfficesByType" bindingOperationName="GetAdministeredOfficesByType" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="99a4d3be-fa0a-4cbf-8240-761d12bbae88" isOneWay="false" action="http://tempuri.org/GetAdministeredUserByOffice" name="GetAdministeredUserByOffice" bindingOperationName="GetAdministeredUserByOffice" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="7035d78e-8ee2-4421-a85d-a647ed36c802" isOneWay="false" action="http://tempuri.org/GetAllAccountSecurityContext" name="GetAllAccountSecurityContext" bindingOperationName="GetAllAccountSecurityContext" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="72912c4f-17b1-4155-9551-a8a63ecc9511" isOneWay="false" action="http://tempuri.org/GetAllActiveOffices" name="GetAllActiveOffices" bindingOperationName="GetAllActiveOffices" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="b4f17801-71e7-4ef7-994c-2f9df6cb089c" isOneWay="false" action="http://tempuri.org/GetAllApplicationRoles" name="GetAllApplicationRoles" bindingOperationName="GetAllApplicationRoles" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="f9968e32-9bda-48e4-acef-52742c4bbb13" isOneWay="false" action="http://tempuri.org/GetAllApplications" name="GetAllApplications" bindingOperationName="GetAllApplications" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="7930361a-55f8-4e92-b422-b0ab7c7e5db3" isOneWay="false" action="http://tempuri.org/GetAllApplicationServices" name="GetAllApplicationServices" bindingOperationName="GetAllApplicationServices" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="c90f98c8-309b-47a8-a300-b0a21a01814b" isOneWay="false" action="http://tempuri.org/GetAllDocumentAlertsCertified" name="GetAllDocumentAlertsCertified" bindingOperationName="GetAllDocumentAlertsCertified" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="5f9624d7-4daa-4144-a8b7-e7c13d4ac4cb" isOneWay="false" action="http://tempuri.org/GetAllDomains" name="GetAllDomains" bindingOperationName="GetAllDomains" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="babddfe7-381b-4092-97de-75fbc9264f11" isOneWay="false" action="http://tempuri.org/GetAllUncertifiedAlert" name="GetAllUncertifiedAlert" bindingOperationName="GetAllUncertifiedAlert" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="11d20a9f-aac4-4309-8894-7d1d3d3badea" isOneWay="false" action="http://tempuri.org/GetAllUsers" name="GetAllUsers" bindingOperationName="GetAllUsers" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="247aa860-3743-430b-b429-3f50ff2ede11" isOneWay="false" action="http://tempuri.org/GetAppInfo" name="GetAppInfo" bindingOperationName="GetAppInfo" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="d933b1f6-8684-4558-a6fa-301ada109df2" isOneWay="false" action="http://tempuri.org/GetApplicationAccounts" name="GetApplicationAccounts" bindingOperationName="GetApplicationAccounts" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="e13fb0d6-b586-4e85-aa35-da80d80f5f17" isOneWay="false" action="http://tempuri.org/GetCertifiedAlertImages" name="GetCertifiedAlertImages" bindingOperationName="GetCertifiedAlertImages" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="5b9c1207-d03d-4457-b12b-da3cd223d26b" isOneWay="false" action="http://tempuri.org/GetDetailsByDatiSidafId" name="GetDetailsByDatiSidafId" bindingOperationName="GetDetailsByDatiSidafId" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="e3637447-d574-4ca4-88d9-8b0c6d5c0f64" isOneWay="false" action="http://tempuri.org/GetDockType" name="GetDockType" bindingOperationName="GetDockType" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="ce42994a-c9bf-4efb-b525-1c9155503a84" isOneWay="false" action="http://tempuri.org/GetDocumentAlerts" name="GetDocumentAlerts" bindingOperationName="GetDocumentAlerts" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="ecb09c40-f5e0-4a6b-b8da-15ff4957ed25" isOneWay="false" action="http://tempuri.org/GetDocumentAlertsCertified" name="GetDocumentAlertsCertified" bindingOperationName="GetDocumentAlertsCertified" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="1239c58e-0f50-477b-9040-419c39cfe187" isOneWay="false" action="http://tempuri.org/GetDocumentAlertsDetailInfo" name="GetDocumentAlertsDetailInfo" bindingOperationName="GetDocumentAlertsDetailInfo" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="7ccd4e85-2e21-4833-be37-ae29af426076" isOneWay="false" action="http://tempuri.org/GetDomainInfo" name="GetDomainInfo" bindingOperationName="GetDomainInfo" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="e3e9af0a-f94c-4e29-a037-0140be24876b" isOneWay="false" action="http://tempuri.org/GetKnowledgeAttachmentFile" name="GetKnowledgeAttachmentFile" bindingOperationName="GetKnowledgeAttachmentFile" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="f00475fc-9bc1-4100-b742-08ea54cd6c97" isOneWay="false" action="http://tempuri.org/GetLoginAuthentication" name="GetLoginAuthentication" bindingOperationName="GetLoginAuthentication" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="848f24d4-acb8-43be-baee-b45afc2b9906" isOneWay="false" action="http://tempuri.org/GetLoginAuthenticationApp" name="GetLoginAuthenticationApp" bindingOperationName="GetLoginAuthenticationApp" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="36b84867-abec-472d-9027-908133ac4f40" isOneWay="false" action="http://tempuri.org/GetLoginAuthenticationClient" name="GetLoginAuthenticationClient" bindingOperationName="GetLoginAuthenticationClient" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="b7b5d64c-5607-4c71-9b2d-722898bc6e57" isOneWay="false" action="http://tempuri.org/GetMachineInfo" name="GetMachineInfo" bindingOperationName="GetMachineInfo" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="3ede4b33-9b21-4920-97d5-f256ed9943fd" isOneWay="false" action="http://tempuri.org/GetMachineInfoApp" name="GetMachineInfoApp" bindingOperationName="GetMachineInfoApp" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="f8c24659-12ab-4a16-9dc3-a5a82332f05a" isOneWay="false" action="http://tempuri.org/GetNotifyKnowledge" name="GetNotifyKnowledge" bindingOperationName="GetNotifyKnowledge" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="3277334f-b70c-44e3-970e-eddc7c2ea6a4" isOneWay="false" action="http://tempuri.org/GetNotifyKnowledgeAttachment" name="GetNotifyKnowledgeAttachment" bindingOperationName="GetNotifyKnowledgeAttachment" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="1df11610-38b5-4420-b96e-fd515c37afec" isOneWay="false" action="http://tempuri.org/GetOfficeInfo" name="GetOfficeInfo" bindingOperationName="GetOfficeInfo" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="8b7791bc-e237-4581-bc72-d5bb17b3a330" isOneWay="false" action="http://tempuri.org/GetOfficeMessages" name="GetOfficeMessages" bindingOperationName="GetOfficeMessages" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="4bb34c57-d3c9-4e2e-a06b-b7428c4e638d" isOneWay="false" action="http://tempuri.org/GetRoleServices" name="GetRoleServices" bindingOperationName="GetRoleServices" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="648267e7-9c8b-42ba-b96e-342994970a25" isOneWay="false" action="http://tempuri.org/GetRoleServicesAuthorizationList" name="GetRoleServicesAuthorizationList" bindingOperationName="GetRoleServicesAuthorizationList" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="6e28e172-61d2-4fe1-80a5-c937edacacc7" isOneWay="false" action="http://tempuri.org/GetServiceRolesAuthorizationList" name="GetServiceRolesAuthorizationList" bindingOperationName="GetServiceRolesAuthorizationList" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="3a6f44d2-3014-4f2a-bb49-d83aaeb33cee" isOneWay="false" action="http://tempuri.org/GetSupervisedMachines" name="GetSupervisedMachines" bindingOperationName="GetSupervisedMachines" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="1022c1f1-681b-4462-969a-f7b645c56389" isOneWay="false" action="http://tempuri.org/GetSupervisedOffices" name="GetSupervisedOffices" bindingOperationName="GetSupervisedOffices" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="fe3b1871-b5cb-434f-89cc-1cce8247e1c4" isOneWay="false" action="http://tempuri.org/GetSystemInfo" name="GetSystemInfo" bindingOperationName="GetSystemInfo" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="4f2fbb79-4351-4ab5-aa49-5a184a301666" isOneWay="false" action="http://tempuri.org/IsSysAdminInContext" name="IsSysAdminInContext" bindingOperationName="IsSysAdminInContext" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="823d3846-4d82-4287-beb6-4dacadb703eb" isOneWay="false" action="http://tempuri.org/LoadDocumentLayoutDatabase" name="LoadDocumentLayoutDatabase" bindingOperationName="LoadDocumentLayoutDatabase" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="7610c9f4-f37e-4108-8d13-c852f0e63994" isOneWay="false" action="http://tempuri.org/LogoutClient" name="LogoutClient" bindingOperationName="LogoutClient" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="d2b6762e-3033-4183-b2b5-9eb227ec0307" isOneWay="false" action="http://tempuri.org/ResetUserPassword" name="ResetUserPassword" bindingOperationName="ResetUserPassword" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="baf834cd-afdf-4865-aa0c-555e53b82f17" isOneWay="false" action="http://tempuri.org/SearchUserInfo" name="SearchUserInfo" bindingOperationName="SearchUserInfo" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="74af1700-5876-46bf-92bd-aa7f9efed0ba" isOneWay="false" action="http://tempuri.org/SendAttachmentFile" name="SendAttachmentFile" bindingOperationName="SendAttachmentFile" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="73d549df-0ff4-4436-9e68-c746df99d66b" isOneWay="false" action="http://tempuri.org/SendBroadcastMessage" name="SendBroadcastMessage" bindingOperationName="SendBroadcastMessage" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="e4e9c1d9-995c-4813-8e62-81e68ac4af83" isOneWay="false" action="http://tempuri.org/TestIvis2" name="TestIvis2" bindingOperationName="TestIvis2" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="d28420ff-48e9-4153-89f1-27591b782518" isOneWay="false" action="http://tempuri.org/UpdAlertStatus" name="UpdAlertStatus" bindingOperationName="UpdAlertStatus" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="a300f0f6-88e1-430d-b65d-f78968eb45b9" isOneWay="false" action="http://tempuri.org/UpdateAccountRoles" name="UpdateAccountRoles" bindingOperationName="UpdateAccountRoles" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="52f87527-5134-4bca-b2e6-5f1eaeb6661d" isOneWay="false" action="http://tempuri.org/UpdateAccountStatus" name="UpdateAccountStatus" bindingOperationName="UpdateAccountStatus" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="4107c10e-9cfe-4924-8744-7c08ba6189c7" isOneWay="false" action="http://tempuri.org/UpdateApplicationRole" name="UpdateApplicationRole" bindingOperationName="UpdateApplicationRole" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="d2626d95-11b3-452c-81ae-abdcf01f8a3c" isOneWay="false" action="http://tempuri.org/UpdateClientMachine" name="UpdateClientMachine" bindingOperationName="UpdateClientMachine" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="6d170e5a-5874-4836-8167-e772f9b4c9a7" isOneWay="false" action="http://tempuri.org/UpdateOfficeData" name="UpdateOfficeData" bindingOperationName="UpdateOfficeData" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="963b1b41-5efa-4054-baad-3d0e9498f1e7" isOneWay="false" action="http://tempuri.org/UpdateRoleServices" name="UpdateRoleServices" bindingOperationName="UpdateRoleServices" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="f3f35c22-0b6b-45e5-906d-2e22de7d7225" isOneWay="false" action="http://tempuri.org/UpdateUserAccount" name="UpdateUserAccount" bindingOperationName="UpdateUserAccount" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="2a8922c5-a187-4254-8aba-fa1ddfd47092" isOneWay="false" action="http://tempuri.org/UpdateUserPassword" name="UpdateUserPassword" bindingOperationName="UpdateUserPassword" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="70c9754f-9398-452b-94da-84e4d31ac2d1" isOneWay="false" action="http://tempuri.org/ValidatePassword" name="ValidatePassword" bindingOperationName="ValidatePassword" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation><con:operation id="0bc9d23b-c95c-4acd-85c5-9aa2cf4c435b" isOneWay="false" action="http://tempuri.org/ViewLog" name="ViewLog" bindingOperationName="ViewLog" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation></con:interface><con:testSuite id="9be8b7be-c0b7-47be-81ab-513830f5d248" name="LOGIN"><con:settings/><con:runType>SEQUENTIAL</con:runType><con:testCase id="1609e72e-857f-457a-a934-efc8357f1ecd" failOnError="true" failTestCaseOnErrors="true" keepSession="false" maxResults="0" name="GetLoginAuthenticationClient TestCase" searchProperties="true"><con:settings/><con:testStep type="request" id="700a9631-1443-4f63-8bb7-aa9735122228" name="GetLoginAuthenticationClient"><con:settings/><con:config xsi:type="con:RequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:interface>wsSIFServicesSoap12</con:interface><con:operation>GetLoginAuthenticationClient</con:operation><con:request name="GetLoginAuthenticationClient" id="3c04bde1-3719-4097-9dea-17a5404654ea"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:encoding>UTF-8</con:encoding><con:endpoint>https://sifint-coll.cen.poliziadistato.it/Sif-WS-Service/sif/wsSIFServicesSoap</con:endpoint><con:request><![CDATA[<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope" xmlns:tem="http://tempuri.org/">
   <soap:Header/>
   <soap:Body>
      <tem:GetLoginAuthenticationClient>
         <tem:accountName>eescatania</tem:accountName>
         <tem:password>MWU0MGMyYjA2ZGZhNWIyZjY4ZDk5YWY1YWQ4Y2JiYjM=</tem:password>  <!-- Init0000.3 -->
         <tem:application>ABC-Immigrazione</tem:application>
         <tem:idMachine>PS-KIOSK-0700603001A</tem:idMachine>   <!-- idMachine kiosk E01 -->
      </tem:GetLoginAuthenticationClient>
   </soap:Body>
</soap:Envelope>]]></con:request><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:wsaConfig action="http://tempuri.org/wsSIFServicesSoap/GetLoginAuthenticationClientRequest" mustUnderstand="NONE" version="200508"/><con:wsrmConfig version="1.2"/></con:request></con:config></con:testStep><con:properties/></con:testCase><con:properties/></con:testSuite><con:mockService id="0673bc7d-1ea8-482b-a854-a8a48712c933" port="8091" path="/" host="localhost" name="LOGIN MOCK SERVICE" bindToHostOnly="false" docroot=""><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.mock.WsdlMockService@require-soap-action">false</con:setting></con:settings><con:properties/><con:mockOperation name="GetLoginAuthenticationClient" id="347de4a1-6fed-4ca4-a1cd-a668d3383ad7" interface="wsSIFServicesSoap12" operation="GetLoginAuthenticationClient"><con:settings/><con:defaultResponse>Response 1</con:defaultResponse><con:dispatchStyle>SEQUENCE</con:dispatchStyle><con:response name="Response 1" id="97e688cd-28ac-4068-827f-3b24a1b59b9f" httpResponseStatus="200" encoding="UTF-8"><con:settings/><con:responseContent><![CDATA[<env:Envelope xmlns:env="http://www.w3.org/2003/05/soap-envelope">
   <env:Body>
      <ns2:GetLoginAuthenticationClientResponse xmlns:ns2="http://tempuri.org/">
         <ns2:GetLoginAuthenticationClientResult>
            <ns2:Status>Active</ns2:Status>
            <ns2:DateOfExpire>2024-12-23T00:12:00.000+01:00</ns2:DateOfExpire>
            <ns2:PasswordExpirationDate>2024-11-27T18:04:36.000+01:00</ns2:PasswordExpirationDate>
            <ns2:IsAuthenticated>true</ns2:IsAuthenticated>
            <ns2:UserName>eesbrindisi</ns2:UserName>
            <ns2:MaxInvalidPasswordAttempts>0</ns2:MaxInvalidPasswordAttempts>
            <ns2:LastLoginDateTime>2024-11-15T10:57:40.000+02:00</ns2:LastLoginDateTime>
            <ns2:Security>
               <ns2:idMachine>PS-KIOSK-0900403003A</ns2:idMachine>
               <ns2:SecurityToken>BCyiV8cBCg1ZN8cu2M3a6A6/CqTEGlhFja79ujN0AN0FioCxB3nRBXRobNx3owwlgEMcRDYWYq0guWvx7wz/eZicRrSlMC0CWTjCHTEtweLaB+x3HB9dJohNsDm3/0ld5//h1dD3MWNB0uwb1ygaNoVh3xrc8+qmcRk7ZYgHI0Ear6EzrYPnujTkrvVfFfz6VjhDUcGjrdDbXgjZCHV66C5wy5hltQXsUIrmgQzw4mtro+XTHyUWiWPLMKTVCPvbN6XBFx7fH05iMjE3SPHDmgFzVXKAqO+7Rt0JXnIMJKsZgx7/T5dqwuScW4On4Tzvb7b3IRqL+mR/GdpbEhZYA0HRO6WIsSZ5M6WmAJdlrIaVBu4moczeualtQt08zgixw61vObQ8p2lAk7MdDaMeX26tWZmZRvfKe6F0fROCLeE8B98YZS4tJuckOZcG6Xcxk9F1wRNDRZlCvtbvdjZXLJQXkPMbge94qpQwyLhjKugOTli++Jq3O57uPlqxi+xfLSH1FY7RjvcAHUjnrc6b9bThCBa1cknxAGisv954XBa6VUeIzmm8xxBNDH9nF+YI83TdxcADCaIk7UYhPsgVnT+/+FDyC9jsfYCNYIpbyiwLRGYxZ/XbZgeuZAVXOFqKZvJjxSFjMFItbdvHvgpoFg==</ns2:SecurityToken>
            </ns2:Security>
         </ns2:GetLoginAuthenticationClientResult>
      </ns2:GetLoginAuthenticationClientResponse>
   </env:Body>
</env:Envelope>]]></con:responseContent><con:wsaConfig mustUnderstand="NONE" version="200508" action="http://tempuri.org/wsSIFServicesSoap/GetLoginAuthenticationClientResponse"/></con:response><con:dispatchConfig/></con:mockOperation></con:mockService><con:properties/><con:wssContainer/><con:oAuth2ProfileContainer/><con:oAuth1ProfileContainer/><con:sensitiveInformation/></con:soapui-project>