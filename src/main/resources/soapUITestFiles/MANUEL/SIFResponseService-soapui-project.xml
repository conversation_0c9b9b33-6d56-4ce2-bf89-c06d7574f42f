<?xml version="1.0" encoding="UTF-8"?>
<con:soapui-project id="77992bf8-55d0-44d6-856e-dfe391b0134d" activeEnvironment="Default" name="SIFResponseService" resourceRoot="" soapui-version="5.7.2" abortOnError="false" runType="SEQUENTIAL" xmlns:con="http://eviware.com/soapui/config"><con:settings/><con:interface xsi:type="con:WsdlInterface" id="d90c3b31-196c-49bd-904a-e375615440fb" wsaVersion="NONE" name="SIFResponseServiceSOAP" type="wsdl" bindingName="{http://sifresponseservice.sif.it/}SIFResponseServiceSOAP" soapVersion="1_1" anonymous="optional" definition="file:/C:/Users/<USER>/Desktop/GIT%20REPO/gateway_ees/src/main/resources/soapFiles/SIFResponseServices/SIFResponseService.wsdl" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:settings/><con:definitionCache type="TEXT" rootPart="file:\C:\Users\<USER>\Desktop\GIT%20REPO\gateway_ees\src\main\resources\soapFiles\SIFResponseServices\SIFResponseService.wsdl"><con:part><con:url>file:\C:\Users\<USER>\Desktop\GIT%20REPO\gateway_ees\src\main\resources\soapFiles\SIFResponseServices\SIFResponseService.wsdl</con:url><con:content><![CDATA[<wsdl:definitions name="SIFResponseService" targetNamespace="http://sifresponseservice.sif.it/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tns="http://sifresponseservice.sif.it/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <wsdl:types>
    <xsd:schema targetNamespace="http://sifresponseservice.sif.it/">
      <xsd:include schemaLocation="RetrieveEESA.xsd"/>
      <xsd:element name="SifHeader" type="tns:SifHeaderType"/>
    </xsd:schema>
  </wsdl:types>
  <wsdl:message name="NewOperationRequest">
    <wsdl:part element="tns:RetrieveEESAsyncRequest" name="parameters"/>
  </wsdl:message>
  <wsdl:message name="NewOperationResponse">
    <wsdl:part element="tns:RetrieveEESAsyncResponse" name="parameters"/>
  </wsdl:message>
  <wsdl:message name="SifHeader">
    <wsdl:part name="header" element="tns:SifHeader"/>
  </wsdl:message>
  <wsdl:portType name="SIFResponseService">
    <wsdl:operation name="GetResponse">
      <wsdl:input message="tns:NewOperationRequest"/>
      <wsdl:output message="tns:NewOperationResponse"/>
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="SIFResponseServiceSOAP" type="tns:SIFResponseService">
    <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
    <wsdl:operation name="GetResponse">
      <soap:operation soapAction="http://sifresponseservice.sif.it/NewOperation"/>
      <wsdl:input>
        <soap:header use="literal" message="tns:SifHeader" part="header"/>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="SIFResponseService">
    <wsdl:port binding="tns:SIFResponseServiceSOAP" name="SIFResponseServiceSOAP">
      <soap:address location="https://sifee-coll.cen.poliziadistato.it/ct/sif-async"/>
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>]]></con:content><con:type>http://schemas.xmlsoap.org/wsdl/</con:type></con:part><con:part><con:url>file:\C:\Users\<USER>\Desktop\GIT%20REPO\gateway_ees\src\main\resources\soapFiles\SIFResponseServices\RetrieveEESA.xsd</con:url><con:content><![CDATA[<xsd:schema targetNamespace="http://sifresponseservice.sif.it/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:tns="http://sifresponseservice.sif.it/" xmlns:ns="http://sifresponseservice.sif.it/stato/">
  <xsd:import namespace="http://sifresponseservice.sif.it/stato/" schemaLocation="./EnumStructureType.xsd"/>
  <xsd:element name="RetrieveEESAsyncResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="out" type="xsd:string"/>
        <xsd:element name="ReturnCodes" type="tns:ReturnCodes" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="RetrieveEESAsyncRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="idTransaction" type="xsd:string"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="SifHeaderType">
    <xsd:sequence>
      <xsd:element name="idUser" type="xsd:string" minOccurs="1"/>
      <xsd:element name="idChiamante" type="xsd:string" minOccurs="1"/>
      <xsd:element name="icd" type="xsd:string" minOccurs="1"/>
      <xsd:element name="offline" type="xsd:boolean" minOccurs="0"/>
      <xsd:element name="numeroDocumento" type="xsd:string" minOccurs="1"/>
      <xsd:element name="fi" type="xsd:boolean" minOccurs="0"/>
      <xsd:element name="fp" type="xsd:boolean" minOccurs="0"/>
      <xsd:element name="stato" type="ns:statoType" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="operazione" type="xsd:string" minOccurs="1"/>
      <xsd:element name="tipoOperazione" type="xsd:string" minOccurs="0"/>
      <xsd:element name="securityToken" type="xsd:string" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="ReturnCodes">
    <xsd:sequence>
      <xsd:element name="ErrorCodes" minOccurs="0">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="ErrorCode" type="tns:ST14_ErrorCodeType" maxOccurs="unbounded"/>
          </xsd:sequence>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="WarningCodes" minOccurs="0">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="WarningCode" type="tns:ST15_WarningCodeType" maxOccurs="unbounded"/>
          </xsd:sequence>
        </xsd:complexType>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:simpleType name="ST14_ErrorCodeType">
    <xsd:annotation>
      <xsd:documentation>Description: This table defines the possible error codes
                within the Response Code Information structure.</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="tns:EnumerationEntryRD"/>
  </xsd:simpleType>
  <xsd:simpleType name="ST15_WarningCodeType">
    <xsd:annotation>
      <xsd:documentation>Description: This table defines the possible warning
                codes within the Response Code Information structure.</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="tns:EnumerationEntryRD"/>
  </xsd:simpleType>
  <xsd:simpleType name="EnumerationEntryRD">
    <xsd:annotation>
      <xsd:documentation>Description: Format for the catalog entry of the code table.</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:length value="4"/>
      <xsd:pattern value="\d{4}"/>
    </xsd:restriction>
  </xsd:simpleType>
  <!--<xsd:element name="RetrieveEESAsyncResponse" type="xsd:string"></xsd:element>-->
</xsd:schema>]]></con:content><con:type>http://www.w3.org/2001/XMLSchema</con:type></con:part><con:part><con:url>file:\C:\Users\<USER>\Desktop\GIT%20REPO\gateway_ees\src\main\resources\soapFiles\SIFResponseServices\EnumStructureType.xsd</con:url><con:content><![CDATA[<xs:schema targetNamespace="http://sifresponseservice.sif.it/stato/" elementFormDefault="qualified" version="1.00" xmlns="http://sifresponseservice.sif.it/stato/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:shared="http://www.europa.eu/schengen/shared/xsd/v1" xmlns:ees="http://www.europa.eu/schengen/ees/xsd/v1">
  <xs:complexType name="statoType">
    <xs:sequence>
      <xs:element minOccurs="1" maxOccurs="1" name="stato">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:enumeration value="NO"/>
            <xs:enumeration value="AP"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
</xs:schema>]]></con:content><con:type>http://www.w3.org/2001/XMLSchema</con:type></con:part></con:definitionCache><con:endpoints><con:endpoint>http://Manuel:8093/</con:endpoint><con:endpoint>https://sifee-coll.cen.poliziadistato.it/ct/sif-async</con:endpoint></con:endpoints><con:operation id="094abeff-5158-47be-849b-e536ef42f1d5" isOneWay="false" action="http://sifresponseservice.sif.it/NewOperation" name="GetResponse" bindingOperationName="GetResponse" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false"><con:settings/></con:operation></con:interface><con:testSuite id="6d63a13b-b4ee-4f54-8252-d841bf1e89f3" name="CALLBACK"><con:settings/><con:runType>SEQUENTIAL</con:runType><con:testCase id="50fdead4-8b5d-4563-8eab-878097f9e8dc" failOnError="true" failTestCaseOnErrors="true" keepSession="false" maxResults="0" name="GetResponse TestCase" searchProperties="true"><con:settings/><con:testStep type="request" id="e49ebe0b-7d70-4fc3-86ff-036b9b585797" name="GetResponse"><con:settings/><con:config xsi:type="con:RequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:interface>SIFResponseServiceSOAP</con:interface><con:operation>GetResponse</con:operation><con:request name="GetResponse" id="d3aebf4a-521d-4e3f-91f8-9995bc8f5582"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:encoding>UTF-8</con:encoding><con:endpoint>https://sifee-coll.cen.poliziadistato.it/pgd/sif-async</con:endpoint><con:request><![CDATA[<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:sif="http://sifresponseservice.sif.it/">
	<soapenv:Header>
		<sif:SifHeader>
			<idUser>eescatania</idUser>							<!-- DTTG: Come da richiesta del servizio asincrono iniziale. -->
			<idChiamante>PS-KIOSK-0700603001A</idChiamante>			<!-- DTTG: Come da richiesta del servizio asincrono iniziale. -->
			<icd>7.0.4</icd>										<!-- DTTG: Come da richiesta del servizio asincrono iniziale. -->
			<numeroDocumento>ARG031220241</numeroDocumento>	<!-- DTTG: Come da richiesta del servizio asincrono iniziale. -->
			<operazione></operazione>								<!-- DTTG: Come da richiesta del servizio asincrono iniziale. -->
			<securityToken>BCyiV8cBCg1ZN8cu2M3a6G2XZ9iQhSPlXwrgpO/rcvjGaUd6U37oluy82axfEddrYW5P+eaPGr/5wHG7KMoJaA5FOAJ/XpgfWYCNP4kuL4lex1j8lepTOpkZtBMZI7mKWJIKurSrWzQijujOhJszXyhT3QLIWu0gK7ESIgSazXhG81Yyz7Q509ee6hdIVuYd1k+Yp/k0fItAoCsQsQ1ZmW3WWcpOoIvsOJzluaOm+nyilTq4wWxNzsMauZaJ8DvHzzS8ZV8j8lVKm3xwOuCAaOfEHISv+L5A5AYu3gF9yKtPNoiraag3aU4DN8939WxH0gNeRxSHIBOPwe7RYPsn2Fl+BREdyOvj/d/jQnJ4HX9t2koEj4giWxtYcH963RrucNdOoLg+HNYCJWZbxjOdioqg1pWEk6h7cW/vVKvBIMZIMMpRfPXOIKmII6lMG6Fk+IjddRQIv8vSPA0odgajTw6U/bPq9COikdmhLHH+K7ez9y0uItQZRzydKoKP1F4Kd9aWzPBElXDi4fc+lXA0Tkw8EzssZKe8c5yOB5TMOjzliFeiycPo+Lo6YDtisXXd4VmRLdBCGpLVgnChRQ3uSgU5EDI7fBhY+vTq8354GF8vLdDgqEo8nwdK3aKFCGPeNXy8rFNaFGQ+Fm7Sqt6c6w==</securityToken>
		</sif:SifHeader>
	</soapenv:Header>
	<soapenv:Body>
		<sif:RetrieveEESAsyncRequest>
			<idTransaction>03122024-TEST1-13</idTransaction>	<!-- DTTG: Come da richiesta del servizio asincrono iniziale. -->
		</sif:RetrieveEESAsyncRequest>
	</soapenv:Body>
</soapenv:Envelope>]]></con:request><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:wsaConfig action="http://sifresponseservice.sif.it/NewOperation" mustUnderstand="NONE" version="200508"/><con:wsrmConfig version="1.2"/></con:request></con:config></con:testStep><con:properties/></con:testCase><con:properties/></con:testSuite><con:mockService id="3c431728-0e73-4413-a471-eca2390e82af" port="8093" path="/" host="localhost" name="CALLBACK MOCK SERVICE" bindToHostOnly="false" docroot=""><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.mock.WsdlMockService@require-soap-action">false</con:setting></con:settings><con:properties/><con:mockOperation name="GetResponse" id="099c67d2-6a32-4675-ab1a-97f13b4fa2aa" interface="SIFResponseServiceSOAP" operation="GetResponse"><con:settings/><con:defaultResponse>REQUIRED DATA 5021</con:defaultResponse><con:dispatchStyle>SEQUENCE</con:dispatchStyle><con:response name="REQUIRED DATA 5021" id="6e0b3882-f676-43f8-88c1-2e082c500dce" httpResponseStatus="200" encoding="UTF-8"><con:settings/><con:responseContent><![CDATA[<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/">
   <SOAP-ENV:Header/>
   <SOAP-ENV:Body>
      <ns3:RetrieveEESAsyncResponse xmlns:ns3="http://sifresponseservice.sif.it/">
         <out><![CDATA[<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope"><soap:Header><ns4:ResponseHeader xmlns:ns4="http://www.europa.eu/schengen/ees-ns/xsd/v1" xmlns:ns2="http://www.europa.eu/schengen/ees/xsd/v1" xmlns:ns3="http://www.europa.eu/schengen/shared/xsd/v1" xmlns:ns5="http://www.europa.eu/schengen/vis/xsd/v3/types/Common" xmlns:ns6="http://www.europa.eu/schengen/vis/xsd/v3/types/Application" xmlns:ns7="http://www.europa.eu/schengen/etias/xsd/v1" xmlns:ns8="http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1"><ns2:TransactionID>22102024-TEST1-599</ns2:TransactionID><ns2:Variant>5001</ns2:Variant><ns2:SystemID>ABC-Immigrazione</ns2:SystemID><ns2:Timestamp>2024-10-22T09:51:27Z</ns2:Timestamp><ns4:User>0014.01</ns4:User></ns4:ResponseHeader></soap:Header><soap:Body><ns3:StartBorderControlResponse xmlns:ns3="http://www.europa.eu/schengen/ees-ns/xsd/v1" xmlns:ns2="http://www.europa.eu/schengen/shared/xsd/v1" xmlns:ns4="http://www.europa.eu/schengen/vis/xsd/v3/types/Common" xmlns:ns5="http://www.europa.eu/schengen/vis/xsd/v3/types/Application" xmlns:ns6="http://www.europa.eu/schengen/etias/xsd/v1" xmlns:ns7="http://www.europa.eu/schengen/ees/xsd/v1" xmlns:ns8="http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1"><ns7:ReturnCodes/><Response><ns7:Responsible><ns7:User>0014.01</ns7:User><ns7:Authority>ITAeur22b</ns7:Authority><ns7:EndUserRole>5004</ns7:EndUserRole></ns7:Responsible><ns7:Timestamp>2024-10-22T09:51:27.772098Z</ns7:Timestamp><ns3:ResponseData><ns3:EESSearchAndVerification/></ns3:ResponseData><ns3:RequiredData><ns3:Data>5021</ns3:Data></ns3:RequiredData><ns3:BorderCrossingPoint>ITAeur22b</ns3:BorderCrossingPoint></Response></ns3:StartBorderControlResponse></soap:Body></soap:Envelope>]]]]>><![CDATA[</out>
      </ns3:RetrieveEESAsyncResponse>
   </SOAP-ENV:Body>
</SOAP-ENV:Envelope>]]></con:responseContent><con:wsaConfig mustUnderstand="NONE" version="200508" action="http://sifresponseservice.sif.it/NewOperation"/></con:response><con:response name="REQUIRED DATA 5022" id="e72a0f3f-0bbd-47a9-bc51-6435921c839f" httpResponseStatus="200" encoding="UTF-8"><con:settings/><con:responseContent><![CDATA[<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/">
   <SOAP-ENV:Header/>
   <SOAP-ENV:Body>
      <ns3:RetrieveEESAsyncResponse xmlns:ns3="http://sifresponseservice.sif.it/">
         <out><![CDATA[<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope"><soap:Header><ns4:ResponseHeader xmlns:ns4="http://www.europa.eu/schengen/ees-ns/xsd/v1" xmlns:ns2="http://www.europa.eu/schengen/ees/xsd/v1" xmlns:ns3="http://www.europa.eu/schengen/shared/xsd/v1" xmlns:ns5="http://www.europa.eu/schengen/vis/xsd/v3/types/Common" xmlns:ns6="http://www.europa.eu/schengen/vis/xsd/v3/types/Application" xmlns:ns7="http://www.europa.eu/schengen/etias/xsd/v1" xmlns:ns8="http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1"><ns2:TransactionID>22102024-TEST1-7</ns2:TransactionID><ns2:Variant>5001</ns2:Variant><ns2:SystemID>ABC-Immigrazione</ns2:SystemID><ns2:Timestamp>2024-10-22T10:03:54Z</ns2:Timestamp><ns4:User>0014.01</ns4:User></ns4:ResponseHeader></soap:Header><soap:Body><ns3:AddDataToBorderControlResponse xmlns:ns3="http://www.europa.eu/schengen/ees-ns/xsd/v1" xmlns:ns2="http://www.europa.eu/schengen/shared/xsd/v1" xmlns:ns4="http://www.europa.eu/schengen/vis/xsd/v3/types/Common" xmlns:ns5="http://www.europa.eu/schengen/vis/xsd/v3/types/Application" xmlns:ns6="http://www.europa.eu/schengen/etias/xsd/v1" xmlns:ns7="http://www.europa.eu/schengen/ees/xsd/v1" xmlns:ns8="http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1"><ns7:ReturnCodes/><Response><ns7:Responsible><ns7:User>0014.01</ns7:User><ns7:Authority>ITAeur22b</ns7:Authority><ns7:EndUserRole>5004</ns7:EndUserRole></ns7:Responsible><ns7:Timestamp>2024-10-22T10:03:54.198512Z</ns7:Timestamp><ns3:ResponseData><ns3:EESSearchAndVerification/><ns3:EESIdentification/><ns3:VISSearchAndVerification/><ns3:VISIdentification/></ns3:ResponseData><ns3:RequiredData><ns3:Data>5022</ns3:Data></ns3:RequiredData><ns3:BorderCrossingPoint>ITAeur22b</ns3:BorderCrossingPoint></Response></ns3:AddDataToBorderControlResponse></soap:Body></soap:Envelope>]]]]>><![CDATA[</out>
      </ns3:RetrieveEESAsyncResponse>
   </SOAP-ENV:Body>
</SOAP-ENV:Envelope>]]></con:responseContent><con:wsaConfig mustUnderstand="NONE" version="200508" action="http://sifresponseservice.sif.it/NewOperation"/></con:response><con:dispatchConfig/></con:mockOperation></con:mockService><con:properties/><con:wssContainer/><con:oAuth2ProfileContainer/><con:oAuth1ProfileContainer/><con:sensitiveInformation/></con:soapui-project>