INSERT INTO sif_add_data_request (id, request_for_kiosk, request_for_egate, value, description) VALUES ('1', 'TRANSACTION_FINISHED','FI_REQUEST', '5001', 'FI for enrolment (data entry)');
INSERT INTO sif_add_data_request (id, request_for_kiosk, request_for_egate, value, description) VALUES ('2', 'TRANSACTION_FINISHED','FP_REQUEST', '5002', 'FP for enrolment (data entry)');
INSERT INTO sif_add_data_request (id, request_for_kiosk, request_for_egate, value, description) VALUES ('3', 'FI_REQUEST','FI_REQUEST', '5003', 'FI for verification/identification');
INSERT INTO sif_add_data_request (id, request_for_kiosk, request_for_egate, value, description) VALUES ('4', 'FP_REQUEST','FP_REQUEST', '5004', 'FP for identification');
INSERT INTO sif_add_data_request (id, request_for_kiosk, request_for_egate, value, description) VALUES ('5', 'TRANSACTION_FINISHED','', '5005', 'Visa information');
INSERT INTO sif_add_data_request (id, request_for_kiosk, request_for_egate, value, description) VALUES ('6', 'TRANSACTION_FINISHED','', '5007', 'Perform Identification Result operation with TravellerFileID');
INSERT INTO sif_add_data_request (id, request_for_kiosk, request_for_egate, value, description) VALUES ('7', 'TRANSACTION_FINISHED','', '5008', 'Perform Identification Result operation with VSN');
INSERT INTO sif_add_data_request (id, request_for_kiosk, request_for_egate, value, description) VALUES ('8', 'TRANSACTION_FINISHED','FP_REQUEST', '5009', 'FP right hand for verification');
INSERT INTO sif_add_data_request (id, request_for_kiosk, request_for_egate, value, description) VALUES ('9', 'TRANSACTION_FINISHED','', '5010', 'FP left hand for verification');
INSERT INTO sif_add_data_request (id, request_for_kiosk, request_for_egate, value, description) VALUES ('10', 'TRANSACTION_FINISHED','FP_REQUEST', '5011', 'FP right hand or FI for verification');
INSERT INTO sif_add_data_request (id, request_for_kiosk, request_for_egate, value, description) VALUES ('11', 'TRANSACTION_FINISHED','', '5012', 'FP left hand or FI for verification');
INSERT INTO sif_add_data_request (id, request_for_kiosk, request_for_egate, value, description) VALUES ('12', 'TRANSACTION_FINISHED','FI_REQUEST', '5013', 'FI for update');
INSERT INTO sif_add_data_request (id, request_for_kiosk, request_for_egate, value, description) VALUES ('13', 'TRANSACTION_FINISHED','FP_REQUEST', '5014', 'FP right hand for update');
INSERT INTO sif_add_data_request (id, request_for_kiosk, request_for_egate, value, description) VALUES ('14', 'TRANSACTION_FINISHED','FI_REQUEST', '5015', 'FI update confirmation');
INSERT INTO sif_add_data_request (id, request_for_kiosk, request_for_egate, value, description) VALUES ('15', 'TRANSACTION_FINISHED','FP_REQUEST', '5016', 'FP right hand update confirmation');
INSERT INTO sif_add_data_request (id, request_for_kiosk, request_for_egate, value, description) VALUES ('16', 'TRANSACTION_FINISHED','TRANSACTION_FINISHED', '5019', 'The transaction may be concluded');
INSERT INTO sif_add_data_request (id, request_for_kiosk, request_for_egate, value, description) VALUES ('17', 'TRANSACTION_FINISHED','', '5018', 'VIS Alphanumeric selection');
INSERT INTO sif_add_data_request (id, request_for_kiosk, request_for_egate, value, description) VALUES ('18', 'TRANSACTION_FINISHED','', '5017', 'EES Alphanumeric selection');
INSERT INTO sif_add_data_request (id, request_for_kiosk, request_for_egate, value, description) VALUES ('19', 'TRANSACTION_FINISHED','FP_REQUEST', '5020', 'FP right hand for verification or FP left hand for verification');
INSERT INTO sif_add_data_request (id, request_for_kiosk, request_for_egate, value, description) VALUES ('20', 'FP_REQUEST','FP_REQUEST', '5021', 'FP for identification and optionally FI for Identification');
INSERT INTO sif_add_data_request (id, request_for_kiosk, request_for_egate, value, description) VALUES ('21', 'TRANSACTION_FINISHED','TRANSACTION_FINISHED', '5022', 'WFE can''t continue please proceed with Atomic Operations');
INSERT INTO sif_add_data_request (id, request_for_kiosk, request_for_egate, value, description) VALUES ('22', 'TRANSACTION_FINISHED','', '5023', 'WFE can''t continue please proceed with Direct Identification in VIS');
INSERT INTO sif_add_data_request (id, request_for_kiosk, request_for_egate, value, description) VALUES ('23', 'FP_REQUEST','FP_REQUEST', '5024', 'FP right hand for Identification and optionally FI for Identification');
INSERT INTO sif_add_data_request (id, request_for_kiosk, request_for_egate, value, description) VALUES ('24', 'TRANSACTION_FINISHED','', '5025', 'FP left hand for Identification and optionally FI for Identification');
INSERT INTO sif_add_data_request (id, request_for_kiosk, request_for_egate, value, description) VALUES ('25', 'FP_REQUEST','FP_REQUEST', '5026', 'FP right hand for Identification');
INSERT INTO sif_add_data_request (id, request_for_kiosk, request_for_egate, value, description) VALUES ('26', 'TRANSACTION_FINISHED','', '5027', 'FP left hand for Identification');
INSERT INTO sif_add_data_request (id, request_for_kiosk, request_for_egate, value, description) VALUES ('27', 'TRANSACTION_FINISHED','', '5028', 'FP left hand for update');
INSERT INTO sif_add_data_request (id, request_for_kiosk, request_for_egate, value, description) VALUES ('28', 'TRANSACTION_FINISHED','', '5029', 'FP left hand update confirmation');
INSERT INTO sif_add_data_request (id, request_for_kiosk, request_for_egate, value, description) VALUES ('29', 'TRANSACTION_FINISHED','', '5030', 'Confirmation for deleting FP');
INSERT INTO sif_add_data_request (id, request_for_kiosk, request_for_egate, value, description) VALUES ('30', 'TRANSACTION_FINISHED','', '5031', 'Confirmation of updating FP-Missing Reasons');
INSERT INTO sif_add_data_request (id, request_for_kiosk, request_for_egate, value, description) VALUES ('31', 'TRANSACTION_FINISHED','FI_REQUEST', '5032', 'FI required for SSS Biometrics Comparison');
INSERT INTO sif_add_data_request (id, request_for_kiosk, request_for_egate, value, description) VALUES ('32', 'TRANSACTION_FINISHED','FP_REQUEST', '5033', 'FP required for SSS Biometrics Comparison');
INSERT INTO sif_add_data_request (id, request_for_kiosk, request_for_egate, value, description) VALUES ('33', 'TRANSACTION_FINISHED','', '5034', 'FTD information');
