#spring.datasource.tomcat.initial-size=20
#spring.datasource.tomcat.max-wait=25000
#spring.datasource.tomcat.max-active=70
#spring.datasource.tomcat.max-idle=20
#spring.datasource.tomcat.min-idle=9
spring.datasource.tomcat.default-auto-commit=true

spring.main.allow-bean-definition-overriding=true

spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration

spring.profiles.active=development
build.version=@project.version@

#TODO: ricorda di impostare profilo attivo qui e ricontrolla queste properties di application.properties

#spring.quartz.wait-for-jobs-to-complete-on-shutdown=true

#spring.quartz.job-store-type=memory
#spring.quartz.properties.instanceId=AUTO
#spring.quartz.properties.instanceName=MyClusteredScheduler
#spring.quartz.properties.isClustered=true

#spring.quartz.job-store-type=jdbc
#spring.quartz.properties.org.quartz.dataSource.myDS.driver=org.postgresql.Driver
#spring.quartz.properties.org.quartz.dataSource.myDS.URL=***********************************************
#spring.quartz.properties.org.quartz.dataSource.myDS.user=docker
#spring.quartz.properties.org.quartz.dataSource.myDS.password=docker
#spring.quartz.properties.org.quartz.dataSource.myDS.maxConnections=5
#spring.quartz.properties.org.quartz.scheduler.instanceName=MyClusteredScheduler
#spring.quartz.properties.org.quartz.scheduler.instanceId=AUTO
#spring.quartz.properties.org.quartz.jobStore.isClustered=true

server.tomcat.connection-timeout=30s
#spring.mvc.async.request-timeout=30000


# --- Hibernate-Caffeine L2 Caching: ---
# --- Impostazioni JPA e Cache di base ---
spring.jpa.properties.hibernate.cache.region.factory_class=org.hibernate.cache.jcache.JCacheRegionFactory
spring.jpa.properties.jakarta.persistence.sharedCache.mode=ENABLE_SELECTIVE
# --- Provider JCache per Hibernate ---
spring.jpa.properties.hibernate.javax.cache.provider=com.github.benmanes.caffeine.jcache.spi.CaffeineCachingProvider
# --- Strategia per cache mancanti ---
#se usi spring.jpa.properties.hibernate.javax.cache.missing_cache_strategy=create-warn vedi warning iniziali che puoi ignorare nel mio caso
spring.jpa.properties.hibernate.javax.cache.missing_cache_strategy=create
# --- Tipo di Cache Generica di Spring (per @Cacheable di Spring) ---
spring.cache.type=jcache
# --- Altre proprieta' per caching ---
spring.jpa.properties.hibernate.cache.region_prefix=
spring.jpa.properties.hibernate.cache.auto_evict_collection_cache=true
spring.jpa.properties.hibernate.cache.use_minimal_puts=false
spring.jpa.properties.hibernate.cache.use_structured_entries=true
# --- Configurazioni Specifiche di Caffeine JCache tramite proprieta' Hibernate ---
spring.jpa.properties.hibernate.javax.cache.properties.caffeine.jcache.default.maximumSize=10000
spring.jpa.properties.hibernate.javax.cache.properties.caffeine.jcache.default.expireAfterWrite=30m
# Configurazione per default-update-timestamps-region - NON DEVE SCADERE (o scadenza estremamente lunga)
spring.jpa.properties.hibernate.javax.cache.properties.caffeine.jcache.default-update-timestamps-region.maximumSize=3000
spring.jpa.properties.hibernate.javax.cache.properties.caffeine.jcache.default-update-timestamps-region.expireAfterWrite=36500d
# Configurazione per default-query-results-region - Scadenza lunga consigliata
spring.jpa.properties.hibernate.javax.cache.properties.caffeine.jcache.default-query-results-region.maximumSize=3000
spring.jpa.properties.hibernate.javax.cache.properties.caffeine.jcache.default-query-results-region.expireAfterWrite=36500d

# Configurazione per la cache dell'entita' SifParameter
spring.jpa.properties.hibernate.javax.cache.properties.caffeine.jcache.com.reco.ees.gateway.repository.model.SifParameter.maximumSize=100
spring.jpa.properties.hibernate.javax.cache.properties.caffeine.jcache.com.reco.ees.gateway.repository.model.SifParameter.expireAfterWrite=36500d
# Configurazione per la cache dell'entita' SifCountryCode
spring.jpa.properties.hibernate.javax.cache.properties.caffeine.jcache.com.reco.ees.gateway.repository.model.SifCountryCode.maximumSize=700
spring.jpa.properties.hibernate.javax.cache.properties.caffeine.jcache.com.reco.ees.gateway.repository.model.SifCountryCode.expireAfterWrite=36500d

# Configurazione per la cache dell'entita' Kiosk (READ_WRITE)
spring.jpa.properties.hibernate.javax.cache.properties.caffeine.jcache.kioskCache.maximumSize=200
spring.jpa.properties.hibernate.javax.cache.properties.caffeine.jcache.kioskCache.expireAfterWrite=12h
# Configurazione per la cache dell'entita' SifAuth (NONSTRICT_READ_WRITE)
spring.jpa.properties.hibernate.javax.cache.properties.caffeine.jcache.sifAuthCache.maximumSize=200
spring.jpa.properties.hibernate.javax.cache.properties.caffeine.jcache.sifAuthCache.expireAfterWrite=6h
# Configurazione per la cache dell'entita' SifAddDataRequest (READ_ONLY)
spring.jpa.properties.hibernate.javax.cache.properties.caffeine.jcache.com.reco.ees.gateway.repository.model.SifAddDataRequest.maximumSize=100
spring.jpa.properties.hibernate.javax.cache.properties.caffeine.jcache.com.reco.ees.gateway.repository.model.SifAddDataRequest.expireAfterWrite=36500d
# Configurazione per la cache dell'entita' SifGenericCode (READ_ONLY)
spring.jpa.properties.hibernate.javax.cache.properties.caffeine.jcache.com.reco.ees.gateway.repository.model.SifGenericCode.maximumSize=100
spring.jpa.properties.hibernate.javax.cache.properties.caffeine.jcache.com.reco.ees.gateway.repository.model.SifGenericCode.expireAfterWrite=36500d
# Configurazione per la cache dell'entita' Parameter (READ_ONLY particolare)
spring.jpa.properties.hibernate.javax.cache.properties.caffeine.jcache.com.reco.ees.gateway.repository.model.Parameter.maximumSize=100
spring.jpa.properties.hibernate.javax.cache.properties.caffeine.jcache.com.reco.ees.gateway.repository.model.Parameter.expireAfterWrite=36500d
# LuoghiSDI e DocumentiSDI non sono piu cachati L2
# Configurazione per la cache della query sifParameterByType (usata anche da Spring Cache)
spring.jpa.properties.hibernate.javax.cache.properties.caffeine.jcache.sifParameterByType.maximumSize=50
spring.jpa.properties.hibernate.javax.cache.properties.caffeine.jcache.sifParameterByType.expireAfterWrite=36500d
# Configurazione per la cache della query sifCountryCodeByKioskValue (usata anche da Spring Cache)
spring.jpa.properties.hibernate.javax.cache.properties.caffeine.jcache.sifCountryCodeByKioskValue.maximumSize=350
spring.jpa.properties.hibernate.javax.cache.properties.caffeine.jcache.sifCountryCodeByKioskValue.expireAfterWrite=36500d
# Configurazione per la cache della query kioskQueries
spring.jpa.properties.hibernate.javax.cache.properties.caffeine.jcache.kioskQueries.maximumSize=200
spring.jpa.properties.hibernate.javax.cache.properties.caffeine.jcache.kioskQueries.expireAfterWrite=12h
# Configurazione per la cache della query sifAuthQueries
spring.jpa.properties.hibernate.javax.cache.properties.caffeine.jcache.sifAuthQueries.maximumSize=200
spring.jpa.properties.hibernate.javax.cache.properties.caffeine.jcache.sifAuthQueries.expireAfterWrite=6h
# Configurazione per la cache della query sifAddDataRequestQueries (usata anche da Spring Cache)
spring.jpa.properties.hibernate.javax.cache.properties.caffeine.jcache.sifAddDataRequestQueries.maximumSize=200
spring.jpa.properties.hibernate.javax.cache.properties.caffeine.jcache.sifAddDataRequestQueries.expireAfterWrite=12h
# Configurazione per la cache della query sifGenericCodeQueries
spring.jpa.properties.hibernate.javax.cache.properties.caffeine.jcache.sifGenericCodeQueries.maximumSize=50
spring.jpa.properties.hibernate.javax.cache.properties.caffeine.jcache.sifGenericCodeQueries.expireAfterWrite=36500d
# Configurazione per la cache della query sifParametersFindAllQueries
spring.jpa.properties.hibernate.javax.cache.properties.caffeine.jcache.sifParametersFindAllQueries.maximumSize=200
spring.jpa.properties.hibernate.javax.cache.properties.caffeine.jcache.sifParametersFindAllQueries.expireAfterWrite=12h
# Configurazione per la cache della query sifCountryCodeFindAllQueries
spring.jpa.properties.hibernate.javax.cache.properties.caffeine.jcache.sifCountryCodeFindAllQueries.maximumSize=200
spring.jpa.properties.hibernate.javax.cache.properties.caffeine.jcache.sifCountryCodeFindAllQueries.expireAfterWrite=12h
# Configurazione per la cache della query parameterFindAllQueries (USATA DA ParameterRepository.findAll())
spring.jpa.properties.hibernate.javax.cache.properties.caffeine.jcache.parameterFindAllQueries.maximumSize=200
spring.jpa.properties.hibernate.javax.cache.properties.caffeine.jcache.parameterFindAllQueries.expireAfterWrite=12h
