//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.etias.xsd.v1;

import java.util.ArrayList;
import java.util.List;
import javax.xml.datatype.XMLGregorianCalendar;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlSeeAlso;
import jakarta.xml.bind.annotation.XmlType;


/**
 * Travel authorisation - a decision issued in accordance with ETIAS Regulation which is required for third-country nationals to fulfil the entry condition.
 * 
 *                 Flag is present only: whether the travel authorisation will expire within the next 90 days and the remaining validity period;
 * 
 *                 [ETIAS Regulation] Article 47(2)(c)
 * 
 * <p>Classe Java per TravelAuthorisationRequestType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="TravelAuthorisationRequestType">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="ApplicationNumber" type="{http://www.europa.eu/schengen/etias/xsd/v1}IdentifierType"/>
 *         <element name="Valid" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         <element name="ValidUntil" type="{http://www.w3.org/2001/XMLSchema}date" minOccurs="0"/>
 *         <element name="TerritorialValidity" minOccurs="0">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="TravelTerritory" type="{http://www.europa.eu/schengen/shared/xsd/v1}CT06_TerritoryType" maxOccurs="unbounded"/>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "TravelAuthorisationRequestType", propOrder = {
    "applicationNumber",
    "valid",
    "validUntil",
    "territorialValidity"
})
@XmlSeeAlso({
    TravelAuthorisationResponseType.class
})
public class TravelAuthorisationRequestType {

    /**
     * Application number of the application file recorded by the ETIAS Central System.
     * 
     *                         [EES Regulation]  Article 17(2)
     *                         [ETIAS Regulation] Article 11a
     * 
     */
    @XmlElement(name = "ApplicationNumber", required = true)
    @XmlSchemaType(name = "anyURI")
    protected String applicationNumber;
    /**
     * Whether or not the person has a valid travel authorisation.
     * 
     *                         [Article 47(2)(a)]
     * 
     */
    @XmlElement(name = "Valid")
    protected boolean valid;
    /**
     * End of validity period of an ETIAS travel authorisation.
     * 
     *                         [EES Regulation]  Article 17(2)
     *                         [ETIAS Regulation] Article 11a
     * 
     */
    @XmlElement(name = "ValidUntil")
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar validUntil;
    /**
     * In the case of a travel authorisation with limited territorial validity issued under Article 44, the Member State(s) for which it is valid
     * 
     *                         [ETIAS Regulation] Article 47(2)(a)
     * 
     *                         In case of a travel authorisation with limited territorial validity, the Member State(s) for which it is valid.
     * 
     *                         [Article 17(2)(c)]
     * 
     */
    @XmlElement(name = "TerritorialValidity")
    protected TravelAuthorisationRequestType.TerritorialValidity territorialValidity;

    /**
     * Application number of the application file recorded by the ETIAS Central System.
     * 
     *                         [EES Regulation]  Article 17(2)
     *                         [ETIAS Regulation] Article 11a
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getApplicationNumber() {
        return applicationNumber;
    }

    /**
     * Imposta il valore della proprietà applicationNumber.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     * @see #getApplicationNumber()
     */
    public void setApplicationNumber(String value) {
        this.applicationNumber = value;
    }

    /**
     * Whether or not the person has a valid travel authorisation.
     * 
     *                         [Article 47(2)(a)]
     * 
     */
    public boolean isValid() {
        return valid;
    }

    /**
     * Imposta il valore della proprietà valid.
     * 
     */
    public void setValid(boolean value) {
        this.valid = value;
    }

    /**
     * End of validity period of an ETIAS travel authorisation.
     * 
     *                         [EES Regulation]  Article 17(2)
     *                         [ETIAS Regulation] Article 11a
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getValidUntil() {
        return validUntil;
    }

    /**
     * Imposta il valore della proprietà validUntil.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     * @see #getValidUntil()
     */
    public void setValidUntil(XMLGregorianCalendar value) {
        this.validUntil = value;
    }

    /**
     * In the case of a travel authorisation with limited territorial validity issued under Article 44, the Member State(s) for which it is valid
     * 
     *                         [ETIAS Regulation] Article 47(2)(a)
     * 
     *                         In case of a travel authorisation with limited territorial validity, the Member State(s) for which it is valid.
     * 
     *                         [Article 17(2)(c)]
     * 
     * @return
     *     possible object is
     *     {@link TravelAuthorisationRequestType.TerritorialValidity }
     *     
     */
    public TravelAuthorisationRequestType.TerritorialValidity getTerritorialValidity() {
        return territorialValidity;
    }

    /**
     * Imposta il valore della proprietà territorialValidity.
     * 
     * @param value
     *     allowed object is
     *     {@link TravelAuthorisationRequestType.TerritorialValidity }
     *     
     * @see #getTerritorialValidity()
     */
    public void setTerritorialValidity(TravelAuthorisationRequestType.TerritorialValidity value) {
        this.territorialValidity = value;
    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="TravelTerritory" type="{http://www.europa.eu/schengen/shared/xsd/v1}CT06_TerritoryType" maxOccurs="unbounded"/>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "travelTerritory"
    })
    public static class TerritorialValidity {

        @XmlElement(name = "TravelTerritory", required = true)
        protected List<String> travelTerritory;

        /**
         * Gets the value of the travelTerritory property.
         * 
         * <p>This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the travelTerritory property.</p>
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * </p>
         * <pre>
         * getTravelTerritory().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link String }
         * </p>
         * 
         * 
         * @return
         *     The value of the travelTerritory property.
         */
        public List<String> getTravelTerritory() {
            if (travelTerritory == null) {
                travelTerritory = new ArrayList<>();
            }
            return this.travelTerritory;
        }

    }

}
