//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.etias.xsd.v1;

import java.util.ArrayList;
import java.util.List;
import eu.europa.schengen.shared.xsd.v1.TravelDocumentType;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSeeAlso;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per TravelAuthorisationResponseType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="TravelAuthorisationResponseType">
 *   <complexContent>
 *     <extension base="{http://www.europa.eu/schengen/etias/xsd/v1}TravelAuthorisationRequestType">
 *       <sequence>
 *         <element name="TravelDocument" type="{http://www.europa.eu/schengen/shared/xsd/v1}TravelDocumentType"/>
 *         <element name="Parent" type="{http://www.europa.eu/schengen/etias/xsd/v1}ParentType" minOccurs="0"/>
 *         <element name="FamilyMember" type="{http://www.europa.eu/schengen/etias/xsd/v1}FamilyMemberType" minOccurs="0"/>
 *         <element name="ETIASFlag" type="{http://www.europa.eu/schengen/etias/xsd/v1}CTX13_ETIASFlagType" maxOccurs="unbounded" minOccurs="0"/>
 *       </sequence>
 *     </extension>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "TravelAuthorisationResponseType", propOrder = {
    "travelDocument",
    "parent",
    "familyMember",
    "etiasFlag"
})
@XmlSeeAlso({
    eu.europa.schengen.ees_ns.xsd.v1.ResponseDataType.ETIASSearch.TravelAuthorisation.class,
    eu.europa.schengen.ees_ns.xsd.v1.IdentificationResultResponseDataType.ETIASSearch.TravelAuthorisation.class
})
public class TravelAuthorisationResponseType
    extends TravelAuthorisationRequestType
{

    @XmlElement(name = "TravelDocument", required = true)
    protected TravelDocumentType travelDocument;
    /**
     * For minors, surname and first name(s), home address, email address and, if available, phone number of the person exercising parental authority or of the applicant's legal guardian
     * 
     *                                 [ETIAS Regulation] Article 17(2)(k)
     * 
     *                                 Note: This data is not stored in the EES database.
     * 
     */
    @XmlElement(name = "Parent")
    protected ParentType parent;
    /**
     * Where he or she claims the status of family member referred to in point (c) of Article 2(1):
     *                                 (i) his or her status of family member;
     *                                 (ii) the surname, first name(s), date of birth, place of birth, country of birth, current nationality, home address, email address and, if available, phone number of the family member with whom the applicant has family ties;
     *                                 (iii) his or her family ties with that family member in accordance with Article 2(2) of Directive 2004/38/EC
     * 
     *                                 [ETIAS Regulation] Article 17(2)(l)
     * 
     *                                 Note: This data is not stored in the EES database.
     * 
     */
    @XmlElement(name = "FamilyMember")
    protected FamilyMemberType familyMember;
    /**
     * Any flag attached to the travel authorisation under Article 36(2) and (3);
     * 
     *                                 [ETIAS Regulation] Article 47(2)(b)
     * 
     *                                 In cases where there is doubt as to whether sufficient reasons to refuse the travel authorisation exist, the ETIAS National Unit of the Member State responsible shall have the possibility, including after an interview, to issue a travel authorisation with a flag recommending to border authorities to proceed with a second line check.
     *                                 (...)
     *                                 The flag shall be removed automatically once the border authorities have carried out the check and have entered the entry record in the EES.
     * 
     *                                 [ETIAS Regulation] Article 36(2)
     * 
     *                                 The ETIAS National Unit of the Member State responsible shall have the possibility to add a flag indicating to border authorities and other authorities with access to the data in the ETIAS Central System that a specific hit triggered during the processing of the application has been assessed and that it has been verified that the hit constituted a false hit or that the manual processing has shown that there were no grounds for the refusal of the travel authorisation.
     * 
     *                                 [ETIAS Regulation] Article 36(3)
     * 
     *                                 Note: This data is not stored in the EES database.
     * 
     */
    @XmlElement(name = "ETIASFlag")
    protected List<String> etiasFlag;

    /**
     * Recupera il valore della proprietà travelDocument.
     * 
     * @return
     *     possible object is
     *     {@link TravelDocumentType }
     *     
     */
    public TravelDocumentType getTravelDocument() {
        return travelDocument;
    }

    /**
     * Imposta il valore della proprietà travelDocument.
     * 
     * @param value
     *     allowed object is
     *     {@link TravelDocumentType }
     *     
     */
    public void setTravelDocument(TravelDocumentType value) {
        this.travelDocument = value;
    }

    /**
     * For minors, surname and first name(s), home address, email address and, if available, phone number of the person exercising parental authority or of the applicant's legal guardian
     * 
     *                                 [ETIAS Regulation] Article 17(2)(k)
     * 
     *                                 Note: This data is not stored in the EES database.
     * 
     * @return
     *     possible object is
     *     {@link ParentType }
     *     
     */
    public ParentType getParent() {
        return parent;
    }

    /**
     * Imposta il valore della proprietà parent.
     * 
     * @param value
     *     allowed object is
     *     {@link ParentType }
     *     
     * @see #getParent()
     */
    public void setParent(ParentType value) {
        this.parent = value;
    }

    /**
     * Where he or she claims the status of family member referred to in point (c) of Article 2(1):
     *                                 (i) his or her status of family member;
     *                                 (ii) the surname, first name(s), date of birth, place of birth, country of birth, current nationality, home address, email address and, if available, phone number of the family member with whom the applicant has family ties;
     *                                 (iii) his or her family ties with that family member in accordance with Article 2(2) of Directive 2004/38/EC
     * 
     *                                 [ETIAS Regulation] Article 17(2)(l)
     * 
     *                                 Note: This data is not stored in the EES database.
     * 
     * @return
     *     possible object is
     *     {@link FamilyMemberType }
     *     
     */
    public FamilyMemberType getFamilyMember() {
        return familyMember;
    }

    /**
     * Imposta il valore della proprietà familyMember.
     * 
     * @param value
     *     allowed object is
     *     {@link FamilyMemberType }
     *     
     * @see #getFamilyMember()
     */
    public void setFamilyMember(FamilyMemberType value) {
        this.familyMember = value;
    }

    /**
     * Any flag attached to the travel authorisation under Article 36(2) and (3);
     * 
     *                                 [ETIAS Regulation] Article 47(2)(b)
     * 
     *                                 In cases where there is doubt as to whether sufficient reasons to refuse the travel authorisation exist, the ETIAS National Unit of the Member State responsible shall have the possibility, including after an interview, to issue a travel authorisation with a flag recommending to border authorities to proceed with a second line check.
     *                                 (...)
     *                                 The flag shall be removed automatically once the border authorities have carried out the check and have entered the entry record in the EES.
     * 
     *                                 [ETIAS Regulation] Article 36(2)
     * 
     *                                 The ETIAS National Unit of the Member State responsible shall have the possibility to add a flag indicating to border authorities and other authorities with access to the data in the ETIAS Central System that a specific hit triggered during the processing of the application has been assessed and that it has been verified that the hit constituted a false hit or that the manual processing has shown that there were no grounds for the refusal of the travel authorisation.
     * 
     *                                 [ETIAS Regulation] Article 36(3)
     * 
     *                                 Note: This data is not stored in the EES database.
     * 
     * Gets the value of the etiasFlag property.
     * 
     * <p>This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the etiasFlag property.</p>
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * </p>
     * <pre>
     * getETIASFlag().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link String }
     * </p>
     * 
     * 
     * @return
     *     The value of the etiasFlag property.
     */
    public List<String> getETIASFlag() {
        if (etiasFlag == null) {
            etiasFlag = new ArrayList<>();
        }
        return this.etiasFlag;
    }

}
