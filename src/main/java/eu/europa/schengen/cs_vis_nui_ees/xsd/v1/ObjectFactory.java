//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// V<PERSON>re https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.cs_vis_nui_ees.xsd.v1;

import jakarta.xml.bind.annotation.XmlRegistry;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the eu.europa.schengen.cs_vis_nui_ees.xsd.v1 package. 
 * <p>An ObjectFactory allows you to programmatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {


    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: eu.europa.schengen.cs_vis_nui_ees.xsd.v1
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link VISEESSearchByTravelDocumentRequest }
     * 
     * @return
     *     the new instance of {@link VISEESSearchByTravelDocumentRequest }
     */
    public VISEESSearchByTravelDocumentRequest createVISEESSearchByTravelDocumentRequest() {
        return new VISEESSearchByTravelDocumentRequest();
    }

    /**
     * Create an instance of {@link VISEESSearchByTravelDocumentResponse }
     * 
     * @return
     *     the new instance of {@link VISEESSearchByTravelDocumentResponse }
     */
    public VISEESSearchByTravelDocumentResponse createVISEESSearchByTravelDocumentResponse() {
        return new VISEESSearchByTravelDocumentResponse();
    }

    /**
     * Create an instance of {@link VISEESSearchByPersonalDataRequest }
     * 
     * @return
     *     the new instance of {@link VISEESSearchByPersonalDataRequest }
     */
    public VISEESSearchByPersonalDataRequest createVISEESSearchByPersonalDataRequest() {
        return new VISEESSearchByPersonalDataRequest();
    }

    /**
     * Create an instance of {@link VISEESSearchByPersonalDataResponse }
     * 
     * @return
     *     the new instance of {@link VISEESSearchByPersonalDataResponse }
     */
    public VISEESSearchByPersonalDataResponse createVISEESSearchByPersonalDataResponse() {
        return new VISEESSearchByPersonalDataResponse();
    }

    /**
     * Create an instance of {@link VISEESIdentificationRequest }
     * 
     * @return
     *     the new instance of {@link VISEESIdentificationRequest }
     */
    public VISEESIdentificationRequest createVISEESIdentificationRequest() {
        return new VISEESIdentificationRequest();
    }

    /**
     * Create an instance of {@link VISEESIdentificationResponse }
     * 
     * @return
     *     the new instance of {@link VISEESIdentificationResponse }
     */
    public VISEESIdentificationResponse createVISEESIdentificationResponse() {
        return new VISEESIdentificationResponse();
    }

    /**
     * Create an instance of {@link VISEESVerificationByFingerprintResponse }
     * 
     * @return
     *     the new instance of {@link VISEESVerificationByFingerprintResponse }
     */
    public VISEESVerificationByFingerprintResponse createVISEESVerificationByFingerprintResponse() {
        return new VISEESVerificationByFingerprintResponse();
    }

    /**
     * Create an instance of {@link VISEESUpdateAuthorityNotification }
     * 
     * @return
     *     the new instance of {@link VISEESUpdateAuthorityNotification }
     */
    public VISEESUpdateAuthorityNotification createVISEESUpdateAuthorityNotification() {
        return new VISEESUpdateAuthorityNotification();
    }

    /**
     * Create an instance of {@link ShortenValidityPeriodWithNewStickerGetEESType }
     * 
     * @return
     *     the new instance of {@link ShortenValidityPeriodWithNewStickerGetEESType }
     */
    public ShortenValidityPeriodWithNewStickerGetEESType createShortenValidityPeriodWithNewStickerGetEESType() {
        return new ShortenValidityPeriodWithNewStickerGetEESType();
    }

    /**
     * Create an instance of {@link ExtendVisaGetEESType }
     * 
     * @return
     *     the new instance of {@link ExtendVisaGetEESType }
     */
    public ExtendVisaGetEESType createExtendVisaGetEESType() {
        return new ExtendVisaGetEESType();
    }

    /**
     * Create an instance of {@link ExtendVisaWithoutNewStickerGetEESType }
     * 
     * @return
     *     the new instance of {@link ExtendVisaWithoutNewStickerGetEESType }
     */
    public ExtendVisaWithoutNewStickerGetEESType createExtendVisaWithoutNewStickerGetEESType() {
        return new ExtendVisaWithoutNewStickerGetEESType();
    }

    /**
     * Create an instance of {@link ResultReadCoreBorderDataEESType }
     * 
     * @return
     *     the new instance of {@link ResultReadCoreBorderDataEESType }
     */
    public ResultReadCoreBorderDataEESType createResultReadCoreBorderDataEESType() {
        return new ResultReadCoreBorderDataEESType();
    }

    /**
     * Create an instance of {@link ResultReadApplicationBaseEESType }
     * 
     * @return
     *     the new instance of {@link ResultReadApplicationBaseEESType }
     */
    public ResultReadApplicationBaseEESType createResultReadApplicationBaseEESType() {
        return new ResultReadApplicationBaseEESType();
    }

    /**
     * Create an instance of {@link VisaApplicationSearchResultEESType }
     * 
     * @return
     *     the new instance of {@link VisaApplicationSearchResultEESType }
     */
    public VisaApplicationSearchResultEESType createVisaApplicationSearchResultEESType() {
        return new VisaApplicationSearchResultEESType();
    }

    /**
     * Create an instance of {@link VisaApplicationOverviewSearchResultVISType }
     * 
     * @return
     *     the new instance of {@link VisaApplicationOverviewSearchResultVISType }
     */
    public VisaApplicationOverviewSearchResultVISType createVisaApplicationOverviewSearchResultVISType() {
        return new VisaApplicationOverviewSearchResultVISType();
    }

    /**
     * Create an instance of {@link PersonalDataSearchVISRequestType }
     * 
     * @return
     *     the new instance of {@link PersonalDataSearchVISRequestType }
     */
    public PersonalDataSearchVISRequestType createPersonalDataSearchVISRequestType() {
        return new PersonalDataSearchVISRequestType();
    }

    /**
     * Create an instance of {@link TravelDocumentSearchVISRequestType }
     * 
     * @return
     *     the new instance of {@link TravelDocumentSearchVISRequestType }
     */
    public TravelDocumentSearchVISRequestType createTravelDocumentSearchVISRequestType() {
        return new TravelDocumentSearchVISRequestType();
    }

    /**
     * Create an instance of {@link IdentificationInVISRequestMessageType }
     * 
     * @return
     *     the new instance of {@link IdentificationInVISRequestMessageType }
     */
    public IdentificationInVISRequestMessageType createIdentificationInVISRequestMessageType() {
        return new IdentificationInVISRequestMessageType();
    }

    /**
     * Create an instance of {@link IdentificationInVISRequestMessageType.SearchData }
     * 
     * @return
     *     the new instance of {@link IdentificationInVISRequestMessageType.SearchData }
     */
    public IdentificationInVISRequestMessageType.SearchData createIdentificationInVISRequestMessageTypeSearchData() {
        return new IdentificationInVISRequestMessageType.SearchData();
    }

    /**
     * Create an instance of {@link VISEESVerificationByFingerprintResponse.Response }
     * 
     * @return
     *     the new instance of {@link VISEESVerificationByFingerprintResponse.Response }
     */
    public VISEESVerificationByFingerprintResponse.Response createVISEESVerificationByFingerprintResponseResponse() {
        return new VISEESVerificationByFingerprintResponse.Response();
    }

    /**
     * Create an instance of {@link VISEESVerificationByFingerprintResponse.Response.ProvidedSample }
     * 
     * @return
     *     the new instance of {@link VISEESVerificationByFingerprintResponse.Response.ProvidedSample }
     */
    public VISEESVerificationByFingerprintResponse.Response.ProvidedSample createVISEESVerificationByFingerprintResponseResponseProvidedSample() {
        return new VISEESVerificationByFingerprintResponse.Response.ProvidedSample();
    }

    /**
     * Create an instance of {@link VerificationByFPInVISRequestMessageType }
     * 
     * @return
     *     the new instance of {@link VerificationByFPInVISRequestMessageType }
     */
    public VerificationByFPInVISRequestMessageType createVerificationByFPInVISRequestMessageType() {
        return new VerificationByFPInVISRequestMessageType();
    }

    /**
     * Create an instance of {@link VISEESIdentificationResponse.Response }
     * 
     * @return
     *     the new instance of {@link VISEESIdentificationResponse.Response }
     */
    public VISEESIdentificationResponse.Response createVISEESIdentificationResponseResponse() {
        return new VISEESIdentificationResponse.Response();
    }

    /**
     * Create an instance of {@link VISEESIdentificationResponse.Response.ProvidedSample }
     * 
     * @return
     *     the new instance of {@link VISEESIdentificationResponse.Response.ProvidedSample }
     */
    public VISEESIdentificationResponse.Response.ProvidedSample createVISEESIdentificationResponseResponseProvidedSample() {
        return new VISEESIdentificationResponse.Response.ProvidedSample();
    }

    /**
     * Create an instance of {@link VISEESIdentificationRequest.Request }
     * 
     * @return
     *     the new instance of {@link VISEESIdentificationRequest.Request }
     */
    public VISEESIdentificationRequest.Request createVISEESIdentificationRequestRequest() {
        return new VISEESIdentificationRequest.Request();
    }

    /**
     * Create an instance of {@link VISEESIdentificationRequest.Request.SearchData }
     * 
     * @return
     *     the new instance of {@link VISEESIdentificationRequest.Request.SearchData }
     */
    public VISEESIdentificationRequest.Request.SearchData createVISEESIdentificationRequestRequestSearchData() {
        return new VISEESIdentificationRequest.Request.SearchData();
    }

    /**
     * Create an instance of {@link VISEESSearchByPersonalDataResponse.Response }
     * 
     * @return
     *     the new instance of {@link VISEESSearchByPersonalDataResponse.Response }
     */
    public VISEESSearchByPersonalDataResponse.Response createVISEESSearchByPersonalDataResponseResponse() {
        return new VISEESSearchByPersonalDataResponse.Response();
    }

    /**
     * Create an instance of {@link VISEESSearchByPersonalDataRequest.Request }
     * 
     * @return
     *     the new instance of {@link VISEESSearchByPersonalDataRequest.Request }
     */
    public VISEESSearchByPersonalDataRequest.Request createVISEESSearchByPersonalDataRequestRequest() {
        return new VISEESSearchByPersonalDataRequest.Request();
    }

    /**
     * Create an instance of {@link VISEESSearchByPersonalDataRequest.Request.SearchData }
     * 
     * @return
     *     the new instance of {@link VISEESSearchByPersonalDataRequest.Request.SearchData }
     */
    public VISEESSearchByPersonalDataRequest.Request.SearchData createVISEESSearchByPersonalDataRequestRequestSearchData() {
        return new VISEESSearchByPersonalDataRequest.Request.SearchData();
    }

    /**
     * Create an instance of {@link VISEESSearchByTravelDocumentResponse.Response }
     * 
     * @return
     *     the new instance of {@link VISEESSearchByTravelDocumentResponse.Response }
     */
    public VISEESSearchByTravelDocumentResponse.Response createVISEESSearchByTravelDocumentResponseResponse() {
        return new VISEESSearchByTravelDocumentResponse.Response();
    }

    /**
     * Create an instance of {@link ReturnCodesVISType }
     * 
     * @return
     *     the new instance of {@link ReturnCodesVISType }
     */
    public ReturnCodesVISType createReturnCodesVISType() {
        return new ReturnCodesVISType();
    }

    /**
     * Create an instance of {@link VISEESSearchByTravelDocumentRequest.Request }
     * 
     * @return
     *     the new instance of {@link VISEESSearchByTravelDocumentRequest.Request }
     */
    public VISEESSearchByTravelDocumentRequest.Request createVISEESSearchByTravelDocumentRequestRequest() {
        return new VISEESSearchByTravelDocumentRequest.Request();
    }

    /**
     * Create an instance of {@link HeaderVISBaseType }
     * 
     * @return
     *     the new instance of {@link HeaderVISBaseType }
     */
    public HeaderVISBaseType createHeaderVISBaseType() {
        return new HeaderVISBaseType();
    }

    /**
     * Create an instance of {@link VISEESVerificationByFingerprintRequest }
     * 
     * @return
     *     the new instance of {@link VISEESVerificationByFingerprintRequest }
     */
    public VISEESVerificationByFingerprintRequest createVISEESVerificationByFingerprintRequest() {
        return new VISEESVerificationByFingerprintRequest();
    }

    /**
     * Create an instance of {@link VISEESUpdateAuthorityNotification.Notification }
     * 
     * @return
     *     the new instance of {@link VISEESUpdateAuthorityNotification.Notification }
     */
    public VISEESUpdateAuthorityNotification.Notification createVISEESUpdateAuthorityNotificationNotification() {
        return new VISEESUpdateAuthorityNotification.Notification();
    }

    /**
     * Create an instance of {@link HeaderRequestType }
     * 
     * @return
     *     the new instance of {@link HeaderRequestType }
     */
    public HeaderRequestType createHeaderRequestType() {
        return new HeaderRequestType();
    }

    /**
     * Create an instance of {@link HeaderResponseType }
     * 
     * @return
     *     the new instance of {@link HeaderResponseType }
     */
    public HeaderResponseType createHeaderResponseType() {
        return new HeaderResponseType();
    }

    /**
     * Create an instance of {@link NationalitiesSearchType }
     * 
     * @return
     *     the new instance of {@link NationalitiesSearchType }
     */
    public NationalitiesSearchType createNationalitiesSearchType() {
        return new NationalitiesSearchType();
    }

    /**
     * Create an instance of {@link AuthorityBaseType }
     * 
     * @return
     *     the new instance of {@link AuthorityBaseType }
     */
    public AuthorityBaseType createAuthorityBaseType() {
        return new AuthorityBaseType();
    }

    /**
     * Create an instance of {@link AuthorityType }
     * 
     * @return
     *     the new instance of {@link AuthorityType }
     */
    public AuthorityType createAuthorityType() {
        return new AuthorityType();
    }

    /**
     * Create an instance of {@link FullNameGetEESType }
     * 
     * @return
     *     the new instance of {@link FullNameGetEESType }
     */
    public FullNameGetEESType createFullNameGetEESType() {
        return new FullNameGetEESType();
    }

    /**
     * Create an instance of {@link ApplicantReadEESType }
     * 
     * @return
     *     the new instance of {@link ApplicantReadEESType }
     */
    public ApplicantReadEESType createApplicantReadEESType() {
        return new ApplicantReadEESType();
    }

    /**
     * Create an instance of {@link HostPersonGetEESType }
     * 
     * @return
     *     the new instance of {@link HostPersonGetEESType }
     */
    public HostPersonGetEESType createHostPersonGetEESType() {
        return new HostPersonGetEESType();
    }

    /**
     * Create an instance of {@link HostGetEESType }
     * 
     * @return
     *     the new instance of {@link HostGetEESType }
     */
    public HostGetEESType createHostGetEESType() {
        return new HostGetEESType();
    }

    /**
     * Create an instance of {@link AddressGetEESType }
     * 
     * @return
     *     the new instance of {@link AddressGetEESType }
     */
    public AddressGetEESType createAddressGetEESType() {
        return new AddressGetEESType();
    }

    /**
     * Create an instance of {@link HostOrganisationGetEESType }
     * 
     * @return
     *     the new instance of {@link HostOrganisationGetEESType }
     */
    public HostOrganisationGetEESType createHostOrganisationGetEESType() {
        return new HostOrganisationGetEESType();
    }

    /**
     * Create an instance of {@link VerificationDecisionHistGetEESType }
     * 
     * @return
     *     the new instance of {@link VerificationDecisionHistGetEESType }
     */
    public VerificationDecisionHistGetEESType createVerificationDecisionHistGetEESType() {
        return new VerificationDecisionHistGetEESType();
    }

    /**
     * Create an instance of {@link VerificationDecisionHistChoiceEESType }
     * 
     * @return
     *     the new instance of {@link VerificationDecisionHistChoiceEESType }
     */
    public VerificationDecisionHistChoiceEESType createVerificationDecisionHistChoiceEESType() {
        return new VerificationDecisionHistChoiceEESType();
    }

    /**
     * Create an instance of {@link VisaDecisionGetEESType }
     * 
     * @return
     *     the new instance of {@link VisaDecisionGetEESType }
     */
    public VisaDecisionGetEESType createVisaDecisionGetEESType() {
        return new VisaDecisionGetEESType();
    }

    /**
     * Create an instance of {@link PeriodEESType }
     * 
     * @return
     *     the new instance of {@link PeriodEESType }
     */
    public PeriodEESType createPeriodEESType() {
        return new PeriodEESType();
    }

    /**
     * Create an instance of {@link TravelDocumentGetEESType }
     * 
     * @return
     *     the new instance of {@link TravelDocumentGetEESType }
     */
    public TravelDocumentGetEESType createTravelDocumentGetEESType() {
        return new TravelDocumentGetEESType();
    }

    /**
     * Create an instance of {@link TravelDocumentBaseEESType }
     * 
     * @return
     *     the new instance of {@link TravelDocumentBaseEESType }
     */
    public TravelDocumentBaseEESType createTravelDocumentBaseEESType() {
        return new TravelDocumentBaseEESType();
    }

    /**
     * Create an instance of {@link VisaCreationDecisionGetEESType }
     * 
     * @return
     *     the new instance of {@link VisaCreationDecisionGetEESType }
     */
    public VisaCreationDecisionGetEESType createVisaCreationDecisionGetEESType() {
        return new VisaCreationDecisionGetEESType();
    }

    /**
     * Create an instance of {@link VisaStickerGetEESType }
     * 
     * @return
     *     the new instance of {@link VisaStickerGetEESType }
     */
    public VisaStickerGetEESType createVisaStickerGetEESType() {
        return new VisaStickerGetEESType();
    }

    /**
     * Create an instance of {@link VisaStickerBaseEESType }
     * 
     * @return
     *     the new instance of {@link VisaStickerBaseEESType }
     */
    public VisaStickerBaseEESType createVisaStickerBaseEESType() {
        return new VisaStickerBaseEESType();
    }

    /**
     * Create an instance of {@link IssueVisaGetEESType }
     * 
     * @return
     *     the new instance of {@link IssueVisaGetEESType }
     */
    public IssueVisaGetEESType createIssueVisaGetEESType() {
        return new IssueVisaGetEESType();
    }

    /**
     * Create an instance of {@link FacialImageGetEESType }
     * 
     * @return
     *     the new instance of {@link FacialImageGetEESType }
     */
    public FacialImageGetEESType createFacialImageGetEESType() {
        return new FacialImageGetEESType();
    }

    /**
     * Create an instance of {@link ShortenValidityPeriodWithNewStickerGetEESType.ReducedDurationGrounds }
     * 
     * @return
     *     the new instance of {@link ShortenValidityPeriodWithNewStickerGetEESType.ReducedDurationGrounds }
     */
    public ShortenValidityPeriodWithNewStickerGetEESType.ReducedDurationGrounds createShortenValidityPeriodWithNewStickerGetEESTypeReducedDurationGrounds() {
        return new ShortenValidityPeriodWithNewStickerGetEESType.ReducedDurationGrounds();
    }

    /**
     * Create an instance of {@link ExtendVisaGetEESType.ExtensionGrounds }
     * 
     * @return
     *     the new instance of {@link ExtendVisaGetEESType.ExtensionGrounds }
     */
    public ExtendVisaGetEESType.ExtensionGrounds createExtendVisaGetEESTypeExtensionGrounds() {
        return new ExtendVisaGetEESType.ExtensionGrounds();
    }

    /**
     * Create an instance of {@link ExtendVisaWithoutNewStickerGetEESType.ExtensionGrounds }
     * 
     * @return
     *     the new instance of {@link ExtendVisaWithoutNewStickerGetEESType.ExtensionGrounds }
     */
    public ExtendVisaWithoutNewStickerGetEESType.ExtensionGrounds createExtendVisaWithoutNewStickerGetEESTypeExtensionGrounds() {
        return new ExtendVisaWithoutNewStickerGetEESType.ExtensionGrounds();
    }

    /**
     * Create an instance of {@link ResultReadCoreBorderDataEESType.Host }
     * 
     * @return
     *     the new instance of {@link ResultReadCoreBorderDataEESType.Host }
     */
    public ResultReadCoreBorderDataEESType.Host createResultReadCoreBorderDataEESTypeHost() {
        return new ResultReadCoreBorderDataEESType.Host();
    }

    /**
     * Create an instance of {@link ResultReadApplicationBaseEESType.FormerFamilyNames }
     * 
     * @return
     *     the new instance of {@link ResultReadApplicationBaseEESType.FormerFamilyNames }
     */
    public ResultReadApplicationBaseEESType.FormerFamilyNames createResultReadApplicationBaseEESTypeFormerFamilyNames() {
        return new ResultReadApplicationBaseEESType.FormerFamilyNames();
    }

    /**
     * Create an instance of {@link VisaApplicationSearchResultEESType.ApplicationData }
     * 
     * @return
     *     the new instance of {@link VisaApplicationSearchResultEESType.ApplicationData }
     */
    public VisaApplicationSearchResultEESType.ApplicationData createVisaApplicationSearchResultEESTypeApplicationData() {
        return new VisaApplicationSearchResultEESType.ApplicationData();
    }

    /**
     * Create an instance of {@link VisaApplicationSearchResultEESType.Decisions }
     * 
     * @return
     *     the new instance of {@link VisaApplicationSearchResultEESType.Decisions }
     */
    public VisaApplicationSearchResultEESType.Decisions createVisaApplicationSearchResultEESTypeDecisions() {
        return new VisaApplicationSearchResultEESType.Decisions();
    }

    /**
     * Create an instance of {@link VisaApplicationSearchResultEESType.FacialImages }
     * 
     * @return
     *     the new instance of {@link VisaApplicationSearchResultEESType.FacialImages }
     */
    public VisaApplicationSearchResultEESType.FacialImages createVisaApplicationSearchResultEESTypeFacialImages() {
        return new VisaApplicationSearchResultEESType.FacialImages();
    }

    /**
     * Create an instance of {@link VisaApplicationSearchResultEESType.GroupSynopses }
     * 
     * @return
     *     the new instance of {@link VisaApplicationSearchResultEESType.GroupSynopses }
     */
    public VisaApplicationSearchResultEESType.GroupSynopses createVisaApplicationSearchResultEESTypeGroupSynopses() {
        return new VisaApplicationSearchResultEESType.GroupSynopses();
    }

    /**
     * Create an instance of {@link VisaApplicationOverviewSearchResultVISType.FacialImage }
     * 
     * @return
     *     the new instance of {@link VisaApplicationOverviewSearchResultVISType.FacialImage }
     */
    public VisaApplicationOverviewSearchResultVISType.FacialImage createVisaApplicationOverviewSearchResultVISTypeFacialImage() {
        return new VisaApplicationOverviewSearchResultVISType.FacialImage();
    }

    /**
     * Create an instance of {@link PersonalDataSearchVISRequestType.Nationalities }
     * 
     * @return
     *     the new instance of {@link PersonalDataSearchVISRequestType.Nationalities }
     */
    public PersonalDataSearchVISRequestType.Nationalities createPersonalDataSearchVISRequestTypeNationalities() {
        return new PersonalDataSearchVISRequestType.Nationalities();
    }

    /**
     * Create an instance of {@link TravelDocumentSearchVISRequestType.Nationalities }
     * 
     * @return
     *     the new instance of {@link TravelDocumentSearchVISRequestType.Nationalities }
     */
    public TravelDocumentSearchVISRequestType.Nationalities createTravelDocumentSearchVISRequestTypeNationalities() {
        return new TravelDocumentSearchVISRequestType.Nationalities();
    }

    /**
     * Create an instance of {@link IdentificationInVISRequestMessageType.ScopeModifiers }
     * 
     * @return
     *     the new instance of {@link IdentificationInVISRequestMessageType.ScopeModifiers }
     */
    public IdentificationInVISRequestMessageType.ScopeModifiers createIdentificationInVISRequestMessageTypeScopeModifiers() {
        return new IdentificationInVISRequestMessageType.ScopeModifiers();
    }

    /**
     * Create an instance of {@link IdentificationInVISRequestMessageType.SearchData.FP }
     * 
     * @return
     *     the new instance of {@link IdentificationInVISRequestMessageType.SearchData.FP }
     */
    public IdentificationInVISRequestMessageType.SearchData.FP createIdentificationInVISRequestMessageTypeSearchDataFP() {
        return new IdentificationInVISRequestMessageType.SearchData.FP();
    }

    /**
     * Create an instance of {@link VISEESVerificationByFingerprintResponse.Response.ProvidedSample.SampleQuality }
     * 
     * @return
     *     the new instance of {@link VISEESVerificationByFingerprintResponse.Response.ProvidedSample.SampleQuality }
     */
    public VISEESVerificationByFingerprintResponse.Response.ProvidedSample.SampleQuality createVISEESVerificationByFingerprintResponseResponseProvidedSampleSampleQuality() {
        return new VISEESVerificationByFingerprintResponse.Response.ProvidedSample.SampleQuality();
    }

    /**
     * Create an instance of {@link VerificationByFPInVISRequestMessageType.ScopeModifiers }
     * 
     * @return
     *     the new instance of {@link VerificationByFPInVISRequestMessageType.ScopeModifiers }
     */
    public VerificationByFPInVISRequestMessageType.ScopeModifiers createVerificationByFPInVISRequestMessageTypeScopeModifiers() {
        return new VerificationByFPInVISRequestMessageType.ScopeModifiers();
    }

    /**
     * Create an instance of {@link VerificationByFPInVISRequestMessageType.FP }
     * 
     * @return
     *     the new instance of {@link VerificationByFPInVISRequestMessageType.FP }
     */
    public VerificationByFPInVISRequestMessageType.FP createVerificationByFPInVISRequestMessageTypeFP() {
        return new VerificationByFPInVISRequestMessageType.FP();
    }

    /**
     * Create an instance of {@link VISEESIdentificationResponse.Response.VisaApplicationOverview }
     * 
     * @return
     *     the new instance of {@link VISEESIdentificationResponse.Response.VisaApplicationOverview }
     */
    public VISEESIdentificationResponse.Response.VisaApplicationOverview createVISEESIdentificationResponseResponseVisaApplicationOverview() {
        return new VISEESIdentificationResponse.Response.VisaApplicationOverview();
    }

    /**
     * Create an instance of {@link VISEESIdentificationResponse.Response.VisaApplication }
     * 
     * @return
     *     the new instance of {@link VISEESIdentificationResponse.Response.VisaApplication }
     */
    public VISEESIdentificationResponse.Response.VisaApplication createVISEESIdentificationResponseResponseVisaApplication() {
        return new VISEESIdentificationResponse.Response.VisaApplication();
    }

    /**
     * Create an instance of {@link VISEESIdentificationResponse.Response.ProvidedSample.SampleQuality }
     * 
     * @return
     *     the new instance of {@link VISEESIdentificationResponse.Response.ProvidedSample.SampleQuality }
     */
    public VISEESIdentificationResponse.Response.ProvidedSample.SampleQuality createVISEESIdentificationResponseResponseProvidedSampleSampleQuality() {
        return new VISEESIdentificationResponse.Response.ProvidedSample.SampleQuality();
    }

    /**
     * Create an instance of {@link VISEESIdentificationRequest.Request.ScopeModifiers }
     * 
     * @return
     *     the new instance of {@link VISEESIdentificationRequest.Request.ScopeModifiers }
     */
    public VISEESIdentificationRequest.Request.ScopeModifiers createVISEESIdentificationRequestRequestScopeModifiers() {
        return new VISEESIdentificationRequest.Request.ScopeModifiers();
    }

    /**
     * Create an instance of {@link VISEESIdentificationRequest.Request.SearchData.FP }
     * 
     * @return
     *     the new instance of {@link VISEESIdentificationRequest.Request.SearchData.FP }
     */
    public VISEESIdentificationRequest.Request.SearchData.FP createVISEESIdentificationRequestRequestSearchDataFP() {
        return new VISEESIdentificationRequest.Request.SearchData.FP();
    }

    /**
     * Create an instance of {@link VISEESSearchByPersonalDataResponse.Response.VisaApplicationOverview }
     * 
     * @return
     *     the new instance of {@link VISEESSearchByPersonalDataResponse.Response.VisaApplicationOverview }
     */
    public VISEESSearchByPersonalDataResponse.Response.VisaApplicationOverview createVISEESSearchByPersonalDataResponseResponseVisaApplicationOverview() {
        return new VISEESSearchByPersonalDataResponse.Response.VisaApplicationOverview();
    }

    /**
     * Create an instance of {@link VISEESSearchByPersonalDataResponse.Response.VisaApplication }
     * 
     * @return
     *     the new instance of {@link VISEESSearchByPersonalDataResponse.Response.VisaApplication }
     */
    public VISEESSearchByPersonalDataResponse.Response.VisaApplication createVISEESSearchByPersonalDataResponseResponseVisaApplication() {
        return new VISEESSearchByPersonalDataResponse.Response.VisaApplication();
    }

    /**
     * Create an instance of {@link VISEESSearchByPersonalDataRequest.Request.ScopeModifiers }
     * 
     * @return
     *     the new instance of {@link VISEESSearchByPersonalDataRequest.Request.ScopeModifiers }
     */
    public VISEESSearchByPersonalDataRequest.Request.ScopeModifiers createVISEESSearchByPersonalDataRequestRequestScopeModifiers() {
        return new VISEESSearchByPersonalDataRequest.Request.ScopeModifiers();
    }

    /**
     * Create an instance of {@link VISEESSearchByPersonalDataRequest.Request.SearchData.TravelDocument }
     * 
     * @return
     *     the new instance of {@link VISEESSearchByPersonalDataRequest.Request.SearchData.TravelDocument }
     */
    public VISEESSearchByPersonalDataRequest.Request.SearchData.TravelDocument createVISEESSearchByPersonalDataRequestRequestSearchDataTravelDocument() {
        return new VISEESSearchByPersonalDataRequest.Request.SearchData.TravelDocument();
    }

    /**
     * Create an instance of {@link VISEESSearchByTravelDocumentResponse.Response.VisaApplicationOverview }
     * 
     * @return
     *     the new instance of {@link VISEESSearchByTravelDocumentResponse.Response.VisaApplicationOverview }
     */
    public VISEESSearchByTravelDocumentResponse.Response.VisaApplicationOverview createVISEESSearchByTravelDocumentResponseResponseVisaApplicationOverview() {
        return new VISEESSearchByTravelDocumentResponse.Response.VisaApplicationOverview();
    }

    /**
     * Create an instance of {@link VISEESSearchByTravelDocumentResponse.Response.VisaApplication }
     * 
     * @return
     *     the new instance of {@link VISEESSearchByTravelDocumentResponse.Response.VisaApplication }
     */
    public VISEESSearchByTravelDocumentResponse.Response.VisaApplication createVISEESSearchByTravelDocumentResponseResponseVisaApplication() {
        return new VISEESSearchByTravelDocumentResponse.Response.VisaApplication();
    }

    /**
     * Create an instance of {@link VISEESSearchByTravelDocumentResponse.Response.VisaApplicationNumber }
     * 
     * @return
     *     the new instance of {@link VISEESSearchByTravelDocumentResponse.Response.VisaApplicationNumber }
     */
    public VISEESSearchByTravelDocumentResponse.Response.VisaApplicationNumber createVISEESSearchByTravelDocumentResponseResponseVisaApplicationNumber() {
        return new VISEESSearchByTravelDocumentResponse.Response.VisaApplicationNumber();
    }

    /**
     * Create an instance of {@link ReturnCodesVISType.ErrorCodes }
     * 
     * @return
     *     the new instance of {@link ReturnCodesVISType.ErrorCodes }
     */
    public ReturnCodesVISType.ErrorCodes createReturnCodesVISTypeErrorCodes() {
        return new ReturnCodesVISType.ErrorCodes();
    }

    /**
     * Create an instance of {@link ReturnCodesVISType.WarningCodes }
     * 
     * @return
     *     the new instance of {@link ReturnCodesVISType.WarningCodes }
     */
    public ReturnCodesVISType.WarningCodes createReturnCodesVISTypeWarningCodes() {
        return new ReturnCodesVISType.WarningCodes();
    }

    /**
     * Create an instance of {@link ReturnCodesVISType.FieldCodes }
     * 
     * @return
     *     the new instance of {@link ReturnCodesVISType.FieldCodes }
     */
    public ReturnCodesVISType.FieldCodes createReturnCodesVISTypeFieldCodes() {
        return new ReturnCodesVISType.FieldCodes();
    }

    /**
     * Create an instance of {@link ReturnCodesVISType.InfoCodes }
     * 
     * @return
     *     the new instance of {@link ReturnCodesVISType.InfoCodes }
     */
    public ReturnCodesVISType.InfoCodes createReturnCodesVISTypeInfoCodes() {
        return new ReturnCodesVISType.InfoCodes();
    }

    /**
     * Create an instance of {@link VISEESSearchByTravelDocumentRequest.Request.ScopeModifiers }
     * 
     * @return
     *     the new instance of {@link VISEESSearchByTravelDocumentRequest.Request.ScopeModifiers }
     */
    public VISEESSearchByTravelDocumentRequest.Request.ScopeModifiers createVISEESSearchByTravelDocumentRequestRequestScopeModifiers() {
        return new VISEESSearchByTravelDocumentRequest.Request.ScopeModifiers();
    }

    /**
     * Create an instance of {@link VISEESSearchByTravelDocumentRequest.Request.SearchData }
     * 
     * @return
     *     the new instance of {@link VISEESSearchByTravelDocumentRequest.Request.SearchData }
     */
    public VISEESSearchByTravelDocumentRequest.Request.SearchData createVISEESSearchByTravelDocumentRequestRequestSearchData() {
        return new VISEESSearchByTravelDocumentRequest.Request.SearchData();
    }

}
