//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.cs_vis_nui_ees.xsd.v1;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per ApplicantReadEESType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="ApplicantReadEESType">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="ParentalAuthorityOrLegalGuardian" type="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}FullNameGetEESType" minOccurs="0"/>
 *         <element name="NationalityAtBirth" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}CT02_CountryOfNationalityType" minOccurs="0"/>
 *         <element name="Nationality" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}CT02_CountryOfNationalityType" minOccurs="0"/>
 *         <element name="ApplicantsHomeAddress" type="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}AddressGetEESType" minOccurs="0"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ApplicantReadEESType", propOrder = {
    "parentalAuthorityOrLegalGuardian",
    "nationalityAtBirth",
    "nationality",
    "applicantsHomeAddress"
})
public class ApplicantReadEESType {

    /**
     * Description: Parent's name of the applicant.
     * This is needed in case the applicant is a minor.
     * 
     */
    @XmlElement(name = "ParentalAuthorityOrLegalGuardian")
    protected FullNameGetEESType parentalAuthorityOrLegalGuardian;
    /**
     * Description: Nationality at birth of the applicant.
     * 
     */
    @XmlElement(name = "NationalityAtBirth")
    protected String nationalityAtBirth;
    /**
     * Description: This nationality is used for the application.
     * 
     */
    @XmlElement(name = "Nationality")
    protected String nationality;
    /**
     * Description: The home address of the applicant. It consists of a country street, number, postcode and city.
     * 
     */
    @XmlElement(name = "ApplicantsHomeAddress")
    protected AddressGetEESType applicantsHomeAddress;

    /**
     * Description: Parent's name of the applicant.
     * This is needed in case the applicant is a minor.
     * 
     * @return
     *     possible object is
     *     {@link FullNameGetEESType }
     *     
     */
    public FullNameGetEESType getParentalAuthorityOrLegalGuardian() {
        return parentalAuthorityOrLegalGuardian;
    }

    /**
     * Imposta il valore della proprietà parentalAuthorityOrLegalGuardian.
     * 
     * @param value
     *     allowed object is
     *     {@link FullNameGetEESType }
     *     
     * @see #getParentalAuthorityOrLegalGuardian()
     */
    public void setParentalAuthorityOrLegalGuardian(FullNameGetEESType value) {
        this.parentalAuthorityOrLegalGuardian = value;
    }

    /**
     * Description: Nationality at birth of the applicant.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNationalityAtBirth() {
        return nationalityAtBirth;
    }

    /**
     * Imposta il valore della proprietà nationalityAtBirth.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     * @see #getNationalityAtBirth()
     */
    public void setNationalityAtBirth(String value) {
        this.nationalityAtBirth = value;
    }

    /**
     * Description: This nationality is used for the application.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNationality() {
        return nationality;
    }

    /**
     * Imposta il valore della proprietà nationality.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     * @see #getNationality()
     */
    public void setNationality(String value) {
        this.nationality = value;
    }

    /**
     * Description: The home address of the applicant. It consists of a country street, number, postcode and city.
     * 
     * @return
     *     possible object is
     *     {@link AddressGetEESType }
     *     
     */
    public AddressGetEESType getApplicantsHomeAddress() {
        return applicantsHomeAddress;
    }

    /**
     * Imposta il valore della proprietà applicantsHomeAddress.
     * 
     * @param value
     *     allowed object is
     *     {@link AddressGetEESType }
     *     
     * @see #getApplicantsHomeAddress()
     */
    public void setApplicantsHomeAddress(AddressGetEESType value) {
        this.applicantsHomeAddress = value;
    }

}
