//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.cs_vis_nui_ees.xsd.v1;

import java.util.ArrayList;
import java.util.List;
import eu.europa.schengen.vis.xsd.v3.types.application.DecisionGetType;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * Description: All information needed to shorten validity period of a new sticker.
 * 
 * <p>Classe Java per ShortenValidityPeriodWithNewStickerGetEESType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="ShortenValidityPeriodWithNewStickerGetEESType">
 *   <complexContent>
 *     <extension base="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}DecisionGetType">
 *       <sequence>
 *         <element name="VisaSticker" type="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}VisaStickerGetEESType"/>
 *         <element name="IssuanceOnSeparateSheet" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}YesNoType"/>
 *         <element name="ReducedDurationGrounds">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="ReducedDurationGround" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}CT53_ReducedDurationGroundType" maxOccurs="unbounded"/>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *       </sequence>
 *     </extension>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ShortenValidityPeriodWithNewStickerGetEESType", propOrder = {
    "visaSticker",
    "issuanceOnSeparateSheet",
    "reducedDurationGrounds"
})
public class ShortenValidityPeriodWithNewStickerGetEESType
    extends DecisionGetType
{

    /**
     * Description: data of the visa sticker.
     * 
     */
    @XmlElement(name = "VisaSticker", required = true)
    protected VisaStickerGetEESType visaSticker;
    /**
     * Description: Information indicating that the visa has been issued on a separate sheet in accordance with Regulation (EC) No. 333/2002.
     * 
     */
    @XmlElement(name = "IssuanceOnSeparateSheet")
    protected boolean issuanceOnSeparateSheet;
    /**
     * Description: Optional grounds for the reduction of the period of validity as described in [VIS-PEP]. Values are defined within the code table "ReducedDurationGround".
     * 
     */
    @XmlElement(name = "ReducedDurationGrounds", required = true)
    protected ShortenValidityPeriodWithNewStickerGetEESType.ReducedDurationGrounds reducedDurationGrounds;

    /**
     * Description: data of the visa sticker.
     * 
     * @return
     *     possible object is
     *     {@link VisaStickerGetEESType }
     *     
     */
    public VisaStickerGetEESType getVisaSticker() {
        return visaSticker;
    }

    /**
     * Imposta il valore della proprietà visaSticker.
     * 
     * @param value
     *     allowed object is
     *     {@link VisaStickerGetEESType }
     *     
     * @see #getVisaSticker()
     */
    public void setVisaSticker(VisaStickerGetEESType value) {
        this.visaSticker = value;
    }

    /**
     * Description: Information indicating that the visa has been issued on a separate sheet in accordance with Regulation (EC) No. 333/2002.
     * 
     */
    public boolean isIssuanceOnSeparateSheet() {
        return issuanceOnSeparateSheet;
    }

    /**
     * Imposta il valore della proprietà issuanceOnSeparateSheet.
     * 
     */
    public void setIssuanceOnSeparateSheet(boolean value) {
        this.issuanceOnSeparateSheet = value;
    }

    /**
     * Description: Optional grounds for the reduction of the period of validity as described in [VIS-PEP]. Values are defined within the code table "ReducedDurationGround".
     * 
     * @return
     *     possible object is
     *     {@link ShortenValidityPeriodWithNewStickerGetEESType.ReducedDurationGrounds }
     *     
     */
    public ShortenValidityPeriodWithNewStickerGetEESType.ReducedDurationGrounds getReducedDurationGrounds() {
        return reducedDurationGrounds;
    }

    /**
     * Imposta il valore della proprietà reducedDurationGrounds.
     * 
     * @param value
     *     allowed object is
     *     {@link ShortenValidityPeriodWithNewStickerGetEESType.ReducedDurationGrounds }
     *     
     * @see #getReducedDurationGrounds()
     */
    public void setReducedDurationGrounds(ShortenValidityPeriodWithNewStickerGetEESType.ReducedDurationGrounds value) {
        this.reducedDurationGrounds = value;
    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="ReducedDurationGround" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}CT53_ReducedDurationGroundType" maxOccurs="unbounded"/>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "reducedDurationGround"
    })
    public static class ReducedDurationGrounds {

        @XmlElement(name = "ReducedDurationGround", required = true)
        protected List<String> reducedDurationGround;

        /**
         * Gets the value of the reducedDurationGround property.
         * 
         * <p>This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the reducedDurationGround property.</p>
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * </p>
         * <pre>
         * getReducedDurationGround().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link String }
         * </p>
         * 
         * 
         * @return
         *     The value of the reducedDurationGround property.
         */
        public List<String> getReducedDurationGround() {
            if (reducedDurationGround == null) {
                reducedDurationGround = new ArrayList<>();
            }
            return this.reducedDurationGround;
        }

    }

}
