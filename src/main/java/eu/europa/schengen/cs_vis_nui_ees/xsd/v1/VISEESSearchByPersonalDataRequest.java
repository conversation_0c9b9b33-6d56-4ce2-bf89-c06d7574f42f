//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.cs_vis_nui_ees.xsd.v1;

import eu.europa.schengen.ees.xsd.v1.CT02CountryOfNationalitySearchField;
import eu.europa.schengen.ees.xsd.v1.PagingRequestType;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per anonymous complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType>
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="Header" type="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}HeaderVISBaseType"/>
 *         <element name="Request">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="Paging" type="{http://www.europa.eu/schengen/ees/xsd/v1}PagingRequestType" minOccurs="0"/>
 *                   <element name="ScopeModifiers" minOccurs="0">
 *                     <complexType>
 *                       <complexContent>
 *                         <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           <sequence>
 *                             <element name="VisaApplications" type="{http://www.europa.eu/schengen/ees/xsd/v1}ST508_VisaApplicationsModifierType"/>
 *                           </sequence>
 *                         </restriction>
 *                       </complexContent>
 *                     </complexType>
 *                   </element>
 *                   <element name="SearchData">
 *                     <complexType>
 *                       <complexContent>
 *                         <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           <sequence>
 *                             <element name="TravelDocument">
 *                               <complexType>
 *                                 <complexContent>
 *                                   <extension base="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}PersonalDataSearchVISRequestType">
 *                                     <sequence>
 *                                       <element name="IssuingCountry" type="{http://www.europa.eu/schengen/ees/xsd/v1}CT02_CountryOfNationalitySearchField" minOccurs="0"/>
 *                                     </sequence>
 *                                   </extension>
 *                                 </complexContent>
 *                               </complexType>
 *                             </element>
 *                           </sequence>
 *                         </restriction>
 *                       </complexContent>
 *                     </complexType>
 *                   </element>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "header",
    "request"
})
@XmlRootElement(name = "VIS-EES.SearchByPersonalDataRequest")
public class VISEESSearchByPersonalDataRequest {

    @XmlElement(name = "Header", required = true)
    protected HeaderVISBaseType header;
    @XmlElement(name = "Request", required = true)
    protected VISEESSearchByPersonalDataRequest.Request request;

    /**
     * Recupera il valore della proprietà header.
     * 
     * @return
     *     possible object is
     *     {@link HeaderVISBaseType }
     *     
     */
    public HeaderVISBaseType getHeader() {
        return header;
    }

    /**
     * Imposta il valore della proprietà header.
     * 
     * @param value
     *     allowed object is
     *     {@link HeaderVISBaseType }
     *     
     */
    public void setHeader(HeaderVISBaseType value) {
        this.header = value;
    }

    /**
     * Recupera il valore della proprietà request.
     * 
     * @return
     *     possible object is
     *     {@link VISEESSearchByPersonalDataRequest.Request }
     *     
     */
    public VISEESSearchByPersonalDataRequest.Request getRequest() {
        return request;
    }

    /**
     * Imposta il valore della proprietà request.
     * 
     * @param value
     *     allowed object is
     *     {@link VISEESSearchByPersonalDataRequest.Request }
     *     
     */
    public void setRequest(VISEESSearchByPersonalDataRequest.Request value) {
        this.request = value;
    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="Paging" type="{http://www.europa.eu/schengen/ees/xsd/v1}PagingRequestType" minOccurs="0"/>
     *         <element name="ScopeModifiers" minOccurs="0">
     *           <complexType>
     *             <complexContent>
     *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 <sequence>
     *                   <element name="VisaApplications" type="{http://www.europa.eu/schengen/ees/xsd/v1}ST508_VisaApplicationsModifierType"/>
     *                 </sequence>
     *               </restriction>
     *             </complexContent>
     *           </complexType>
     *         </element>
     *         <element name="SearchData">
     *           <complexType>
     *             <complexContent>
     *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 <sequence>
     *                   <element name="TravelDocument">
     *                     <complexType>
     *                       <complexContent>
     *                         <extension base="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}PersonalDataSearchVISRequestType">
     *                           <sequence>
     *                             <element name="IssuingCountry" type="{http://www.europa.eu/schengen/ees/xsd/v1}CT02_CountryOfNationalitySearchField" minOccurs="0"/>
     *                           </sequence>
     *                         </extension>
     *                       </complexContent>
     *                     </complexType>
     *                   </element>
     *                 </sequence>
     *               </restriction>
     *             </complexContent>
     *           </complexType>
     *         </element>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "paging",
        "scopeModifiers",
        "searchData"
    })
    public static class Request {

        @XmlElement(name = "Paging")
        protected PagingRequestType paging;
        @XmlElement(name = "ScopeModifiers")
        protected VISEESSearchByPersonalDataRequest.Request.ScopeModifiers scopeModifiers;
        @XmlElement(name = "SearchData", required = true)
        protected VISEESSearchByPersonalDataRequest.Request.SearchData searchData;

        /**
         * Recupera il valore della proprietà paging.
         * 
         * @return
         *     possible object is
         *     {@link PagingRequestType }
         *     
         */
        public PagingRequestType getPaging() {
            return paging;
        }

        /**
         * Imposta il valore della proprietà paging.
         * 
         * @param value
         *     allowed object is
         *     {@link PagingRequestType }
         *     
         */
        public void setPaging(PagingRequestType value) {
            this.paging = value;
        }

        /**
         * Recupera il valore della proprietà scopeModifiers.
         * 
         * @return
         *     possible object is
         *     {@link VISEESSearchByPersonalDataRequest.Request.ScopeModifiers }
         *     
         */
        public VISEESSearchByPersonalDataRequest.Request.ScopeModifiers getScopeModifiers() {
            return scopeModifiers;
        }

        /**
         * Imposta il valore della proprietà scopeModifiers.
         * 
         * @param value
         *     allowed object is
         *     {@link VISEESSearchByPersonalDataRequest.Request.ScopeModifiers }
         *     
         */
        public void setScopeModifiers(VISEESSearchByPersonalDataRequest.Request.ScopeModifiers value) {
            this.scopeModifiers = value;
        }

        /**
         * Recupera il valore della proprietà searchData.
         * 
         * @return
         *     possible object is
         *     {@link VISEESSearchByPersonalDataRequest.Request.SearchData }
         *     
         */
        public VISEESSearchByPersonalDataRequest.Request.SearchData getSearchData() {
            return searchData;
        }

        /**
         * Imposta il valore della proprietà searchData.
         * 
         * @param value
         *     allowed object is
         *     {@link VISEESSearchByPersonalDataRequest.Request.SearchData }
         *     
         */
        public void setSearchData(VISEESSearchByPersonalDataRequest.Request.SearchData value) {
            this.searchData = value;
        }


        /**
         * <p>Classe Java per anonymous complex type.</p>
         * 
         * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
         * 
         * <pre>{@code
         * <complexType>
         *   <complexContent>
         *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       <sequence>
         *         <element name="VisaApplications" type="{http://www.europa.eu/schengen/ees/xsd/v1}ST508_VisaApplicationsModifierType"/>
         *       </sequence>
         *     </restriction>
         *   </complexContent>
         * </complexType>
         * }</pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "visaApplications"
        })
        public static class ScopeModifiers {

            /**
             * If not specified then application overviews are returned.
             * 
             */
            @XmlElement(name = "VisaApplications", required = true)
            protected String visaApplications;

            /**
             * If not specified then application overviews are returned.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getVisaApplications() {
                return visaApplications;
            }

            /**
             * Imposta il valore della proprietà visaApplications.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             * @see #getVisaApplications()
             */
            public void setVisaApplications(String value) {
                this.visaApplications = value;
            }

        }


        /**
         * <p>Classe Java per anonymous complex type.</p>
         * 
         * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
         * 
         * <pre>{@code
         * <complexType>
         *   <complexContent>
         *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       <sequence>
         *         <element name="TravelDocument">
         *           <complexType>
         *             <complexContent>
         *               <extension base="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}PersonalDataSearchVISRequestType">
         *                 <sequence>
         *                   <element name="IssuingCountry" type="{http://www.europa.eu/schengen/ees/xsd/v1}CT02_CountryOfNationalitySearchField" minOccurs="0"/>
         *                 </sequence>
         *               </extension>
         *             </complexContent>
         *           </complexType>
         *         </element>
         *       </sequence>
         *     </restriction>
         *   </complexContent>
         * </complexType>
         * }</pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "travelDocument"
        })
        public static class SearchData {

            @XmlElement(name = "TravelDocument", required = true)
            protected VISEESSearchByPersonalDataRequest.Request.SearchData.TravelDocument travelDocument;

            /**
             * Recupera il valore della proprietà travelDocument.
             * 
             * @return
             *     possible object is
             *     {@link VISEESSearchByPersonalDataRequest.Request.SearchData.TravelDocument }
             *     
             */
            public VISEESSearchByPersonalDataRequest.Request.SearchData.TravelDocument getTravelDocument() {
                return travelDocument;
            }

            /**
             * Imposta il valore della proprietà travelDocument.
             * 
             * @param value
             *     allowed object is
             *     {@link VISEESSearchByPersonalDataRequest.Request.SearchData.TravelDocument }
             *     
             */
            public void setTravelDocument(VISEESSearchByPersonalDataRequest.Request.SearchData.TravelDocument value) {
                this.travelDocument = value;
            }


            /**
             * <p>Classe Java per anonymous complex type.</p>
             * 
             * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
             * 
             * <pre>{@code
             * <complexType>
             *   <complexContent>
             *     <extension base="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}PersonalDataSearchVISRequestType">
             *       <sequence>
             *         <element name="IssuingCountry" type="{http://www.europa.eu/schengen/ees/xsd/v1}CT02_CountryOfNationalitySearchField" minOccurs="0"/>
             *       </sequence>
             *     </extension>
             *   </complexContent>
             * </complexType>
             * }</pre>
             * 
             * 
             */
            @XmlAccessorType(XmlAccessType.FIELD)
            @XmlType(name = "", propOrder = {
                "issuingCountry"
            })
            public static class TravelDocument
                extends PersonalDataSearchVISRequestType
            {

                @XmlElement(name = "IssuingCountry")
                protected CT02CountryOfNationalitySearchField issuingCountry;

                /**
                 * Recupera il valore della proprietà issuingCountry.
                 * 
                 * @return
                 *     possible object is
                 *     {@link CT02CountryOfNationalitySearchField }
                 *     
                 */
                public CT02CountryOfNationalitySearchField getIssuingCountry() {
                    return issuingCountry;
                }

                /**
                 * Imposta il valore della proprietà issuingCountry.
                 * 
                 * @param value
                 *     allowed object is
                 *     {@link CT02CountryOfNationalitySearchField }
                 *     
                 */
                public void setIssuingCountry(CT02CountryOfNationalitySearchField value) {
                    this.issuingCountry = value;
                }

            }

        }

    }

}
