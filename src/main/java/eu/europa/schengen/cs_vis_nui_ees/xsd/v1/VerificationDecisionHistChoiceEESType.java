//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.cs_vis_nui_ees.xsd.v1;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSeeAlso;
import jakarta.xml.bind.annotation.XmlType;


/**
 * Description: Information describing the decision.
 * 
 * <p>Classe Java per VerificationDecisionHistChoiceEESType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="VerificationDecisionHistChoiceEESType">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <choice>
 *         <element name="VisaDecision" type="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}VisaDecisionGetEESType"/>
 *         <element name="VisaCreationDecision" type="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}VisaCreationDecisionGetEESType"/>
 *       </choice>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "VerificationDecisionHistChoiceEESType", propOrder = {
    "visaDecision",
    "visaCreationDecision"
})
@XmlSeeAlso({
    VerificationDecisionHistGetEESType.class
})
public class VerificationDecisionHistChoiceEESType {

    /**
     * Description: Information of an VisaDecision.
     * 
     */
    @XmlElement(name = "VisaDecision")
    protected VisaDecisionGetEESType visaDecision;
    /**
     * Description: Information of an VisaCreationDecision.
     * 
     */
    @XmlElement(name = "VisaCreationDecision")
    protected VisaCreationDecisionGetEESType visaCreationDecision;

    /**
     * Description: Information of an VisaDecision.
     * 
     * @return
     *     possible object is
     *     {@link VisaDecisionGetEESType }
     *     
     */
    public VisaDecisionGetEESType getVisaDecision() {
        return visaDecision;
    }

    /**
     * Imposta il valore della proprietà visaDecision.
     * 
     * @param value
     *     allowed object is
     *     {@link VisaDecisionGetEESType }
     *     
     * @see #getVisaDecision()
     */
    public void setVisaDecision(VisaDecisionGetEESType value) {
        this.visaDecision = value;
    }

    /**
     * Description: Information of an VisaCreationDecision.
     * 
     * @return
     *     possible object is
     *     {@link VisaCreationDecisionGetEESType }
     *     
     */
    public VisaCreationDecisionGetEESType getVisaCreationDecision() {
        return visaCreationDecision;
    }

    /**
     * Imposta il valore della proprietà visaCreationDecision.
     * 
     * @param value
     *     allowed object is
     *     {@link VisaCreationDecisionGetEESType }
     *     
     * @see #getVisaCreationDecision()
     */
    public void setVisaCreationDecision(VisaCreationDecisionGetEESType value) {
        this.visaCreationDecision = value;
    }

}
