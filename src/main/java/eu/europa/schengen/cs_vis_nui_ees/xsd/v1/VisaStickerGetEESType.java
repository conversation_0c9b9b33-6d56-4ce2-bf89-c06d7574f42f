//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.cs_vis_nui_ees.xsd.v1;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * Description: Information describing a Visa Sticker.
 * 
 * <p>Classe Java per VisaStickerGetEESType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="VisaStickerGetEESType">
 *   <complexContent>
 *     <extension base="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}VisaStickerBaseEESType">
 *       <sequence>
 *         <element name="VisaStickerStatus" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}ST32_VisaStickerStatusType"/>
 *       </sequence>
 *     </extension>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "VisaStickerGetEESType", propOrder = {
    "visaStickerStatus"
})
public class VisaStickerGetEESType
    extends VisaStickerBaseEESType
{

    /**
     * Description: Current status of the visa sticker.
     * 
     */
    @XmlElement(name = "VisaStickerStatus", required = true)
    protected String visaStickerStatus;

    /**
     * Description: Current status of the visa sticker.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getVisaStickerStatus() {
        return visaStickerStatus;
    }

    /**
     * Imposta il valore della proprietà visaStickerStatus.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     * @see #getVisaStickerStatus()
     */
    public void setVisaStickerStatus(String value) {
        this.visaStickerStatus = value;
    }

}
