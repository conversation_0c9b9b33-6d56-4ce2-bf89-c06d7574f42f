//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.cs_vis_nui_ees.xsd.v1;

import java.util.ArrayList;
import java.util.List;
import eu.europa.schengen.ees.xsd.v1.AnyNameSearchField;
import eu.europa.schengen.ees.xsd.v1.CT04GenderSearchField;
import eu.europa.schengen.ees.xsd.v1.CT512TravelDocumentTypeSearchField;
import eu.europa.schengen.ees.xsd.v1.NameSearchField;
import eu.europa.schengen.ees.xsd.v1.NumberSearchField;
import eu.europa.schengen.ees.xsd.v1.PseudoDateSearchField;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlAttribute;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * Fields of the search operation
 * 
 * 				[Article 16(1)(a)] Surname (family name); first name or names (given names); date of birth; nationality or nationalities; sex.
 * 				[Article 16(1)(b)] The type and number of the travel document or documents and three letter code of the issuing country of the travel document or documents.
 * 				[Article 16(1)(c)] The date of expiry of the validity of the travel document or documents.
 * 
 * <p>Classe Java per TravelDocumentSearchVISRequestType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="TravelDocumentSearchVISRequestType">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="FamilyName" type="{http://www.europa.eu/schengen/ees/xsd/v1}AnyNameSearchField"/>
 *         <element name="FirstName" type="{http://www.europa.eu/schengen/ees/xsd/v1}NameSearchField" maxOccurs="unbounded" minOccurs="0"/>
 *         <element name="DateOfBirth" type="{http://www.europa.eu/schengen/ees/xsd/v1}PseudoDateSearchField"/>
 *         <element name="Nationalities">
 *           <complexType>
 *             <complexContent>
 *               <extension base="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}NationalitiesSearchType">
 *                 <attribute name="Weight" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}WeightType" />
 *               </extension>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *         <element name="Gender" type="{http://www.europa.eu/schengen/ees/xsd/v1}CT04_GenderSearchField"/>
 *         <element name="DocumentNumber" type="{http://www.europa.eu/schengen/ees/xsd/v1}NumberSearchField"/>
 *         <element name="DocumentType" type="{http://www.europa.eu/schengen/ees/xsd/v1}CT512_TravelDocumentTypeSearchField"/>
 *         <element name="ValidUntil" type="{http://www.europa.eu/schengen/ees/xsd/v1}PseudoDateSearchField"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "TravelDocumentSearchVISRequestType", propOrder = {
    "familyName",
    "firstName",
    "dateOfBirth",
    "nationalities",
    "gender",
    "documentNumber",
    "documentType",
    "validUntil"
})
public class TravelDocumentSearchVISRequestType {

    /**
     * Surname (family name); first name(s) (given names) (with any name modifier).
     * 
     */
    @XmlElement(name = "FamilyName", required = true)
    protected AnyNameSearchField familyName;
    /**
     * First name(s) (given names).
     * 
     */
    @XmlElement(name = "FirstName")
    protected List<NameSearchField> firstName;
    /**
     * Date of birth.
     * 
     */
    @XmlElement(name = "DateOfBirth", required = true)
    protected PseudoDateSearchField dateOfBirth;
    /**
     * Current nationality or nationalities.
     * 
     */
    @XmlElement(name = "Nationalities", required = true)
    protected TravelDocumentSearchVISRequestType.Nationalities nationalities;
    /**
     * Sex.
     * 
     */
    @XmlElement(name = "Gender", required = true)
    protected CT04GenderSearchField gender;
    /**
     * Number of the travel document.
     * 
     */
    @XmlElement(name = "DocumentNumber", required = true)
    protected NumberSearchField documentNumber;
    /**
     * Type of the travel document.
     * 
     */
    @XmlElement(name = "DocumentType", required = true)
    protected CT512TravelDocumentTypeSearchField documentType;
    /**
     * The date of expiry of the validity of the travel document or documents.
     * 
     */
    @XmlElement(name = "ValidUntil", required = true)
    protected PseudoDateSearchField validUntil;

    /**
     * Surname (family name); first name(s) (given names) (with any name modifier).
     * 
     * @return
     *     possible object is
     *     {@link AnyNameSearchField }
     *     
     */
    public AnyNameSearchField getFamilyName() {
        return familyName;
    }

    /**
     * Imposta il valore della proprietà familyName.
     * 
     * @param value
     *     allowed object is
     *     {@link AnyNameSearchField }
     *     
     * @see #getFamilyName()
     */
    public void setFamilyName(AnyNameSearchField value) {
        this.familyName = value;
    }

    /**
     * First name(s) (given names).
     * 
     * Gets the value of the firstName property.
     * 
     * <p>This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the firstName property.</p>
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * </p>
     * <pre>
     * getFirstName().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link NameSearchField }
     * </p>
     * 
     * 
     * @return
     *     The value of the firstName property.
     */
    public List<NameSearchField> getFirstName() {
        if (firstName == null) {
            firstName = new ArrayList<>();
        }
        return this.firstName;
    }

    /**
     * Date of birth.
     * 
     * @return
     *     possible object is
     *     {@link PseudoDateSearchField }
     *     
     */
    public PseudoDateSearchField getDateOfBirth() {
        return dateOfBirth;
    }

    /**
     * Imposta il valore della proprietà dateOfBirth.
     * 
     * @param value
     *     allowed object is
     *     {@link PseudoDateSearchField }
     *     
     * @see #getDateOfBirth()
     */
    public void setDateOfBirth(PseudoDateSearchField value) {
        this.dateOfBirth = value;
    }

    /**
     * Current nationality or nationalities.
     * 
     * @return
     *     possible object is
     *     {@link TravelDocumentSearchVISRequestType.Nationalities }
     *     
     */
    public TravelDocumentSearchVISRequestType.Nationalities getNationalities() {
        return nationalities;
    }

    /**
     * Imposta il valore della proprietà nationalities.
     * 
     * @param value
     *     allowed object is
     *     {@link TravelDocumentSearchVISRequestType.Nationalities }
     *     
     * @see #getNationalities()
     */
    public void setNationalities(TravelDocumentSearchVISRequestType.Nationalities value) {
        this.nationalities = value;
    }

    /**
     * Sex.
     * 
     * @return
     *     possible object is
     *     {@link CT04GenderSearchField }
     *     
     */
    public CT04GenderSearchField getGender() {
        return gender;
    }

    /**
     * Imposta il valore della proprietà gender.
     * 
     * @param value
     *     allowed object is
     *     {@link CT04GenderSearchField }
     *     
     * @see #getGender()
     */
    public void setGender(CT04GenderSearchField value) {
        this.gender = value;
    }

    /**
     * Number of the travel document.
     * 
     * @return
     *     possible object is
     *     {@link NumberSearchField }
     *     
     */
    public NumberSearchField getDocumentNumber() {
        return documentNumber;
    }

    /**
     * Imposta il valore della proprietà documentNumber.
     * 
     * @param value
     *     allowed object is
     *     {@link NumberSearchField }
     *     
     * @see #getDocumentNumber()
     */
    public void setDocumentNumber(NumberSearchField value) {
        this.documentNumber = value;
    }

    /**
     * Type of the travel document.
     * 
     * @return
     *     possible object is
     *     {@link CT512TravelDocumentTypeSearchField }
     *     
     */
    public CT512TravelDocumentTypeSearchField getDocumentType() {
        return documentType;
    }

    /**
     * Imposta il valore della proprietà documentType.
     * 
     * @param value
     *     allowed object is
     *     {@link CT512TravelDocumentTypeSearchField }
     *     
     * @see #getDocumentType()
     */
    public void setDocumentType(CT512TravelDocumentTypeSearchField value) {
        this.documentType = value;
    }

    /**
     * The date of expiry of the validity of the travel document or documents.
     * 
     * @return
     *     possible object is
     *     {@link PseudoDateSearchField }
     *     
     */
    public PseudoDateSearchField getValidUntil() {
        return validUntil;
    }

    /**
     * Imposta il valore della proprietà validUntil.
     * 
     * @param value
     *     allowed object is
     *     {@link PseudoDateSearchField }
     *     
     * @see #getValidUntil()
     */
    public void setValidUntil(PseudoDateSearchField value) {
        this.validUntil = value;
    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <extension base="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}NationalitiesSearchType">
     *       <attribute name="Weight" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}WeightType" />
     *     </extension>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "")
    public static class Nationalities
        extends NationalitiesSearchType
    {

        /**
         * Description: End-user defined weights
         * 
         */
        @XmlAttribute(name = "Weight")
        protected Short weight;

        /**
         * Description: End-user defined weights
         * 
         * @return
         *     possible object is
         *     {@link Short }
         *     
         */
        public Short getWeight() {
            return weight;
        }

        /**
         * Imposta il valore della proprietà weight.
         * 
         * @param value
         *     allowed object is
         *     {@link Short }
         *     
         * @see #getWeight()
         */
        public void setWeight(Short value) {
            this.weight = value;
        }

    }

}
