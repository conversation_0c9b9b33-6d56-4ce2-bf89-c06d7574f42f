//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.cs_vis_nui_ees.xsd.v1;

import eu.europa.schengen.vis.xsd.v3.types.application.TransTextType;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * Decription: Name of a person it means surname and firstname.
 * 
 * <p>Classe Java per FullNameGetEESType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="FullNameGetEESType">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="FamilyName" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}TransTextType"/>
 *         <element name="Firstnames" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}TransTextType" minOccurs="0"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "FullNameGetEESType", propOrder = {
    "familyName",
    "firstnames"
})
public class FullNameGetEESType {

    /**
     * Description: This attribute contains all surnames of a person. If a person happens to have more than one surname, the names are concatenated using a comma.
     * 
     */
    @XmlElement(name = "FamilyName", required = true)
    protected TransTextType familyName;
    /**
     * Description: This attribute contains all first names of a person. If there is more than one first name, the names are concatenated using a comma.
     * 
     */
    @XmlElement(name = "Firstnames")
    protected TransTextType firstnames;

    /**
     * Description: This attribute contains all surnames of a person. If a person happens to have more than one surname, the names are concatenated using a comma.
     * 
     * @return
     *     possible object is
     *     {@link TransTextType }
     *     
     */
    public TransTextType getFamilyName() {
        return familyName;
    }

    /**
     * Imposta il valore della proprietà familyName.
     * 
     * @param value
     *     allowed object is
     *     {@link TransTextType }
     *     
     * @see #getFamilyName()
     */
    public void setFamilyName(TransTextType value) {
        this.familyName = value;
    }

    /**
     * Description: This attribute contains all first names of a person. If there is more than one first name, the names are concatenated using a comma.
     * 
     * @return
     *     possible object is
     *     {@link TransTextType }
     *     
     */
    public TransTextType getFirstnames() {
        return firstnames;
    }

    /**
     * Imposta il valore della proprietà firstnames.
     * 
     * @param value
     *     allowed object is
     *     {@link TransTextType }
     *     
     * @see #getFirstnames()
     */
    public void setFirstnames(TransTextType value) {
        this.firstnames = value;
    }

}
