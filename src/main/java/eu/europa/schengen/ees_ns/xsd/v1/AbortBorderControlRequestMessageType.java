//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.ees_ns.xsd.v1;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per AbortBorderControlRequestMessageType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="AbortBorderControlRequestMessageType">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="ScopeModifiers" type="{http://www.europa.eu/schengen/ees-ns/xsd/v1}WFEScopeModifiersType" minOccurs="0"/>
 *         <element name="CollectedData">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="TravelDocument">
 *                     <complexType>
 *                       <complexContent>
 *                         <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           <sequence>
 *                             <element name="DocumentNumber" type="{http://www.europa.eu/schengen/shared/xsd/v1}TravelDocumentNumberType"/>
 *                           </sequence>
 *                         </restriction>
 *                       </complexContent>
 *                     </complexType>
 *                   </element>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *         <element name="BorderCrossingPoint" type="{http://www.europa.eu/schengen/shared/xsd/v1}AuthorityUniqueIDType"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "AbortBorderControlRequestMessageType", propOrder = {
    "scopeModifiers",
    "collectedData",
    "borderCrossingPoint"
})
public class AbortBorderControlRequestMessageType {

    @XmlElement(name = "ScopeModifiers")
    protected WFEScopeModifiersType scopeModifiers;
    @XmlElement(name = "CollectedData", required = true)
    protected AbortBorderControlRequestMessageType.CollectedData collectedData;
    @XmlElement(name = "BorderCrossingPoint", required = true)
    protected String borderCrossingPoint;

    /**
     * Recupera il valore della proprietà scopeModifiers.
     * 
     * @return
     *     possible object is
     *     {@link WFEScopeModifiersType }
     *     
     */
    public WFEScopeModifiersType getScopeModifiers() {
        return scopeModifiers;
    }

    /**
     * Imposta il valore della proprietà scopeModifiers.
     * 
     * @param value
     *     allowed object is
     *     {@link WFEScopeModifiersType }
     *     
     */
    public void setScopeModifiers(WFEScopeModifiersType value) {
        this.scopeModifiers = value;
    }

    /**
     * Recupera il valore della proprietà collectedData.
     * 
     * @return
     *     possible object is
     *     {@link AbortBorderControlRequestMessageType.CollectedData }
     *     
     */
    public AbortBorderControlRequestMessageType.CollectedData getCollectedData() {
        return collectedData;
    }

    /**
     * Imposta il valore della proprietà collectedData.
     * 
     * @param value
     *     allowed object is
     *     {@link AbortBorderControlRequestMessageType.CollectedData }
     *     
     */
    public void setCollectedData(AbortBorderControlRequestMessageType.CollectedData value) {
        this.collectedData = value;
    }

    /**
     * Recupera il valore della proprietà borderCrossingPoint.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBorderCrossingPoint() {
        return borderCrossingPoint;
    }

    /**
     * Imposta il valore della proprietà borderCrossingPoint.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBorderCrossingPoint(String value) {
        this.borderCrossingPoint = value;
    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="TravelDocument">
     *           <complexType>
     *             <complexContent>
     *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 <sequence>
     *                   <element name="DocumentNumber" type="{http://www.europa.eu/schengen/shared/xsd/v1}TravelDocumentNumberType"/>
     *                 </sequence>
     *               </restriction>
     *             </complexContent>
     *           </complexType>
     *         </element>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "travelDocument"
    })
    public static class CollectedData {

        @XmlElement(name = "TravelDocument", required = true)
        protected AbortBorderControlRequestMessageType.CollectedData.TravelDocument travelDocument;

        /**
         * Recupera il valore della proprietà travelDocument.
         * 
         * @return
         *     possible object is
         *     {@link AbortBorderControlRequestMessageType.CollectedData.TravelDocument }
         *     
         */
        public AbortBorderControlRequestMessageType.CollectedData.TravelDocument getTravelDocument() {
            return travelDocument;
        }

        /**
         * Imposta il valore della proprietà travelDocument.
         * 
         * @param value
         *     allowed object is
         *     {@link AbortBorderControlRequestMessageType.CollectedData.TravelDocument }
         *     
         */
        public void setTravelDocument(AbortBorderControlRequestMessageType.CollectedData.TravelDocument value) {
            this.travelDocument = value;
        }


        /**
         * <p>Classe Java per anonymous complex type.</p>
         * 
         * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
         * 
         * <pre>{@code
         * <complexType>
         *   <complexContent>
         *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       <sequence>
         *         <element name="DocumentNumber" type="{http://www.europa.eu/schengen/shared/xsd/v1}TravelDocumentNumberType"/>
         *       </sequence>
         *     </restriction>
         *   </complexContent>
         * </complexType>
         * }</pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "documentNumber"
        })
        public static class TravelDocument {

            @XmlElement(name = "DocumentNumber", required = true)
            protected String documentNumber;

            /**
             * Recupera il valore della proprietà documentNumber.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getDocumentNumber() {
                return documentNumber;
            }

            /**
             * Imposta il valore della proprietà documentNumber.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setDocumentNumber(String value) {
                this.documentNumber = value;
            }

        }

    }

}
