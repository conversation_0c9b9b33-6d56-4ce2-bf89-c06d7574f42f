//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.ees_ns.xsd.v1;

import java.util.ArrayList;
import java.util.List;
import eu.europa.schengen.cs_vis_nui_ees.xsd.v1.VisaApplicationOverviewSearchResultVISType;
import eu.europa.schengen.cs_vis_nui_ees.xsd.v1.VisaApplicationSearchResultEESType;
import eu.europa.schengen.ees.xsd.v1.ExtendedCalculatorResultType;
import eu.europa.schengen.ees.xsd.v1.TravellerFileSearchResponseType;
import eu.europa.schengen.etias.xsd.v1.TravelAuthorisationResponseType;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per SearchByTravelDocumentResponseMessageType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="SearchByTravelDocumentResponseMessageType">
 *   <complexContent>
 *     <extension base="{http://www.europa.eu/schengen/ees-ns/xsd/v1}MessageResponseType">
 *       <sequence>
 *         <element name="Response" minOccurs="0">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="TravellerFile" maxOccurs="unbounded" minOccurs="0">
 *                     <complexType>
 *                       <complexContent>
 *                         <extension base="{http://www.europa.eu/schengen/ees/xsd/v1}TravellerFileSearchResponseType">
 *                           <sequence>
 *                             <element name="Calculator" type="{http://www.europa.eu/schengen/ees/xsd/v1}ExtendedCalculatorResultType" minOccurs="0"/>
 *                           </sequence>
 *                         </extension>
 *                       </complexContent>
 *                     </complexType>
 *                   </element>
 *                   <choice minOccurs="0">
 *                     <element name="VisaApplicationOverview" maxOccurs="unbounded">
 *                       <complexType>
 *                         <complexContent>
 *                           <extension base="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}VisaApplicationOverviewSearchResultVISType">
 *                             <sequence>
 *                               <group ref="{http://www.europa.eu/schengen/ees/xsd/v1}RankScoreGroup"/>
 *                               <element name="DossierID" type="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}DossierIDType"/>
 *                             </sequence>
 *                           </extension>
 *                         </complexContent>
 *                       </complexType>
 *                     </element>
 *                     <element name="VisaApplication" maxOccurs="unbounded">
 *                       <complexType>
 *                         <complexContent>
 *                           <extension base="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}VisaApplicationSearchResultEESType">
 *                             <sequence>
 *                               <group ref="{http://www.europa.eu/schengen/ees/xsd/v1}RankScoreGroup"/>
 *                               <element name="DossierID" type="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}DossierIDType"/>
 *                             </sequence>
 *                           </extension>
 *                         </complexContent>
 *                       </complexType>
 *                     </element>
 *                     <element name="VisaApplicationNumber" maxOccurs="unbounded">
 *                       <complexType>
 *                         <complexContent>
 *                           <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                             <sequence>
 *                               <element name="ApplicationNumber" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}ApplicationNumberType"/>
 *                               <element name="DossierID" type="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}DossierIDType"/>
 *                             </sequence>
 *                           </restriction>
 *                         </complexContent>
 *                       </complexType>
 *                     </element>
 *                   </choice>
 *                   <element name="TravelAuthorisation" type="{http://www.europa.eu/schengen/etias/xsd/v1}TravelAuthorisationResponseType" maxOccurs="unbounded" minOccurs="0"/>
 *                   <element name="TotalNoOfHits">
 *                     <complexType>
 *                       <complexContent>
 *                         <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           <sequence>
 *                             <element name="TravelDocument" type="{http://www.w3.org/2001/XMLSchema}unsignedInt" minOccurs="0"/>
 *                             <element name="VisaApplication" type="{http://www.w3.org/2001/XMLSchema}unsignedInt" minOccurs="0"/>
 *                             <element name="TravelAuthorisation" type="{http://www.w3.org/2001/XMLSchema}unsignedInt" minOccurs="0"/>
 *                           </sequence>
 *                         </restriction>
 *                       </complexContent>
 *                     </complexType>
 *                   </element>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *       </sequence>
 *     </extension>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SearchByTravelDocumentResponseMessageType", propOrder = {
    "response"
})
public class SearchByTravelDocumentResponseMessageType
    extends MessageResponseType
{

    @XmlElement(name = "Response")
    protected SearchByTravelDocumentResponseMessageType.Response response;

    /**
     * Recupera il valore della proprietà response.
     * 
     * @return
     *     possible object is
     *     {@link SearchByTravelDocumentResponseMessageType.Response }
     *     
     */
    public SearchByTravelDocumentResponseMessageType.Response getResponse() {
        return response;
    }

    /**
     * Imposta il valore della proprietà response.
     * 
     * @param value
     *     allowed object is
     *     {@link SearchByTravelDocumentResponseMessageType.Response }
     *     
     */
    public void setResponse(SearchByTravelDocumentResponseMessageType.Response value) {
        this.response = value;
    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="TravellerFile" maxOccurs="unbounded" minOccurs="0">
     *           <complexType>
     *             <complexContent>
     *               <extension base="{http://www.europa.eu/schengen/ees/xsd/v1}TravellerFileSearchResponseType">
     *                 <sequence>
     *                   <element name="Calculator" type="{http://www.europa.eu/schengen/ees/xsd/v1}ExtendedCalculatorResultType" minOccurs="0"/>
     *                 </sequence>
     *               </extension>
     *             </complexContent>
     *           </complexType>
     *         </element>
     *         <choice minOccurs="0">
     *           <element name="VisaApplicationOverview" maxOccurs="unbounded">
     *             <complexType>
     *               <complexContent>
     *                 <extension base="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}VisaApplicationOverviewSearchResultVISType">
     *                   <sequence>
     *                     <group ref="{http://www.europa.eu/schengen/ees/xsd/v1}RankScoreGroup"/>
     *                     <element name="DossierID" type="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}DossierIDType"/>
     *                   </sequence>
     *                 </extension>
     *               </complexContent>
     *             </complexType>
     *           </element>
     *           <element name="VisaApplication" maxOccurs="unbounded">
     *             <complexType>
     *               <complexContent>
     *                 <extension base="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}VisaApplicationSearchResultEESType">
     *                   <sequence>
     *                     <group ref="{http://www.europa.eu/schengen/ees/xsd/v1}RankScoreGroup"/>
     *                     <element name="DossierID" type="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}DossierIDType"/>
     *                   </sequence>
     *                 </extension>
     *               </complexContent>
     *             </complexType>
     *           </element>
     *           <element name="VisaApplicationNumber" maxOccurs="unbounded">
     *             <complexType>
     *               <complexContent>
     *                 <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                   <sequence>
     *                     <element name="ApplicationNumber" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}ApplicationNumberType"/>
     *                     <element name="DossierID" type="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}DossierIDType"/>
     *                   </sequence>
     *                 </restriction>
     *               </complexContent>
     *             </complexType>
     *           </element>
     *         </choice>
     *         <element name="TravelAuthorisation" type="{http://www.europa.eu/schengen/etias/xsd/v1}TravelAuthorisationResponseType" maxOccurs="unbounded" minOccurs="0"/>
     *         <element name="TotalNoOfHits">
     *           <complexType>
     *             <complexContent>
     *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 <sequence>
     *                   <element name="TravelDocument" type="{http://www.w3.org/2001/XMLSchema}unsignedInt" minOccurs="0"/>
     *                   <element name="VisaApplication" type="{http://www.w3.org/2001/XMLSchema}unsignedInt" minOccurs="0"/>
     *                   <element name="TravelAuthorisation" type="{http://www.w3.org/2001/XMLSchema}unsignedInt" minOccurs="0"/>
     *                 </sequence>
     *               </restriction>
     *             </complexContent>
     *           </complexType>
     *         </element>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "travellerFile",
        "visaApplicationOverview",
        "visaApplication",
        "visaApplicationNumber",
        "travelAuthorisation",
        "totalNoOfHits"
    })
    public static class Response {

        @XmlElement(name = "TravellerFile")
        protected List<SearchByTravelDocumentResponseMessageType.Response.TravellerFile> travellerFile;
        @XmlElement(name = "VisaApplicationOverview")
        protected List<SearchByTravelDocumentResponseMessageType.Response.VisaApplicationOverview> visaApplicationOverview;
        @XmlElement(name = "VisaApplication")
        protected List<SearchByTravelDocumentResponseMessageType.Response.VisaApplication> visaApplication;
        @XmlElement(name = "VisaApplicationNumber")
        protected List<SearchByTravelDocumentResponseMessageType.Response.VisaApplicationNumber> visaApplicationNumber;
        @XmlElement(name = "TravelAuthorisation")
        protected List<TravelAuthorisationResponseType> travelAuthorisation;
        @XmlElement(name = "TotalNoOfHits", required = true)
        protected SearchByTravelDocumentResponseMessageType.Response.TotalNoOfHits totalNoOfHits;

        /**
         * Gets the value of the travellerFile property.
         * 
         * <p>This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the travellerFile property.</p>
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * </p>
         * <pre>
         * getTravellerFile().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link SearchByTravelDocumentResponseMessageType.Response.TravellerFile }
         * </p>
         * 
         * 
         * @return
         *     The value of the travellerFile property.
         */
        public List<SearchByTravelDocumentResponseMessageType.Response.TravellerFile> getTravellerFile() {
            if (travellerFile == null) {
                travellerFile = new ArrayList<>();
            }
            return this.travellerFile;
        }

        /**
         * Gets the value of the visaApplicationOverview property.
         * 
         * <p>This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the visaApplicationOverview property.</p>
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * </p>
         * <pre>
         * getVisaApplicationOverview().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link SearchByTravelDocumentResponseMessageType.Response.VisaApplicationOverview }
         * </p>
         * 
         * 
         * @return
         *     The value of the visaApplicationOverview property.
         */
        public List<SearchByTravelDocumentResponseMessageType.Response.VisaApplicationOverview> getVisaApplicationOverview() {
            if (visaApplicationOverview == null) {
                visaApplicationOverview = new ArrayList<>();
            }
            return this.visaApplicationOverview;
        }

        /**
         * Gets the value of the visaApplication property.
         * 
         * <p>This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the visaApplication property.</p>
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * </p>
         * <pre>
         * getVisaApplication().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link SearchByTravelDocumentResponseMessageType.Response.VisaApplication }
         * </p>
         * 
         * 
         * @return
         *     The value of the visaApplication property.
         */
        public List<SearchByTravelDocumentResponseMessageType.Response.VisaApplication> getVisaApplication() {
            if (visaApplication == null) {
                visaApplication = new ArrayList<>();
            }
            return this.visaApplication;
        }

        /**
         * Gets the value of the visaApplicationNumber property.
         * 
         * <p>This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the visaApplicationNumber property.</p>
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * </p>
         * <pre>
         * getVisaApplicationNumber().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link SearchByTravelDocumentResponseMessageType.Response.VisaApplicationNumber }
         * </p>
         * 
         * 
         * @return
         *     The value of the visaApplicationNumber property.
         */
        public List<SearchByTravelDocumentResponseMessageType.Response.VisaApplicationNumber> getVisaApplicationNumber() {
            if (visaApplicationNumber == null) {
                visaApplicationNumber = new ArrayList<>();
            }
            return this.visaApplicationNumber;
        }

        /**
         * Gets the value of the travelAuthorisation property.
         * 
         * <p>This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the travelAuthorisation property.</p>
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * </p>
         * <pre>
         * getTravelAuthorisation().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link TravelAuthorisationResponseType }
         * </p>
         * 
         * 
         * @return
         *     The value of the travelAuthorisation property.
         */
        public List<TravelAuthorisationResponseType> getTravelAuthorisation() {
            if (travelAuthorisation == null) {
                travelAuthorisation = new ArrayList<>();
            }
            return this.travelAuthorisation;
        }

        /**
         * Recupera il valore della proprietà totalNoOfHits.
         * 
         * @return
         *     possible object is
         *     {@link SearchByTravelDocumentResponseMessageType.Response.TotalNoOfHits }
         *     
         */
        public SearchByTravelDocumentResponseMessageType.Response.TotalNoOfHits getTotalNoOfHits() {
            return totalNoOfHits;
        }

        /**
         * Imposta il valore della proprietà totalNoOfHits.
         * 
         * @param value
         *     allowed object is
         *     {@link SearchByTravelDocumentResponseMessageType.Response.TotalNoOfHits }
         *     
         */
        public void setTotalNoOfHits(SearchByTravelDocumentResponseMessageType.Response.TotalNoOfHits value) {
            this.totalNoOfHits = value;
        }


        /**
         * <p>Classe Java per anonymous complex type.</p>
         * 
         * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
         * 
         * <pre>{@code
         * <complexType>
         *   <complexContent>
         *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       <sequence>
         *         <element name="TravelDocument" type="{http://www.w3.org/2001/XMLSchema}unsignedInt" minOccurs="0"/>
         *         <element name="VisaApplication" type="{http://www.w3.org/2001/XMLSchema}unsignedInt" minOccurs="0"/>
         *         <element name="TravelAuthorisation" type="{http://www.w3.org/2001/XMLSchema}unsignedInt" minOccurs="0"/>
         *       </sequence>
         *     </restriction>
         *   </complexContent>
         * </complexType>
         * }</pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "travelDocument",
            "visaApplication",
            "travelAuthorisation"
        })
        public static class TotalNoOfHits {

            @XmlElement(name = "TravelDocument")
            @XmlSchemaType(name = "unsignedInt")
            protected Long travelDocument;
            @XmlElement(name = "VisaApplication")
            @XmlSchemaType(name = "unsignedInt")
            protected Long visaApplication;
            @XmlElement(name = "TravelAuthorisation")
            @XmlSchemaType(name = "unsignedInt")
            protected Long travelAuthorisation;

            /**
             * Recupera il valore della proprietà travelDocument.
             * 
             * @return
             *     possible object is
             *     {@link Long }
             *     
             */
            public Long getTravelDocument() {
                return travelDocument;
            }

            /**
             * Imposta il valore della proprietà travelDocument.
             * 
             * @param value
             *     allowed object is
             *     {@link Long }
             *     
             */
            public void setTravelDocument(Long value) {
                this.travelDocument = value;
            }

            /**
             * Recupera il valore della proprietà visaApplication.
             * 
             * @return
             *     possible object is
             *     {@link Long }
             *     
             */
            public Long getVisaApplication() {
                return visaApplication;
            }

            /**
             * Imposta il valore della proprietà visaApplication.
             * 
             * @param value
             *     allowed object is
             *     {@link Long }
             *     
             */
            public void setVisaApplication(Long value) {
                this.visaApplication = value;
            }

            /**
             * Recupera il valore della proprietà travelAuthorisation.
             * 
             * @return
             *     possible object is
             *     {@link Long }
             *     
             */
            public Long getTravelAuthorisation() {
                return travelAuthorisation;
            }

            /**
             * Imposta il valore della proprietà travelAuthorisation.
             * 
             * @param value
             *     allowed object is
             *     {@link Long }
             *     
             */
            public void setTravelAuthorisation(Long value) {
                this.travelAuthorisation = value;
            }

        }


        /**
         * <p>Classe Java per anonymous complex type.</p>
         * 
         * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
         * 
         * <pre>{@code
         * <complexType>
         *   <complexContent>
         *     <extension base="{http://www.europa.eu/schengen/ees/xsd/v1}TravellerFileSearchResponseType">
         *       <sequence>
         *         <element name="Calculator" type="{http://www.europa.eu/schengen/ees/xsd/v1}ExtendedCalculatorResultType" minOccurs="0"/>
         *       </sequence>
         *     </extension>
         *   </complexContent>
         * </complexType>
         * }</pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "calculator"
        })
        public static class TravellerFile
            extends TravellerFileSearchResponseType
        {

            @XmlElement(name = "Calculator")
            protected ExtendedCalculatorResultType calculator;

            /**
             * Recupera il valore della proprietà calculator.
             * 
             * @return
             *     possible object is
             *     {@link ExtendedCalculatorResultType }
             *     
             */
            public ExtendedCalculatorResultType getCalculator() {
                return calculator;
            }

            /**
             * Imposta il valore della proprietà calculator.
             * 
             * @param value
             *     allowed object is
             *     {@link ExtendedCalculatorResultType }
             *     
             */
            public void setCalculator(ExtendedCalculatorResultType value) {
                this.calculator = value;
            }

        }


        /**
         * <p>Classe Java per anonymous complex type.</p>
         * 
         * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
         * 
         * <pre>{@code
         * <complexType>
         *   <complexContent>
         *     <extension base="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}VisaApplicationSearchResultEESType">
         *       <sequence>
         *         <group ref="{http://www.europa.eu/schengen/ees/xsd/v1}RankScoreGroup"/>
         *         <element name="DossierID" type="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}DossierIDType"/>
         *       </sequence>
         *     </extension>
         *   </complexContent>
         * </complexType>
         * }</pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "rank",
            "score",
            "dossierID"
        })
        public static class VisaApplication
            extends VisaApplicationSearchResultEESType
        {

            @XmlElement(name = "Rank", namespace = "http://www.europa.eu/schengen/ees/xsd/v1")
            @XmlSchemaType(name = "unsignedInt")
            protected long rank;
            /**
             * Normalised matched score between 0 and 100 for alphanumeric searches. 100 is the highest score for alphanumeric searches.
             * 						Matching score between 0 and 100000 for biometric searches. 100000 is the highest score for biometric searches.
             * 
             */
            @XmlElement(name = "Score", namespace = "http://www.europa.eu/schengen/ees/xsd/v1")
            @XmlSchemaType(name = "unsignedInt")
            protected long score;
            /**
             * Description: ID of the Dossier.
             * 
             */
            @XmlElement(name = "DossierID")
            protected long dossierID;

            /**
             * Recupera il valore della proprietà rank.
             * 
             */
            public long getRank() {
                return rank;
            }

            /**
             * Imposta il valore della proprietà rank.
             * 
             */
            public void setRank(long value) {
                this.rank = value;
            }

            /**
             * Normalised matched score between 0 and 100 for alphanumeric searches. 100 is the highest score for alphanumeric searches.
             * 						Matching score between 0 and 100000 for biometric searches. 100000 is the highest score for biometric searches.
             * 
             */
            public long getScore() {
                return score;
            }

            /**
             * Imposta il valore della proprietà score.
             * 
             */
            public void setScore(long value) {
                this.score = value;
            }

            /**
             * Description: ID of the Dossier.
             * 
             */
            public long getDossierID() {
                return dossierID;
            }

            /**
             * Imposta il valore della proprietà dossierID.
             * 
             */
            public void setDossierID(long value) {
                this.dossierID = value;
            }

        }


        /**
         * <p>Classe Java per anonymous complex type.</p>
         * 
         * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
         * 
         * <pre>{@code
         * <complexType>
         *   <complexContent>
         *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       <sequence>
         *         <element name="ApplicationNumber" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}ApplicationNumberType"/>
         *         <element name="DossierID" type="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}DossierIDType"/>
         *       </sequence>
         *     </restriction>
         *   </complexContent>
         * </complexType>
         * }</pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "applicationNumber",
            "dossierID"
        })
        public static class VisaApplicationNumber {

            /**
             * Description: ID of the Application.
             * 
             */
            @XmlElement(name = "ApplicationNumber", required = true)
            protected String applicationNumber;
            /**
             * Description: ID of the Dossier.
             * 
             */
            @XmlElement(name = "DossierID")
            protected long dossierID;

            /**
             * Description: ID of the Application.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getApplicationNumber() {
                return applicationNumber;
            }

            /**
             * Imposta il valore della proprietà applicationNumber.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             * @see #getApplicationNumber()
             */
            public void setApplicationNumber(String value) {
                this.applicationNumber = value;
            }

            /**
             * Description: ID of the Dossier.
             * 
             */
            public long getDossierID() {
                return dossierID;
            }

            /**
             * Imposta il valore della proprietà dossierID.
             * 
             */
            public void setDossierID(long value) {
                this.dossierID = value;
            }

        }


        /**
         * <p>Classe Java per anonymous complex type.</p>
         * 
         * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
         * 
         * <pre>{@code
         * <complexType>
         *   <complexContent>
         *     <extension base="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}VisaApplicationOverviewSearchResultVISType">
         *       <sequence>
         *         <group ref="{http://www.europa.eu/schengen/ees/xsd/v1}RankScoreGroup"/>
         *         <element name="DossierID" type="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}DossierIDType"/>
         *       </sequence>
         *     </extension>
         *   </complexContent>
         * </complexType>
         * }</pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "rank",
            "score",
            "dossierID"
        })
        public static class VisaApplicationOverview
            extends VisaApplicationOverviewSearchResultVISType
        {

            @XmlElement(name = "Rank", namespace = "http://www.europa.eu/schengen/ees/xsd/v1")
            @XmlSchemaType(name = "unsignedInt")
            protected long rank;
            /**
             * Normalised matched score between 0 and 100 for alphanumeric searches. 100 is the highest score for alphanumeric searches.
             * 						Matching score between 0 and 100000 for biometric searches. 100000 is the highest score for biometric searches.
             * 
             */
            @XmlElement(name = "Score", namespace = "http://www.europa.eu/schengen/ees/xsd/v1")
            @XmlSchemaType(name = "unsignedInt")
            protected long score;
            /**
             * Description: ID of the Dossier.
             * 
             */
            @XmlElement(name = "DossierID")
            protected long dossierID;

            /**
             * Recupera il valore della proprietà rank.
             * 
             */
            public long getRank() {
                return rank;
            }

            /**
             * Imposta il valore della proprietà rank.
             * 
             */
            public void setRank(long value) {
                this.rank = value;
            }

            /**
             * Normalised matched score between 0 and 100 for alphanumeric searches. 100 is the highest score for alphanumeric searches.
             * 						Matching score between 0 and 100000 for biometric searches. 100000 is the highest score for biometric searches.
             * 
             */
            public long getScore() {
                return score;
            }

            /**
             * Imposta il valore della proprietà score.
             * 
             */
            public void setScore(long value) {
                this.score = value;
            }

            /**
             * Description: ID of the Dossier.
             * 
             */
            public long getDossierID() {
                return dossierID;
            }

            /**
             * Imposta il valore della proprietà dossierID.
             * 
             */
            public void setDossierID(long value) {
                this.dossierID = value;
            }

        }

    }

}
