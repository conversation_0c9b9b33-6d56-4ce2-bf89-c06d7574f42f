//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.ees_ns.xsd.v1;

import eu.europa.schengen.ees.xsd.v1.FIResponseType;
import eu.europa.schengen.ees.xsd.v1.FPResponseType;
import eu.europa.schengen.ees.xsd.v1.FTDInformationType;
import eu.europa.schengen.shared.xsd.v1.TravelDocumentType;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * CollectedData (part of WFE cache) - type for the response.
 * 
 * <p>Classe Java per CollectedDataResponseType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="CollectedDataResponseType">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="TravelDocument">
 *           <complexType>
 *             <complexContent>
 *               <extension base="{http://www.europa.eu/schengen/shared/xsd/v1}TravelDocumentType">
 *                 <sequence>
 *                   <element name="PreEnrolment" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *                 </sequence>
 *               </extension>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *         <element name="SSSPreEnrolledBiometrics" minOccurs="0">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="FI" type="{http://www.europa.eu/schengen/ees/xsd/v1}FIResponseType" minOccurs="0"/>
 *                   <element name="FP" minOccurs="0">
 *                     <complexType>
 *                       <complexContent>
 *                         <extension base="{http://www.europa.eu/schengen/ees/xsd/v1}FPResponseType">
 *                           <sequence>
 *                             <element name="SufficientForEnrolment" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *                           </sequence>
 *                         </extension>
 *                       </complexContent>
 *                     </complexType>
 *                   </element>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *         <element name="BorderGuardPreEnrolledBiometrics" minOccurs="0">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="FI" type="{http://www.europa.eu/schengen/ees/xsd/v1}FIResponseType" minOccurs="0"/>
 *                   <element name="FP" minOccurs="0">
 *                     <complexType>
 *                       <complexContent>
 *                         <extension base="{http://www.europa.eu/schengen/ees/xsd/v1}FPResponseType">
 *                           <sequence>
 *                             <element name="SufficientForEnrolment" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *                           </sequence>
 *                         </extension>
 *                       </complexContent>
 *                     </complexType>
 *                   </element>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *         <element name="TCNType" type="{http://www.europa.eu/schengen/ees/xsd/v1}ST511_TCNTypeType"/>
 *         <element name="FTDInformation" minOccurs="0">
 *           <complexType>
 *             <complexContent>
 *               <extension base="{http://www.europa.eu/schengen/ees/xsd/v1}FTDInformationType">
 *                 <sequence>
 *                   <element name="PreEnrolment" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *                 </sequence>
 *               </extension>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *         <element name="PersonStatus" type="{http://www.europa.eu/schengen/shared/xsd/v1}CT71_PersonStatusType"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "CollectedDataResponseType", propOrder = {
    "travelDocument",
    "sssPreEnrolledBiometrics",
    "borderGuardPreEnrolledBiometrics",
    "tcnType",
    "ftdInformation",
    "personStatus"
})
public class CollectedDataResponseType {

    @XmlElement(name = "TravelDocument", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1", required = true)
    protected CollectedDataResponseType.TravelDocument travelDocument;
    @XmlElement(name = "SSSPreEnrolledBiometrics", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1")
    protected CollectedDataResponseType.SSSPreEnrolledBiometrics sssPreEnrolledBiometrics;
    @XmlElement(name = "BorderGuardPreEnrolledBiometrics", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1")
    protected CollectedDataResponseType.BorderGuardPreEnrolledBiometrics borderGuardPreEnrolledBiometrics;
    @XmlElement(name = "TCNType", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1", required = true)
    protected String tcnType;
    @XmlElement(name = "FTDInformation", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1")
    protected CollectedDataResponseType.FTDInformation ftdInformation;
    @XmlElement(name = "PersonStatus", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1", required = true)
    protected String personStatus;

    /**
     * Recupera il valore della proprietà travelDocument.
     * 
     * @return
     *     possible object is
     *     {@link CollectedDataResponseType.TravelDocument }
     *     
     */
    public CollectedDataResponseType.TravelDocument getTravelDocument() {
        return travelDocument;
    }

    /**
     * Imposta il valore della proprietà travelDocument.
     * 
     * @param value
     *     allowed object is
     *     {@link CollectedDataResponseType.TravelDocument }
     *     
     */
    public void setTravelDocument(CollectedDataResponseType.TravelDocument value) {
        this.travelDocument = value;
    }

    /**
     * Recupera il valore della proprietà sssPreEnrolledBiometrics.
     * 
     * @return
     *     possible object is
     *     {@link CollectedDataResponseType.SSSPreEnrolledBiometrics }
     *     
     */
    public CollectedDataResponseType.SSSPreEnrolledBiometrics getSSSPreEnrolledBiometrics() {
        return sssPreEnrolledBiometrics;
    }

    /**
     * Imposta il valore della proprietà sssPreEnrolledBiometrics.
     * 
     * @param value
     *     allowed object is
     *     {@link CollectedDataResponseType.SSSPreEnrolledBiometrics }
     *     
     */
    public void setSSSPreEnrolledBiometrics(CollectedDataResponseType.SSSPreEnrolledBiometrics value) {
        this.sssPreEnrolledBiometrics = value;
    }

    /**
     * Recupera il valore della proprietà borderGuardPreEnrolledBiometrics.
     * 
     * @return
     *     possible object is
     *     {@link CollectedDataResponseType.BorderGuardPreEnrolledBiometrics }
     *     
     */
    public CollectedDataResponseType.BorderGuardPreEnrolledBiometrics getBorderGuardPreEnrolledBiometrics() {
        return borderGuardPreEnrolledBiometrics;
    }

    /**
     * Imposta il valore della proprietà borderGuardPreEnrolledBiometrics.
     * 
     * @param value
     *     allowed object is
     *     {@link CollectedDataResponseType.BorderGuardPreEnrolledBiometrics }
     *     
     */
    public void setBorderGuardPreEnrolledBiometrics(CollectedDataResponseType.BorderGuardPreEnrolledBiometrics value) {
        this.borderGuardPreEnrolledBiometrics = value;
    }

    /**
     * Recupera il valore della proprietà tcnType.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTCNType() {
        return tcnType;
    }

    /**
     * Imposta il valore della proprietà tcnType.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTCNType(String value) {
        this.tcnType = value;
    }

    /**
     * Recupera il valore della proprietà ftdInformation.
     * 
     * @return
     *     possible object is
     *     {@link CollectedDataResponseType.FTDInformation }
     *     
     */
    public CollectedDataResponseType.FTDInformation getFTDInformation() {
        return ftdInformation;
    }

    /**
     * Imposta il valore della proprietà ftdInformation.
     * 
     * @param value
     *     allowed object is
     *     {@link CollectedDataResponseType.FTDInformation }
     *     
     */
    public void setFTDInformation(CollectedDataResponseType.FTDInformation value) {
        this.ftdInformation = value;
    }

    /**
     * Recupera il valore della proprietà personStatus.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPersonStatus() {
        return personStatus;
    }

    /**
     * Imposta il valore della proprietà personStatus.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPersonStatus(String value) {
        this.personStatus = value;
    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="FI" type="{http://www.europa.eu/schengen/ees/xsd/v1}FIResponseType" minOccurs="0"/>
     *         <element name="FP" minOccurs="0">
     *           <complexType>
     *             <complexContent>
     *               <extension base="{http://www.europa.eu/schengen/ees/xsd/v1}FPResponseType">
     *                 <sequence>
     *                   <element name="SufficientForEnrolment" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
     *                 </sequence>
     *               </extension>
     *             </complexContent>
     *           </complexType>
     *         </element>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "fi",
        "fp"
    })
    public static class BorderGuardPreEnrolledBiometrics {

        @XmlElement(name = "FI", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1")
        protected FIResponseType fi;
        @XmlElement(name = "FP", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1")
        protected CollectedDataResponseType.BorderGuardPreEnrolledBiometrics.FP fp;

        /**
         * Recupera il valore della proprietà fi.
         * 
         * @return
         *     possible object is
         *     {@link FIResponseType }
         *     
         */
        public FIResponseType getFI() {
            return fi;
        }

        /**
         * Imposta il valore della proprietà fi.
         * 
         * @param value
         *     allowed object is
         *     {@link FIResponseType }
         *     
         */
        public void setFI(FIResponseType value) {
            this.fi = value;
        }

        /**
         * Recupera il valore della proprietà fp.
         * 
         * @return
         *     possible object is
         *     {@link CollectedDataResponseType.BorderGuardPreEnrolledBiometrics.FP }
         *     
         */
        public CollectedDataResponseType.BorderGuardPreEnrolledBiometrics.FP getFP() {
            return fp;
        }

        /**
         * Imposta il valore della proprietà fp.
         * 
         * @param value
         *     allowed object is
         *     {@link CollectedDataResponseType.BorderGuardPreEnrolledBiometrics.FP }
         *     
         */
        public void setFP(CollectedDataResponseType.BorderGuardPreEnrolledBiometrics.FP value) {
            this.fp = value;
        }


        /**
         * <p>Classe Java per anonymous complex type.</p>
         * 
         * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
         * 
         * <pre>{@code
         * <complexType>
         *   <complexContent>
         *     <extension base="{http://www.europa.eu/schengen/ees/xsd/v1}FPResponseType">
         *       <sequence>
         *         <element name="SufficientForEnrolment" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
         *       </sequence>
         *     </extension>
         *   </complexContent>
         * </complexType>
         * }</pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "sufficientForEnrolment"
        })
        public static class FP
            extends FPResponseType
        {

            @XmlElement(name = "SufficientForEnrolment", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1")
            protected boolean sufficientForEnrolment;

            /**
             * Recupera il valore della proprietà sufficientForEnrolment.
             * 
             */
            public boolean isSufficientForEnrolment() {
                return sufficientForEnrolment;
            }

            /**
             * Imposta il valore della proprietà sufficientForEnrolment.
             * 
             */
            public void setSufficientForEnrolment(boolean value) {
                this.sufficientForEnrolment = value;
            }

        }

    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <extension base="{http://www.europa.eu/schengen/ees/xsd/v1}FTDInformationType">
     *       <sequence>
     *         <element name="PreEnrolment" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
     *       </sequence>
     *     </extension>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "preEnrolment"
    })
    public static class FTDInformation
        extends FTDInformationType
    {

        @XmlElement(name = "PreEnrolment", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1")
        protected boolean preEnrolment;

        /**
         * Recupera il valore della proprietà preEnrolment.
         * 
         */
        public boolean isPreEnrolment() {
            return preEnrolment;
        }

        /**
         * Imposta il valore della proprietà preEnrolment.
         * 
         */
        public void setPreEnrolment(boolean value) {
            this.preEnrolment = value;
        }

    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="FI" type="{http://www.europa.eu/schengen/ees/xsd/v1}FIResponseType" minOccurs="0"/>
     *         <element name="FP" minOccurs="0">
     *           <complexType>
     *             <complexContent>
     *               <extension base="{http://www.europa.eu/schengen/ees/xsd/v1}FPResponseType">
     *                 <sequence>
     *                   <element name="SufficientForEnrolment" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
     *                 </sequence>
     *               </extension>
     *             </complexContent>
     *           </complexType>
     *         </element>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "fi",
        "fp"
    })
    public static class SSSPreEnrolledBiometrics {

        @XmlElement(name = "FI", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1")
        protected FIResponseType fi;
        @XmlElement(name = "FP", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1")
        protected CollectedDataResponseType.SSSPreEnrolledBiometrics.FP fp;

        /**
         * Recupera il valore della proprietà fi.
         * 
         * @return
         *     possible object is
         *     {@link FIResponseType }
         *     
         */
        public FIResponseType getFI() {
            return fi;
        }

        /**
         * Imposta il valore della proprietà fi.
         * 
         * @param value
         *     allowed object is
         *     {@link FIResponseType }
         *     
         */
        public void setFI(FIResponseType value) {
            this.fi = value;
        }

        /**
         * Recupera il valore della proprietà fp.
         * 
         * @return
         *     possible object is
         *     {@link CollectedDataResponseType.SSSPreEnrolledBiometrics.FP }
         *     
         */
        public CollectedDataResponseType.SSSPreEnrolledBiometrics.FP getFP() {
            return fp;
        }

        /**
         * Imposta il valore della proprietà fp.
         * 
         * @param value
         *     allowed object is
         *     {@link CollectedDataResponseType.SSSPreEnrolledBiometrics.FP }
         *     
         */
        public void setFP(CollectedDataResponseType.SSSPreEnrolledBiometrics.FP value) {
            this.fp = value;
        }


        /**
         * <p>Classe Java per anonymous complex type.</p>
         * 
         * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
         * 
         * <pre>{@code
         * <complexType>
         *   <complexContent>
         *     <extension base="{http://www.europa.eu/schengen/ees/xsd/v1}FPResponseType">
         *       <sequence>
         *         <element name="SufficientForEnrolment" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
         *       </sequence>
         *     </extension>
         *   </complexContent>
         * </complexType>
         * }</pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "sufficientForEnrolment"
        })
        public static class FP
            extends FPResponseType
        {

            @XmlElement(name = "SufficientForEnrolment", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1")
            protected boolean sufficientForEnrolment;

            /**
             * Recupera il valore della proprietà sufficientForEnrolment.
             * 
             */
            public boolean isSufficientForEnrolment() {
                return sufficientForEnrolment;
            }

            /**
             * Imposta il valore della proprietà sufficientForEnrolment.
             * 
             */
            public void setSufficientForEnrolment(boolean value) {
                this.sufficientForEnrolment = value;
            }

        }

    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <extension base="{http://www.europa.eu/schengen/shared/xsd/v1}TravelDocumentType">
     *       <sequence>
     *         <element name="PreEnrolment" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
     *       </sequence>
     *     </extension>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "preEnrolment"
    })
    public static class TravelDocument
        extends TravelDocumentType
    {

        @XmlElement(name = "PreEnrolment", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1")
        protected boolean preEnrolment;

        /**
         * Recupera il valore della proprietà preEnrolment.
         * 
         */
        public boolean isPreEnrolment() {
            return preEnrolment;
        }

        /**
         * Imposta il valore della proprietà preEnrolment.
         * 
         */
        public void setPreEnrolment(boolean value) {
            this.preEnrolment = value;
        }

    }

}
