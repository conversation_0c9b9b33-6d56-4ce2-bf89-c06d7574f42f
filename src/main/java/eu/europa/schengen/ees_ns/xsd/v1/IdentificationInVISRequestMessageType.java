//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.ees_ns.xsd.v1;

import eu.europa.schengen.ees.xsd.v1.PagingRequestType;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per IdentificationInVISRequestMessageType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="IdentificationInVISRequestMessageType">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="Paging" type="{http://www.europa.eu/schengen/ees/xsd/v1}PagingRequestType" minOccurs="0"/>
 *         <element name="ScopeModifiers" minOccurs="0">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="VisaApplications" type="{http://www.europa.eu/schengen/ees/xsd/v1}ST508_VisaApplicationsModifierType"/>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *         <element name="SearchData">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="Configuration" type="{http://www.europa.eu/schengen/shared/xsd/v1}ST509_BiometricSearchConfigurationType"/>
 *                   <element name="FP">
 *                     <complexType>
 *                       <complexContent>
 *                         <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           <sequence>
 *                             <element name="NISTFile" type="{http://www.europa.eu/schengen/ees/xsd/v1}FPNISTFile"/>
 *                           </sequence>
 *                         </restriction>
 *                       </complexContent>
 *                     </complexType>
 *                   </element>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "IdentificationInVISRequestMessageType", propOrder = {
    "paging",
    "scopeModifiers",
    "searchData"
})
public class IdentificationInVISRequestMessageType {

    @XmlElement(name = "Paging")
    protected PagingRequestType paging;
    @XmlElement(name = "ScopeModifiers")
    protected IdentificationInVISRequestMessageType.ScopeModifiers scopeModifiers;
    @XmlElement(name = "SearchData", required = true)
    protected IdentificationInVISRequestMessageType.SearchData searchData;

    /**
     * Recupera il valore della proprietà paging.
     * 
     * @return
     *     possible object is
     *     {@link PagingRequestType }
     *     
     */
    public PagingRequestType getPaging() {
        return paging;
    }

    /**
     * Imposta il valore della proprietà paging.
     * 
     * @param value
     *     allowed object is
     *     {@link PagingRequestType }
     *     
     */
    public void setPaging(PagingRequestType value) {
        this.paging = value;
    }

    /**
     * Recupera il valore della proprietà scopeModifiers.
     * 
     * @return
     *     possible object is
     *     {@link IdentificationInVISRequestMessageType.ScopeModifiers }
     *     
     */
    public IdentificationInVISRequestMessageType.ScopeModifiers getScopeModifiers() {
        return scopeModifiers;
    }

    /**
     * Imposta il valore della proprietà scopeModifiers.
     * 
     * @param value
     *     allowed object is
     *     {@link IdentificationInVISRequestMessageType.ScopeModifiers }
     *     
     */
    public void setScopeModifiers(IdentificationInVISRequestMessageType.ScopeModifiers value) {
        this.scopeModifiers = value;
    }

    /**
     * Recupera il valore della proprietà searchData.
     * 
     * @return
     *     possible object is
     *     {@link IdentificationInVISRequestMessageType.SearchData }
     *     
     */
    public IdentificationInVISRequestMessageType.SearchData getSearchData() {
        return searchData;
    }

    /**
     * Imposta il valore della proprietà searchData.
     * 
     * @param value
     *     allowed object is
     *     {@link IdentificationInVISRequestMessageType.SearchData }
     *     
     */
    public void setSearchData(IdentificationInVISRequestMessageType.SearchData value) {
        this.searchData = value;
    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="VisaApplications" type="{http://www.europa.eu/schengen/ees/xsd/v1}ST508_VisaApplicationsModifierType"/>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "visaApplications"
    })
    public static class ScopeModifiers {

        /**
         * If not specified then visa applications are not returned.
         * 
         */
        @XmlElement(name = "VisaApplications", required = true)
        protected String visaApplications;

        /**
         * If not specified then visa applications are not returned.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getVisaApplications() {
            return visaApplications;
        }

        /**
         * Imposta il valore della proprietà visaApplications.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         * @see #getVisaApplications()
         */
        public void setVisaApplications(String value) {
            this.visaApplications = value;
        }

    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="Configuration" type="{http://www.europa.eu/schengen/shared/xsd/v1}ST509_BiometricSearchConfigurationType"/>
     *         <element name="FP">
     *           <complexType>
     *             <complexContent>
     *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 <sequence>
     *                   <element name="NISTFile" type="{http://www.europa.eu/schengen/ees/xsd/v1}FPNISTFile"/>
     *                 </sequence>
     *               </restriction>
     *             </complexContent>
     *           </complexType>
     *         </element>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "configuration",
        "fp"
    })
    public static class SearchData {

        @XmlElement(name = "Configuration", required = true)
        protected String configuration;
        @XmlElement(name = "FP", required = true)
        protected IdentificationInVISRequestMessageType.SearchData.FP fp;

        /**
         * Recupera il valore della proprietà configuration.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getConfiguration() {
            return configuration;
        }

        /**
         * Imposta il valore della proprietà configuration.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setConfiguration(String value) {
            this.configuration = value;
        }

        /**
         * Recupera il valore della proprietà fp.
         * 
         * @return
         *     possible object is
         *     {@link IdentificationInVISRequestMessageType.SearchData.FP }
         *     
         */
        public IdentificationInVISRequestMessageType.SearchData.FP getFP() {
            return fp;
        }

        /**
         * Imposta il valore della proprietà fp.
         * 
         * @param value
         *     allowed object is
         *     {@link IdentificationInVISRequestMessageType.SearchData.FP }
         *     
         */
        public void setFP(IdentificationInVISRequestMessageType.SearchData.FP value) {
            this.fp = value;
        }


        /**
         * <p>Classe Java per anonymous complex type.</p>
         * 
         * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
         * 
         * <pre>{@code
         * <complexType>
         *   <complexContent>
         *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       <sequence>
         *         <element name="NISTFile" type="{http://www.europa.eu/schengen/ees/xsd/v1}FPNISTFile"/>
         *       </sequence>
         *     </restriction>
         *   </complexContent>
         * </complexType>
         * }</pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "nistFile"
        })
        public static class FP {

            @XmlElement(name = "NISTFile", required = true)
            protected byte[] nistFile;

            /**
             * Recupera il valore della proprietà nistFile.
             * 
             * @return
             *     possible object is
             *     byte[]
             */
            public byte[] getNISTFile() {
                return nistFile;
            }

            /**
             * Imposta il valore della proprietà nistFile.
             * 
             * @param value
             *     allowed object is
             *     byte[]
             */
            public void setNISTFile(byte[] value) {
                this.nistFile = value;
            }

        }

    }

}
