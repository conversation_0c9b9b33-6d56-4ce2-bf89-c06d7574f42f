//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.ees_ns.xsd.v1;

import javax.xml.datatype.XMLGregorianCalendar;
import jakarta.xml.bind.JAXBElement;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlElementRef;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per MachineAttributeResponseType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="MachineAttributeResponseType">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="TipoAttributo" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="StatoUfficio" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="StatoClient" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="DataInizioValidita" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/>
 *         <element name="DataFineValidita" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "MachineAttributeResponseType", propOrder = {
    "tipoAttributo",
    "statoUfficio",
    "statoClient",
    "dataInizioValidita",
    "dataFineValidita"
})
public class MachineAttributeResponseType {

    @XmlElement(name = "TipoAttributo")
    protected String tipoAttributo;
    @XmlElement(name = "StatoUfficio")
    protected String statoUfficio;
    @XmlElementRef(name = "StatoClient", type = JAXBElement.class, required = false)
    protected JAXBElement<String> statoClient;
    @XmlElementRef(name = "DataInizioValidita", type = JAXBElement.class, required = false)
    protected JAXBElement<XMLGregorianCalendar> dataInizioValidita;
    @XmlElementRef(name = "DataFineValidita", type = JAXBElement.class, required = false)
    protected JAXBElement<XMLGregorianCalendar> dataFineValidita;

    /**
     * Recupera il valore della proprietà tipoAttributo.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTipoAttributo() {
        return tipoAttributo;
    }

    /**
     * Imposta il valore della proprietà tipoAttributo.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTipoAttributo(String value) {
        this.tipoAttributo = value;
    }

    /**
     * Recupera il valore della proprietà statoUfficio.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getStatoUfficio() {
        return statoUfficio;
    }

    /**
     * Imposta il valore della proprietà statoUfficio.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setStatoUfficio(String value) {
        this.statoUfficio = value;
    }

    /**
     * Recupera il valore della proprietà statoClient.
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getStatoClient() {
        return statoClient;
    }

    /**
     * Imposta il valore della proprietà statoClient.
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setStatoClient(JAXBElement<String> value) {
        this.statoClient = value;
    }

    /**
     * Recupera il valore della proprietà dataInizioValidita.
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link XMLGregorianCalendar }{@code >}
     *     
     */
    public JAXBElement<XMLGregorianCalendar> getDataInizioValidita() {
        return dataInizioValidita;
    }

    /**
     * Imposta il valore della proprietà dataInizioValidita.
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link XMLGregorianCalendar }{@code >}
     *     
     */
    public void setDataInizioValidita(JAXBElement<XMLGregorianCalendar> value) {
        this.dataInizioValidita = value;
    }

    /**
     * Recupera il valore della proprietà dataFineValidita.
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link XMLGregorianCalendar }{@code >}
     *     
     */
    public JAXBElement<XMLGregorianCalendar> getDataFineValidita() {
        return dataFineValidita;
    }

    /**
     * Imposta il valore della proprietà dataFineValidita.
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link XMLGregorianCalendar }{@code >}
     *     
     */
    public void setDataFineValidita(JAXBElement<XMLGregorianCalendar> value) {
        this.dataFineValidita = value;
    }

}
