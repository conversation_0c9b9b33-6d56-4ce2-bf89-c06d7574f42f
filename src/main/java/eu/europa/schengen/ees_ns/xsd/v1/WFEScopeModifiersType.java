//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.ees_ns.xsd.v1;

import eu.europa.schengen.ees.xsd.v1.CalculatorScopeType;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per WFEScopeModifiersType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="WFEScopeModifiersType">
 *   <complexContent>
 *     <extension base="{http://www.europa.eu/schengen/ees-ns/xsd/v1}WFEScopeModifiersWithoutCalculatorType">
 *       <sequence>
 *         <element name="Calculator" type="{http://www.europa.eu/schengen/ees/xsd/v1}CalculatorScopeType" minOccurs="0"/>
 *       </sequence>
 *     </extension>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "WFEScopeModifiersType", propOrder = {
    "calculator"
})
public class WFEScopeModifiersType
    extends WFEScopeModifiersWithoutCalculatorType
{

    @XmlElement(name = "Calculator", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1")
    protected CalculatorScopeType calculator;

    /**
     * Recupera il valore della proprietà calculator.
     * 
     * @return
     *     possible object is
     *     {@link CalculatorScopeType }
     *     
     */
    public CalculatorScopeType getCalculator() {
        return calculator;
    }

    /**
     * Imposta il valore della proprietà calculator.
     * 
     * @param value
     *     allowed object is
     *     {@link CalculatorScopeType }
     *     
     */
    public void setCalculator(CalculatorScopeType value) {
        this.calculator = value;
    }

}
