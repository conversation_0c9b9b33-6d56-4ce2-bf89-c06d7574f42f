//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.ees_ns.xsd.v1;

import java.util.ArrayList;
import java.util.List;
import eu.europa.schengen.cs_vis_nui_ees.xsd.v1.VisaApplicationOverviewSearchResultVISType;
import eu.europa.schengen.cs_vis_nui_ees.xsd.v1.VisaApplicationSearchResultEESType;
import eu.europa.schengen.ees.xsd.v1.PagingResponseType;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per SearchByPersonalDataInVISResponseMessageType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="SearchByPersonalDataInVISResponseMessageType">
 *   <complexContent>
 *     <extension base="{http://www.europa.eu/schengen/ees-ns/xsd/v1}MessageResponseType">
 *       <sequence>
 *         <element name="Response" minOccurs="0">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="Paging" type="{http://www.europa.eu/schengen/ees/xsd/v1}PagingResponseType"/>
 *                   <choice minOccurs="0">
 *                     <element name="VisaApplicationOverview" maxOccurs="unbounded">
 *                       <complexType>
 *                         <complexContent>
 *                           <extension base="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}VisaApplicationOverviewSearchResultVISType">
 *                             <sequence>
 *                               <group ref="{http://www.europa.eu/schengen/ees/xsd/v1}RankScoreGroup"/>
 *                               <element name="DossierID" type="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}DossierIDType"/>
 *                             </sequence>
 *                           </extension>
 *                         </complexContent>
 *                       </complexType>
 *                     </element>
 *                     <element name="VisaApplication" maxOccurs="unbounded">
 *                       <complexType>
 *                         <complexContent>
 *                           <extension base="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}VisaApplicationSearchResultEESType">
 *                             <sequence>
 *                               <group ref="{http://www.europa.eu/schengen/ees/xsd/v1}RankScoreGroup"/>
 *                               <element name="DossierID" type="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}DossierIDType"/>
 *                             </sequence>
 *                           </extension>
 *                         </complexContent>
 *                       </complexType>
 *                     </element>
 *                   </choice>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *       </sequence>
 *     </extension>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SearchByPersonalDataInVISResponseMessageType", propOrder = {
    "response"
})
public class SearchByPersonalDataInVISResponseMessageType
    extends MessageResponseType
{

    @XmlElement(name = "Response")
    protected SearchByPersonalDataInVISResponseMessageType.Response response;

    /**
     * Recupera il valore della proprietà response.
     * 
     * @return
     *     possible object is
     *     {@link SearchByPersonalDataInVISResponseMessageType.Response }
     *     
     */
    public SearchByPersonalDataInVISResponseMessageType.Response getResponse() {
        return response;
    }

    /**
     * Imposta il valore della proprietà response.
     * 
     * @param value
     *     allowed object is
     *     {@link SearchByPersonalDataInVISResponseMessageType.Response }
     *     
     */
    public void setResponse(SearchByPersonalDataInVISResponseMessageType.Response value) {
        this.response = value;
    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="Paging" type="{http://www.europa.eu/schengen/ees/xsd/v1}PagingResponseType"/>
     *         <choice minOccurs="0">
     *           <element name="VisaApplicationOverview" maxOccurs="unbounded">
     *             <complexType>
     *               <complexContent>
     *                 <extension base="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}VisaApplicationOverviewSearchResultVISType">
     *                   <sequence>
     *                     <group ref="{http://www.europa.eu/schengen/ees/xsd/v1}RankScoreGroup"/>
     *                     <element name="DossierID" type="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}DossierIDType"/>
     *                   </sequence>
     *                 </extension>
     *               </complexContent>
     *             </complexType>
     *           </element>
     *           <element name="VisaApplication" maxOccurs="unbounded">
     *             <complexType>
     *               <complexContent>
     *                 <extension base="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}VisaApplicationSearchResultEESType">
     *                   <sequence>
     *                     <group ref="{http://www.europa.eu/schengen/ees/xsd/v1}RankScoreGroup"/>
     *                     <element name="DossierID" type="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}DossierIDType"/>
     *                   </sequence>
     *                 </extension>
     *               </complexContent>
     *             </complexType>
     *           </element>
     *         </choice>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "paging",
        "visaApplicationOverview",
        "visaApplication"
    })
    public static class Response {

        @XmlElement(name = "Paging", required = true)
        protected PagingResponseType paging;
        @XmlElement(name = "VisaApplicationOverview")
        protected List<SearchByPersonalDataInVISResponseMessageType.Response.VisaApplicationOverview> visaApplicationOverview;
        @XmlElement(name = "VisaApplication")
        protected List<SearchByPersonalDataInVISResponseMessageType.Response.VisaApplication> visaApplication;

        /**
         * Recupera il valore della proprietà paging.
         * 
         * @return
         *     possible object is
         *     {@link PagingResponseType }
         *     
         */
        public PagingResponseType getPaging() {
            return paging;
        }

        /**
         * Imposta il valore della proprietà paging.
         * 
         * @param value
         *     allowed object is
         *     {@link PagingResponseType }
         *     
         */
        public void setPaging(PagingResponseType value) {
            this.paging = value;
        }

        /**
         * Gets the value of the visaApplicationOverview property.
         * 
         * <p>This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the visaApplicationOverview property.</p>
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * </p>
         * <pre>
         * getVisaApplicationOverview().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link SearchByPersonalDataInVISResponseMessageType.Response.VisaApplicationOverview }
         * </p>
         * 
         * 
         * @return
         *     The value of the visaApplicationOverview property.
         */
        public List<SearchByPersonalDataInVISResponseMessageType.Response.VisaApplicationOverview> getVisaApplicationOverview() {
            if (visaApplicationOverview == null) {
                visaApplicationOverview = new ArrayList<>();
            }
            return this.visaApplicationOverview;
        }

        /**
         * Gets the value of the visaApplication property.
         * 
         * <p>This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the visaApplication property.</p>
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * </p>
         * <pre>
         * getVisaApplication().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link SearchByPersonalDataInVISResponseMessageType.Response.VisaApplication }
         * </p>
         * 
         * 
         * @return
         *     The value of the visaApplication property.
         */
        public List<SearchByPersonalDataInVISResponseMessageType.Response.VisaApplication> getVisaApplication() {
            if (visaApplication == null) {
                visaApplication = new ArrayList<>();
            }
            return this.visaApplication;
        }


        /**
         * <p>Classe Java per anonymous complex type.</p>
         * 
         * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
         * 
         * <pre>{@code
         * <complexType>
         *   <complexContent>
         *     <extension base="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}VisaApplicationSearchResultEESType">
         *       <sequence>
         *         <group ref="{http://www.europa.eu/schengen/ees/xsd/v1}RankScoreGroup"/>
         *         <element name="DossierID" type="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}DossierIDType"/>
         *       </sequence>
         *     </extension>
         *   </complexContent>
         * </complexType>
         * }</pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "rank",
            "score",
            "dossierID"
        })
        public static class VisaApplication
            extends VisaApplicationSearchResultEESType
        {

            @XmlElement(name = "Rank", namespace = "http://www.europa.eu/schengen/ees/xsd/v1")
            @XmlSchemaType(name = "unsignedInt")
            protected long rank;
            /**
             * Normalised matched score between 0 and 100 for alphanumeric searches. 100 is the highest score for alphanumeric searches.
             * 						Matching score between 0 and 100000 for biometric searches. 100000 is the highest score for biometric searches.
             * 
             */
            @XmlElement(name = "Score", namespace = "http://www.europa.eu/schengen/ees/xsd/v1")
            @XmlSchemaType(name = "unsignedInt")
            protected long score;
            /**
             * Description: ID of the Dossier.
             * 
             */
            @XmlElement(name = "DossierID")
            protected long dossierID;

            /**
             * Recupera il valore della proprietà rank.
             * 
             */
            public long getRank() {
                return rank;
            }

            /**
             * Imposta il valore della proprietà rank.
             * 
             */
            public void setRank(long value) {
                this.rank = value;
            }

            /**
             * Normalised matched score between 0 and 100 for alphanumeric searches. 100 is the highest score for alphanumeric searches.
             * 						Matching score between 0 and 100000 for biometric searches. 100000 is the highest score for biometric searches.
             * 
             */
            public long getScore() {
                return score;
            }

            /**
             * Imposta il valore della proprietà score.
             * 
             */
            public void setScore(long value) {
                this.score = value;
            }

            /**
             * Description: ID of the Dossier.
             * 
             */
            public long getDossierID() {
                return dossierID;
            }

            /**
             * Imposta il valore della proprietà dossierID.
             * 
             */
            public void setDossierID(long value) {
                this.dossierID = value;
            }

        }


        /**
         * <p>Classe Java per anonymous complex type.</p>
         * 
         * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
         * 
         * <pre>{@code
         * <complexType>
         *   <complexContent>
         *     <extension base="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}VisaApplicationOverviewSearchResultVISType">
         *       <sequence>
         *         <group ref="{http://www.europa.eu/schengen/ees/xsd/v1}RankScoreGroup"/>
         *         <element name="DossierID" type="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}DossierIDType"/>
         *       </sequence>
         *     </extension>
         *   </complexContent>
         * </complexType>
         * }</pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "rank",
            "score",
            "dossierID"
        })
        public static class VisaApplicationOverview
            extends VisaApplicationOverviewSearchResultVISType
        {

            @XmlElement(name = "Rank", namespace = "http://www.europa.eu/schengen/ees/xsd/v1")
            @XmlSchemaType(name = "unsignedInt")
            protected long rank;
            /**
             * Normalised matched score between 0 and 100 for alphanumeric searches. 100 is the highest score for alphanumeric searches.
             * 						Matching score between 0 and 100000 for biometric searches. 100000 is the highest score for biometric searches.
             * 
             */
            @XmlElement(name = "Score", namespace = "http://www.europa.eu/schengen/ees/xsd/v1")
            @XmlSchemaType(name = "unsignedInt")
            protected long score;
            /**
             * Description: ID of the Dossier.
             * 
             */
            @XmlElement(name = "DossierID")
            protected long dossierID;

            /**
             * Recupera il valore della proprietà rank.
             * 
             */
            public long getRank() {
                return rank;
            }

            /**
             * Imposta il valore della proprietà rank.
             * 
             */
            public void setRank(long value) {
                this.rank = value;
            }

            /**
             * Normalised matched score between 0 and 100 for alphanumeric searches. 100 is the highest score for alphanumeric searches.
             * 						Matching score between 0 and 100000 for biometric searches. 100000 is the highest score for biometric searches.
             * 
             */
            public long getScore() {
                return score;
            }

            /**
             * Imposta il valore della proprietà score.
             * 
             */
            public void setScore(long value) {
                this.score = value;
            }

            /**
             * Description: ID of the Dossier.
             * 
             */
            public long getDossierID() {
                return dossierID;
            }

            /**
             * Imposta il valore della proprietà dossierID.
             * 
             */
            public void setDossierID(long value) {
                this.dossierID = value;
            }

        }

    }

}
