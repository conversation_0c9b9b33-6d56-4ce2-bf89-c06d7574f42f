//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.ees_ns.xsd.v1;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per VerificationByFIInEESRequestMessageType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="VerificationByFIInEESRequestMessageType">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="TravellerFile">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="TravellerFileID" type="{http://www.europa.eu/schengen/ees/xsd/v1}IdentifierType"/>
 *                   <element name="FI">
 *                     <complexType>
 *                       <complexContent>
 *                         <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           <sequence>
 *                             <element name="NISTFile" type="{http://www.europa.eu/schengen/ees/xsd/v1}FINISTFile"/>
 *                           </sequence>
 *                         </restriction>
 *                       </complexContent>
 *                     </complexType>
 *                   </element>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "VerificationByFIInEESRequestMessageType", propOrder = {
    "travellerFile"
})
public class VerificationByFIInEESRequestMessageType {

    @XmlElement(name = "TravellerFile", required = true)
    protected VerificationByFIInEESRequestMessageType.TravellerFile travellerFile;

    /**
     * Recupera il valore della proprietà travellerFile.
     * 
     * @return
     *     possible object is
     *     {@link VerificationByFIInEESRequestMessageType.TravellerFile }
     *     
     */
    public VerificationByFIInEESRequestMessageType.TravellerFile getTravellerFile() {
        return travellerFile;
    }

    /**
     * Imposta il valore della proprietà travellerFile.
     * 
     * @param value
     *     allowed object is
     *     {@link VerificationByFIInEESRequestMessageType.TravellerFile }
     *     
     */
    public void setTravellerFile(VerificationByFIInEESRequestMessageType.TravellerFile value) {
        this.travellerFile = value;
    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="TravellerFileID" type="{http://www.europa.eu/schengen/ees/xsd/v1}IdentifierType"/>
     *         <element name="FI">
     *           <complexType>
     *             <complexContent>
     *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 <sequence>
     *                   <element name="NISTFile" type="{http://www.europa.eu/schengen/ees/xsd/v1}FINISTFile"/>
     *                 </sequence>
     *               </restriction>
     *             </complexContent>
     *           </complexType>
     *         </element>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "travellerFileID",
        "fi"
    })
    public static class TravellerFile {

        @XmlElement(name = "TravellerFileID", required = true)
        @XmlSchemaType(name = "anyURI")
        protected String travellerFileID;
        @XmlElement(name = "FI", required = true)
        protected VerificationByFIInEESRequestMessageType.TravellerFile.FI fi;

        /**
         * Recupera il valore della proprietà travellerFileID.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getTravellerFileID() {
            return travellerFileID;
        }

        /**
         * Imposta il valore della proprietà travellerFileID.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setTravellerFileID(String value) {
            this.travellerFileID = value;
        }

        /**
         * Recupera il valore della proprietà fi.
         * 
         * @return
         *     possible object is
         *     {@link VerificationByFIInEESRequestMessageType.TravellerFile.FI }
         *     
         */
        public VerificationByFIInEESRequestMessageType.TravellerFile.FI getFI() {
            return fi;
        }

        /**
         * Imposta il valore della proprietà fi.
         * 
         * @param value
         *     allowed object is
         *     {@link VerificationByFIInEESRequestMessageType.TravellerFile.FI }
         *     
         */
        public void setFI(VerificationByFIInEESRequestMessageType.TravellerFile.FI value) {
            this.fi = value;
        }


        /**
         * <p>Classe Java per anonymous complex type.</p>
         * 
         * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
         * 
         * <pre>{@code
         * <complexType>
         *   <complexContent>
         *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       <sequence>
         *         <element name="NISTFile" type="{http://www.europa.eu/schengen/ees/xsd/v1}FINISTFile"/>
         *       </sequence>
         *     </restriction>
         *   </complexContent>
         * </complexType>
         * }</pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "nistFile"
        })
        public static class FI {

            @XmlElement(name = "NISTFile", required = true)
            protected byte[] nistFile;

            /**
             * Recupera il valore della proprietà nistFile.
             * 
             * @return
             *     possible object is
             *     byte[]
             */
            public byte[] getNISTFile() {
                return nistFile;
            }

            /**
             * Imposta il valore della proprietà nistFile.
             * 
             * @param value
             *     allowed object is
             *     byte[]
             */
            public void setNISTFile(byte[] value) {
                this.nistFile = value;
            }

        }

    }

}
