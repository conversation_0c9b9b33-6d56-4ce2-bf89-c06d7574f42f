//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.ees_ns.xsd.v1;

import eu.europa.schengen.ees.xsd.v1.BaseScopeModifiersType;
import eu.europa.schengen.ees.xsd.v1.ExtendedCalculatorWithoutVisaScopeType;
import eu.europa.schengen.ees.xsd.v1.TravelDocumentSearchWithMandatoryFieldsRequestType;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per SearchByTravelDocumentRequestMessageType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="SearchByTravelDocumentRequestMessageType">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="ScopeModifiers" minOccurs="0">
 *           <complexType>
 *             <complexContent>
 *               <extension base="{http://www.europa.eu/schengen/ees/xsd/v1}BaseScopeModifiersType">
 *                 <sequence>
 *                   <element name="VisaApplications" type="{http://www.europa.eu/schengen/ees/xsd/v1}ST508_VisaApplicationsModifierType" minOccurs="0"/>
 *                   <element name="OperationModifier" type="{http://www.europa.eu/schengen/ees/xsd/v1}ST506_OperationModifierType" minOccurs="0"/>
 *                   <element name="Calculator" type="{http://www.europa.eu/schengen/ees/xsd/v1}ExtendedCalculatorWithoutVisaScopeType" minOccurs="0"/>
 *                 </sequence>
 *               </extension>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *         <element name="SearchData">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="TravellerFile">
 *                     <complexType>
 *                       <complexContent>
 *                         <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           <sequence>
 *                             <element name="TravelDocument" type="{http://www.europa.eu/schengen/ees/xsd/v1}TravelDocumentSearchWithMandatoryFieldsRequestType"/>
 *                           </sequence>
 *                         </restriction>
 *                       </complexContent>
 *                     </complexType>
 *                   </element>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SearchByTravelDocumentRequestMessageType", propOrder = {
    "scopeModifiers",
    "searchData"
})
public class SearchByTravelDocumentRequestMessageType {

    @XmlElement(name = "ScopeModifiers")
    protected SearchByTravelDocumentRequestMessageType.ScopeModifiers scopeModifiers;
    @XmlElement(name = "SearchData", required = true)
    protected SearchByTravelDocumentRequestMessageType.SearchData searchData;

    /**
     * Recupera il valore della proprietà scopeModifiers.
     * 
     * @return
     *     possible object is
     *     {@link SearchByTravelDocumentRequestMessageType.ScopeModifiers }
     *     
     */
    public SearchByTravelDocumentRequestMessageType.ScopeModifiers getScopeModifiers() {
        return scopeModifiers;
    }

    /**
     * Imposta il valore della proprietà scopeModifiers.
     * 
     * @param value
     *     allowed object is
     *     {@link SearchByTravelDocumentRequestMessageType.ScopeModifiers }
     *     
     */
    public void setScopeModifiers(SearchByTravelDocumentRequestMessageType.ScopeModifiers value) {
        this.scopeModifiers = value;
    }

    /**
     * Recupera il valore della proprietà searchData.
     * 
     * @return
     *     possible object is
     *     {@link SearchByTravelDocumentRequestMessageType.SearchData }
     *     
     */
    public SearchByTravelDocumentRequestMessageType.SearchData getSearchData() {
        return searchData;
    }

    /**
     * Imposta il valore della proprietà searchData.
     * 
     * @param value
     *     allowed object is
     *     {@link SearchByTravelDocumentRequestMessageType.SearchData }
     *     
     */
    public void setSearchData(SearchByTravelDocumentRequestMessageType.SearchData value) {
        this.searchData = value;
    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <extension base="{http://www.europa.eu/schengen/ees/xsd/v1}BaseScopeModifiersType">
     *       <sequence>
     *         <element name="VisaApplications" type="{http://www.europa.eu/schengen/ees/xsd/v1}ST508_VisaApplicationsModifierType" minOccurs="0"/>
     *         <element name="OperationModifier" type="{http://www.europa.eu/schengen/ees/xsd/v1}ST506_OperationModifierType" minOccurs="0"/>
     *         <element name="Calculator" type="{http://www.europa.eu/schengen/ees/xsd/v1}ExtendedCalculatorWithoutVisaScopeType" minOccurs="0"/>
     *       </sequence>
     *     </extension>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "visaApplications",
        "operationModifier",
        "calculator"
    })
    public static class ScopeModifiers
        extends BaseScopeModifiersType
    {

        /**
         * If not specified then visa applications are not
         *                                             returned.
         * 
         */
        @XmlElement(name = "VisaApplications")
        protected String visaApplications;
        /**
         * If not specified then default value AUTO is assumed.
         * 
         */
        @XmlElement(name = "OperationModifier")
        protected String operationModifier;
        /**
         * VisaInformation can be omitted for VH when search is
         *                                             performed in Auto mode.
         * 
         */
        @XmlElement(name = "Calculator")
        protected ExtendedCalculatorWithoutVisaScopeType calculator;

        /**
         * If not specified then visa applications are not
         *                                             returned.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getVisaApplications() {
            return visaApplications;
        }

        /**
         * Imposta il valore della proprietà visaApplications.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         * @see #getVisaApplications()
         */
        public void setVisaApplications(String value) {
            this.visaApplications = value;
        }

        /**
         * If not specified then default value AUTO is assumed.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getOperationModifier() {
            return operationModifier;
        }

        /**
         * Imposta il valore della proprietà operationModifier.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         * @see #getOperationModifier()
         */
        public void setOperationModifier(String value) {
            this.operationModifier = value;
        }

        /**
         * VisaInformation can be omitted for VH when search is
         *                                             performed in Auto mode.
         * 
         * @return
         *     possible object is
         *     {@link ExtendedCalculatorWithoutVisaScopeType }
         *     
         */
        public ExtendedCalculatorWithoutVisaScopeType getCalculator() {
            return calculator;
        }

        /**
         * Imposta il valore della proprietà calculator.
         * 
         * @param value
         *     allowed object is
         *     {@link ExtendedCalculatorWithoutVisaScopeType }
         *     
         * @see #getCalculator()
         */
        public void setCalculator(ExtendedCalculatorWithoutVisaScopeType value) {
            this.calculator = value;
        }

    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="TravellerFile">
     *           <complexType>
     *             <complexContent>
     *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 <sequence>
     *                   <element name="TravelDocument" type="{http://www.europa.eu/schengen/ees/xsd/v1}TravelDocumentSearchWithMandatoryFieldsRequestType"/>
     *                 </sequence>
     *               </restriction>
     *             </complexContent>
     *           </complexType>
     *         </element>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "travellerFile"
    })
    public static class SearchData {

        @XmlElement(name = "TravellerFile", required = true)
        protected SearchByTravelDocumentRequestMessageType.SearchData.TravellerFile travellerFile;

        /**
         * Recupera il valore della proprietà travellerFile.
         * 
         * @return
         *     possible object is
         *     {@link SearchByTravelDocumentRequestMessageType.SearchData.TravellerFile }
         *     
         */
        public SearchByTravelDocumentRequestMessageType.SearchData.TravellerFile getTravellerFile() {
            return travellerFile;
        }

        /**
         * Imposta il valore della proprietà travellerFile.
         * 
         * @param value
         *     allowed object is
         *     {@link SearchByTravelDocumentRequestMessageType.SearchData.TravellerFile }
         *     
         */
        public void setTravellerFile(SearchByTravelDocumentRequestMessageType.SearchData.TravellerFile value) {
            this.travellerFile = value;
        }


        /**
         * <p>Classe Java per anonymous complex type.</p>
         * 
         * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
         * 
         * <pre>{@code
         * <complexType>
         *   <complexContent>
         *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       <sequence>
         *         <element name="TravelDocument" type="{http://www.europa.eu/schengen/ees/xsd/v1}TravelDocumentSearchWithMandatoryFieldsRequestType"/>
         *       </sequence>
         *     </restriction>
         *   </complexContent>
         * </complexType>
         * }</pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "travelDocument"
        })
        public static class TravellerFile {

            @XmlElement(name = "TravelDocument", required = true)
            protected TravelDocumentSearchWithMandatoryFieldsRequestType travelDocument;

            /**
             * Recupera il valore della proprietà travelDocument.
             * 
             * @return
             *     possible object is
             *     {@link TravelDocumentSearchWithMandatoryFieldsRequestType }
             *     
             */
            public TravelDocumentSearchWithMandatoryFieldsRequestType getTravelDocument() {
                return travelDocument;
            }

            /**
             * Imposta il valore della proprietà travelDocument.
             * 
             * @param value
             *     allowed object is
             *     {@link TravelDocumentSearchWithMandatoryFieldsRequestType }
             *     
             */
            public void setTravelDocument(TravelDocumentSearchWithMandatoryFieldsRequestType value) {
                this.travelDocument = value;
            }

        }

    }

}
