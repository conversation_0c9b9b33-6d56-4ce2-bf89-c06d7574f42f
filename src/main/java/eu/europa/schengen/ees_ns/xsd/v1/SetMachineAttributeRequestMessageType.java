//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.ees_ns.xsd.v1;

import java.util.ArrayList;
import java.util.List;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per SetMachineAttributeRequestMessageType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="SetMachineAttributeRequestMessageType">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="IdUfficio" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         <element name="IdClient" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="IdUtente" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         <element name="MachineAttributes">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="MachineAttribute" type="{http://www.europa.eu/schengen/ees-ns/xsd/v1}MachineAttributeRequestType" maxOccurs="unbounded"/>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SetMachineAttributeRequestMessageType", propOrder = {
    "idUfficio",
    "idClient",
    "idUtente",
    "machineAttributes"
})
public class SetMachineAttributeRequestMessageType {

    @XmlElement(name = "IdUfficio", required = true)
    protected String idUfficio;
    @XmlElement(name = "IdClient")
    protected String idClient;
    @XmlElement(name = "IdUtente", required = true)
    protected String idUtente;
    @XmlElement(name = "MachineAttributes", required = true)
    protected SetMachineAttributeRequestMessageType.MachineAttributes machineAttributes;

    /**
     * Recupera il valore della proprietà idUfficio.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIdUfficio() {
        return idUfficio;
    }

    /**
     * Imposta il valore della proprietà idUfficio.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIdUfficio(String value) {
        this.idUfficio = value;
    }

    /**
     * Recupera il valore della proprietà idClient.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIdClient() {
        return idClient;
    }

    /**
     * Imposta il valore della proprietà idClient.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIdClient(String value) {
        this.idClient = value;
    }

    /**
     * Recupera il valore della proprietà idUtente.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIdUtente() {
        return idUtente;
    }

    /**
     * Imposta il valore della proprietà idUtente.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIdUtente(String value) {
        this.idUtente = value;
    }

    /**
     * Recupera il valore della proprietà machineAttributes.
     * 
     * @return
     *     possible object is
     *     {@link SetMachineAttributeRequestMessageType.MachineAttributes }
     *     
     */
    public SetMachineAttributeRequestMessageType.MachineAttributes getMachineAttributes() {
        return machineAttributes;
    }

    /**
     * Imposta il valore della proprietà machineAttributes.
     * 
     * @param value
     *     allowed object is
     *     {@link SetMachineAttributeRequestMessageType.MachineAttributes }
     *     
     */
    public void setMachineAttributes(SetMachineAttributeRequestMessageType.MachineAttributes value) {
        this.machineAttributes = value;
    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="MachineAttribute" type="{http://www.europa.eu/schengen/ees-ns/xsd/v1}MachineAttributeRequestType" maxOccurs="unbounded"/>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "machineAttribute"
    })
    public static class MachineAttributes {

        @XmlElement(name = "MachineAttribute", required = true)
        protected List<MachineAttributeRequestType> machineAttribute;

        /**
         * Gets the value of the machineAttribute property.
         * 
         * <p>This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the machineAttribute property.</p>
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * </p>
         * <pre>
         * getMachineAttribute().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link MachineAttributeRequestType }
         * </p>
         * 
         * 
         * @return
         *     The value of the machineAttribute property.
         */
        public List<MachineAttributeRequestType> getMachineAttribute() {
            if (machineAttribute == null) {
                machineAttribute = new ArrayList<>();
            }
            return this.machineAttribute;
        }

    }

}
