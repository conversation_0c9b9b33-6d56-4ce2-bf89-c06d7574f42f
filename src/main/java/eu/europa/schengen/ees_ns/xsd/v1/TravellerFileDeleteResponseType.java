//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.ees_ns.xsd.v1;

import java.util.ArrayList;
import java.util.List;
import eu.europa.schengen.ees.xsd.v1.FlagsType;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per TravellerFileDeleteResponseType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="TravellerFileDeleteResponseType">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <sequence>
 *           <element name="TravelDocument" maxOccurs="unbounded" minOccurs="0">
 *             <complexType>
 *               <complexContent>
 *                 <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                   <sequence>
 *                     <element name="DocumentNumber" type="{http://www.europa.eu/schengen/shared/xsd/v1}TravelDocumentNumberType"/>
 *                   </sequence>
 *                 </restriction>
 *               </complexContent>
 *             </complexType>
 *           </element>
 *           <element name="NFP" minOccurs="0">
 *             <complexType>
 *               <complexContent>
 *                 <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                   <sequence>
 *                     <element name="EstablishingMS" type="{http://www.europa.eu/schengen/shared/xsd/v1}CT70_UserType"/>
 *                   </sequence>
 *                 </restriction>
 *               </complexContent>
 *             </complexType>
 *           </element>
 *           <element name="EntryRecord" maxOccurs="unbounded" minOccurs="0">
 *             <complexType>
 *               <complexContent>
 *                 <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                   <sequence>
 *                     <element name="EntryRecordID" type="{http://www.europa.eu/schengen/ees/xsd/v1}IdentifierType"/>
 *                   </sequence>
 *                 </restriction>
 *               </complexContent>
 *             </complexType>
 *           </element>
 *           <element name="ExitRecord" maxOccurs="unbounded" minOccurs="0">
 *             <complexType>
 *               <complexContent>
 *                 <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                   <sequence>
 *                     <element name="ExitRecordID" type="{http://www.europa.eu/schengen/ees/xsd/v1}IdentifierType"/>
 *                   </sequence>
 *                 </restriction>
 *               </complexContent>
 *             </complexType>
 *           </element>
 *           <element name="RefusalRecord" maxOccurs="unbounded" minOccurs="0">
 *             <complexType>
 *               <complexContent>
 *                 <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                   <sequence>
 *                     <element name="RefusalRecordID" type="{http://www.europa.eu/schengen/ees/xsd/v1}IdentifierType"/>
 *                   </sequence>
 *                 </restriction>
 *               </complexContent>
 *             </complexType>
 *           </element>
 *         </sequence>
 *         <element name="TravellerFileID" type="{http://www.europa.eu/schengen/ees/xsd/v1}IdentifierType"/>
 *         <element name="Flags" type="{http://www.europa.eu/schengen/ees/xsd/v1}FlagsType" minOccurs="0"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "TravellerFileDeleteResponseType", propOrder = {
    "travelDocument",
    "nfp",
    "entryRecord",
    "exitRecord",
    "refusalRecord",
    "travellerFileID",
    "flags"
})
public class TravellerFileDeleteResponseType {

    @XmlElement(name = "TravelDocument")
    protected List<TravellerFileDeleteResponseType.TravelDocument> travelDocument;
    @XmlElement(name = "NFP")
    protected TravellerFileDeleteResponseType.NFP nfp;
    @XmlElement(name = "EntryRecord")
    protected List<TravellerFileDeleteResponseType.EntryRecord> entryRecord;
    @XmlElement(name = "ExitRecord")
    protected List<TravellerFileDeleteResponseType.ExitRecord> exitRecord;
    @XmlElement(name = "RefusalRecord")
    protected List<TravellerFileDeleteResponseType.RefusalRecord> refusalRecord;
    @XmlElement(name = "TravellerFileID", required = true)
    @XmlSchemaType(name = "anyURI")
    protected String travellerFileID;
    @XmlElement(name = "Flags")
    protected FlagsType flags;

    /**
     * Gets the value of the travelDocument property.
     * 
     * <p>This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the travelDocument property.</p>
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * </p>
     * <pre>
     * getTravelDocument().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link TravellerFileDeleteResponseType.TravelDocument }
     * </p>
     * 
     * 
     * @return
     *     The value of the travelDocument property.
     */
    public List<TravellerFileDeleteResponseType.TravelDocument> getTravelDocument() {
        if (travelDocument == null) {
            travelDocument = new ArrayList<>();
        }
        return this.travelDocument;
    }

    /**
     * Recupera il valore della proprietà nfp.
     * 
     * @return
     *     possible object is
     *     {@link TravellerFileDeleteResponseType.NFP }
     *     
     */
    public TravellerFileDeleteResponseType.NFP getNFP() {
        return nfp;
    }

    /**
     * Imposta il valore della proprietà nfp.
     * 
     * @param value
     *     allowed object is
     *     {@link TravellerFileDeleteResponseType.NFP }
     *     
     */
    public void setNFP(TravellerFileDeleteResponseType.NFP value) {
        this.nfp = value;
    }

    /**
     * Gets the value of the entryRecord property.
     * 
     * <p>This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the entryRecord property.</p>
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * </p>
     * <pre>
     * getEntryRecord().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link TravellerFileDeleteResponseType.EntryRecord }
     * </p>
     * 
     * 
     * @return
     *     The value of the entryRecord property.
     */
    public List<TravellerFileDeleteResponseType.EntryRecord> getEntryRecord() {
        if (entryRecord == null) {
            entryRecord = new ArrayList<>();
        }
        return this.entryRecord;
    }

    /**
     * Gets the value of the exitRecord property.
     * 
     * <p>This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the exitRecord property.</p>
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * </p>
     * <pre>
     * getExitRecord().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link TravellerFileDeleteResponseType.ExitRecord }
     * </p>
     * 
     * 
     * @return
     *     The value of the exitRecord property.
     */
    public List<TravellerFileDeleteResponseType.ExitRecord> getExitRecord() {
        if (exitRecord == null) {
            exitRecord = new ArrayList<>();
        }
        return this.exitRecord;
    }

    /**
     * Gets the value of the refusalRecord property.
     * 
     * <p>This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the refusalRecord property.</p>
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * </p>
     * <pre>
     * getRefusalRecord().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link TravellerFileDeleteResponseType.RefusalRecord }
     * </p>
     * 
     * 
     * @return
     *     The value of the refusalRecord property.
     */
    public List<TravellerFileDeleteResponseType.RefusalRecord> getRefusalRecord() {
        if (refusalRecord == null) {
            refusalRecord = new ArrayList<>();
        }
        return this.refusalRecord;
    }

    /**
     * Recupera il valore della proprietà travellerFileID.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTravellerFileID() {
        return travellerFileID;
    }

    /**
     * Imposta il valore della proprietà travellerFileID.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTravellerFileID(String value) {
        this.travellerFileID = value;
    }

    /**
     * Recupera il valore della proprietà flags.
     * 
     * @return
     *     possible object is
     *     {@link FlagsType }
     *     
     */
    public FlagsType getFlags() {
        return flags;
    }

    /**
     * Imposta il valore della proprietà flags.
     * 
     * @param value
     *     allowed object is
     *     {@link FlagsType }
     *     
     */
    public void setFlags(FlagsType value) {
        this.flags = value;
    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="EntryRecordID" type="{http://www.europa.eu/schengen/ees/xsd/v1}IdentifierType"/>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "entryRecordID"
    })
    public static class EntryRecord {

        @XmlElement(name = "EntryRecordID", required = true)
        @XmlSchemaType(name = "anyURI")
        protected String entryRecordID;

        /**
         * Recupera il valore della proprietà entryRecordID.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getEntryRecordID() {
            return entryRecordID;
        }

        /**
         * Imposta il valore della proprietà entryRecordID.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setEntryRecordID(String value) {
            this.entryRecordID = value;
        }

    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="ExitRecordID" type="{http://www.europa.eu/schengen/ees/xsd/v1}IdentifierType"/>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "exitRecordID"
    })
    public static class ExitRecord {

        @XmlElement(name = "ExitRecordID", required = true)
        @XmlSchemaType(name = "anyURI")
        protected String exitRecordID;

        /**
         * Recupera il valore della proprietà exitRecordID.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getExitRecordID() {
            return exitRecordID;
        }

        /**
         * Imposta il valore della proprietà exitRecordID.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setExitRecordID(String value) {
            this.exitRecordID = value;
        }

    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="EstablishingMS" type="{http://www.europa.eu/schengen/shared/xsd/v1}CT70_UserType"/>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "establishingMS"
    })
    public static class NFP {

        @XmlElement(name = "EstablishingMS", required = true)
        protected String establishingMS;

        /**
         * Recupera il valore della proprietà establishingMS.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getEstablishingMS() {
            return establishingMS;
        }

        /**
         * Imposta il valore della proprietà establishingMS.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setEstablishingMS(String value) {
            this.establishingMS = value;
        }

    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="RefusalRecordID" type="{http://www.europa.eu/schengen/ees/xsd/v1}IdentifierType"/>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "refusalRecordID"
    })
    public static class RefusalRecord {

        @XmlElement(name = "RefusalRecordID", required = true)
        @XmlSchemaType(name = "anyURI")
        protected String refusalRecordID;

        /**
         * Recupera il valore della proprietà refusalRecordID.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getRefusalRecordID() {
            return refusalRecordID;
        }

        /**
         * Imposta il valore della proprietà refusalRecordID.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setRefusalRecordID(String value) {
            this.refusalRecordID = value;
        }

    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="DocumentNumber" type="{http://www.europa.eu/schengen/shared/xsd/v1}TravelDocumentNumberType"/>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "documentNumber"
    })
    public static class TravelDocument {

        @XmlElement(name = "DocumentNumber", required = true)
        protected String documentNumber;

        /**
         * Recupera il valore della proprietà documentNumber.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getDocumentNumber() {
            return documentNumber;
        }

        /**
         * Imposta il valore della proprietà documentNumber.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setDocumentNumber(String value) {
            this.documentNumber = value;
        }

    }

}
