//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.ees_ns.xsd.v1;

import eu.europa.schengen.ees.xsd.v1.DirectVisaInformationType;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per IdentificationResultRequestMessageType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="IdentificationResultRequestMessageType">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="ScopeModifiers" type="{http://www.europa.eu/schengen/ees-ns/xsd/v1}WFEScopeModifiersType" minOccurs="0"/>
 *         <element name="CollectedData">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="TravelDocument">
 *                     <complexType>
 *                       <complexContent>
 *                         <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           <sequence>
 *                             <element name="DocumentNumber" type="{http://www.europa.eu/schengen/shared/xsd/v1}TravelDocumentNumberType"/>
 *                           </sequence>
 *                         </restriction>
 *                       </complexContent>
 *                     </complexType>
 *                   </element>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *         <element name="SelectedResponseData">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="EESIdentification" minOccurs="0">
 *                     <complexType>
 *                       <complexContent>
 *                         <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           <sequence>
 *                             <element name="TravellerFile">
 *                               <complexType>
 *                                 <complexContent>
 *                                   <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                     <sequence>
 *                                       <element name="TravellerFileID" type="{http://www.europa.eu/schengen/ees/xsd/v1}IdentifierType" minOccurs="0"/>
 *                                     </sequence>
 *                                   </restriction>
 *                                 </complexContent>
 *                               </complexType>
 *                             </element>
 *                           </sequence>
 *                         </restriction>
 *                       </complexContent>
 *                     </complexType>
 *                   </element>
 *                   <element name="EESAlphanumericIdentification" minOccurs="0">
 *                     <complexType>
 *                       <complexContent>
 *                         <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           <sequence>
 *                             <element name="TravellerFile">
 *                               <complexType>
 *                                 <complexContent>
 *                                   <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                     <sequence>
 *                                       <element name="TravellerFileID" type="{http://www.europa.eu/schengen/ees/xsd/v1}IdentifierType" minOccurs="0"/>
 *                                     </sequence>
 *                                   </restriction>
 *                                 </complexContent>
 *                               </complexType>
 *                             </element>
 *                           </sequence>
 *                         </restriction>
 *                       </complexContent>
 *                     </complexType>
 *                   </element>
 *                   <element name="VISIdentification" minOccurs="0">
 *                     <complexType>
 *                       <complexContent>
 *                         <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           <sequence>
 *                             <element name="VisaApplication">
 *                               <complexType>
 *                                 <complexContent>
 *                                   <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                     <choice minOccurs="0">
 *                                       <element name="VisaStickerNumber" type="{http://www.europa.eu/schengen/shared/xsd/v1}VisaStickerNumberType"/>
 *                                       <element name="VisaApplicationNumber" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}ApplicationNumberType"/>
 *                                     </choice>
 *                                   </restriction>
 *                                 </complexContent>
 *                               </complexType>
 *                             </element>
 *                           </sequence>
 *                         </restriction>
 *                       </complexContent>
 *                     </complexType>
 *                   </element>
 *                   <element name="VISAlphanumericIdentification" minOccurs="0">
 *                     <complexType>
 *                       <complexContent>
 *                         <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           <sequence>
 *                             <element name="DirectVisaInformation" type="{http://www.europa.eu/schengen/ees/xsd/v1}DirectVisaInformationType"/>
 *                           </sequence>
 *                         </restriction>
 *                       </complexContent>
 *                     </complexType>
 *                   </element>
 *                   <element name="VISDirectIdentification" minOccurs="0">
 *                     <complexType>
 *                       <complexContent>
 *                         <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           <sequence>
 *                             <element name="DirectVisaInformation" type="{http://www.europa.eu/schengen/ees/xsd/v1}DirectVisaInformationType"/>
 *                           </sequence>
 *                         </restriction>
 *                       </complexContent>
 *                     </complexType>
 *                   </element>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *         <element name="BorderCrossingPoint" type="{http://www.europa.eu/schengen/shared/xsd/v1}AuthorityUniqueIDType"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "IdentificationResultRequestMessageType", propOrder = {
    "scopeModifiers",
    "collectedData",
    "selectedResponseData",
    "borderCrossingPoint"
})
public class IdentificationResultRequestMessageType {

    @XmlElement(name = "ScopeModifiers")
    protected WFEScopeModifiersType scopeModifiers;
    @XmlElement(name = "CollectedData", required = true)
    protected IdentificationResultRequestMessageType.CollectedData collectedData;
    @XmlElement(name = "SelectedResponseData", required = true)
    protected IdentificationResultRequestMessageType.SelectedResponseData selectedResponseData;
    @XmlElement(name = "BorderCrossingPoint", required = true)
    protected String borderCrossingPoint;

    /**
     * Recupera il valore della proprietà scopeModifiers.
     * 
     * @return
     *     possible object is
     *     {@link WFEScopeModifiersType }
     *     
     */
    public WFEScopeModifiersType getScopeModifiers() {
        return scopeModifiers;
    }

    /**
     * Imposta il valore della proprietà scopeModifiers.
     * 
     * @param value
     *     allowed object is
     *     {@link WFEScopeModifiersType }
     *     
     */
    public void setScopeModifiers(WFEScopeModifiersType value) {
        this.scopeModifiers = value;
    }

    /**
     * Recupera il valore della proprietà collectedData.
     * 
     * @return
     *     possible object is
     *     {@link IdentificationResultRequestMessageType.CollectedData }
     *     
     */
    public IdentificationResultRequestMessageType.CollectedData getCollectedData() {
        return collectedData;
    }

    /**
     * Imposta il valore della proprietà collectedData.
     * 
     * @param value
     *     allowed object is
     *     {@link IdentificationResultRequestMessageType.CollectedData }
     *     
     */
    public void setCollectedData(IdentificationResultRequestMessageType.CollectedData value) {
        this.collectedData = value;
    }

    /**
     * Recupera il valore della proprietà selectedResponseData.
     * 
     * @return
     *     possible object is
     *     {@link IdentificationResultRequestMessageType.SelectedResponseData }
     *     
     */
    public IdentificationResultRequestMessageType.SelectedResponseData getSelectedResponseData() {
        return selectedResponseData;
    }

    /**
     * Imposta il valore della proprietà selectedResponseData.
     * 
     * @param value
     *     allowed object is
     *     {@link IdentificationResultRequestMessageType.SelectedResponseData }
     *     
     */
    public void setSelectedResponseData(IdentificationResultRequestMessageType.SelectedResponseData value) {
        this.selectedResponseData = value;
    }

    /**
     * Recupera il valore della proprietà borderCrossingPoint.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBorderCrossingPoint() {
        return borderCrossingPoint;
    }

    /**
     * Imposta il valore della proprietà borderCrossingPoint.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBorderCrossingPoint(String value) {
        this.borderCrossingPoint = value;
    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="TravelDocument">
     *           <complexType>
     *             <complexContent>
     *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 <sequence>
     *                   <element name="DocumentNumber" type="{http://www.europa.eu/schengen/shared/xsd/v1}TravelDocumentNumberType"/>
     *                 </sequence>
     *               </restriction>
     *             </complexContent>
     *           </complexType>
     *         </element>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "travelDocument"
    })
    public static class CollectedData {

        @XmlElement(name = "TravelDocument", required = true)
        protected IdentificationResultRequestMessageType.CollectedData.TravelDocument travelDocument;

        /**
         * Recupera il valore della proprietà travelDocument.
         * 
         * @return
         *     possible object is
         *     {@link IdentificationResultRequestMessageType.CollectedData.TravelDocument }
         *     
         */
        public IdentificationResultRequestMessageType.CollectedData.TravelDocument getTravelDocument() {
            return travelDocument;
        }

        /**
         * Imposta il valore della proprietà travelDocument.
         * 
         * @param value
         *     allowed object is
         *     {@link IdentificationResultRequestMessageType.CollectedData.TravelDocument }
         *     
         */
        public void setTravelDocument(IdentificationResultRequestMessageType.CollectedData.TravelDocument value) {
            this.travelDocument = value;
        }


        /**
         * <p>Classe Java per anonymous complex type.</p>
         * 
         * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
         * 
         * <pre>{@code
         * <complexType>
         *   <complexContent>
         *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       <sequence>
         *         <element name="DocumentNumber" type="{http://www.europa.eu/schengen/shared/xsd/v1}TravelDocumentNumberType"/>
         *       </sequence>
         *     </restriction>
         *   </complexContent>
         * </complexType>
         * }</pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "documentNumber"
        })
        public static class TravelDocument {

            @XmlElement(name = "DocumentNumber", required = true)
            protected String documentNumber;

            /**
             * Recupera il valore della proprietà documentNumber.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getDocumentNumber() {
                return documentNumber;
            }

            /**
             * Imposta il valore della proprietà documentNumber.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setDocumentNumber(String value) {
                this.documentNumber = value;
            }

        }

    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="EESIdentification" minOccurs="0">
     *           <complexType>
     *             <complexContent>
     *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 <sequence>
     *                   <element name="TravellerFile">
     *                     <complexType>
     *                       <complexContent>
     *                         <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                           <sequence>
     *                             <element name="TravellerFileID" type="{http://www.europa.eu/schengen/ees/xsd/v1}IdentifierType" minOccurs="0"/>
     *                           </sequence>
     *                         </restriction>
     *                       </complexContent>
     *                     </complexType>
     *                   </element>
     *                 </sequence>
     *               </restriction>
     *             </complexContent>
     *           </complexType>
     *         </element>
     *         <element name="EESAlphanumericIdentification" minOccurs="0">
     *           <complexType>
     *             <complexContent>
     *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 <sequence>
     *                   <element name="TravellerFile">
     *                     <complexType>
     *                       <complexContent>
     *                         <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                           <sequence>
     *                             <element name="TravellerFileID" type="{http://www.europa.eu/schengen/ees/xsd/v1}IdentifierType" minOccurs="0"/>
     *                           </sequence>
     *                         </restriction>
     *                       </complexContent>
     *                     </complexType>
     *                   </element>
     *                 </sequence>
     *               </restriction>
     *             </complexContent>
     *           </complexType>
     *         </element>
     *         <element name="VISIdentification" minOccurs="0">
     *           <complexType>
     *             <complexContent>
     *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 <sequence>
     *                   <element name="VisaApplication">
     *                     <complexType>
     *                       <complexContent>
     *                         <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                           <choice minOccurs="0">
     *                             <element name="VisaStickerNumber" type="{http://www.europa.eu/schengen/shared/xsd/v1}VisaStickerNumberType"/>
     *                             <element name="VisaApplicationNumber" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}ApplicationNumberType"/>
     *                           </choice>
     *                         </restriction>
     *                       </complexContent>
     *                     </complexType>
     *                   </element>
     *                 </sequence>
     *               </restriction>
     *             </complexContent>
     *           </complexType>
     *         </element>
     *         <element name="VISAlphanumericIdentification" minOccurs="0">
     *           <complexType>
     *             <complexContent>
     *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 <sequence>
     *                   <element name="DirectVisaInformation" type="{http://www.europa.eu/schengen/ees/xsd/v1}DirectVisaInformationType"/>
     *                 </sequence>
     *               </restriction>
     *             </complexContent>
     *           </complexType>
     *         </element>
     *         <element name="VISDirectIdentification" minOccurs="0">
     *           <complexType>
     *             <complexContent>
     *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 <sequence>
     *                   <element name="DirectVisaInformation" type="{http://www.europa.eu/schengen/ees/xsd/v1}DirectVisaInformationType"/>
     *                 </sequence>
     *               </restriction>
     *             </complexContent>
     *           </complexType>
     *         </element>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "eesIdentification",
        "eesAlphanumericIdentification",
        "visIdentification",
        "visAlphanumericIdentification",
        "visDirectIdentification"
    })
    public static class SelectedResponseData {

        @XmlElement(name = "EESIdentification")
        protected IdentificationResultRequestMessageType.SelectedResponseData.EESIdentification eesIdentification;
        @XmlElement(name = "EESAlphanumericIdentification")
        protected IdentificationResultRequestMessageType.SelectedResponseData.EESAlphanumericIdentification eesAlphanumericIdentification;
        @XmlElement(name = "VISIdentification")
        protected IdentificationResultRequestMessageType.SelectedResponseData.VISIdentification visIdentification;
        @XmlElement(name = "VISAlphanumericIdentification")
        protected IdentificationResultRequestMessageType.SelectedResponseData.VISAlphanumericIdentification visAlphanumericIdentification;
        @XmlElement(name = "VISDirectIdentification")
        protected IdentificationResultRequestMessageType.SelectedResponseData.VISDirectIdentification visDirectIdentification;

        /**
         * Recupera il valore della proprietà eesIdentification.
         * 
         * @return
         *     possible object is
         *     {@link IdentificationResultRequestMessageType.SelectedResponseData.EESIdentification }
         *     
         */
        public IdentificationResultRequestMessageType.SelectedResponseData.EESIdentification getEESIdentification() {
            return eesIdentification;
        }

        /**
         * Imposta il valore della proprietà eesIdentification.
         * 
         * @param value
         *     allowed object is
         *     {@link IdentificationResultRequestMessageType.SelectedResponseData.EESIdentification }
         *     
         */
        public void setEESIdentification(IdentificationResultRequestMessageType.SelectedResponseData.EESIdentification value) {
            this.eesIdentification = value;
        }

        /**
         * Recupera il valore della proprietà eesAlphanumericIdentification.
         * 
         * @return
         *     possible object is
         *     {@link IdentificationResultRequestMessageType.SelectedResponseData.EESAlphanumericIdentification }
         *     
         */
        public IdentificationResultRequestMessageType.SelectedResponseData.EESAlphanumericIdentification getEESAlphanumericIdentification() {
            return eesAlphanumericIdentification;
        }

        /**
         * Imposta il valore della proprietà eesAlphanumericIdentification.
         * 
         * @param value
         *     allowed object is
         *     {@link IdentificationResultRequestMessageType.SelectedResponseData.EESAlphanumericIdentification }
         *     
         */
        public void setEESAlphanumericIdentification(IdentificationResultRequestMessageType.SelectedResponseData.EESAlphanumericIdentification value) {
            this.eesAlphanumericIdentification = value;
        }

        /**
         * Recupera il valore della proprietà visIdentification.
         * 
         * @return
         *     possible object is
         *     {@link IdentificationResultRequestMessageType.SelectedResponseData.VISIdentification }
         *     
         */
        public IdentificationResultRequestMessageType.SelectedResponseData.VISIdentification getVISIdentification() {
            return visIdentification;
        }

        /**
         * Imposta il valore della proprietà visIdentification.
         * 
         * @param value
         *     allowed object is
         *     {@link IdentificationResultRequestMessageType.SelectedResponseData.VISIdentification }
         *     
         */
        public void setVISIdentification(IdentificationResultRequestMessageType.SelectedResponseData.VISIdentification value) {
            this.visIdentification = value;
        }

        /**
         * Recupera il valore della proprietà visAlphanumericIdentification.
         * 
         * @return
         *     possible object is
         *     {@link IdentificationResultRequestMessageType.SelectedResponseData.VISAlphanumericIdentification }
         *     
         */
        public IdentificationResultRequestMessageType.SelectedResponseData.VISAlphanumericIdentification getVISAlphanumericIdentification() {
            return visAlphanumericIdentification;
        }

        /**
         * Imposta il valore della proprietà visAlphanumericIdentification.
         * 
         * @param value
         *     allowed object is
         *     {@link IdentificationResultRequestMessageType.SelectedResponseData.VISAlphanumericIdentification }
         *     
         */
        public void setVISAlphanumericIdentification(IdentificationResultRequestMessageType.SelectedResponseData.VISAlphanumericIdentification value) {
            this.visAlphanumericIdentification = value;
        }

        /**
         * Recupera il valore della proprietà visDirectIdentification.
         * 
         * @return
         *     possible object is
         *     {@link IdentificationResultRequestMessageType.SelectedResponseData.VISDirectIdentification }
         *     
         */
        public IdentificationResultRequestMessageType.SelectedResponseData.VISDirectIdentification getVISDirectIdentification() {
            return visDirectIdentification;
        }

        /**
         * Imposta il valore della proprietà visDirectIdentification.
         * 
         * @param value
         *     allowed object is
         *     {@link IdentificationResultRequestMessageType.SelectedResponseData.VISDirectIdentification }
         *     
         */
        public void setVISDirectIdentification(IdentificationResultRequestMessageType.SelectedResponseData.VISDirectIdentification value) {
            this.visDirectIdentification = value;
        }


        /**
         * <p>Classe Java per anonymous complex type.</p>
         * 
         * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
         * 
         * <pre>{@code
         * <complexType>
         *   <complexContent>
         *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       <sequence>
         *         <element name="TravellerFile">
         *           <complexType>
         *             <complexContent>
         *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                 <sequence>
         *                   <element name="TravellerFileID" type="{http://www.europa.eu/schengen/ees/xsd/v1}IdentifierType" minOccurs="0"/>
         *                 </sequence>
         *               </restriction>
         *             </complexContent>
         *           </complexType>
         *         </element>
         *       </sequence>
         *     </restriction>
         *   </complexContent>
         * </complexType>
         * }</pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "travellerFile"
        })
        public static class EESAlphanumericIdentification {

            @XmlElement(name = "TravellerFile", required = true)
            protected IdentificationResultRequestMessageType.SelectedResponseData.EESAlphanumericIdentification.TravellerFile travellerFile;

            /**
             * Recupera il valore della proprietà travellerFile.
             * 
             * @return
             *     possible object is
             *     {@link IdentificationResultRequestMessageType.SelectedResponseData.EESAlphanumericIdentification.TravellerFile }
             *     
             */
            public IdentificationResultRequestMessageType.SelectedResponseData.EESAlphanumericIdentification.TravellerFile getTravellerFile() {
                return travellerFile;
            }

            /**
             * Imposta il valore della proprietà travellerFile.
             * 
             * @param value
             *     allowed object is
             *     {@link IdentificationResultRequestMessageType.SelectedResponseData.EESAlphanumericIdentification.TravellerFile }
             *     
             */
            public void setTravellerFile(IdentificationResultRequestMessageType.SelectedResponseData.EESAlphanumericIdentification.TravellerFile value) {
                this.travellerFile = value;
            }


            /**
             * <p>Classe Java per anonymous complex type.</p>
             * 
             * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
             * 
             * <pre>{@code
             * <complexType>
             *   <complexContent>
             *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *       <sequence>
             *         <element name="TravellerFileID" type="{http://www.europa.eu/schengen/ees/xsd/v1}IdentifierType" minOccurs="0"/>
             *       </sequence>
             *     </restriction>
             *   </complexContent>
             * </complexType>
             * }</pre>
             * 
             * 
             */
            @XmlAccessorType(XmlAccessType.FIELD)
            @XmlType(name = "", propOrder = {
                "travellerFileID"
            })
            public static class TravellerFile {

                @XmlElement(name = "TravellerFileID")
                @XmlSchemaType(name = "anyURI")
                protected String travellerFileID;

                /**
                 * Recupera il valore della proprietà travellerFileID.
                 * 
                 * @return
                 *     possible object is
                 *     {@link String }
                 *     
                 */
                public String getTravellerFileID() {
                    return travellerFileID;
                }

                /**
                 * Imposta il valore della proprietà travellerFileID.
                 * 
                 * @param value
                 *     allowed object is
                 *     {@link String }
                 *     
                 */
                public void setTravellerFileID(String value) {
                    this.travellerFileID = value;
                }

            }

        }


        /**
         * <p>Classe Java per anonymous complex type.</p>
         * 
         * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
         * 
         * <pre>{@code
         * <complexType>
         *   <complexContent>
         *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       <sequence>
         *         <element name="TravellerFile">
         *           <complexType>
         *             <complexContent>
         *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                 <sequence>
         *                   <element name="TravellerFileID" type="{http://www.europa.eu/schengen/ees/xsd/v1}IdentifierType" minOccurs="0"/>
         *                 </sequence>
         *               </restriction>
         *             </complexContent>
         *           </complexType>
         *         </element>
         *       </sequence>
         *     </restriction>
         *   </complexContent>
         * </complexType>
         * }</pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "travellerFile"
        })
        public static class EESIdentification {

            @XmlElement(name = "TravellerFile", required = true)
            protected IdentificationResultRequestMessageType.SelectedResponseData.EESIdentification.TravellerFile travellerFile;

            /**
             * Recupera il valore della proprietà travellerFile.
             * 
             * @return
             *     possible object is
             *     {@link IdentificationResultRequestMessageType.SelectedResponseData.EESIdentification.TravellerFile }
             *     
             */
            public IdentificationResultRequestMessageType.SelectedResponseData.EESIdentification.TravellerFile getTravellerFile() {
                return travellerFile;
            }

            /**
             * Imposta il valore della proprietà travellerFile.
             * 
             * @param value
             *     allowed object is
             *     {@link IdentificationResultRequestMessageType.SelectedResponseData.EESIdentification.TravellerFile }
             *     
             */
            public void setTravellerFile(IdentificationResultRequestMessageType.SelectedResponseData.EESIdentification.TravellerFile value) {
                this.travellerFile = value;
            }


            /**
             * <p>Classe Java per anonymous complex type.</p>
             * 
             * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
             * 
             * <pre>{@code
             * <complexType>
             *   <complexContent>
             *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *       <sequence>
             *         <element name="TravellerFileID" type="{http://www.europa.eu/schengen/ees/xsd/v1}IdentifierType" minOccurs="0"/>
             *       </sequence>
             *     </restriction>
             *   </complexContent>
             * </complexType>
             * }</pre>
             * 
             * 
             */
            @XmlAccessorType(XmlAccessType.FIELD)
            @XmlType(name = "", propOrder = {
                "travellerFileID"
            })
            public static class TravellerFile {

                @XmlElement(name = "TravellerFileID")
                @XmlSchemaType(name = "anyURI")
                protected String travellerFileID;

                /**
                 * Recupera il valore della proprietà travellerFileID.
                 * 
                 * @return
                 *     possible object is
                 *     {@link String }
                 *     
                 */
                public String getTravellerFileID() {
                    return travellerFileID;
                }

                /**
                 * Imposta il valore della proprietà travellerFileID.
                 * 
                 * @param value
                 *     allowed object is
                 *     {@link String }
                 *     
                 */
                public void setTravellerFileID(String value) {
                    this.travellerFileID = value;
                }

            }

        }


        /**
         * <p>Classe Java per anonymous complex type.</p>
         * 
         * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
         * 
         * <pre>{@code
         * <complexType>
         *   <complexContent>
         *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       <sequence>
         *         <element name="DirectVisaInformation" type="{http://www.europa.eu/schengen/ees/xsd/v1}DirectVisaInformationType"/>
         *       </sequence>
         *     </restriction>
         *   </complexContent>
         * </complexType>
         * }</pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "directVisaInformation"
        })
        public static class VISAlphanumericIdentification {

            @XmlElement(name = "DirectVisaInformation", required = true)
            protected DirectVisaInformationType directVisaInformation;

            /**
             * Recupera il valore della proprietà directVisaInformation.
             * 
             * @return
             *     possible object is
             *     {@link DirectVisaInformationType }
             *     
             */
            public DirectVisaInformationType getDirectVisaInformation() {
                return directVisaInformation;
            }

            /**
             * Imposta il valore della proprietà directVisaInformation.
             * 
             * @param value
             *     allowed object is
             *     {@link DirectVisaInformationType }
             *     
             */
            public void setDirectVisaInformation(DirectVisaInformationType value) {
                this.directVisaInformation = value;
            }

        }


        /**
         * <p>Classe Java per anonymous complex type.</p>
         * 
         * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
         * 
         * <pre>{@code
         * <complexType>
         *   <complexContent>
         *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       <sequence>
         *         <element name="DirectVisaInformation" type="{http://www.europa.eu/schengen/ees/xsd/v1}DirectVisaInformationType"/>
         *       </sequence>
         *     </restriction>
         *   </complexContent>
         * </complexType>
         * }</pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "directVisaInformation"
        })
        public static class VISDirectIdentification {

            @XmlElement(name = "DirectVisaInformation", required = true)
            protected DirectVisaInformationType directVisaInformation;

            /**
             * Recupera il valore della proprietà directVisaInformation.
             * 
             * @return
             *     possible object is
             *     {@link DirectVisaInformationType }
             *     
             */
            public DirectVisaInformationType getDirectVisaInformation() {
                return directVisaInformation;
            }

            /**
             * Imposta il valore della proprietà directVisaInformation.
             * 
             * @param value
             *     allowed object is
             *     {@link DirectVisaInformationType }
             *     
             */
            public void setDirectVisaInformation(DirectVisaInformationType value) {
                this.directVisaInformation = value;
            }

        }


        /**
         * <p>Classe Java per anonymous complex type.</p>
         * 
         * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
         * 
         * <pre>{@code
         * <complexType>
         *   <complexContent>
         *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       <sequence>
         *         <element name="VisaApplication">
         *           <complexType>
         *             <complexContent>
         *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                 <choice minOccurs="0">
         *                   <element name="VisaStickerNumber" type="{http://www.europa.eu/schengen/shared/xsd/v1}VisaStickerNumberType"/>
         *                   <element name="VisaApplicationNumber" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}ApplicationNumberType"/>
         *                 </choice>
         *               </restriction>
         *             </complexContent>
         *           </complexType>
         *         </element>
         *       </sequence>
         *     </restriction>
         *   </complexContent>
         * </complexType>
         * }</pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "visaApplication"
        })
        public static class VISIdentification {

            @XmlElement(name = "VisaApplication", required = true)
            protected IdentificationResultRequestMessageType.SelectedResponseData.VISIdentification.VisaApplication visaApplication;

            /**
             * Recupera il valore della proprietà visaApplication.
             * 
             * @return
             *     possible object is
             *     {@link IdentificationResultRequestMessageType.SelectedResponseData.VISIdentification.VisaApplication }
             *     
             */
            public IdentificationResultRequestMessageType.SelectedResponseData.VISIdentification.VisaApplication getVisaApplication() {
                return visaApplication;
            }

            /**
             * Imposta il valore della proprietà visaApplication.
             * 
             * @param value
             *     allowed object is
             *     {@link IdentificationResultRequestMessageType.SelectedResponseData.VISIdentification.VisaApplication }
             *     
             */
            public void setVisaApplication(IdentificationResultRequestMessageType.SelectedResponseData.VISIdentification.VisaApplication value) {
                this.visaApplication = value;
            }


            /**
             * <p>Classe Java per anonymous complex type.</p>
             * 
             * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
             * 
             * <pre>{@code
             * <complexType>
             *   <complexContent>
             *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *       <choice minOccurs="0">
             *         <element name="VisaStickerNumber" type="{http://www.europa.eu/schengen/shared/xsd/v1}VisaStickerNumberType"/>
             *         <element name="VisaApplicationNumber" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}ApplicationNumberType"/>
             *       </choice>
             *     </restriction>
             *   </complexContent>
             * </complexType>
             * }</pre>
             * 
             * 
             */
            @XmlAccessorType(XmlAccessType.FIELD)
            @XmlType(name = "", propOrder = {
                "visaStickerNumber",
                "visaApplicationNumber"
            })
            public static class VisaApplication {

                @XmlElement(name = "VisaStickerNumber")
                protected String visaStickerNumber;
                @XmlElement(name = "VisaApplicationNumber")
                protected String visaApplicationNumber;

                /**
                 * Recupera il valore della proprietà visaStickerNumber.
                 * 
                 * @return
                 *     possible object is
                 *     {@link String }
                 *     
                 */
                public String getVisaStickerNumber() {
                    return visaStickerNumber;
                }

                /**
                 * Imposta il valore della proprietà visaStickerNumber.
                 * 
                 * @param value
                 *     allowed object is
                 *     {@link String }
                 *     
                 */
                public void setVisaStickerNumber(String value) {
                    this.visaStickerNumber = value;
                }

                /**
                 * Recupera il valore della proprietà visaApplicationNumber.
                 * 
                 * @return
                 *     possible object is
                 *     {@link String }
                 *     
                 */
                public String getVisaApplicationNumber() {
                    return visaApplicationNumber;
                }

                /**
                 * Imposta il valore della proprietà visaApplicationNumber.
                 * 
                 * @param value
                 *     allowed object is
                 *     {@link String }
                 *     
                 */
                public void setVisaApplicationNumber(String value) {
                    this.visaApplicationNumber = value;
                }

            }

        }

    }

}
