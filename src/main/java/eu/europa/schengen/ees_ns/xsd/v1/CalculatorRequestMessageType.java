//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.ees_ns.xsd.v1;

import eu.europa.schengen.ees.xsd.v1.ExtendedCalculatorScopeType;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per CalculatorRequestMessageType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="CalculatorRequestMessageType">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="Calculator">
 *           <complexType>
 *             <complexContent>
 *               <extension base="{http://www.europa.eu/schengen/ees/xsd/v1}ExtendedCalculatorScopeType">
 *                 <choice>
 *                   <element name="TravellerFileID" type="{http://www.europa.eu/schengen/ees/xsd/v1}IdentifierType"/>
 *                   <element name="TravelDocument">
 *                     <complexType>
 *                       <complexContent>
 *                         <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           <sequence>
 *                             <element name="DocumentNumber" type="{http://www.europa.eu/schengen/shared/xsd/v1}TravelDocumentNumberType"/>
 *                           </sequence>
 *                         </restriction>
 *                       </complexContent>
 *                     </complexType>
 *                   </element>
 *                 </choice>
 *               </extension>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "CalculatorRequestMessageType", propOrder = {
    "calculator"
})
public class CalculatorRequestMessageType {

    @XmlElement(name = "Calculator", required = true)
    protected CalculatorRequestMessageType.Calculator calculator;

    /**
     * Recupera il valore della proprietà calculator.
     * 
     * @return
     *     possible object is
     *     {@link CalculatorRequestMessageType.Calculator }
     *     
     */
    public CalculatorRequestMessageType.Calculator getCalculator() {
        return calculator;
    }

    /**
     * Imposta il valore della proprietà calculator.
     * 
     * @param value
     *     allowed object is
     *     {@link CalculatorRequestMessageType.Calculator }
     *     
     */
    public void setCalculator(CalculatorRequestMessageType.Calculator value) {
        this.calculator = value;
    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <extension base="{http://www.europa.eu/schengen/ees/xsd/v1}ExtendedCalculatorScopeType">
     *       <choice>
     *         <element name="TravellerFileID" type="{http://www.europa.eu/schengen/ees/xsd/v1}IdentifierType"/>
     *         <element name="TravelDocument">
     *           <complexType>
     *             <complexContent>
     *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 <sequence>
     *                   <element name="DocumentNumber" type="{http://www.europa.eu/schengen/shared/xsd/v1}TravelDocumentNumberType"/>
     *                 </sequence>
     *               </restriction>
     *             </complexContent>
     *           </complexType>
     *         </element>
     *       </choice>
     *     </extension>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "travellerFileID",
        "travelDocument"
    })
    public static class Calculator
        extends ExtendedCalculatorScopeType
    {

        @XmlElement(name = "TravellerFileID")
        @XmlSchemaType(name = "anyURI")
        protected String travellerFileID;
        @XmlElement(name = "TravelDocument")
        protected CalculatorRequestMessageType.Calculator.TravelDocument travelDocument;

        /**
         * Recupera il valore della proprietà travellerFileID.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getTravellerFileID() {
            return travellerFileID;
        }

        /**
         * Imposta il valore della proprietà travellerFileID.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setTravellerFileID(String value) {
            this.travellerFileID = value;
        }

        /**
         * Recupera il valore della proprietà travelDocument.
         * 
         * @return
         *     possible object is
         *     {@link CalculatorRequestMessageType.Calculator.TravelDocument }
         *     
         */
        public CalculatorRequestMessageType.Calculator.TravelDocument getTravelDocument() {
            return travelDocument;
        }

        /**
         * Imposta il valore della proprietà travelDocument.
         * 
         * @param value
         *     allowed object is
         *     {@link CalculatorRequestMessageType.Calculator.TravelDocument }
         *     
         */
        public void setTravelDocument(CalculatorRequestMessageType.Calculator.TravelDocument value) {
            this.travelDocument = value;
        }


        /**
         * <p>Classe Java per anonymous complex type.</p>
         * 
         * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
         * 
         * <pre>{@code
         * <complexType>
         *   <complexContent>
         *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       <sequence>
         *         <element name="DocumentNumber" type="{http://www.europa.eu/schengen/shared/xsd/v1}TravelDocumentNumberType"/>
         *       </sequence>
         *     </restriction>
         *   </complexContent>
         * </complexType>
         * }</pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "documentNumber"
        })
        public static class TravelDocument {

            @XmlElement(name = "DocumentNumber", required = true)
            protected String documentNumber;

            /**
             * Recupera il valore della proprietà documentNumber.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getDocumentNumber() {
                return documentNumber;
            }

            /**
             * Imposta il valore della proprietà documentNumber.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setDocumentNumber(String value) {
                this.documentNumber = value;
            }

        }

    }

}
