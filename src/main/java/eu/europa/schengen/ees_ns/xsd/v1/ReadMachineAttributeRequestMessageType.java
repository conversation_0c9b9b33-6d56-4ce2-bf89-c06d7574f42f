//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.ees_ns.xsd.v1;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per ReadMachineAttributeRequestMessageType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="ReadMachineAttributeRequestMessageType">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="IdUfficio" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         <element name="IdClient" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         <element name="TipoAttributo" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ReadMachineAttributeRequestMessageType", propOrder = {
    "idUfficio",
    "idClient",
    "tipoAttributo"
})
public class ReadMachineAttributeRequestMessageType {

    @XmlElement(name = "IdUfficio", required = true)
    protected String idUfficio;
    @XmlElement(name = "IdClient", required = true, nillable = true)
    protected String idClient;
    @XmlElement(name = "TipoAttributo", required = true, nillable = true)
    protected String tipoAttributo;

    /**
     * Recupera il valore della proprietà idUfficio.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIdUfficio() {
        return idUfficio;
    }

    /**
     * Imposta il valore della proprietà idUfficio.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIdUfficio(String value) {
        this.idUfficio = value;
    }

    /**
     * Recupera il valore della proprietà idClient.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIdClient() {
        return idClient;
    }

    /**
     * Imposta il valore della proprietà idClient.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIdClient(String value) {
        this.idClient = value;
    }

    /**
     * Recupera il valore della proprietà tipoAttributo.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTipoAttributo() {
        return tipoAttributo;
    }

    /**
     * Imposta il valore della proprietà tipoAttributo.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTipoAttributo(String value) {
        this.tipoAttributo = value;
    }

}
