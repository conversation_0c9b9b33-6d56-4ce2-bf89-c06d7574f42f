//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.ees_ns.xsd.v1;

import eu.europa.schengen.ees.xsd.v1.CalculatorResultType;
import eu.europa.schengen.ees.xsd.v1.TravellerFileWithHistoryType;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per RetrievePreEnrolledDataResponseMessageType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="RetrievePreEnrolledDataResponseMessageType">
 *   <complexContent>
 *     <extension base="{http://www.europa.eu/schengen/ees-ns/xsd/v1}MessageResponseType">
 *       <sequence>
 *         <element name="Response" minOccurs="0">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="PreEnrolmentData" minOccurs="0">
 *                     <complexType>
 *                       <complexContent>
 *                         <extension base="{http://www.europa.eu/schengen/ees-ns/xsd/v1}PreEnrolmentDataResponseType">
 *                           <sequence>
 *                             <element name="Calculator" type="{http://www.europa.eu/schengen/ees/xsd/v1}CalculatorResultType" minOccurs="0"/>
 *                           </sequence>
 *                         </extension>
 *                       </complexContent>
 *                     </complexType>
 *                   </element>
 *                   <element name="TravellerFile" type="{http://www.europa.eu/schengen/ees/xsd/v1}TravellerFileWithHistoryType" minOccurs="0"/>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *       </sequence>
 *     </extension>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "RetrievePreEnrolledDataResponseMessageType", propOrder = {
    "response"
})
public class RetrievePreEnrolledDataResponseMessageType
    extends MessageResponseType
{

    @XmlElement(name = "Response")
    protected RetrievePreEnrolledDataResponseMessageType.Response response;

    /**
     * Recupera il valore della proprietà response.
     * 
     * @return
     *     possible object is
     *     {@link RetrievePreEnrolledDataResponseMessageType.Response }
     *     
     */
    public RetrievePreEnrolledDataResponseMessageType.Response getResponse() {
        return response;
    }

    /**
     * Imposta il valore della proprietà response.
     * 
     * @param value
     *     allowed object is
     *     {@link RetrievePreEnrolledDataResponseMessageType.Response }
     *     
     */
    public void setResponse(RetrievePreEnrolledDataResponseMessageType.Response value) {
        this.response = value;
    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="PreEnrolmentData" minOccurs="0">
     *           <complexType>
     *             <complexContent>
     *               <extension base="{http://www.europa.eu/schengen/ees-ns/xsd/v1}PreEnrolmentDataResponseType">
     *                 <sequence>
     *                   <element name="Calculator" type="{http://www.europa.eu/schengen/ees/xsd/v1}CalculatorResultType" minOccurs="0"/>
     *                 </sequence>
     *               </extension>
     *             </complexContent>
     *           </complexType>
     *         </element>
     *         <element name="TravellerFile" type="{http://www.europa.eu/schengen/ees/xsd/v1}TravellerFileWithHistoryType" minOccurs="0"/>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "preEnrolmentData",
        "travellerFile"
    })
    public static class Response {

        /**
         * For biometrics only quality metrics are returned
         * 
         */
        @XmlElement(name = "PreEnrolmentData")
        protected RetrievePreEnrolledDataResponseMessageType.Response.PreEnrolmentData preEnrolmentData;
        @XmlElement(name = "TravellerFile")
        protected TravellerFileWithHistoryType travellerFile;

        /**
         * For biometrics only quality metrics are returned
         * 
         * @return
         *     possible object is
         *     {@link RetrievePreEnrolledDataResponseMessageType.Response.PreEnrolmentData }
         *     
         */
        public RetrievePreEnrolledDataResponseMessageType.Response.PreEnrolmentData getPreEnrolmentData() {
            return preEnrolmentData;
        }

        /**
         * Imposta il valore della proprietà preEnrolmentData.
         * 
         * @param value
         *     allowed object is
         *     {@link RetrievePreEnrolledDataResponseMessageType.Response.PreEnrolmentData }
         *     
         * @see #getPreEnrolmentData()
         */
        public void setPreEnrolmentData(RetrievePreEnrolledDataResponseMessageType.Response.PreEnrolmentData value) {
            this.preEnrolmentData = value;
        }

        /**
         * Recupera il valore della proprietà travellerFile.
         * 
         * @return
         *     possible object is
         *     {@link TravellerFileWithHistoryType }
         *     
         */
        public TravellerFileWithHistoryType getTravellerFile() {
            return travellerFile;
        }

        /**
         * Imposta il valore della proprietà travellerFile.
         * 
         * @param value
         *     allowed object is
         *     {@link TravellerFileWithHistoryType }
         *     
         */
        public void setTravellerFile(TravellerFileWithHistoryType value) {
            this.travellerFile = value;
        }


        /**
         * <p>Classe Java per anonymous complex type.</p>
         * 
         * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
         * 
         * <pre>{@code
         * <complexType>
         *   <complexContent>
         *     <extension base="{http://www.europa.eu/schengen/ees-ns/xsd/v1}PreEnrolmentDataResponseType">
         *       <sequence>
         *         <element name="Calculator" type="{http://www.europa.eu/schengen/ees/xsd/v1}CalculatorResultType" minOccurs="0"/>
         *       </sequence>
         *     </extension>
         *   </complexContent>
         * </complexType>
         * }</pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "calculator"
        })
        public static class PreEnrolmentData
            extends PreEnrolmentDataResponseType
        {

            @XmlElement(name = "Calculator")
            protected CalculatorResultType calculator;

            /**
             * Recupera il valore della proprietà calculator.
             * 
             * @return
             *     possible object is
             *     {@link CalculatorResultType }
             *     
             */
            public CalculatorResultType getCalculator() {
                return calculator;
            }

            /**
             * Imposta il valore della proprietà calculator.
             * 
             * @param value
             *     allowed object is
             *     {@link CalculatorResultType }
             *     
             */
            public void setCalculator(CalculatorResultType value) {
                this.calculator = value;
            }

        }

    }

}
