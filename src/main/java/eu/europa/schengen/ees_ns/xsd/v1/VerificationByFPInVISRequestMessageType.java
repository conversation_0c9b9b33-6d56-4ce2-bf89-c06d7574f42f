//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.ees_ns.xsd.v1;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per VerificationByFPInVISRequestMessageType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="VerificationByFPInVISRequestMessageType">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="ScopeModifiers" minOccurs="0">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="OperationModifier" type="{http://www.europa.eu/schengen/ees/xsd/v1}ST506_OperationModifierType" minOccurs="0"/>
 *                   <element name="VisaApplications" type="{http://www.europa.eu/schengen/ees/xsd/v1}ST508_VisaApplicationsModifierType" minOccurs="0"/>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *         <choice>
 *           <element name="VisaStickerNumber" type="{http://www.europa.eu/schengen/shared/xsd/v1}VisaStickerNumberType"/>
 *           <element name="ApplicationNumber" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}ApplicationNumberType"/>
 *         </choice>
 *         <element name="FP">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="NISTFile" type="{http://www.europa.eu/schengen/ees/xsd/v1}FPNISTFile"/>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "VerificationByFPInVISRequestMessageType", propOrder = {
    "scopeModifiers",
    "visaStickerNumber",
    "applicationNumber",
    "fp"
})
public class VerificationByFPInVISRequestMessageType {

    @XmlElement(name = "ScopeModifiers")
    protected VerificationByFPInVISRequestMessageType.ScopeModifiers scopeModifiers;
    @XmlElement(name = "VisaStickerNumber")
    protected String visaStickerNumber;
    @XmlElement(name = "ApplicationNumber")
    protected String applicationNumber;
    @XmlElement(name = "FP", required = true)
    protected VerificationByFPInVISRequestMessageType.FP fp;

    /**
     * Recupera il valore della proprietà scopeModifiers.
     * 
     * @return
     *     possible object is
     *     {@link VerificationByFPInVISRequestMessageType.ScopeModifiers }
     *     
     */
    public VerificationByFPInVISRequestMessageType.ScopeModifiers getScopeModifiers() {
        return scopeModifiers;
    }

    /**
     * Imposta il valore della proprietà scopeModifiers.
     * 
     * @param value
     *     allowed object is
     *     {@link VerificationByFPInVISRequestMessageType.ScopeModifiers }
     *     
     */
    public void setScopeModifiers(VerificationByFPInVISRequestMessageType.ScopeModifiers value) {
        this.scopeModifiers = value;
    }

    /**
     * Recupera il valore della proprietà visaStickerNumber.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getVisaStickerNumber() {
        return visaStickerNumber;
    }

    /**
     * Imposta il valore della proprietà visaStickerNumber.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setVisaStickerNumber(String value) {
        this.visaStickerNumber = value;
    }

    /**
     * Recupera il valore della proprietà applicationNumber.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getApplicationNumber() {
        return applicationNumber;
    }

    /**
     * Imposta il valore della proprietà applicationNumber.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setApplicationNumber(String value) {
        this.applicationNumber = value;
    }

    /**
     * Recupera il valore della proprietà fp.
     * 
     * @return
     *     possible object is
     *     {@link VerificationByFPInVISRequestMessageType.FP }
     *     
     */
    public VerificationByFPInVISRequestMessageType.FP getFP() {
        return fp;
    }

    /**
     * Imposta il valore della proprietà fp.
     * 
     * @param value
     *     allowed object is
     *     {@link VerificationByFPInVISRequestMessageType.FP }
     *     
     */
    public void setFP(VerificationByFPInVISRequestMessageType.FP value) {
        this.fp = value;
    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="NISTFile" type="{http://www.europa.eu/schengen/ees/xsd/v1}FPNISTFile"/>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "nistFile"
    })
    public static class FP {

        @XmlElement(name = "NISTFile", required = true)
        protected byte[] nistFile;

        /**
         * Recupera il valore della proprietà nistFile.
         * 
         * @return
         *     possible object is
         *     byte[]
         */
        public byte[] getNISTFile() {
            return nistFile;
        }

        /**
         * Imposta il valore della proprietà nistFile.
         * 
         * @param value
         *     allowed object is
         *     byte[]
         */
        public void setNISTFile(byte[] value) {
            this.nistFile = value;
        }

    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="OperationModifier" type="{http://www.europa.eu/schengen/ees/xsd/v1}ST506_OperationModifierType" minOccurs="0"/>
     *         <element name="VisaApplications" type="{http://www.europa.eu/schengen/ees/xsd/v1}ST508_VisaApplicationsModifierType" minOccurs="0"/>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "operationModifier",
        "visaApplications"
    })
    public static class ScopeModifiers {

        @XmlElement(name = "OperationModifier")
        protected String operationModifier;
        @XmlElement(name = "VisaApplications")
        protected String visaApplications;

        /**
         * Recupera il valore della proprietà operationModifier.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getOperationModifier() {
            return operationModifier;
        }

        /**
         * Imposta il valore della proprietà operationModifier.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setOperationModifier(String value) {
            this.operationModifier = value;
        }

        /**
         * Recupera il valore della proprietà visaApplications.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getVisaApplications() {
            return visaApplications;
        }

        /**
         * Imposta il valore della proprietà visaApplications.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setVisaApplications(String value) {
            this.visaApplications = value;
        }

    }

}
