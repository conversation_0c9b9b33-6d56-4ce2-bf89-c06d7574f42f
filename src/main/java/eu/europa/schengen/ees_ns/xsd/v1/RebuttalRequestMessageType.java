//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.ees_ns.xsd.v1;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per RebuttalRequestMessageType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="RebuttalRequestMessageType">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <choice>
 *         <element name="Create" type="{http://www.europa.eu/schengen/ees-ns/xsd/v1}RebuttalCreateRequest"/>
 *         <element name="Update" type="{http://www.europa.eu/schengen/ees-ns/xsd/v1}RebuttalUpdateRequest"/>
 *         <element name="Delete" type="{http://www.europa.eu/schengen/ees-ns/xsd/v1}RebuttalDeleteRequest"/>
 *         <element name="Offline" type="{http://www.europa.eu/schengen/ees-ns/xsd/v1}RebuttalOfflineRequest"/>
 *       </choice>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "RebuttalRequestMessageType", propOrder = {
    "create",
    "update",
    "delete",
    "offline"
})
public class RebuttalRequestMessageType {

    @XmlElement(name = "Create")
    protected RebuttalCreateRequest create;
    @XmlElement(name = "Update")
    protected RebuttalUpdateRequest update;
    @XmlElement(name = "Delete")
    protected RebuttalDeleteRequest delete;
    @XmlElement(name = "Offline")
    protected RebuttalOfflineRequest offline;

    /**
     * Recupera il valore della proprietà create.
     * 
     * @return
     *     possible object is
     *     {@link RebuttalCreateRequest }
     *     
     */
    public RebuttalCreateRequest getCreate() {
        return create;
    }

    /**
     * Imposta il valore della proprietà create.
     * 
     * @param value
     *     allowed object is
     *     {@link RebuttalCreateRequest }
     *     
     */
    public void setCreate(RebuttalCreateRequest value) {
        this.create = value;
    }

    /**
     * Recupera il valore della proprietà update.
     * 
     * @return
     *     possible object is
     *     {@link RebuttalUpdateRequest }
     *     
     */
    public RebuttalUpdateRequest getUpdate() {
        return update;
    }

    /**
     * Imposta il valore della proprietà update.
     * 
     * @param value
     *     allowed object is
     *     {@link RebuttalUpdateRequest }
     *     
     */
    public void setUpdate(RebuttalUpdateRequest value) {
        this.update = value;
    }

    /**
     * Recupera il valore della proprietà delete.
     * 
     * @return
     *     possible object is
     *     {@link RebuttalDeleteRequest }
     *     
     */
    public RebuttalDeleteRequest getDelete() {
        return delete;
    }

    /**
     * Imposta il valore della proprietà delete.
     * 
     * @param value
     *     allowed object is
     *     {@link RebuttalDeleteRequest }
     *     
     */
    public void setDelete(RebuttalDeleteRequest value) {
        this.delete = value;
    }

    /**
     * Recupera il valore della proprietà offline.
     * 
     * @return
     *     possible object is
     *     {@link RebuttalOfflineRequest }
     *     
     */
    public RebuttalOfflineRequest getOffline() {
        return offline;
    }

    /**
     * Imposta il valore della proprietà offline.
     * 
     * @param value
     *     allowed object is
     *     {@link RebuttalOfflineRequest }
     *     
     */
    public void setOffline(RebuttalOfflineRequest value) {
        this.offline = value;
    }

}
