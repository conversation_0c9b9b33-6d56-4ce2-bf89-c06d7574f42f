//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.ees_ns.xsd.v1;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per MachineAttributeRequestType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="MachineAttributeRequestType">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="TipoAttributo" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         <element name="Valore" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         <element name="PeriodoValidita" type="{http://www.europa.eu/schengen/ees-ns/xsd/v1}PeriodValidationType"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "MachineAttributeRequestType", propOrder = {
    "tipoAttributo",
    "valore",
    "periodoValidita"
})
public class MachineAttributeRequestType {

    @XmlElement(name = "TipoAttributo", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1", required = true)
    protected String tipoAttributo;
    @XmlElement(name = "Valore", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1", required = true)
    protected String valore;
    @XmlElement(name = "PeriodoValidita", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1", required = true, nillable = true)
    protected PeriodValidationType periodoValidita;

    /**
     * Recupera il valore della proprietà tipoAttributo.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTipoAttributo() {
        return tipoAttributo;
    }

    /**
     * Imposta il valore della proprietà tipoAttributo.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTipoAttributo(String value) {
        this.tipoAttributo = value;
    }

    /**
     * Recupera il valore della proprietà valore.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getValore() {
        return valore;
    }

    /**
     * Imposta il valore della proprietà valore.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setValore(String value) {
        this.valore = value;
    }

    /**
     * Recupera il valore della proprietà periodoValidita.
     * 
     * @return
     *     possible object is
     *     {@link PeriodValidationType }
     *     
     */
    public PeriodValidationType getPeriodoValidita() {
        return periodoValidita;
    }

    /**
     * Imposta il valore della proprietà periodoValidita.
     * 
     * @param value
     *     allowed object is
     *     {@link PeriodValidationType }
     *     
     */
    public void setPeriodoValidita(PeriodValidationType value) {
        this.periodoValidita = value;
    }

}
