//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.ees_ns.xsd.v1;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per DataEntryRequestMessageType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="DataEntryRequestMessageType">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <choice>
 *         <element name="Create">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="TravellerFile" type="{http://www.europa.eu/schengen/ees-ns/xsd/v1}DataEntryCreateRequestType"/>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *         <element name="Update">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="TravellerFile" type="{http://www.europa.eu/schengen/ees-ns/xsd/v1}DataEntryUpdateRequestType"/>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *         <element name="Offline">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="TravellerFile" type="{http://www.europa.eu/schengen/ees-ns/xsd/v1}DataEntryOfflineRequestType"/>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *       </choice>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "DataEntryRequestMessageType", propOrder = {
    "create",
    "update",
    "offline"
})
public class DataEntryRequestMessageType {

    @XmlElement(name = "Create")
    protected DataEntryRequestMessageType.Create create;
    @XmlElement(name = "Update")
    protected DataEntryRequestMessageType.Update update;
    @XmlElement(name = "Offline")
    protected DataEntryRequestMessageType.Offline offline;

    /**
     * Recupera il valore della proprietà create.
     * 
     * @return
     *     possible object is
     *     {@link DataEntryRequestMessageType.Create }
     *     
     */
    public DataEntryRequestMessageType.Create getCreate() {
        return create;
    }

    /**
     * Imposta il valore della proprietà create.
     * 
     * @param value
     *     allowed object is
     *     {@link DataEntryRequestMessageType.Create }
     *     
     */
    public void setCreate(DataEntryRequestMessageType.Create value) {
        this.create = value;
    }

    /**
     * Recupera il valore della proprietà update.
     * 
     * @return
     *     possible object is
     *     {@link DataEntryRequestMessageType.Update }
     *     
     */
    public DataEntryRequestMessageType.Update getUpdate() {
        return update;
    }

    /**
     * Imposta il valore della proprietà update.
     * 
     * @param value
     *     allowed object is
     *     {@link DataEntryRequestMessageType.Update }
     *     
     */
    public void setUpdate(DataEntryRequestMessageType.Update value) {
        this.update = value;
    }

    /**
     * Recupera il valore della proprietà offline.
     * 
     * @return
     *     possible object is
     *     {@link DataEntryRequestMessageType.Offline }
     *     
     */
    public DataEntryRequestMessageType.Offline getOffline() {
        return offline;
    }

    /**
     * Imposta il valore della proprietà offline.
     * 
     * @param value
     *     allowed object is
     *     {@link DataEntryRequestMessageType.Offline }
     *     
     */
    public void setOffline(DataEntryRequestMessageType.Offline value) {
        this.offline = value;
    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="TravellerFile" type="{http://www.europa.eu/schengen/ees-ns/xsd/v1}DataEntryCreateRequestType"/>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "travellerFile"
    })
    public static class Create {

        @XmlElement(name = "TravellerFile", required = true)
        protected DataEntryCreateRequestType travellerFile;

        /**
         * Recupera il valore della proprietà travellerFile.
         * 
         * @return
         *     possible object is
         *     {@link DataEntryCreateRequestType }
         *     
         */
        public DataEntryCreateRequestType getTravellerFile() {
            return travellerFile;
        }

        /**
         * Imposta il valore della proprietà travellerFile.
         * 
         * @param value
         *     allowed object is
         *     {@link DataEntryCreateRequestType }
         *     
         */
        public void setTravellerFile(DataEntryCreateRequestType value) {
            this.travellerFile = value;
        }

    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="TravellerFile" type="{http://www.europa.eu/schengen/ees-ns/xsd/v1}DataEntryOfflineRequestType"/>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "travellerFile"
    })
    public static class Offline {

        @XmlElement(name = "TravellerFile", required = true)
        protected DataEntryOfflineRequestType travellerFile;

        /**
         * Recupera il valore della proprietà travellerFile.
         * 
         * @return
         *     possible object is
         *     {@link DataEntryOfflineRequestType }
         *     
         */
        public DataEntryOfflineRequestType getTravellerFile() {
            return travellerFile;
        }

        /**
         * Imposta il valore della proprietà travellerFile.
         * 
         * @param value
         *     allowed object is
         *     {@link DataEntryOfflineRequestType }
         *     
         */
        public void setTravellerFile(DataEntryOfflineRequestType value) {
            this.travellerFile = value;
        }

    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="TravellerFile" type="{http://www.europa.eu/schengen/ees-ns/xsd/v1}DataEntryUpdateRequestType"/>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "travellerFile"
    })
    public static class Update {

        @XmlElement(name = "TravellerFile", required = true)
        protected DataEntryUpdateRequestType travellerFile;

        /**
         * Recupera il valore della proprietà travellerFile.
         * 
         * @return
         *     possible object is
         *     {@link DataEntryUpdateRequestType }
         *     
         */
        public DataEntryUpdateRequestType getTravellerFile() {
            return travellerFile;
        }

        /**
         * Imposta il valore della proprietà travellerFile.
         * 
         * @param value
         *     allowed object is
         *     {@link DataEntryUpdateRequestType }
         *     
         */
        public void setTravellerFile(DataEntryUpdateRequestType value) {
            this.travellerFile = value;
        }

    }

}
