//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.ees_ns.xsd.v1;

import eu.europa.schengen.ees.xsd.v1.BaseMessageResponseType;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlSeeAlso;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per MessageResponseType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="MessageResponseType">
 *   <complexContent>
 *     <extension base="{http://www.europa.eu/schengen/ees/xsd/v1}BaseMessageResponseType">
 *     </extension>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "MessageResponseType")
@XmlSeeAlso({
    SetMachineAttributeResponseMessageType.class,
    ReadMachineAttributeResponseMessageType.class,
    DataEntryResponseMessageType.class,
    DataAmendmentResponseMessageType.class,
    AdvanceDataDeletionResponseMessageType.class,
    RebuttalResponseMessageType.class,
    ChangeAuthorisationResponseMessageType.class,
    SearchByTravelDocumentResponseMessageType.class,
    SearchForTravelHistoryResponseMessageType.class,
    VerificationByFIInEESResponseMessageType.class,
    VerificationByFPInEESResponseMessageType.class,
    OverstayersReportResponseMessageType.class,
    CalculatorResponseMessageType.class,
    AttachmentResponseType.class,
    SurveyGetResponseType.class,
    SurveyInsertResponseType.class,
    SearchByPersonalDataInVISResponseMessageType.class,
    VerificationByFPInVISResponseMessageType.class,
    IdentificationInVISResponseMessageType.class,
    DataPreEnrolmentResponseMessageType.class,
    RetrievePreEnrolledDataResponseMessageType.class,
    SSSBiometricsComparisonResponseMessageType.class,
    UpdateAuthorityResponseMessageType.class,
    SearchAuthorityResponseMessageType.class,
    EndBorderControlResponseMessageType.class,
    AbortBorderControlResponseMessageType.class,
    IdentificationResultResponseMessageType.class,
    SearchOngoingBorderControlTransactionsResponseMessageType.class,
    StartBorderControlResponseMessageType.class,
    AddDataToBorderControlResponseMessageType.class,
    OverstayersScheduledDeletionNotificationMessageType.class,
    TravellerFileDeletionNotificationMessageType.class
})
public class MessageResponseType
    extends BaseMessageResponseType
{


}
