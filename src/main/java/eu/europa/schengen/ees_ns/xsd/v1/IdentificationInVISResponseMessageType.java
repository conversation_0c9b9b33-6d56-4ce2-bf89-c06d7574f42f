//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.ees_ns.xsd.v1;

import java.util.ArrayList;
import java.util.List;
import eu.europa.schengen.cs_vis_nui_ees.xsd.v1.VisaApplicationOverviewSearchResultVISType;
import eu.europa.schengen.cs_vis_nui_ees.xsd.v1.VisaApplicationSearchResultEESType;
import eu.europa.schengen.ees.xsd.v1.FPImageQualityType;
import eu.europa.schengen.ees.xsd.v1.PagingResponseType;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per IdentificationInVISResponseMessageType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="IdentificationInVISResponseMessageType">
 *   <complexContent>
 *     <extension base="{http://www.europa.eu/schengen/ees-ns/xsd/v1}MessageResponseType">
 *       <sequence>
 *         <element name="Response" minOccurs="0">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="Paging" type="{http://www.europa.eu/schengen/ees/xsd/v1}PagingResponseType"/>
 *                   <choice minOccurs="0">
 *                     <element name="VisaApplicationOverview" maxOccurs="unbounded">
 *                       <complexType>
 *                         <complexContent>
 *                           <extension base="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}VisaApplicationOverviewSearchResultVISType">
 *                             <sequence>
 *                               <group ref="{http://www.europa.eu/schengen/ees/xsd/v1}RankScoreGroup"/>
 *                               <element name="DossierID" type="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}DossierIDType"/>
 *                             </sequence>
 *                           </extension>
 *                         </complexContent>
 *                       </complexType>
 *                     </element>
 *                     <element name="VisaApplication" maxOccurs="unbounded">
 *                       <complexType>
 *                         <complexContent>
 *                           <extension base="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}VisaApplicationSearchResultEESType">
 *                             <sequence>
 *                               <group ref="{http://www.europa.eu/schengen/ees/xsd/v1}RankScoreGroup"/>
 *                               <element name="DossierID" type="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}DossierIDType"/>
 *                             </sequence>
 *                           </extension>
 *                         </complexContent>
 *                       </complexType>
 *                     </element>
 *                   </choice>
 *                   <element name="BiometricsQuality" minOccurs="0">
 *                     <complexType>
 *                       <complexContent>
 *                         <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           <sequence>
 *                             <element name="FP">
 *                               <complexType>
 *                                 <complexContent>
 *                                   <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                     <group ref="{http://www.europa.eu/schengen/ees/xsd/v1}FPQualityGroup"/>
 *                                   </restriction>
 *                                 </complexContent>
 *                               </complexType>
 *                             </element>
 *                           </sequence>
 *                         </restriction>
 *                       </complexContent>
 *                     </complexType>
 *                   </element>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *       </sequence>
 *     </extension>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "IdentificationInVISResponseMessageType", propOrder = {
    "response"
})
public class IdentificationInVISResponseMessageType
    extends MessageResponseType
{

    @XmlElement(name = "Response")
    protected IdentificationInVISResponseMessageType.Response response;

    /**
     * Recupera il valore della proprietà response.
     * 
     * @return
     *     possible object is
     *     {@link IdentificationInVISResponseMessageType.Response }
     *     
     */
    public IdentificationInVISResponseMessageType.Response getResponse() {
        return response;
    }

    /**
     * Imposta il valore della proprietà response.
     * 
     * @param value
     *     allowed object is
     *     {@link IdentificationInVISResponseMessageType.Response }
     *     
     */
    public void setResponse(IdentificationInVISResponseMessageType.Response value) {
        this.response = value;
    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="Paging" type="{http://www.europa.eu/schengen/ees/xsd/v1}PagingResponseType"/>
     *         <choice minOccurs="0">
     *           <element name="VisaApplicationOverview" maxOccurs="unbounded">
     *             <complexType>
     *               <complexContent>
     *                 <extension base="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}VisaApplicationOverviewSearchResultVISType">
     *                   <sequence>
     *                     <group ref="{http://www.europa.eu/schengen/ees/xsd/v1}RankScoreGroup"/>
     *                     <element name="DossierID" type="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}DossierIDType"/>
     *                   </sequence>
     *                 </extension>
     *               </complexContent>
     *             </complexType>
     *           </element>
     *           <element name="VisaApplication" maxOccurs="unbounded">
     *             <complexType>
     *               <complexContent>
     *                 <extension base="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}VisaApplicationSearchResultEESType">
     *                   <sequence>
     *                     <group ref="{http://www.europa.eu/schengen/ees/xsd/v1}RankScoreGroup"/>
     *                     <element name="DossierID" type="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}DossierIDType"/>
     *                   </sequence>
     *                 </extension>
     *               </complexContent>
     *             </complexType>
     *           </element>
     *         </choice>
     *         <element name="BiometricsQuality" minOccurs="0">
     *           <complexType>
     *             <complexContent>
     *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 <sequence>
     *                   <element name="FP">
     *                     <complexType>
     *                       <complexContent>
     *                         <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                           <group ref="{http://www.europa.eu/schengen/ees/xsd/v1}FPQualityGroup"/>
     *                         </restriction>
     *                       </complexContent>
     *                     </complexType>
     *                   </element>
     *                 </sequence>
     *               </restriction>
     *             </complexContent>
     *           </complexType>
     *         </element>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "paging",
        "visaApplicationOverview",
        "visaApplication",
        "biometricsQuality"
    })
    public static class Response {

        @XmlElement(name = "Paging", required = true)
        protected PagingResponseType paging;
        @XmlElement(name = "VisaApplicationOverview")
        protected List<IdentificationInVISResponseMessageType.Response.VisaApplicationOverview> visaApplicationOverview;
        @XmlElement(name = "VisaApplication")
        protected List<IdentificationInVISResponseMessageType.Response.VisaApplication> visaApplication;
        @XmlElement(name = "BiometricsQuality")
        protected IdentificationInVISResponseMessageType.Response.BiometricsQuality biometricsQuality;

        /**
         * Recupera il valore della proprietà paging.
         * 
         * @return
         *     possible object is
         *     {@link PagingResponseType }
         *     
         */
        public PagingResponseType getPaging() {
            return paging;
        }

        /**
         * Imposta il valore della proprietà paging.
         * 
         * @param value
         *     allowed object is
         *     {@link PagingResponseType }
         *     
         */
        public void setPaging(PagingResponseType value) {
            this.paging = value;
        }

        /**
         * Gets the value of the visaApplicationOverview property.
         * 
         * <p>This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the visaApplicationOverview property.</p>
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * </p>
         * <pre>
         * getVisaApplicationOverview().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link IdentificationInVISResponseMessageType.Response.VisaApplicationOverview }
         * </p>
         * 
         * 
         * @return
         *     The value of the visaApplicationOverview property.
         */
        public List<IdentificationInVISResponseMessageType.Response.VisaApplicationOverview> getVisaApplicationOverview() {
            if (visaApplicationOverview == null) {
                visaApplicationOverview = new ArrayList<>();
            }
            return this.visaApplicationOverview;
        }

        /**
         * Gets the value of the visaApplication property.
         * 
         * <p>This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the visaApplication property.</p>
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * </p>
         * <pre>
         * getVisaApplication().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link IdentificationInVISResponseMessageType.Response.VisaApplication }
         * </p>
         * 
         * 
         * @return
         *     The value of the visaApplication property.
         */
        public List<IdentificationInVISResponseMessageType.Response.VisaApplication> getVisaApplication() {
            if (visaApplication == null) {
                visaApplication = new ArrayList<>();
            }
            return this.visaApplication;
        }

        /**
         * Recupera il valore della proprietà biometricsQuality.
         * 
         * @return
         *     possible object is
         *     {@link IdentificationInVISResponseMessageType.Response.BiometricsQuality }
         *     
         */
        public IdentificationInVISResponseMessageType.Response.BiometricsQuality getBiometricsQuality() {
            return biometricsQuality;
        }

        /**
         * Imposta il valore della proprietà biometricsQuality.
         * 
         * @param value
         *     allowed object is
         *     {@link IdentificationInVISResponseMessageType.Response.BiometricsQuality }
         *     
         */
        public void setBiometricsQuality(IdentificationInVISResponseMessageType.Response.BiometricsQuality value) {
            this.biometricsQuality = value;
        }


        /**
         * <p>Classe Java per anonymous complex type.</p>
         * 
         * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
         * 
         * <pre>{@code
         * <complexType>
         *   <complexContent>
         *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       <sequence>
         *         <element name="FP">
         *           <complexType>
         *             <complexContent>
         *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                 <group ref="{http://www.europa.eu/schengen/ees/xsd/v1}FPQualityGroup"/>
         *               </restriction>
         *             </complexContent>
         *           </complexType>
         *         </element>
         *       </sequence>
         *     </restriction>
         *   </complexContent>
         * </complexType>
         * }</pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "fp"
        })
        public static class BiometricsQuality {

            @XmlElement(name = "FP", required = true)
            protected IdentificationInVISResponseMessageType.Response.BiometricsQuality.FP fp;

            /**
             * Recupera il valore della proprietà fp.
             * 
             * @return
             *     possible object is
             *     {@link IdentificationInVISResponseMessageType.Response.BiometricsQuality.FP }
             *     
             */
            public IdentificationInVISResponseMessageType.Response.BiometricsQuality.FP getFP() {
                return fp;
            }

            /**
             * Imposta il valore della proprietà fp.
             * 
             * @param value
             *     allowed object is
             *     {@link IdentificationInVISResponseMessageType.Response.BiometricsQuality.FP }
             *     
             */
            public void setFP(IdentificationInVISResponseMessageType.Response.BiometricsQuality.FP value) {
                this.fp = value;
            }


            /**
             * <p>Classe Java per anonymous complex type.</p>
             * 
             * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
             * 
             * <pre>{@code
             * <complexType>
             *   <complexContent>
             *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *       <group ref="{http://www.europa.eu/schengen/ees/xsd/v1}FPQualityGroup"/>
             *     </restriction>
             *   </complexContent>
             * </complexType>
             * }</pre>
             * 
             * 
             */
            @XmlAccessorType(XmlAccessType.FIELD)
            @XmlType(name = "", propOrder = {
                "imageQuality"
            })
            public static class FP {

                /**
                 * A fingerprint image quality
                 * 
                 */
                @XmlElement(name = "ImageQuality", namespace = "http://www.europa.eu/schengen/ees/xsd/v1")
                protected List<FPImageQualityType> imageQuality;

                /**
                 * A fingerprint image quality
                 * 
                 * Gets the value of the imageQuality property.
                 * 
                 * <p>This accessor method returns a reference to the live list,
                 * not a snapshot. Therefore any modification you make to the
                 * returned list will be present inside the JAXB object.
                 * This is why there is not a <CODE>set</CODE> method for the imageQuality property.</p>
                 * 
                 * <p>
                 * For example, to add a new item, do as follows:
                 * </p>
                 * <pre>
                 * getImageQuality().add(newItem);
                 * </pre>
                 * 
                 * 
                 * <p>
                 * Objects of the following type(s) are allowed in the list
                 * {@link FPImageQualityType }
                 * </p>
                 * 
                 * 
                 * @return
                 *     The value of the imageQuality property.
                 */
                public List<FPImageQualityType> getImageQuality() {
                    if (imageQuality == null) {
                        imageQuality = new ArrayList<>();
                    }
                    return this.imageQuality;
                }

            }

        }


        /**
         * <p>Classe Java per anonymous complex type.</p>
         * 
         * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
         * 
         * <pre>{@code
         * <complexType>
         *   <complexContent>
         *     <extension base="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}VisaApplicationSearchResultEESType">
         *       <sequence>
         *         <group ref="{http://www.europa.eu/schengen/ees/xsd/v1}RankScoreGroup"/>
         *         <element name="DossierID" type="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}DossierIDType"/>
         *       </sequence>
         *     </extension>
         *   </complexContent>
         * </complexType>
         * }</pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "rank",
            "score",
            "dossierID"
        })
        public static class VisaApplication
            extends VisaApplicationSearchResultEESType
        {

            @XmlElement(name = "Rank", namespace = "http://www.europa.eu/schengen/ees/xsd/v1")
            @XmlSchemaType(name = "unsignedInt")
            protected long rank;
            /**
             * Normalised matched score between 0 and 100 for alphanumeric searches. 100 is the highest score for alphanumeric searches.
             * 						Matching score between 0 and 100000 for biometric searches. 100000 is the highest score for biometric searches.
             * 
             */
            @XmlElement(name = "Score", namespace = "http://www.europa.eu/schengen/ees/xsd/v1")
            @XmlSchemaType(name = "unsignedInt")
            protected long score;
            /**
             * Description: ID of the Dossier.
             * 
             */
            @XmlElement(name = "DossierID")
            protected long dossierID;

            /**
             * Recupera il valore della proprietà rank.
             * 
             */
            public long getRank() {
                return rank;
            }

            /**
             * Imposta il valore della proprietà rank.
             * 
             */
            public void setRank(long value) {
                this.rank = value;
            }

            /**
             * Normalised matched score between 0 and 100 for alphanumeric searches. 100 is the highest score for alphanumeric searches.
             * 						Matching score between 0 and 100000 for biometric searches. 100000 is the highest score for biometric searches.
             * 
             */
            public long getScore() {
                return score;
            }

            /**
             * Imposta il valore della proprietà score.
             * 
             */
            public void setScore(long value) {
                this.score = value;
            }

            /**
             * Description: ID of the Dossier.
             * 
             */
            public long getDossierID() {
                return dossierID;
            }

            /**
             * Imposta il valore della proprietà dossierID.
             * 
             */
            public void setDossierID(long value) {
                this.dossierID = value;
            }

        }


        /**
         * <p>Classe Java per anonymous complex type.</p>
         * 
         * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
         * 
         * <pre>{@code
         * <complexType>
         *   <complexContent>
         *     <extension base="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}VisaApplicationOverviewSearchResultVISType">
         *       <sequence>
         *         <group ref="{http://www.europa.eu/schengen/ees/xsd/v1}RankScoreGroup"/>
         *         <element name="DossierID" type="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}DossierIDType"/>
         *       </sequence>
         *     </extension>
         *   </complexContent>
         * </complexType>
         * }</pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "rank",
            "score",
            "dossierID"
        })
        public static class VisaApplicationOverview
            extends VisaApplicationOverviewSearchResultVISType
        {

            @XmlElement(name = "Rank", namespace = "http://www.europa.eu/schengen/ees/xsd/v1")
            @XmlSchemaType(name = "unsignedInt")
            protected long rank;
            /**
             * Normalised matched score between 0 and 100 for alphanumeric searches. 100 is the highest score for alphanumeric searches.
             * 						Matching score between 0 and 100000 for biometric searches. 100000 is the highest score for biometric searches.
             * 
             */
            @XmlElement(name = "Score", namespace = "http://www.europa.eu/schengen/ees/xsd/v1")
            @XmlSchemaType(name = "unsignedInt")
            protected long score;
            /**
             * Description: ID of the Dossier.
             * 
             */
            @XmlElement(name = "DossierID")
            protected long dossierID;

            /**
             * Recupera il valore della proprietà rank.
             * 
             */
            public long getRank() {
                return rank;
            }

            /**
             * Imposta il valore della proprietà rank.
             * 
             */
            public void setRank(long value) {
                this.rank = value;
            }

            /**
             * Normalised matched score between 0 and 100 for alphanumeric searches. 100 is the highest score for alphanumeric searches.
             * 						Matching score between 0 and 100000 for biometric searches. 100000 is the highest score for biometric searches.
             * 
             */
            public long getScore() {
                return score;
            }

            /**
             * Imposta il valore della proprietà score.
             * 
             */
            public void setScore(long value) {
                this.score = value;
            }

            /**
             * Description: ID of the Dossier.
             * 
             */
            public long getDossierID() {
                return dossierID;
            }

            /**
             * Imposta il valore della proprietà dossierID.
             * 
             */
            public void setDossierID(long value) {
                this.dossierID = value;
            }

        }

    }

}
