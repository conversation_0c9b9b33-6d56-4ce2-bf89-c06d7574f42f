//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.ees_ns.xsd.v1;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per AdvanceDataDeletionRequestMessageType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="AdvanceDataDeletionRequestMessageType">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="Delete">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="TravellerFile">
 *                     <complexType>
 *                       <complexContent>
 *                         <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           <sequence>
 *                             <element name="TravellerFileID" type="{http://www.europa.eu/schengen/ees/xsd/v1}IdentifierType"/>
 *                           </sequence>
 *                         </restriction>
 *                       </complexContent>
 *                     </complexType>
 *                   </element>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "AdvanceDataDeletionRequestMessageType", propOrder = {
    "delete"
})
public class AdvanceDataDeletionRequestMessageType {

    @XmlElement(name = "Delete", required = true)
    protected AdvanceDataDeletionRequestMessageType.Delete delete;

    /**
     * Recupera il valore della proprietà delete.
     * 
     * @return
     *     possible object is
     *     {@link AdvanceDataDeletionRequestMessageType.Delete }
     *     
     */
    public AdvanceDataDeletionRequestMessageType.Delete getDelete() {
        return delete;
    }

    /**
     * Imposta il valore della proprietà delete.
     * 
     * @param value
     *     allowed object is
     *     {@link AdvanceDataDeletionRequestMessageType.Delete }
     *     
     */
    public void setDelete(AdvanceDataDeletionRequestMessageType.Delete value) {
        this.delete = value;
    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="TravellerFile">
     *           <complexType>
     *             <complexContent>
     *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 <sequence>
     *                   <element name="TravellerFileID" type="{http://www.europa.eu/schengen/ees/xsd/v1}IdentifierType"/>
     *                 </sequence>
     *               </restriction>
     *             </complexContent>
     *           </complexType>
     *         </element>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "travellerFile"
    })
    public static class Delete {

        @XmlElement(name = "TravellerFile", required = true)
        protected AdvanceDataDeletionRequestMessageType.Delete.TravellerFile travellerFile;

        /**
         * Recupera il valore della proprietà travellerFile.
         * 
         * @return
         *     possible object is
         *     {@link AdvanceDataDeletionRequestMessageType.Delete.TravellerFile }
         *     
         */
        public AdvanceDataDeletionRequestMessageType.Delete.TravellerFile getTravellerFile() {
            return travellerFile;
        }

        /**
         * Imposta il valore della proprietà travellerFile.
         * 
         * @param value
         *     allowed object is
         *     {@link AdvanceDataDeletionRequestMessageType.Delete.TravellerFile }
         *     
         */
        public void setTravellerFile(AdvanceDataDeletionRequestMessageType.Delete.TravellerFile value) {
            this.travellerFile = value;
        }


        /**
         * <p>Classe Java per anonymous complex type.</p>
         * 
         * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
         * 
         * <pre>{@code
         * <complexType>
         *   <complexContent>
         *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       <sequence>
         *         <element name="TravellerFileID" type="{http://www.europa.eu/schengen/ees/xsd/v1}IdentifierType"/>
         *       </sequence>
         *     </restriction>
         *   </complexContent>
         * </complexType>
         * }</pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "travellerFileID"
        })
        public static class TravellerFile {

            @XmlElement(name = "TravellerFileID", required = true)
            @XmlSchemaType(name = "anyURI")
            protected String travellerFileID;

            /**
             * Recupera il valore della proprietà travellerFileID.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getTravellerFileID() {
                return travellerFileID;
            }

            /**
             * Imposta il valore della proprietà travellerFileID.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setTravellerFileID(String value) {
                this.travellerFileID = value;
            }

        }

    }

}
