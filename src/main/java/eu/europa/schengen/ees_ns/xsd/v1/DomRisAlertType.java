//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.ees_ns.xsd.v1;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per domRisAlertType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="domRisAlertType">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="domRisAlert">
 *           <simpleType>
 *             <restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               <enumeration value="OK"/>
 *               <enumeration value="WARNING"/>
 *               <enumeration value="ALERT"/>
 *             </restriction>
 *           </simpleType>
 *         </element>
 *         <element name="codiceDomanda" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         <element name="domanda" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         <element name="risposta" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "domRisAlertType", propOrder = {
    "domRisAlert",
    "codiceDomanda",
    "domanda",
    "risposta"
})
public class DomRisAlertType {

    @XmlElement(required = true)
    protected String domRisAlert;
    @XmlElement(required = true)
    protected String codiceDomanda;
    @XmlElement(required = true)
    protected String domanda;
    @XmlElement(required = true)
    protected String risposta;

    /**
     * Recupera il valore della proprietà domRisAlert.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDomRisAlert() {
        return domRisAlert;
    }

    /**
     * Imposta il valore della proprietà domRisAlert.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDomRisAlert(String value) {
        this.domRisAlert = value;
    }

    /**
     * Recupera il valore della proprietà codiceDomanda.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCodiceDomanda() {
        return codiceDomanda;
    }

    /**
     * Imposta il valore della proprietà codiceDomanda.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCodiceDomanda(String value) {
        this.codiceDomanda = value;
    }

    /**
     * Recupera il valore della proprietà domanda.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDomanda() {
        return domanda;
    }

    /**
     * Imposta il valore della proprietà domanda.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDomanda(String value) {
        this.domanda = value;
    }

    /**
     * Recupera il valore della proprietà risposta.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRisposta() {
        return risposta;
    }

    /**
     * Imposta il valore della proprietà risposta.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRisposta(String value) {
        this.risposta = value;
    }

}
