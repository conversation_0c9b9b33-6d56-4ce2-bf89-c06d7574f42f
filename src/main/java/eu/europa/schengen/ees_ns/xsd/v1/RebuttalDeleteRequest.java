//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.ees_ns.xsd.v1;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per RebuttalDeleteRequest complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="RebuttalDeleteRequest">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="TravellerFile">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="TravellerFileID" type="{http://www.europa.eu/schengen/ees/xsd/v1}IdentifierType"/>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "RebuttalDeleteRequest", propOrder = {
    "travellerFile"
})
public class RebuttalDeleteRequest {

    @XmlElement(name = "TravellerFile", required = true)
    protected RebuttalDeleteRequest.TravellerFile travellerFile;

    /**
     * Recupera il valore della proprietà travellerFile.
     * 
     * @return
     *     possible object is
     *     {@link RebuttalDeleteRequest.TravellerFile }
     *     
     */
    public RebuttalDeleteRequest.TravellerFile getTravellerFile() {
        return travellerFile;
    }

    /**
     * Imposta il valore della proprietà travellerFile.
     * 
     * @param value
     *     allowed object is
     *     {@link RebuttalDeleteRequest.TravellerFile }
     *     
     */
    public void setTravellerFile(RebuttalDeleteRequest.TravellerFile value) {
        this.travellerFile = value;
    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="TravellerFileID" type="{http://www.europa.eu/schengen/ees/xsd/v1}IdentifierType"/>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "travellerFileID"
    })
    public static class TravellerFile {

        @XmlElement(name = "TravellerFileID", required = true)
        @XmlSchemaType(name = "anyURI")
        protected String travellerFileID;

        /**
         * Recupera il valore della proprietà travellerFileID.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getTravellerFileID() {
            return travellerFileID;
        }

        /**
         * Imposta il valore della proprietà travellerFileID.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setTravellerFileID(String value) {
            this.travellerFileID = value;
        }

    }

}
