//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.ees_ns.xsd.v1;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per AbortBorderControlResponseMessageType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="AbortBorderControlResponseMessageType">
 *   <complexContent>
 *     <extension base="{http://www.europa.eu/schengen/ees-ns/xsd/v1}MessageResponseType">
 *       <sequence>
 *         <element name="Response" type="{http://www.europa.eu/schengen/ees-ns/xsd/v1}WFEResponseType" minOccurs="0"/>
 *       </sequence>
 *     </extension>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "AbortBorderControlResponseMessageType", propOrder = {
    "response"
})
public class AbortBorderControlResponseMessageType
    extends MessageResponseType
{

    @XmlElement(name = "Response")
    protected WFEResponseType response;

    /**
     * Recupera il valore della proprietà response.
     * 
     * @return
     *     possible object is
     *     {@link WFEResponseType }
     *     
     */
    public WFEResponseType getResponse() {
        return response;
    }

    /**
     * Imposta il valore della proprietà response.
     * 
     * @param value
     *     allowed object is
     *     {@link WFEResponseType }
     *     
     */
    public void setResponse(WFEResponseType value) {
        this.response = value;
    }

}
