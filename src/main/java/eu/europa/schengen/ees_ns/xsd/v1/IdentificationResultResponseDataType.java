//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.ees_ns.xsd.v1;

import java.util.ArrayList;
import java.util.List;
import eu.europa.schengen.cs_vis_nui_ees.xsd.v1.VisaApplicationOverviewSearchResultVISType;
import eu.europa.schengen.cs_vis_nui_ees.xsd.v1.VisaApplicationSearchResultEESType;
import eu.europa.schengen.ees.xsd.v1.TravellerFileSearchResponse2Type;
import eu.europa.schengen.ees.xsd.v1.TravellerFileSearchResponseType;
import eu.europa.schengen.ees.xsd.v1.VisaInformationType;
import eu.europa.schengen.etias.xsd.v1.TravelAuthorisationResponseType;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per IdentificationResultResponseDataType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="IdentificationResultResponseDataType">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="ComparisonResults" minOccurs="0">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="FI" minOccurs="0">
 *                     <complexType>
 *                       <complexContent>
 *                         <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           <sequence>
 *                             <group ref="{http://www.europa.eu/schengen/ees/xsd/v1}ResultScoreGroup"/>
 *                           </sequence>
 *                         </restriction>
 *                       </complexContent>
 *                     </complexType>
 *                   </element>
 *                   <element name="FP" minOccurs="0">
 *                     <complexType>
 *                       <complexContent>
 *                         <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           <sequence>
 *                             <group ref="{http://www.europa.eu/schengen/ees/xsd/v1}ResultScoreGroup"/>
 *                           </sequence>
 *                         </restriction>
 *                       </complexContent>
 *                     </complexType>
 *                   </element>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *         <element name="EESSearchAndVerification" minOccurs="0">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="TravellerFile" maxOccurs="unbounded" minOccurs="0">
 *                     <complexType>
 *                       <complexContent>
 *                         <extension base="{http://www.europa.eu/schengen/ees/xsd/v1}TravellerFileSearchResponseType">
 *                           <sequence>
 *                             <element name="FIVerification" minOccurs="0">
 *                               <complexType>
 *                                 <complexContent>
 *                                   <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                     <sequence>
 *                                       <group ref="{http://www.europa.eu/schengen/ees/xsd/v1}ResultScoreGroup"/>
 *                                     </sequence>
 *                                   </restriction>
 *                                 </complexContent>
 *                               </complexType>
 *                             </element>
 *                             <element name="FPVerification" minOccurs="0">
 *                               <complexType>
 *                                 <complexContent>
 *                                   <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                     <sequence>
 *                                       <group ref="{http://www.europa.eu/schengen/ees/xsd/v1}ResultScoreGroup"/>
 *                                     </sequence>
 *                                   </restriction>
 *                                 </complexContent>
 *                               </complexType>
 *                             </element>
 *                             <element name="SelectedMatch" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
 *                           </sequence>
 *                         </extension>
 *                       </complexContent>
 *                     </complexType>
 *                   </element>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *         <element name="EESIdentification" minOccurs="0">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="TravellerFile" maxOccurs="unbounded" minOccurs="0">
 *                     <complexType>
 *                       <complexContent>
 *                         <extension base="{http://www.europa.eu/schengen/ees/xsd/v1}TravellerFileSearchResponse2Type">
 *                           <sequence>
 *                             <choice>
 *                               <element name="FPIdentification" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *                               <element name="FIFPIdentification" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *                             </choice>
 *                             <element name="SelectedMatch" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
 *                           </sequence>
 *                         </extension>
 *                       </complexContent>
 *                     </complexType>
 *                   </element>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *         <element name="EESAlphanumericIdentification" minOccurs="0">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="TravellerFile">
 *                     <complexType>
 *                       <complexContent>
 *                         <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           <sequence>
 *                             <element name="TravellerFileID" type="{http://www.europa.eu/schengen/ees/xsd/v1}IdentifierType" minOccurs="0"/>
 *                           </sequence>
 *                         </restriction>
 *                       </complexContent>
 *                     </complexType>
 *                   </element>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *         <element name="VISSearchAndVerification" minOccurs="0">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <choice>
 *                   <element name="VisaApplicationOverview" maxOccurs="unbounded" minOccurs="0">
 *                     <complexType>
 *                       <complexContent>
 *                         <extension base="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}VisaApplicationOverviewSearchResultVISType">
 *                           <sequence>
 *                             <group ref="{http://www.europa.eu/schengen/ees/xsd/v1}RankScoreGroup"/>
 *                             <element name="DossierID" type="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}DossierIDType"/>
 *                             <element name="FPVerification" minOccurs="0">
 *                               <complexType>
 *                                 <complexContent>
 *                                   <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                     <sequence>
 *                                       <group ref="{http://www.europa.eu/schengen/ees/xsd/v1}VISResultScoreGroup"/>
 *                                     </sequence>
 *                                   </restriction>
 *                                 </complexContent>
 *                               </complexType>
 *                             </element>
 *                             <element name="SelectedMatch" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
 *                           </sequence>
 *                         </extension>
 *                       </complexContent>
 *                     </complexType>
 *                   </element>
 *                   <element name="VisaApplication" maxOccurs="unbounded" minOccurs="0">
 *                     <complexType>
 *                       <complexContent>
 *                         <extension base="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}VisaApplicationSearchResultEESType">
 *                           <sequence>
 *                             <group ref="{http://www.europa.eu/schengen/ees/xsd/v1}RankScoreGroup"/>
 *                             <element name="DossierID" type="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}DossierIDType"/>
 *                             <element name="FPVerification" minOccurs="0">
 *                               <complexType>
 *                                 <complexContent>
 *                                   <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                     <sequence>
 *                                       <group ref="{http://www.europa.eu/schengen/ees/xsd/v1}VISResultScoreGroup"/>
 *                                     </sequence>
 *                                   </restriction>
 *                                 </complexContent>
 *                               </complexType>
 *                             </element>
 *                             <element name="SelectedMatch" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
 *                           </sequence>
 *                         </extension>
 *                       </complexContent>
 *                     </complexType>
 *                   </element>
 *                   <element name="VisaApplicationNumber" maxOccurs="unbounded" minOccurs="0">
 *                     <complexType>
 *                       <complexContent>
 *                         <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           <sequence>
 *                             <element name="ApplicationNumber" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}ApplicationNumberType"/>
 *                             <element name="DossierID" type="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}DossierIDType"/>
 *                             <element name="SelectedMatch" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
 *                           </sequence>
 *                         </restriction>
 *                       </complexContent>
 *                     </complexType>
 *                   </element>
 *                 </choice>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *         <element name="VISIdentification" minOccurs="0">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <choice>
 *                   <element name="VisaApplicationOverview" maxOccurs="unbounded" minOccurs="0">
 *                     <complexType>
 *                       <complexContent>
 *                         <extension base="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}VisaApplicationOverviewSearchResultVISType">
 *                           <sequence>
 *                             <group ref="{http://www.europa.eu/schengen/ees/xsd/v1}RankScoreGroup"/>
 *                             <element name="DossierID" type="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}DossierIDType"/>
 *                             <element name="FPIdentification" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *                             <element name="SelectedMatch" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
 *                           </sequence>
 *                         </extension>
 *                       </complexContent>
 *                     </complexType>
 *                   </element>
 *                   <element name="VisaApplication" maxOccurs="unbounded" minOccurs="0">
 *                     <complexType>
 *                       <complexContent>
 *                         <extension base="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}VisaApplicationSearchResultEESType">
 *                           <sequence>
 *                             <group ref="{http://www.europa.eu/schengen/ees/xsd/v1}RankScoreGroup"/>
 *                             <element name="DossierID" type="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}DossierIDType"/>
 *                             <element name="FPIdentification" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *                             <element name="SelectedMatch" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
 *                           </sequence>
 *                         </extension>
 *                       </complexContent>
 *                     </complexType>
 *                   </element>
 *                 </choice>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *         <element name="VISAlphanumericIdentification" minOccurs="0">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="VisaInformation" type="{http://www.europa.eu/schengen/ees/xsd/v1}VisaInformationType"/>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *         <element name="VISDirectIdentification" minOccurs="0">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="VisaInformation" type="{http://www.europa.eu/schengen/ees/xsd/v1}VisaInformationType"/>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *         <element name="ETIASSearch" minOccurs="0">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="TravelAuthorisation" maxOccurs="unbounded" minOccurs="0">
 *                     <complexType>
 *                       <complexContent>
 *                         <extension base="{http://www.europa.eu/schengen/etias/xsd/v1}TravelAuthorisationResponseType">
 *                           <sequence>
 *                             <element name="SelectedMatch" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *                           </sequence>
 *                         </extension>
 *                       </complexContent>
 *                     </complexType>
 *                   </element>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "IdentificationResultResponseDataType", propOrder = {
    "comparisonResults",
    "eesSearchAndVerification",
    "eesIdentification",
    "eesAlphanumericIdentification",
    "visSearchAndVerification",
    "visIdentification",
    "visAlphanumericIdentification",
    "visDirectIdentification",
    "etiasSearch"
})
public class IdentificationResultResponseDataType {

    @XmlElement(name = "ComparisonResults", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1")
    protected IdentificationResultResponseDataType.ComparisonResults comparisonResults;
    @XmlElement(name = "EESSearchAndVerification", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1")
    protected IdentificationResultResponseDataType.EESSearchAndVerification eesSearchAndVerification;
    @XmlElement(name = "EESIdentification", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1")
    protected IdentificationResultResponseDataType.EESIdentification eesIdentification;
    @XmlElement(name = "EESAlphanumericIdentification", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1")
    protected IdentificationResultResponseDataType.EESAlphanumericIdentification eesAlphanumericIdentification;
    @XmlElement(name = "VISSearchAndVerification", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1")
    protected IdentificationResultResponseDataType.VISSearchAndVerification visSearchAndVerification;
    @XmlElement(name = "VISIdentification", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1")
    protected IdentificationResultResponseDataType.VISIdentification visIdentification;
    @XmlElement(name = "VISAlphanumericIdentification", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1")
    protected IdentificationResultResponseDataType.VISAlphanumericIdentification visAlphanumericIdentification;
    @XmlElement(name = "VISDirectIdentification", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1")
    protected IdentificationResultResponseDataType.VISDirectIdentification visDirectIdentification;
    @XmlElement(name = "ETIASSearch", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1")
    protected IdentificationResultResponseDataType.ETIASSearch etiasSearch;

    /**
     * Recupera il valore della proprietà comparisonResults.
     * 
     * @return
     *     possible object is
     *     {@link IdentificationResultResponseDataType.ComparisonResults }
     *     
     */
    public IdentificationResultResponseDataType.ComparisonResults getComparisonResults() {
        return comparisonResults;
    }

    /**
     * Imposta il valore della proprietà comparisonResults.
     * 
     * @param value
     *     allowed object is
     *     {@link IdentificationResultResponseDataType.ComparisonResults }
     *     
     */
    public void setComparisonResults(IdentificationResultResponseDataType.ComparisonResults value) {
        this.comparisonResults = value;
    }

    /**
     * Recupera il valore della proprietà eesSearchAndVerification.
     * 
     * @return
     *     possible object is
     *     {@link IdentificationResultResponseDataType.EESSearchAndVerification }
     *     
     */
    public IdentificationResultResponseDataType.EESSearchAndVerification getEESSearchAndVerification() {
        return eesSearchAndVerification;
    }

    /**
     * Imposta il valore della proprietà eesSearchAndVerification.
     * 
     * @param value
     *     allowed object is
     *     {@link IdentificationResultResponseDataType.EESSearchAndVerification }
     *     
     */
    public void setEESSearchAndVerification(IdentificationResultResponseDataType.EESSearchAndVerification value) {
        this.eesSearchAndVerification = value;
    }

    /**
     * Recupera il valore della proprietà eesIdentification.
     * 
     * @return
     *     possible object is
     *     {@link IdentificationResultResponseDataType.EESIdentification }
     *     
     */
    public IdentificationResultResponseDataType.EESIdentification getEESIdentification() {
        return eesIdentification;
    }

    /**
     * Imposta il valore della proprietà eesIdentification.
     * 
     * @param value
     *     allowed object is
     *     {@link IdentificationResultResponseDataType.EESIdentification }
     *     
     */
    public void setEESIdentification(IdentificationResultResponseDataType.EESIdentification value) {
        this.eesIdentification = value;
    }

    /**
     * Recupera il valore della proprietà eesAlphanumericIdentification.
     * 
     * @return
     *     possible object is
     *     {@link IdentificationResultResponseDataType.EESAlphanumericIdentification }
     *     
     */
    public IdentificationResultResponseDataType.EESAlphanumericIdentification getEESAlphanumericIdentification() {
        return eesAlphanumericIdentification;
    }

    /**
     * Imposta il valore della proprietà eesAlphanumericIdentification.
     * 
     * @param value
     *     allowed object is
     *     {@link IdentificationResultResponseDataType.EESAlphanumericIdentification }
     *     
     */
    public void setEESAlphanumericIdentification(IdentificationResultResponseDataType.EESAlphanumericIdentification value) {
        this.eesAlphanumericIdentification = value;
    }

    /**
     * Recupera il valore della proprietà visSearchAndVerification.
     * 
     * @return
     *     possible object is
     *     {@link IdentificationResultResponseDataType.VISSearchAndVerification }
     *     
     */
    public IdentificationResultResponseDataType.VISSearchAndVerification getVISSearchAndVerification() {
        return visSearchAndVerification;
    }

    /**
     * Imposta il valore della proprietà visSearchAndVerification.
     * 
     * @param value
     *     allowed object is
     *     {@link IdentificationResultResponseDataType.VISSearchAndVerification }
     *     
     */
    public void setVISSearchAndVerification(IdentificationResultResponseDataType.VISSearchAndVerification value) {
        this.visSearchAndVerification = value;
    }

    /**
     * Recupera il valore della proprietà visIdentification.
     * 
     * @return
     *     possible object is
     *     {@link IdentificationResultResponseDataType.VISIdentification }
     *     
     */
    public IdentificationResultResponseDataType.VISIdentification getVISIdentification() {
        return visIdentification;
    }

    /**
     * Imposta il valore della proprietà visIdentification.
     * 
     * @param value
     *     allowed object is
     *     {@link IdentificationResultResponseDataType.VISIdentification }
     *     
     */
    public void setVISIdentification(IdentificationResultResponseDataType.VISIdentification value) {
        this.visIdentification = value;
    }

    /**
     * Recupera il valore della proprietà visAlphanumericIdentification.
     * 
     * @return
     *     possible object is
     *     {@link IdentificationResultResponseDataType.VISAlphanumericIdentification }
     *     
     */
    public IdentificationResultResponseDataType.VISAlphanumericIdentification getVISAlphanumericIdentification() {
        return visAlphanumericIdentification;
    }

    /**
     * Imposta il valore della proprietà visAlphanumericIdentification.
     * 
     * @param value
     *     allowed object is
     *     {@link IdentificationResultResponseDataType.VISAlphanumericIdentification }
     *     
     */
    public void setVISAlphanumericIdentification(IdentificationResultResponseDataType.VISAlphanumericIdentification value) {
        this.visAlphanumericIdentification = value;
    }

    /**
     * Recupera il valore della proprietà visDirectIdentification.
     * 
     * @return
     *     possible object is
     *     {@link IdentificationResultResponseDataType.VISDirectIdentification }
     *     
     */
    public IdentificationResultResponseDataType.VISDirectIdentification getVISDirectIdentification() {
        return visDirectIdentification;
    }

    /**
     * Imposta il valore della proprietà visDirectIdentification.
     * 
     * @param value
     *     allowed object is
     *     {@link IdentificationResultResponseDataType.VISDirectIdentification }
     *     
     */
    public void setVISDirectIdentification(IdentificationResultResponseDataType.VISDirectIdentification value) {
        this.visDirectIdentification = value;
    }

    /**
     * Recupera il valore della proprietà etiasSearch.
     * 
     * @return
     *     possible object is
     *     {@link IdentificationResultResponseDataType.ETIASSearch }
     *     
     */
    public IdentificationResultResponseDataType.ETIASSearch getETIASSearch() {
        return etiasSearch;
    }

    /**
     * Imposta il valore della proprietà etiasSearch.
     * 
     * @param value
     *     allowed object is
     *     {@link IdentificationResultResponseDataType.ETIASSearch }
     *     
     */
    public void setETIASSearch(IdentificationResultResponseDataType.ETIASSearch value) {
        this.etiasSearch = value;
    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="FI" minOccurs="0">
     *           <complexType>
     *             <complexContent>
     *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 <sequence>
     *                   <group ref="{http://www.europa.eu/schengen/ees/xsd/v1}ResultScoreGroup"/>
     *                 </sequence>
     *               </restriction>
     *             </complexContent>
     *           </complexType>
     *         </element>
     *         <element name="FP" minOccurs="0">
     *           <complexType>
     *             <complexContent>
     *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 <sequence>
     *                   <group ref="{http://www.europa.eu/schengen/ees/xsd/v1}ResultScoreGroup"/>
     *                 </sequence>
     *               </restriction>
     *             </complexContent>
     *           </complexType>
     *         </element>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "fi",
        "fp"
    })
    public static class ComparisonResults {

        @XmlElement(name = "FI", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1")
        protected IdentificationResultResponseDataType.ComparisonResults.FI fi;
        @XmlElement(name = "FP", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1")
        protected IdentificationResultResponseDataType.ComparisonResults.FP fp;

        /**
         * Recupera il valore della proprietà fi.
         * 
         * @return
         *     possible object is
         *     {@link IdentificationResultResponseDataType.ComparisonResults.FI }
         *     
         */
        public IdentificationResultResponseDataType.ComparisonResults.FI getFI() {
            return fi;
        }

        /**
         * Imposta il valore della proprietà fi.
         * 
         * @param value
         *     allowed object is
         *     {@link IdentificationResultResponseDataType.ComparisonResults.FI }
         *     
         */
        public void setFI(IdentificationResultResponseDataType.ComparisonResults.FI value) {
            this.fi = value;
        }

        /**
         * Recupera il valore della proprietà fp.
         * 
         * @return
         *     possible object is
         *     {@link IdentificationResultResponseDataType.ComparisonResults.FP }
         *     
         */
        public IdentificationResultResponseDataType.ComparisonResults.FP getFP() {
            return fp;
        }

        /**
         * Imposta il valore della proprietà fp.
         * 
         * @param value
         *     allowed object is
         *     {@link IdentificationResultResponseDataType.ComparisonResults.FP }
         *     
         */
        public void setFP(IdentificationResultResponseDataType.ComparisonResults.FP value) {
            this.fp = value;
        }


        /**
         * <p>Classe Java per anonymous complex type.</p>
         * 
         * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
         * 
         * <pre>{@code
         * <complexType>
         *   <complexContent>
         *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       <sequence>
         *         <group ref="{http://www.europa.eu/schengen/ees/xsd/v1}ResultScoreGroup"/>
         *       </sequence>
         *     </restriction>
         *   </complexContent>
         * </complexType>
         * }</pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "result",
            "score"
        })
        public static class FI {

            @XmlElement(name = "Result", namespace = "http://www.europa.eu/schengen/ees/xsd/v1")
            protected boolean result;
            /**
             * Normalised matched score between 0 and 100 for alphanumeric searches. 100 is the highest score for alphanumeric searches.
             * 						Matching score between 0 and 100000 for biometric searches. 100000 is the highest score for biometric searches.
             * 
             */
            @XmlElement(name = "Score", namespace = "http://www.europa.eu/schengen/ees/xsd/v1")
            @XmlSchemaType(name = "unsignedInt")
            protected long score;

            /**
             * Recupera il valore della proprietà result.
             * 
             */
            public boolean isResult() {
                return result;
            }

            /**
             * Imposta il valore della proprietà result.
             * 
             */
            public void setResult(boolean value) {
                this.result = value;
            }

            /**
             * Normalised matched score between 0 and 100 for alphanumeric searches. 100 is the highest score for alphanumeric searches.
             * 						Matching score between 0 and 100000 for biometric searches. 100000 is the highest score for biometric searches.
             * 
             */
            public long getScore() {
                return score;
            }

            /**
             * Imposta il valore della proprietà score.
             * 
             */
            public void setScore(long value) {
                this.score = value;
            }

        }


        /**
         * <p>Classe Java per anonymous complex type.</p>
         * 
         * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
         * 
         * <pre>{@code
         * <complexType>
         *   <complexContent>
         *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       <sequence>
         *         <group ref="{http://www.europa.eu/schengen/ees/xsd/v1}ResultScoreGroup"/>
         *       </sequence>
         *     </restriction>
         *   </complexContent>
         * </complexType>
         * }</pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "result",
            "score"
        })
        public static class FP {

            @XmlElement(name = "Result", namespace = "http://www.europa.eu/schengen/ees/xsd/v1")
            protected boolean result;
            /**
             * Normalised matched score between 0 and 100 for alphanumeric searches. 100 is the highest score for alphanumeric searches.
             * 						Matching score between 0 and 100000 for biometric searches. 100000 is the highest score for biometric searches.
             * 
             */
            @XmlElement(name = "Score", namespace = "http://www.europa.eu/schengen/ees/xsd/v1")
            @XmlSchemaType(name = "unsignedInt")
            protected long score;

            /**
             * Recupera il valore della proprietà result.
             * 
             */
            public boolean isResult() {
                return result;
            }

            /**
             * Imposta il valore della proprietà result.
             * 
             */
            public void setResult(boolean value) {
                this.result = value;
            }

            /**
             * Normalised matched score between 0 and 100 for alphanumeric searches. 100 is the highest score for alphanumeric searches.
             * 						Matching score between 0 and 100000 for biometric searches. 100000 is the highest score for biometric searches.
             * 
             */
            public long getScore() {
                return score;
            }

            /**
             * Imposta il valore della proprietà score.
             * 
             */
            public void setScore(long value) {
                this.score = value;
            }

        }

    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="TravellerFile">
     *           <complexType>
     *             <complexContent>
     *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 <sequence>
     *                   <element name="TravellerFileID" type="{http://www.europa.eu/schengen/ees/xsd/v1}IdentifierType" minOccurs="0"/>
     *                 </sequence>
     *               </restriction>
     *             </complexContent>
     *           </complexType>
     *         </element>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "travellerFile"
    })
    public static class EESAlphanumericIdentification {

        @XmlElement(name = "TravellerFile", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1", required = true)
        protected IdentificationResultResponseDataType.EESAlphanumericIdentification.TravellerFile travellerFile;

        /**
         * Recupera il valore della proprietà travellerFile.
         * 
         * @return
         *     possible object is
         *     {@link IdentificationResultResponseDataType.EESAlphanumericIdentification.TravellerFile }
         *     
         */
        public IdentificationResultResponseDataType.EESAlphanumericIdentification.TravellerFile getTravellerFile() {
            return travellerFile;
        }

        /**
         * Imposta il valore della proprietà travellerFile.
         * 
         * @param value
         *     allowed object is
         *     {@link IdentificationResultResponseDataType.EESAlphanumericIdentification.TravellerFile }
         *     
         */
        public void setTravellerFile(IdentificationResultResponseDataType.EESAlphanumericIdentification.TravellerFile value) {
            this.travellerFile = value;
        }


        /**
         * <p>Classe Java per anonymous complex type.</p>
         * 
         * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
         * 
         * <pre>{@code
         * <complexType>
         *   <complexContent>
         *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       <sequence>
         *         <element name="TravellerFileID" type="{http://www.europa.eu/schengen/ees/xsd/v1}IdentifierType" minOccurs="0"/>
         *       </sequence>
         *     </restriction>
         *   </complexContent>
         * </complexType>
         * }</pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "travellerFileID"
        })
        public static class TravellerFile {

            @XmlElement(name = "TravellerFileID", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1")
            @XmlSchemaType(name = "anyURI")
            protected String travellerFileID;

            /**
             * Recupera il valore della proprietà travellerFileID.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getTravellerFileID() {
                return travellerFileID;
            }

            /**
             * Imposta il valore della proprietà travellerFileID.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setTravellerFileID(String value) {
                this.travellerFileID = value;
            }

        }

    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="TravellerFile" maxOccurs="unbounded" minOccurs="0">
     *           <complexType>
     *             <complexContent>
     *               <extension base="{http://www.europa.eu/schengen/ees/xsd/v1}TravellerFileSearchResponse2Type">
     *                 <sequence>
     *                   <choice>
     *                     <element name="FPIdentification" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
     *                     <element name="FIFPIdentification" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
     *                   </choice>
     *                   <element name="SelectedMatch" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
     *                 </sequence>
     *               </extension>
     *             </complexContent>
     *           </complexType>
     *         </element>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "travellerFile"
    })
    public static class EESIdentification {

        @XmlElement(name = "TravellerFile", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1")
        protected List<IdentificationResultResponseDataType.EESIdentification.TravellerFile> travellerFile;

        /**
         * Gets the value of the travellerFile property.
         * 
         * <p>This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the travellerFile property.</p>
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * </p>
         * <pre>
         * getTravellerFile().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link IdentificationResultResponseDataType.EESIdentification.TravellerFile }
         * </p>
         * 
         * 
         * @return
         *     The value of the travellerFile property.
         */
        public List<IdentificationResultResponseDataType.EESIdentification.TravellerFile> getTravellerFile() {
            if (travellerFile == null) {
                travellerFile = new ArrayList<>();
            }
            return this.travellerFile;
        }


        /**
         * <p>Classe Java per anonymous complex type.</p>
         * 
         * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
         * 
         * <pre>{@code
         * <complexType>
         *   <complexContent>
         *     <extension base="{http://www.europa.eu/schengen/ees/xsd/v1}TravellerFileSearchResponse2Type">
         *       <sequence>
         *         <choice>
         *           <element name="FPIdentification" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
         *           <element name="FIFPIdentification" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
         *         </choice>
         *         <element name="SelectedMatch" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
         *       </sequence>
         *     </extension>
         *   </complexContent>
         * </complexType>
         * }</pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "fpIdentification",
            "fifpIdentification",
            "selectedMatch"
        })
        public static class TravellerFile
            extends TravellerFileSearchResponse2Type
        {

            @XmlElement(name = "FPIdentification", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1")
            protected Boolean fpIdentification;
            @XmlElement(name = "FIFPIdentification", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1")
            protected Boolean fifpIdentification;
            @XmlElement(name = "SelectedMatch", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1")
            protected Boolean selectedMatch;

            /**
             * Recupera il valore della proprietà fpIdentification.
             * 
             * @return
             *     possible object is
             *     {@link Boolean }
             *     
             */
            public Boolean isFPIdentification() {
                return fpIdentification;
            }

            /**
             * Imposta il valore della proprietà fpIdentification.
             * 
             * @param value
             *     allowed object is
             *     {@link Boolean }
             *     
             */
            public void setFPIdentification(Boolean value) {
                this.fpIdentification = value;
            }

            /**
             * Recupera il valore della proprietà fifpIdentification.
             * 
             * @return
             *     possible object is
             *     {@link Boolean }
             *     
             */
            public Boolean isFIFPIdentification() {
                return fifpIdentification;
            }

            /**
             * Imposta il valore della proprietà fifpIdentification.
             * 
             * @param value
             *     allowed object is
             *     {@link Boolean }
             *     
             */
            public void setFIFPIdentification(Boolean value) {
                this.fifpIdentification = value;
            }

            /**
             * Recupera il valore della proprietà selectedMatch.
             * 
             * @return
             *     possible object is
             *     {@link Boolean }
             *     
             */
            public Boolean isSelectedMatch() {
                return selectedMatch;
            }

            /**
             * Imposta il valore della proprietà selectedMatch.
             * 
             * @param value
             *     allowed object is
             *     {@link Boolean }
             *     
             */
            public void setSelectedMatch(Boolean value) {
                this.selectedMatch = value;
            }

        }

    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="TravellerFile" maxOccurs="unbounded" minOccurs="0">
     *           <complexType>
     *             <complexContent>
     *               <extension base="{http://www.europa.eu/schengen/ees/xsd/v1}TravellerFileSearchResponseType">
     *                 <sequence>
     *                   <element name="FIVerification" minOccurs="0">
     *                     <complexType>
     *                       <complexContent>
     *                         <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                           <sequence>
     *                             <group ref="{http://www.europa.eu/schengen/ees/xsd/v1}ResultScoreGroup"/>
     *                           </sequence>
     *                         </restriction>
     *                       </complexContent>
     *                     </complexType>
     *                   </element>
     *                   <element name="FPVerification" minOccurs="0">
     *                     <complexType>
     *                       <complexContent>
     *                         <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                           <sequence>
     *                             <group ref="{http://www.europa.eu/schengen/ees/xsd/v1}ResultScoreGroup"/>
     *                           </sequence>
     *                         </restriction>
     *                       </complexContent>
     *                     </complexType>
     *                   </element>
     *                   <element name="SelectedMatch" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
     *                 </sequence>
     *               </extension>
     *             </complexContent>
     *           </complexType>
     *         </element>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "travellerFile"
    })
    public static class EESSearchAndVerification {

        @XmlElement(name = "TravellerFile", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1")
        protected List<IdentificationResultResponseDataType.EESSearchAndVerification.TravellerFile> travellerFile;

        /**
         * Gets the value of the travellerFile property.
         * 
         * <p>This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the travellerFile property.</p>
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * </p>
         * <pre>
         * getTravellerFile().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link IdentificationResultResponseDataType.EESSearchAndVerification.TravellerFile }
         * </p>
         * 
         * 
         * @return
         *     The value of the travellerFile property.
         */
        public List<IdentificationResultResponseDataType.EESSearchAndVerification.TravellerFile> getTravellerFile() {
            if (travellerFile == null) {
                travellerFile = new ArrayList<>();
            }
            return this.travellerFile;
        }


        /**
         * <p>Classe Java per anonymous complex type.</p>
         * 
         * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
         * 
         * <pre>{@code
         * <complexType>
         *   <complexContent>
         *     <extension base="{http://www.europa.eu/schengen/ees/xsd/v1}TravellerFileSearchResponseType">
         *       <sequence>
         *         <element name="FIVerification" minOccurs="0">
         *           <complexType>
         *             <complexContent>
         *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                 <sequence>
         *                   <group ref="{http://www.europa.eu/schengen/ees/xsd/v1}ResultScoreGroup"/>
         *                 </sequence>
         *               </restriction>
         *             </complexContent>
         *           </complexType>
         *         </element>
         *         <element name="FPVerification" minOccurs="0">
         *           <complexType>
         *             <complexContent>
         *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                 <sequence>
         *                   <group ref="{http://www.europa.eu/schengen/ees/xsd/v1}ResultScoreGroup"/>
         *                 </sequence>
         *               </restriction>
         *             </complexContent>
         *           </complexType>
         *         </element>
         *         <element name="SelectedMatch" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
         *       </sequence>
         *     </extension>
         *   </complexContent>
         * </complexType>
         * }</pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "fiVerification",
            "fpVerification",
            "selectedMatch"
        })
        public static class TravellerFile
            extends TravellerFileSearchResponseType
        {

            @XmlElement(name = "FIVerification", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1")
            protected IdentificationResultResponseDataType.EESSearchAndVerification.TravellerFile.FIVerification fiVerification;
            @XmlElement(name = "FPVerification", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1")
            protected IdentificationResultResponseDataType.EESSearchAndVerification.TravellerFile.FPVerification fpVerification;
            @XmlElement(name = "SelectedMatch", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1")
            protected Boolean selectedMatch;

            /**
             * Recupera il valore della proprietà fiVerification.
             * 
             * @return
             *     possible object is
             *     {@link IdentificationResultResponseDataType.EESSearchAndVerification.TravellerFile.FIVerification }
             *     
             */
            public IdentificationResultResponseDataType.EESSearchAndVerification.TravellerFile.FIVerification getFIVerification() {
                return fiVerification;
            }

            /**
             * Imposta il valore della proprietà fiVerification.
             * 
             * @param value
             *     allowed object is
             *     {@link IdentificationResultResponseDataType.EESSearchAndVerification.TravellerFile.FIVerification }
             *     
             */
            public void setFIVerification(IdentificationResultResponseDataType.EESSearchAndVerification.TravellerFile.FIVerification value) {
                this.fiVerification = value;
            }

            /**
             * Recupera il valore della proprietà fpVerification.
             * 
             * @return
             *     possible object is
             *     {@link IdentificationResultResponseDataType.EESSearchAndVerification.TravellerFile.FPVerification }
             *     
             */
            public IdentificationResultResponseDataType.EESSearchAndVerification.TravellerFile.FPVerification getFPVerification() {
                return fpVerification;
            }

            /**
             * Imposta il valore della proprietà fpVerification.
             * 
             * @param value
             *     allowed object is
             *     {@link IdentificationResultResponseDataType.EESSearchAndVerification.TravellerFile.FPVerification }
             *     
             */
            public void setFPVerification(IdentificationResultResponseDataType.EESSearchAndVerification.TravellerFile.FPVerification value) {
                this.fpVerification = value;
            }

            /**
             * Recupera il valore della proprietà selectedMatch.
             * 
             * @return
             *     possible object is
             *     {@link Boolean }
             *     
             */
            public Boolean isSelectedMatch() {
                return selectedMatch;
            }

            /**
             * Imposta il valore della proprietà selectedMatch.
             * 
             * @param value
             *     allowed object is
             *     {@link Boolean }
             *     
             */
            public void setSelectedMatch(Boolean value) {
                this.selectedMatch = value;
            }


            /**
             * <p>Classe Java per anonymous complex type.</p>
             * 
             * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
             * 
             * <pre>{@code
             * <complexType>
             *   <complexContent>
             *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *       <sequence>
             *         <group ref="{http://www.europa.eu/schengen/ees/xsd/v1}ResultScoreGroup"/>
             *       </sequence>
             *     </restriction>
             *   </complexContent>
             * </complexType>
             * }</pre>
             * 
             * 
             */
            @XmlAccessorType(XmlAccessType.FIELD)
            @XmlType(name = "", propOrder = {
                "result",
                "score"
            })
            public static class FIVerification {

                @XmlElement(name = "Result", namespace = "http://www.europa.eu/schengen/ees/xsd/v1")
                protected boolean result;
                /**
                 * Normalised matched score between 0 and 100 for alphanumeric searches. 100 is the highest score for alphanumeric searches.
                 * 						Matching score between 0 and 100000 for biometric searches. 100000 is the highest score for biometric searches.
                 * 
                 */
                @XmlElement(name = "Score", namespace = "http://www.europa.eu/schengen/ees/xsd/v1")
                @XmlSchemaType(name = "unsignedInt")
                protected long score;

                /**
                 * Recupera il valore della proprietà result.
                 * 
                 */
                public boolean isResult() {
                    return result;
                }

                /**
                 * Imposta il valore della proprietà result.
                 * 
                 */
                public void setResult(boolean value) {
                    this.result = value;
                }

                /**
                 * Normalised matched score between 0 and 100 for alphanumeric searches. 100 is the highest score for alphanumeric searches.
                 * 						Matching score between 0 and 100000 for biometric searches. 100000 is the highest score for biometric searches.
                 * 
                 */
                public long getScore() {
                    return score;
                }

                /**
                 * Imposta il valore della proprietà score.
                 * 
                 */
                public void setScore(long value) {
                    this.score = value;
                }

            }


            /**
             * <p>Classe Java per anonymous complex type.</p>
             * 
             * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
             * 
             * <pre>{@code
             * <complexType>
             *   <complexContent>
             *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *       <sequence>
             *         <group ref="{http://www.europa.eu/schengen/ees/xsd/v1}ResultScoreGroup"/>
             *       </sequence>
             *     </restriction>
             *   </complexContent>
             * </complexType>
             * }</pre>
             * 
             * 
             */
            @XmlAccessorType(XmlAccessType.FIELD)
            @XmlType(name = "", propOrder = {
                "result",
                "score"
            })
            public static class FPVerification {

                @XmlElement(name = "Result", namespace = "http://www.europa.eu/schengen/ees/xsd/v1")
                protected boolean result;
                /**
                 * Normalised matched score between 0 and 100 for alphanumeric searches. 100 is the highest score for alphanumeric searches.
                 * 						Matching score between 0 and 100000 for biometric searches. 100000 is the highest score for biometric searches.
                 * 
                 */
                @XmlElement(name = "Score", namespace = "http://www.europa.eu/schengen/ees/xsd/v1")
                @XmlSchemaType(name = "unsignedInt")
                protected long score;

                /**
                 * Recupera il valore della proprietà result.
                 * 
                 */
                public boolean isResult() {
                    return result;
                }

                /**
                 * Imposta il valore della proprietà result.
                 * 
                 */
                public void setResult(boolean value) {
                    this.result = value;
                }

                /**
                 * Normalised matched score between 0 and 100 for alphanumeric searches. 100 is the highest score for alphanumeric searches.
                 * 						Matching score between 0 and 100000 for biometric searches. 100000 is the highest score for biometric searches.
                 * 
                 */
                public long getScore() {
                    return score;
                }

                /**
                 * Imposta il valore della proprietà score.
                 * 
                 */
                public void setScore(long value) {
                    this.score = value;
                }

            }

        }

    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="TravelAuthorisation" maxOccurs="unbounded" minOccurs="0">
     *           <complexType>
     *             <complexContent>
     *               <extension base="{http://www.europa.eu/schengen/etias/xsd/v1}TravelAuthorisationResponseType">
     *                 <sequence>
     *                   <element name="SelectedMatch" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
     *                 </sequence>
     *               </extension>
     *             </complexContent>
     *           </complexType>
     *         </element>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "travelAuthorisation"
    })
    public static class ETIASSearch {

        @XmlElement(name = "TravelAuthorisation", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1")
        protected List<IdentificationResultResponseDataType.ETIASSearch.TravelAuthorisation> travelAuthorisation;

        /**
         * Gets the value of the travelAuthorisation property.
         * 
         * <p>This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the travelAuthorisation property.</p>
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * </p>
         * <pre>
         * getTravelAuthorisation().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link IdentificationResultResponseDataType.ETIASSearch.TravelAuthorisation }
         * </p>
         * 
         * 
         * @return
         *     The value of the travelAuthorisation property.
         */
        public List<IdentificationResultResponseDataType.ETIASSearch.TravelAuthorisation> getTravelAuthorisation() {
            if (travelAuthorisation == null) {
                travelAuthorisation = new ArrayList<>();
            }
            return this.travelAuthorisation;
        }


        /**
         * <p>Classe Java per anonymous complex type.</p>
         * 
         * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
         * 
         * <pre>{@code
         * <complexType>
         *   <complexContent>
         *     <extension base="{http://www.europa.eu/schengen/etias/xsd/v1}TravelAuthorisationResponseType">
         *       <sequence>
         *         <element name="SelectedMatch" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
         *       </sequence>
         *     </extension>
         *   </complexContent>
         * </complexType>
         * }</pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "selectedMatch"
        })
        public static class TravelAuthorisation
            extends TravelAuthorisationResponseType
        {

            @XmlElement(name = "SelectedMatch", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1")
            protected boolean selectedMatch;

            /**
             * Recupera il valore della proprietà selectedMatch.
             * 
             */
            public boolean isSelectedMatch() {
                return selectedMatch;
            }

            /**
             * Imposta il valore della proprietà selectedMatch.
             * 
             */
            public void setSelectedMatch(boolean value) {
                this.selectedMatch = value;
            }

        }

    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="VisaInformation" type="{http://www.europa.eu/schengen/ees/xsd/v1}VisaInformationType"/>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "visaInformation"
    })
    public static class VISAlphanumericIdentification {

        @XmlElement(name = "VisaInformation", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1", required = true)
        protected VisaInformationType visaInformation;

        /**
         * Recupera il valore della proprietà visaInformation.
         * 
         * @return
         *     possible object is
         *     {@link VisaInformationType }
         *     
         */
        public VisaInformationType getVisaInformation() {
            return visaInformation;
        }

        /**
         * Imposta il valore della proprietà visaInformation.
         * 
         * @param value
         *     allowed object is
         *     {@link VisaInformationType }
         *     
         */
        public void setVisaInformation(VisaInformationType value) {
            this.visaInformation = value;
        }

    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="VisaInformation" type="{http://www.europa.eu/schengen/ees/xsd/v1}VisaInformationType"/>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "visaInformation"
    })
    public static class VISDirectIdentification {

        @XmlElement(name = "VisaInformation", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1", required = true)
        protected VisaInformationType visaInformation;

        /**
         * Recupera il valore della proprietà visaInformation.
         * 
         * @return
         *     possible object is
         *     {@link VisaInformationType }
         *     
         */
        public VisaInformationType getVisaInformation() {
            return visaInformation;
        }

        /**
         * Imposta il valore della proprietà visaInformation.
         * 
         * @param value
         *     allowed object is
         *     {@link VisaInformationType }
         *     
         */
        public void setVisaInformation(VisaInformationType value) {
            this.visaInformation = value;
        }

    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <choice>
     *         <element name="VisaApplicationOverview" maxOccurs="unbounded" minOccurs="0">
     *           <complexType>
     *             <complexContent>
     *               <extension base="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}VisaApplicationOverviewSearchResultVISType">
     *                 <sequence>
     *                   <group ref="{http://www.europa.eu/schengen/ees/xsd/v1}RankScoreGroup"/>
     *                   <element name="DossierID" type="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}DossierIDType"/>
     *                   <element name="FPIdentification" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
     *                   <element name="SelectedMatch" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
     *                 </sequence>
     *               </extension>
     *             </complexContent>
     *           </complexType>
     *         </element>
     *         <element name="VisaApplication" maxOccurs="unbounded" minOccurs="0">
     *           <complexType>
     *             <complexContent>
     *               <extension base="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}VisaApplicationSearchResultEESType">
     *                 <sequence>
     *                   <group ref="{http://www.europa.eu/schengen/ees/xsd/v1}RankScoreGroup"/>
     *                   <element name="DossierID" type="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}DossierIDType"/>
     *                   <element name="FPIdentification" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
     *                   <element name="SelectedMatch" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
     *                 </sequence>
     *               </extension>
     *             </complexContent>
     *           </complexType>
     *         </element>
     *       </choice>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "visaApplicationOverview",
        "visaApplication"
    })
    public static class VISIdentification {

        @XmlElement(name = "VisaApplicationOverview", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1")
        protected List<IdentificationResultResponseDataType.VISIdentification.VisaApplicationOverview> visaApplicationOverview;
        @XmlElement(name = "VisaApplication", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1")
        protected List<IdentificationResultResponseDataType.VISIdentification.VisaApplication> visaApplication;

        /**
         * Gets the value of the visaApplicationOverview property.
         * 
         * <p>This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the visaApplicationOverview property.</p>
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * </p>
         * <pre>
         * getVisaApplicationOverview().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link IdentificationResultResponseDataType.VISIdentification.VisaApplicationOverview }
         * </p>
         * 
         * 
         * @return
         *     The value of the visaApplicationOverview property.
         */
        public List<IdentificationResultResponseDataType.VISIdentification.VisaApplicationOverview> getVisaApplicationOverview() {
            if (visaApplicationOverview == null) {
                visaApplicationOverview = new ArrayList<>();
            }
            return this.visaApplicationOverview;
        }

        /**
         * Gets the value of the visaApplication property.
         * 
         * <p>This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the visaApplication property.</p>
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * </p>
         * <pre>
         * getVisaApplication().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link IdentificationResultResponseDataType.VISIdentification.VisaApplication }
         * </p>
         * 
         * 
         * @return
         *     The value of the visaApplication property.
         */
        public List<IdentificationResultResponseDataType.VISIdentification.VisaApplication> getVisaApplication() {
            if (visaApplication == null) {
                visaApplication = new ArrayList<>();
            }
            return this.visaApplication;
        }


        /**
         * <p>Classe Java per anonymous complex type.</p>
         * 
         * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
         * 
         * <pre>{@code
         * <complexType>
         *   <complexContent>
         *     <extension base="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}VisaApplicationSearchResultEESType">
         *       <sequence>
         *         <group ref="{http://www.europa.eu/schengen/ees/xsd/v1}RankScoreGroup"/>
         *         <element name="DossierID" type="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}DossierIDType"/>
         *         <element name="FPIdentification" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
         *         <element name="SelectedMatch" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
         *       </sequence>
         *     </extension>
         *   </complexContent>
         * </complexType>
         * }</pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "rank",
            "score",
            "dossierID",
            "fpIdentification",
            "selectedMatch"
        })
        public static class VisaApplication
            extends VisaApplicationSearchResultEESType
        {

            @XmlElement(name = "Rank", namespace = "http://www.europa.eu/schengen/ees/xsd/v1")
            @XmlSchemaType(name = "unsignedInt")
            protected long rank;
            /**
             * Normalised matched score between 0 and 100 for alphanumeric searches. 100 is the highest score for alphanumeric searches.
             * 						Matching score between 0 and 100000 for biometric searches. 100000 is the highest score for biometric searches.
             * 
             */
            @XmlElement(name = "Score", namespace = "http://www.europa.eu/schengen/ees/xsd/v1")
            @XmlSchemaType(name = "unsignedInt")
            protected long score;
            /**
             * Description: ID of the Dossier.
             * 
             */
            @XmlElement(name = "DossierID", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1")
            protected long dossierID;
            @XmlElement(name = "FPIdentification", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1")
            protected boolean fpIdentification;
            @XmlElement(name = "SelectedMatch", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1")
            protected Boolean selectedMatch;

            /**
             * Recupera il valore della proprietà rank.
             * 
             */
            public long getRank() {
                return rank;
            }

            /**
             * Imposta il valore della proprietà rank.
             * 
             */
            public void setRank(long value) {
                this.rank = value;
            }

            /**
             * Normalised matched score between 0 and 100 for alphanumeric searches. 100 is the highest score for alphanumeric searches.
             * 						Matching score between 0 and 100000 for biometric searches. 100000 is the highest score for biometric searches.
             * 
             */
            public long getScore() {
                return score;
            }

            /**
             * Imposta il valore della proprietà score.
             * 
             */
            public void setScore(long value) {
                this.score = value;
            }

            /**
             * Description: ID of the Dossier.
             * 
             */
            public long getDossierID() {
                return dossierID;
            }

            /**
             * Imposta il valore della proprietà dossierID.
             * 
             */
            public void setDossierID(long value) {
                this.dossierID = value;
            }

            /**
             * Recupera il valore della proprietà fpIdentification.
             * 
             */
            public boolean isFPIdentification() {
                return fpIdentification;
            }

            /**
             * Imposta il valore della proprietà fpIdentification.
             * 
             */
            public void setFPIdentification(boolean value) {
                this.fpIdentification = value;
            }

            /**
             * Recupera il valore della proprietà selectedMatch.
             * 
             * @return
             *     possible object is
             *     {@link Boolean }
             *     
             */
            public Boolean isSelectedMatch() {
                return selectedMatch;
            }

            /**
             * Imposta il valore della proprietà selectedMatch.
             * 
             * @param value
             *     allowed object is
             *     {@link Boolean }
             *     
             */
            public void setSelectedMatch(Boolean value) {
                this.selectedMatch = value;
            }

        }


        /**
         * <p>Classe Java per anonymous complex type.</p>
         * 
         * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
         * 
         * <pre>{@code
         * <complexType>
         *   <complexContent>
         *     <extension base="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}VisaApplicationOverviewSearchResultVISType">
         *       <sequence>
         *         <group ref="{http://www.europa.eu/schengen/ees/xsd/v1}RankScoreGroup"/>
         *         <element name="DossierID" type="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}DossierIDType"/>
         *         <element name="FPIdentification" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
         *         <element name="SelectedMatch" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
         *       </sequence>
         *     </extension>
         *   </complexContent>
         * </complexType>
         * }</pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "rank",
            "score",
            "dossierID",
            "fpIdentification",
            "selectedMatch"
        })
        public static class VisaApplicationOverview
            extends VisaApplicationOverviewSearchResultVISType
        {

            @XmlElement(name = "Rank", namespace = "http://www.europa.eu/schengen/ees/xsd/v1")
            @XmlSchemaType(name = "unsignedInt")
            protected long rank;
            /**
             * Normalised matched score between 0 and 100 for alphanumeric searches. 100 is the highest score for alphanumeric searches.
             * 						Matching score between 0 and 100000 for biometric searches. 100000 is the highest score for biometric searches.
             * 
             */
            @XmlElement(name = "Score", namespace = "http://www.europa.eu/schengen/ees/xsd/v1")
            @XmlSchemaType(name = "unsignedInt")
            protected long score;
            /**
             * Description: ID of the Dossier.
             * 
             */
            @XmlElement(name = "DossierID", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1")
            protected long dossierID;
            @XmlElement(name = "FPIdentification", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1")
            protected boolean fpIdentification;
            @XmlElement(name = "SelectedMatch", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1")
            protected Boolean selectedMatch;

            /**
             * Recupera il valore della proprietà rank.
             * 
             */
            public long getRank() {
                return rank;
            }

            /**
             * Imposta il valore della proprietà rank.
             * 
             */
            public void setRank(long value) {
                this.rank = value;
            }

            /**
             * Normalised matched score between 0 and 100 for alphanumeric searches. 100 is the highest score for alphanumeric searches.
             * 						Matching score between 0 and 100000 for biometric searches. 100000 is the highest score for biometric searches.
             * 
             */
            public long getScore() {
                return score;
            }

            /**
             * Imposta il valore della proprietà score.
             * 
             */
            public void setScore(long value) {
                this.score = value;
            }

            /**
             * Description: ID of the Dossier.
             * 
             */
            public long getDossierID() {
                return dossierID;
            }

            /**
             * Imposta il valore della proprietà dossierID.
             * 
             */
            public void setDossierID(long value) {
                this.dossierID = value;
            }

            /**
             * Recupera il valore della proprietà fpIdentification.
             * 
             */
            public boolean isFPIdentification() {
                return fpIdentification;
            }

            /**
             * Imposta il valore della proprietà fpIdentification.
             * 
             */
            public void setFPIdentification(boolean value) {
                this.fpIdentification = value;
            }

            /**
             * Recupera il valore della proprietà selectedMatch.
             * 
             * @return
             *     possible object is
             *     {@link Boolean }
             *     
             */
            public Boolean isSelectedMatch() {
                return selectedMatch;
            }

            /**
             * Imposta il valore della proprietà selectedMatch.
             * 
             * @param value
             *     allowed object is
             *     {@link Boolean }
             *     
             */
            public void setSelectedMatch(Boolean value) {
                this.selectedMatch = value;
            }

        }

    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <choice>
     *         <element name="VisaApplicationOverview" maxOccurs="unbounded" minOccurs="0">
     *           <complexType>
     *             <complexContent>
     *               <extension base="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}VisaApplicationOverviewSearchResultVISType">
     *                 <sequence>
     *                   <group ref="{http://www.europa.eu/schengen/ees/xsd/v1}RankScoreGroup"/>
     *                   <element name="DossierID" type="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}DossierIDType"/>
     *                   <element name="FPVerification" minOccurs="0">
     *                     <complexType>
     *                       <complexContent>
     *                         <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                           <sequence>
     *                             <group ref="{http://www.europa.eu/schengen/ees/xsd/v1}VISResultScoreGroup"/>
     *                           </sequence>
     *                         </restriction>
     *                       </complexContent>
     *                     </complexType>
     *                   </element>
     *                   <element name="SelectedMatch" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
     *                 </sequence>
     *               </extension>
     *             </complexContent>
     *           </complexType>
     *         </element>
     *         <element name="VisaApplication" maxOccurs="unbounded" minOccurs="0">
     *           <complexType>
     *             <complexContent>
     *               <extension base="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}VisaApplicationSearchResultEESType">
     *                 <sequence>
     *                   <group ref="{http://www.europa.eu/schengen/ees/xsd/v1}RankScoreGroup"/>
     *                   <element name="DossierID" type="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}DossierIDType"/>
     *                   <element name="FPVerification" minOccurs="0">
     *                     <complexType>
     *                       <complexContent>
     *                         <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                           <sequence>
     *                             <group ref="{http://www.europa.eu/schengen/ees/xsd/v1}VISResultScoreGroup"/>
     *                           </sequence>
     *                         </restriction>
     *                       </complexContent>
     *                     </complexType>
     *                   </element>
     *                   <element name="SelectedMatch" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
     *                 </sequence>
     *               </extension>
     *             </complexContent>
     *           </complexType>
     *         </element>
     *         <element name="VisaApplicationNumber" maxOccurs="unbounded" minOccurs="0">
     *           <complexType>
     *             <complexContent>
     *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 <sequence>
     *                   <element name="ApplicationNumber" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}ApplicationNumberType"/>
     *                   <element name="DossierID" type="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}DossierIDType"/>
     *                   <element name="SelectedMatch" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
     *                 </sequence>
     *               </restriction>
     *             </complexContent>
     *           </complexType>
     *         </element>
     *       </choice>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "visaApplicationOverview",
        "visaApplication",
        "visaApplicationNumber"
    })
    public static class VISSearchAndVerification {

        @XmlElement(name = "VisaApplicationOverview", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1")
        protected List<IdentificationResultResponseDataType.VISSearchAndVerification.VisaApplicationOverview> visaApplicationOverview;
        @XmlElement(name = "VisaApplication", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1")
        protected List<IdentificationResultResponseDataType.VISSearchAndVerification.VisaApplication> visaApplication;
        @XmlElement(name = "VisaApplicationNumber", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1")
        protected List<IdentificationResultResponseDataType.VISSearchAndVerification.VisaApplicationNumber> visaApplicationNumber;

        /**
         * Gets the value of the visaApplicationOverview property.
         * 
         * <p>This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the visaApplicationOverview property.</p>
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * </p>
         * <pre>
         * getVisaApplicationOverview().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link IdentificationResultResponseDataType.VISSearchAndVerification.VisaApplicationOverview }
         * </p>
         * 
         * 
         * @return
         *     The value of the visaApplicationOverview property.
         */
        public List<IdentificationResultResponseDataType.VISSearchAndVerification.VisaApplicationOverview> getVisaApplicationOverview() {
            if (visaApplicationOverview == null) {
                visaApplicationOverview = new ArrayList<>();
            }
            return this.visaApplicationOverview;
        }

        /**
         * Gets the value of the visaApplication property.
         * 
         * <p>This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the visaApplication property.</p>
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * </p>
         * <pre>
         * getVisaApplication().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link IdentificationResultResponseDataType.VISSearchAndVerification.VisaApplication }
         * </p>
         * 
         * 
         * @return
         *     The value of the visaApplication property.
         */
        public List<IdentificationResultResponseDataType.VISSearchAndVerification.VisaApplication> getVisaApplication() {
            if (visaApplication == null) {
                visaApplication = new ArrayList<>();
            }
            return this.visaApplication;
        }

        /**
         * Gets the value of the visaApplicationNumber property.
         * 
         * <p>This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the visaApplicationNumber property.</p>
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * </p>
         * <pre>
         * getVisaApplicationNumber().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link IdentificationResultResponseDataType.VISSearchAndVerification.VisaApplicationNumber }
         * </p>
         * 
         * 
         * @return
         *     The value of the visaApplicationNumber property.
         */
        public List<IdentificationResultResponseDataType.VISSearchAndVerification.VisaApplicationNumber> getVisaApplicationNumber() {
            if (visaApplicationNumber == null) {
                visaApplicationNumber = new ArrayList<>();
            }
            return this.visaApplicationNumber;
        }


        /**
         * <p>Classe Java per anonymous complex type.</p>
         * 
         * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
         * 
         * <pre>{@code
         * <complexType>
         *   <complexContent>
         *     <extension base="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}VisaApplicationSearchResultEESType">
         *       <sequence>
         *         <group ref="{http://www.europa.eu/schengen/ees/xsd/v1}RankScoreGroup"/>
         *         <element name="DossierID" type="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}DossierIDType"/>
         *         <element name="FPVerification" minOccurs="0">
         *           <complexType>
         *             <complexContent>
         *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                 <sequence>
         *                   <group ref="{http://www.europa.eu/schengen/ees/xsd/v1}VISResultScoreGroup"/>
         *                 </sequence>
         *               </restriction>
         *             </complexContent>
         *           </complexType>
         *         </element>
         *         <element name="SelectedMatch" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
         *       </sequence>
         *     </extension>
         *   </complexContent>
         * </complexType>
         * }</pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "rank",
            "score",
            "dossierID",
            "fpVerification",
            "selectedMatch"
        })
        public static class VisaApplication
            extends VisaApplicationSearchResultEESType
        {

            @XmlElement(name = "Rank", namespace = "http://www.europa.eu/schengen/ees/xsd/v1")
            @XmlSchemaType(name = "unsignedInt")
            protected long rank;
            /**
             * Normalised matched score between 0 and 100 for alphanumeric searches. 100 is the highest score for alphanumeric searches.
             * 						Matching score between 0 and 100000 for biometric searches. 100000 is the highest score for biometric searches.
             * 
             */
            @XmlElement(name = "Score", namespace = "http://www.europa.eu/schengen/ees/xsd/v1")
            @XmlSchemaType(name = "unsignedInt")
            protected long score;
            /**
             * Description: ID of the Dossier.
             * 
             */
            @XmlElement(name = "DossierID", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1")
            protected long dossierID;
            @XmlElement(name = "FPVerification", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1")
            protected IdentificationResultResponseDataType.VISSearchAndVerification.VisaApplication.FPVerification fpVerification;
            @XmlElement(name = "SelectedMatch", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1")
            protected Boolean selectedMatch;

            /**
             * Recupera il valore della proprietà rank.
             * 
             */
            public long getRank() {
                return rank;
            }

            /**
             * Imposta il valore della proprietà rank.
             * 
             */
            public void setRank(long value) {
                this.rank = value;
            }

            /**
             * Normalised matched score between 0 and 100 for alphanumeric searches. 100 is the highest score for alphanumeric searches.
             * 						Matching score between 0 and 100000 for biometric searches. 100000 is the highest score for biometric searches.
             * 
             */
            public long getScore() {
                return score;
            }

            /**
             * Imposta il valore della proprietà score.
             * 
             */
            public void setScore(long value) {
                this.score = value;
            }

            /**
             * Description: ID of the Dossier.
             * 
             */
            public long getDossierID() {
                return dossierID;
            }

            /**
             * Imposta il valore della proprietà dossierID.
             * 
             */
            public void setDossierID(long value) {
                this.dossierID = value;
            }

            /**
             * Recupera il valore della proprietà fpVerification.
             * 
             * @return
             *     possible object is
             *     {@link IdentificationResultResponseDataType.VISSearchAndVerification.VisaApplication.FPVerification }
             *     
             */
            public IdentificationResultResponseDataType.VISSearchAndVerification.VisaApplication.FPVerification getFPVerification() {
                return fpVerification;
            }

            /**
             * Imposta il valore della proprietà fpVerification.
             * 
             * @param value
             *     allowed object is
             *     {@link IdentificationResultResponseDataType.VISSearchAndVerification.VisaApplication.FPVerification }
             *     
             */
            public void setFPVerification(IdentificationResultResponseDataType.VISSearchAndVerification.VisaApplication.FPVerification value) {
                this.fpVerification = value;
            }

            /**
             * Recupera il valore della proprietà selectedMatch.
             * 
             * @return
             *     possible object is
             *     {@link Boolean }
             *     
             */
            public Boolean isSelectedMatch() {
                return selectedMatch;
            }

            /**
             * Imposta il valore della proprietà selectedMatch.
             * 
             * @param value
             *     allowed object is
             *     {@link Boolean }
             *     
             */
            public void setSelectedMatch(Boolean value) {
                this.selectedMatch = value;
            }


            /**
             * <p>Classe Java per anonymous complex type.</p>
             * 
             * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
             * 
             * <pre>{@code
             * <complexType>
             *   <complexContent>
             *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *       <sequence>
             *         <group ref="{http://www.europa.eu/schengen/ees/xsd/v1}VISResultScoreGroup"/>
             *       </sequence>
             *     </restriction>
             *   </complexContent>
             * </complexType>
             * }</pre>
             * 
             * 
             */
            @XmlAccessorType(XmlAccessType.FIELD)
            @XmlType(name = "", propOrder = {
                "result"
            })
            public static class FPVerification {

                @XmlElement(name = "Result", namespace = "http://www.europa.eu/schengen/ees/xsd/v1")
                protected boolean result;

                /**
                 * Recupera il valore della proprietà result.
                 * 
                 */
                public boolean isResult() {
                    return result;
                }

                /**
                 * Imposta il valore della proprietà result.
                 * 
                 */
                public void setResult(boolean value) {
                    this.result = value;
                }

            }

        }


        /**
         * <p>Classe Java per anonymous complex type.</p>
         * 
         * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
         * 
         * <pre>{@code
         * <complexType>
         *   <complexContent>
         *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       <sequence>
         *         <element name="ApplicationNumber" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}ApplicationNumberType"/>
         *         <element name="DossierID" type="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}DossierIDType"/>
         *         <element name="SelectedMatch" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
         *       </sequence>
         *     </restriction>
         *   </complexContent>
         * </complexType>
         * }</pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "applicationNumber",
            "dossierID",
            "selectedMatch"
        })
        public static class VisaApplicationNumber {

            /**
             * Description: ID of the Application.
             * 
             */
            @XmlElement(name = "ApplicationNumber", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1", required = true)
            protected String applicationNumber;
            /**
             * Description: ID of the Dossier.
             * 
             */
            @XmlElement(name = "DossierID", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1")
            protected long dossierID;
            @XmlElement(name = "SelectedMatch", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1")
            protected Boolean selectedMatch;

            /**
             * Description: ID of the Application.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getApplicationNumber() {
                return applicationNumber;
            }

            /**
             * Imposta il valore della proprietà applicationNumber.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             * @see #getApplicationNumber()
             */
            public void setApplicationNumber(String value) {
                this.applicationNumber = value;
            }

            /**
             * Description: ID of the Dossier.
             * 
             */
            public long getDossierID() {
                return dossierID;
            }

            /**
             * Imposta il valore della proprietà dossierID.
             * 
             */
            public void setDossierID(long value) {
                this.dossierID = value;
            }

            /**
             * Recupera il valore della proprietà selectedMatch.
             * 
             * @return
             *     possible object is
             *     {@link Boolean }
             *     
             */
            public Boolean isSelectedMatch() {
                return selectedMatch;
            }

            /**
             * Imposta il valore della proprietà selectedMatch.
             * 
             * @param value
             *     allowed object is
             *     {@link Boolean }
             *     
             */
            public void setSelectedMatch(Boolean value) {
                this.selectedMatch = value;
            }

        }


        /**
         * <p>Classe Java per anonymous complex type.</p>
         * 
         * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
         * 
         * <pre>{@code
         * <complexType>
         *   <complexContent>
         *     <extension base="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}VisaApplicationOverviewSearchResultVISType">
         *       <sequence>
         *         <group ref="{http://www.europa.eu/schengen/ees/xsd/v1}RankScoreGroup"/>
         *         <element name="DossierID" type="{http://www.europa.eu/schengen/cs_vis-nui_ees/xsd/v1}DossierIDType"/>
         *         <element name="FPVerification" minOccurs="0">
         *           <complexType>
         *             <complexContent>
         *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                 <sequence>
         *                   <group ref="{http://www.europa.eu/schengen/ees/xsd/v1}VISResultScoreGroup"/>
         *                 </sequence>
         *               </restriction>
         *             </complexContent>
         *           </complexType>
         *         </element>
         *         <element name="SelectedMatch" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
         *       </sequence>
         *     </extension>
         *   </complexContent>
         * </complexType>
         * }</pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "rank",
            "score",
            "dossierID",
            "fpVerification",
            "selectedMatch"
        })
        public static class VisaApplicationOverview
            extends VisaApplicationOverviewSearchResultVISType
        {

            @XmlElement(name = "Rank", namespace = "http://www.europa.eu/schengen/ees/xsd/v1")
            @XmlSchemaType(name = "unsignedInt")
            protected long rank;
            /**
             * Normalised matched score between 0 and 100 for alphanumeric searches. 100 is the highest score for alphanumeric searches.
             * 						Matching score between 0 and 100000 for biometric searches. 100000 is the highest score for biometric searches.
             * 
             */
            @XmlElement(name = "Score", namespace = "http://www.europa.eu/schengen/ees/xsd/v1")
            @XmlSchemaType(name = "unsignedInt")
            protected long score;
            /**
             * Description: ID of the Dossier.
             * 
             */
            @XmlElement(name = "DossierID", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1")
            protected long dossierID;
            @XmlElement(name = "FPVerification", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1")
            protected IdentificationResultResponseDataType.VISSearchAndVerification.VisaApplicationOverview.FPVerification fpVerification;
            @XmlElement(name = "SelectedMatch", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1")
            protected Boolean selectedMatch;

            /**
             * Recupera il valore della proprietà rank.
             * 
             */
            public long getRank() {
                return rank;
            }

            /**
             * Imposta il valore della proprietà rank.
             * 
             */
            public void setRank(long value) {
                this.rank = value;
            }

            /**
             * Normalised matched score between 0 and 100 for alphanumeric searches. 100 is the highest score for alphanumeric searches.
             * 						Matching score between 0 and 100000 for biometric searches. 100000 is the highest score for biometric searches.
             * 
             */
            public long getScore() {
                return score;
            }

            /**
             * Imposta il valore della proprietà score.
             * 
             */
            public void setScore(long value) {
                this.score = value;
            }

            /**
             * Description: ID of the Dossier.
             * 
             */
            public long getDossierID() {
                return dossierID;
            }

            /**
             * Imposta il valore della proprietà dossierID.
             * 
             */
            public void setDossierID(long value) {
                this.dossierID = value;
            }

            /**
             * Recupera il valore della proprietà fpVerification.
             * 
             * @return
             *     possible object is
             *     {@link IdentificationResultResponseDataType.VISSearchAndVerification.VisaApplicationOverview.FPVerification }
             *     
             */
            public IdentificationResultResponseDataType.VISSearchAndVerification.VisaApplicationOverview.FPVerification getFPVerification() {
                return fpVerification;
            }

            /**
             * Imposta il valore della proprietà fpVerification.
             * 
             * @param value
             *     allowed object is
             *     {@link IdentificationResultResponseDataType.VISSearchAndVerification.VisaApplicationOverview.FPVerification }
             *     
             */
            public void setFPVerification(IdentificationResultResponseDataType.VISSearchAndVerification.VisaApplicationOverview.FPVerification value) {
                this.fpVerification = value;
            }

            /**
             * Recupera il valore della proprietà selectedMatch.
             * 
             * @return
             *     possible object is
             *     {@link Boolean }
             *     
             */
            public Boolean isSelectedMatch() {
                return selectedMatch;
            }

            /**
             * Imposta il valore della proprietà selectedMatch.
             * 
             * @param value
             *     allowed object is
             *     {@link Boolean }
             *     
             */
            public void setSelectedMatch(Boolean value) {
                this.selectedMatch = value;
            }


            /**
             * <p>Classe Java per anonymous complex type.</p>
             * 
             * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
             * 
             * <pre>{@code
             * <complexType>
             *   <complexContent>
             *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *       <sequence>
             *         <group ref="{http://www.europa.eu/schengen/ees/xsd/v1}VISResultScoreGroup"/>
             *       </sequence>
             *     </restriction>
             *   </complexContent>
             * </complexType>
             * }</pre>
             * 
             * 
             */
            @XmlAccessorType(XmlAccessType.FIELD)
            @XmlType(name = "", propOrder = {
                "result"
            })
            public static class FPVerification {

                @XmlElement(name = "Result", namespace = "http://www.europa.eu/schengen/ees/xsd/v1")
                protected boolean result;

                /**
                 * Recupera il valore della proprietà result.
                 * 
                 */
                public boolean isResult() {
                    return result;
                }

                /**
                 * Imposta il valore della proprietà result.
                 * 
                 */
                public void setResult(boolean value) {
                    this.result = value;
                }

            }

        }

    }

}
