//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.ees_ns.xsd.v1;

import java.util.ArrayList;
import java.util.List;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlAttribute;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;
import jakarta.xml.bind.annotation.XmlValue;


/**
 * <p>Classe Java per WFENameSearchField complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="WFENameSearchField">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="PrimaryValue" type="{http://www.europa.eu/schengen/ees-ns/xsd/v1}NameTypeRestriction"/>
 *         <element name="AlternativeSpelling" maxOccurs="5" minOccurs="0">
 *           <complexType>
 *             <simpleContent>
 *               <extension base="<http://www.europa.eu/schengen/shared/xsd/v1>AlternativeSpellingType">
 *                 <attribute name="Source" use="required" type="{http://www.europa.eu/schengen/shared/xsd/v1}CT508_NameDataSourceType" />
 *               </extension>
 *             </simpleContent>
 *           </complexType>
 *         </element>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "WFENameSearchField", propOrder = {
    "primaryValue",
    "alternativeSpelling"
})
public class WFENameSearchField {

    @XmlElement(name = "PrimaryValue", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1", required = true)
    protected NameTypeRestriction primaryValue;
    /**
     * Taken from other sources, e.g. vizual inspection zone (VIZ) of the travel document, etc.
     * 
     */
    @XmlElement(name = "AlternativeSpelling", namespace = "http://www.europa.eu/schengen/ees-ns/xsd/v1")
    protected List<WFENameSearchField.AlternativeSpelling> alternativeSpelling;

    /**
     * Recupera il valore della proprietà primaryValue.
     * 
     * @return
     *     possible object is
     *     {@link NameTypeRestriction }
     *     
     */
    public NameTypeRestriction getPrimaryValue() {
        return primaryValue;
    }

    /**
     * Imposta il valore della proprietà primaryValue.
     * 
     * @param value
     *     allowed object is
     *     {@link NameTypeRestriction }
     *     
     */
    public void setPrimaryValue(NameTypeRestriction value) {
        this.primaryValue = value;
    }

    /**
     * Taken from other sources, e.g. vizual inspection zone (VIZ) of the travel document, etc.
     * 
     * Gets the value of the alternativeSpelling property.
     * 
     * <p>This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the alternativeSpelling property.</p>
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * </p>
     * <pre>
     * getAlternativeSpelling().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link WFENameSearchField.AlternativeSpelling }
     * </p>
     * 
     * 
     * @return
     *     The value of the alternativeSpelling property.
     */
    public List<WFENameSearchField.AlternativeSpelling> getAlternativeSpelling() {
        if (alternativeSpelling == null) {
            alternativeSpelling = new ArrayList<>();
        }
        return this.alternativeSpelling;
    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <simpleContent>
     *     <extension base="<http://www.europa.eu/schengen/shared/xsd/v1>AlternativeSpellingType">
     *       <attribute name="Source" use="required" type="{http://www.europa.eu/schengen/shared/xsd/v1}CT508_NameDataSourceType" />
     *     </extension>
     *   </simpleContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "value"
    })
    public static class AlternativeSpelling {

        @XmlValue
        protected String value;
        @XmlAttribute(name = "Source", required = true)
        protected String source;

        /**
         * Recupera il valore della proprietà value.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getValue() {
            return value;
        }

        /**
         * Imposta il valore della proprietà value.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setValue(String value) {
            this.value = value;
        }

        /**
         * Recupera il valore della proprietà source.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getSource() {
            return source;
        }

        /**
         * Imposta il valore della proprietà source.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setSource(String value) {
            this.source = value;
        }

    }

}
