//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.shared.xsd.v1;

import javax.xml.datatype.XMLGregorianCalendar;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlSeeAlso;
import jakarta.xml.bind.annotation.XmlType;


/**
 * Authority data - type for the request.
 * 
 * <p>Classe Java per AuthorityBaseType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="AuthorityBaseType">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="Country" type="{http://www.europa.eu/schengen/shared/xsd/v1}CT01_CountryType"/>
 *         <element name="AuthorityUniqueId" type="{http://www.europa.eu/schengen/shared/xsd/v1}AuthorityUniqueIDType"/>
 *         <element name="Location" type="{http://www.europa.eu/schengen/shared/xsd/v1}AuthorityLocationType"/>
 *         <element name="Name" type="{http://www.europa.eu/schengen/shared/xsd/v1}AuthorityNameTypeExtended"/>
 *         <element name="AuthorityType" type="{http://www.europa.eu/schengen/shared/xsd/v1}CT10_AuthorityTypeType"/>
 *         <element name="AddressLine1" type="{http://www.europa.eu/schengen/shared/xsd/v1}RawValueType" minOccurs="0"/>
 *         <element name="AddressLine2" type="{http://www.europa.eu/schengen/shared/xsd/v1}RawValueType" minOccurs="0"/>
 *         <element name="PostCode" type="{http://www.europa.eu/schengen/shared/xsd/v1}RawValueType" minOccurs="0"/>
 *         <element name="Landline1" type="{http://www.europa.eu/schengen/shared/xsd/v1}PhoneNumberType" minOccurs="0"/>
 *         <element name="Landline2" type="{http://www.europa.eu/schengen/shared/xsd/v1}PhoneNumberType" minOccurs="0"/>
 *         <element name="Landline3" type="{http://www.europa.eu/schengen/shared/xsd/v1}PhoneNumberType" minOccurs="0"/>
 *         <element name="Mobile" type="{http://www.europa.eu/schengen/shared/xsd/v1}PhoneNumberType" minOccurs="0"/>
 *         <element name="Fax" type="{http://www.europa.eu/schengen/shared/xsd/v1}PhoneNumberType" minOccurs="0"/>
 *         <element name="Email1" type="{http://www.europa.eu/schengen/shared/xsd/v1}EmailType" minOccurs="0"/>
 *         <element name="Email2" type="{http://www.europa.eu/schengen/shared/xsd/v1}EmailType" minOccurs="0"/>
 *         <element name="Email3" type="{http://www.europa.eu/schengen/shared/xsd/v1}EmailType" minOccurs="0"/>
 *         <element name="VISMail" type="{http://www.europa.eu/schengen/shared/xsd/v1}EmailType" minOccurs="0"/>
 *         <element name="ValidStart" type="{http://www.w3.org/2001/XMLSchema}date"/>
 *         <element name="ValidEnd" type="{http://www.w3.org/2001/XMLSchema}date"/>
 *         <element name="ConsultationFlag" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "AuthorityBaseType", propOrder = {
    "country",
    "authorityUniqueId",
    "location",
    "name",
    "authorityType",
    "addressLine1",
    "addressLine2",
    "postCode",
    "landline1",
    "landline2",
    "landline3",
    "mobile",
    "fax",
    "email1",
    "email2",
    "email3",
    "visMail",
    "validStart",
    "validEnd",
    "consultationFlag"
})
@XmlSeeAlso({
    AuthorityType.class
})
public class AuthorityBaseType {

    /**
     * Description: The country where the authority is located. A code table value.
     * 
     */
    @XmlElement(name = "Country", required = true)
    protected String country;
    /**
     * Description: A unique identificator of an Authority.
     * 
     */
    @XmlElement(name = "AuthorityUniqueId", required = true)
    protected String authorityUniqueId;
    /**
     * Description: A value referencing a location of an Authority.
     * 
     */
    @XmlElement(name = "Location", required = true)
    protected String location;
    /**
     * Description: Official name of authority.
     * 
     */
    @XmlElement(name = "Name", required = true)
    protected String name;
    /**
     * Description: A value referencing a Authority type.
     * 
     */
    @XmlElement(name = "AuthorityType", required = true)
    protected String authorityType;
    @XmlElement(name = "AddressLine1")
    protected String addressLine1;
    @XmlElement(name = "AddressLine2")
    protected String addressLine2;
    /**
     * Description: PostCode of Authority.
     * 
     */
    @XmlElement(name = "PostCode")
    protected String postCode;
    @XmlElement(name = "Landline1")
    protected String landline1;
    @XmlElement(name = "Landline2")
    protected String landline2;
    @XmlElement(name = "Landline3")
    protected String landline3;
    @XmlElement(name = "Mobile")
    protected String mobile;
    @XmlElement(name = "Fax")
    protected String fax;
    @XmlElement(name = "Email1")
    protected String email1;
    @XmlElement(name = "Email2")
    protected String email2;
    @XmlElement(name = "Email3")
    protected String email3;
    /**
     * Description: The Authority's VISMail email address.
     * 
     */
    @XmlElement(name = "VISMail")
    protected String visMail;
    @XmlElement(name = "ValidStart", required = true)
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar validStart;
    @XmlElement(name = "ValidEnd", required = true)
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar validEnd;
    /**
     * Description: Only one authority can be designated for consultation and notification purposes, and have this flag set to TRUE
     * 
     */
    @XmlElement(name = "ConsultationFlag")
    protected boolean consultationFlag;

    /**
     * Description: The country where the authority is located. A code table value.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCountry() {
        return country;
    }

    /**
     * Imposta il valore della proprietà country.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     * @see #getCountry()
     */
    public void setCountry(String value) {
        this.country = value;
    }

    /**
     * Description: A unique identificator of an Authority.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAuthorityUniqueId() {
        return authorityUniqueId;
    }

    /**
     * Imposta il valore della proprietà authorityUniqueId.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     * @see #getAuthorityUniqueId()
     */
    public void setAuthorityUniqueId(String value) {
        this.authorityUniqueId = value;
    }

    /**
     * Description: A value referencing a location of an Authority.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLocation() {
        return location;
    }

    /**
     * Imposta il valore della proprietà location.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     * @see #getLocation()
     */
    public void setLocation(String value) {
        this.location = value;
    }

    /**
     * Description: Official name of authority.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getName() {
        return name;
    }

    /**
     * Imposta il valore della proprietà name.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     * @see #getName()
     */
    public void setName(String value) {
        this.name = value;
    }

    /**
     * Description: A value referencing a Authority type.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAuthorityType() {
        return authorityType;
    }

    /**
     * Imposta il valore della proprietà authorityType.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     * @see #getAuthorityType()
     */
    public void setAuthorityType(String value) {
        this.authorityType = value;
    }

    /**
     * Recupera il valore della proprietà addressLine1.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAddressLine1() {
        return addressLine1;
    }

    /**
     * Imposta il valore della proprietà addressLine1.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAddressLine1(String value) {
        this.addressLine1 = value;
    }

    /**
     * Recupera il valore della proprietà addressLine2.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAddressLine2() {
        return addressLine2;
    }

    /**
     * Imposta il valore della proprietà addressLine2.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAddressLine2(String value) {
        this.addressLine2 = value;
    }

    /**
     * Description: PostCode of Authority.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPostCode() {
        return postCode;
    }

    /**
     * Imposta il valore della proprietà postCode.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     * @see #getPostCode()
     */
    public void setPostCode(String value) {
        this.postCode = value;
    }

    /**
     * Recupera il valore della proprietà landline1.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLandline1() {
        return landline1;
    }

    /**
     * Imposta il valore della proprietà landline1.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLandline1(String value) {
        this.landline1 = value;
    }

    /**
     * Recupera il valore della proprietà landline2.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLandline2() {
        return landline2;
    }

    /**
     * Imposta il valore della proprietà landline2.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLandline2(String value) {
        this.landline2 = value;
    }

    /**
     * Recupera il valore della proprietà landline3.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLandline3() {
        return landline3;
    }

    /**
     * Imposta il valore della proprietà landline3.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLandline3(String value) {
        this.landline3 = value;
    }

    /**
     * Recupera il valore della proprietà mobile.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMobile() {
        return mobile;
    }

    /**
     * Imposta il valore della proprietà mobile.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMobile(String value) {
        this.mobile = value;
    }

    /**
     * Recupera il valore della proprietà fax.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFax() {
        return fax;
    }

    /**
     * Imposta il valore della proprietà fax.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFax(String value) {
        this.fax = value;
    }

    /**
     * Recupera il valore della proprietà email1.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEmail1() {
        return email1;
    }

    /**
     * Imposta il valore della proprietà email1.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEmail1(String value) {
        this.email1 = value;
    }

    /**
     * Recupera il valore della proprietà email2.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEmail2() {
        return email2;
    }

    /**
     * Imposta il valore della proprietà email2.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEmail2(String value) {
        this.email2 = value;
    }

    /**
     * Recupera il valore della proprietà email3.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEmail3() {
        return email3;
    }

    /**
     * Imposta il valore della proprietà email3.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEmail3(String value) {
        this.email3 = value;
    }

    /**
     * Description: The Authority's VISMail email address.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getVISMail() {
        return visMail;
    }

    /**
     * Imposta il valore della proprietà visMail.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     * @see #getVISMail()
     */
    public void setVISMail(String value) {
        this.visMail = value;
    }

    /**
     * Recupera il valore della proprietà validStart.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getValidStart() {
        return validStart;
    }

    /**
     * Imposta il valore della proprietà validStart.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setValidStart(XMLGregorianCalendar value) {
        this.validStart = value;
    }

    /**
     * Recupera il valore della proprietà validEnd.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getValidEnd() {
        return validEnd;
    }

    /**
     * Imposta il valore della proprietà validEnd.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setValidEnd(XMLGregorianCalendar value) {
        this.validEnd = value;
    }

    /**
     * Description: Only one authority can be designated for consultation and notification purposes, and have this flag set to TRUE
     * 
     */
    public boolean isConsultationFlag() {
        return consultationFlag;
    }

    /**
     * Imposta il valore della proprietà consultationFlag.
     * 
     */
    public void setConsultationFlag(boolean value) {
        this.consultationFlag = value;
    }

}
