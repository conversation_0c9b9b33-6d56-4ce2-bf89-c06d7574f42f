//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.vis.xsd.v3.types.application;

import java.util.ArrayList;
import java.util.List;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * Description: All alphanumeric data of the attachment.
 * 
 * <p>Classe Java per BiometricReferencesType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="BiometricReferencesType">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="FacialImageAttachmentIDs" minOccurs="0">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="FacialImageAttachmentID" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}AttachmentIDType" maxOccurs="unbounded"/>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *         <element name="FingerprintSetAttachmentIDs" minOccurs="0">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="FingerprintSetAttachmentID" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}AttachmentIDType" maxOccurs="unbounded"/>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "BiometricReferencesType", propOrder = {
    "facialImageAttachmentIDs",
    "fingerprintSetAttachmentIDs"
})
public class BiometricReferencesType {

    /**
     * Description: Reference of facial images.
     * 
     */
    @XmlElement(name = "FacialImageAttachmentIDs")
    protected BiometricReferencesType.FacialImageAttachmentIDs facialImageAttachmentIDs;
    /**
     * Description: Reference of fingerprintsets.
     * 
     */
    @XmlElement(name = "FingerprintSetAttachmentIDs")
    protected BiometricReferencesType.FingerprintSetAttachmentIDs fingerprintSetAttachmentIDs;

    /**
     * Description: Reference of facial images.
     * 
     * @return
     *     possible object is
     *     {@link BiometricReferencesType.FacialImageAttachmentIDs }
     *     
     */
    public BiometricReferencesType.FacialImageAttachmentIDs getFacialImageAttachmentIDs() {
        return facialImageAttachmentIDs;
    }

    /**
     * Imposta il valore della proprietà facialImageAttachmentIDs.
     * 
     * @param value
     *     allowed object is
     *     {@link BiometricReferencesType.FacialImageAttachmentIDs }
     *     
     * @see #getFacialImageAttachmentIDs()
     */
    public void setFacialImageAttachmentIDs(BiometricReferencesType.FacialImageAttachmentIDs value) {
        this.facialImageAttachmentIDs = value;
    }

    /**
     * Description: Reference of fingerprintsets.
     * 
     * @return
     *     possible object is
     *     {@link BiometricReferencesType.FingerprintSetAttachmentIDs }
     *     
     */
    public BiometricReferencesType.FingerprintSetAttachmentIDs getFingerprintSetAttachmentIDs() {
        return fingerprintSetAttachmentIDs;
    }

    /**
     * Imposta il valore della proprietà fingerprintSetAttachmentIDs.
     * 
     * @param value
     *     allowed object is
     *     {@link BiometricReferencesType.FingerprintSetAttachmentIDs }
     *     
     * @see #getFingerprintSetAttachmentIDs()
     */
    public void setFingerprintSetAttachmentIDs(BiometricReferencesType.FingerprintSetAttachmentIDs value) {
        this.fingerprintSetAttachmentIDs = value;
    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="FacialImageAttachmentID" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}AttachmentIDType" maxOccurs="unbounded"/>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "facialImageAttachmentID"
    })
    public static class FacialImageAttachmentIDs {

        /**
         * Description: The unique ID of an attachment. It is generated by the system at creation of the attachment.
         * 
         */
        @XmlElement(name = "FacialImageAttachmentID", type = Long.class)
        protected List<Long> facialImageAttachmentID;

        /**
         * Description: The unique ID of an attachment. It is generated by the system at creation of the attachment.
         * 
         * Gets the value of the facialImageAttachmentID property.
         * 
         * <p>This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the facialImageAttachmentID property.</p>
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * </p>
         * <pre>
         * getFacialImageAttachmentID().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link Long }
         * </p>
         * 
         * 
         * @return
         *     The value of the facialImageAttachmentID property.
         */
        public List<Long> getFacialImageAttachmentID() {
            if (facialImageAttachmentID == null) {
                facialImageAttachmentID = new ArrayList<>();
            }
            return this.facialImageAttachmentID;
        }

    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="FingerprintSetAttachmentID" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}AttachmentIDType" maxOccurs="unbounded"/>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "fingerprintSetAttachmentID"
    })
    public static class FingerprintSetAttachmentIDs {

        /**
         * Description: The unique ID of an attachment. It is generated by the system at creation of the attachment.
         * 
         */
        @XmlElement(name = "FingerprintSetAttachmentID", type = Long.class)
        protected List<Long> fingerprintSetAttachmentID;

        /**
         * Description: The unique ID of an attachment. It is generated by the system at creation of the attachment.
         * 
         * Gets the value of the fingerprintSetAttachmentID property.
         * 
         * <p>This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the fingerprintSetAttachmentID property.</p>
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * </p>
         * <pre>
         * getFingerprintSetAttachmentID().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link Long }
         * </p>
         * 
         * 
         * @return
         *     The value of the fingerprintSetAttachmentID property.
         */
        public List<Long> getFingerprintSetAttachmentID() {
            if (fingerprintSetAttachmentID == null) {
                fingerprintSetAttachmentID = new ArrayList<>();
            }
            return this.fingerprintSetAttachmentID;
        }

    }

}
