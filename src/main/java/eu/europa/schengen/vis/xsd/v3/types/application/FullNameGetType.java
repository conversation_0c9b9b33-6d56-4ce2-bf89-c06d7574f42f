//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.vis.xsd.v3.types.application;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * Decription: Name of a person it means surname and firstname.
 * 
 * <p>Classe Java per FullNameGetType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="FullNameGetType">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="Surname" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}TransTextType"/>
 *         <element name="Firstnames" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}TransTextType" minOccurs="0"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "FullNameGetType", propOrder = {
    "surname",
    "firstnames"
})
public class FullNameGetType {

    /**
     * Description: This attribute contains all surnames of a person. If a person happens to have more than one surname, the names are concatenated using a comma.
     * 
     */
    @XmlElement(name = "Surname", required = true)
    protected TransTextType surname;
    /**
     * Description: This attribute contains all first names of a person. If there is more than one first name, the names are concatenated using a comma.
     * 
     */
    @XmlElement(name = "Firstnames")
    protected TransTextType firstnames;

    /**
     * Description: This attribute contains all surnames of a person. If a person happens to have more than one surname, the names are concatenated using a comma.
     * 
     * @return
     *     possible object is
     *     {@link TransTextType }
     *     
     */
    public TransTextType getSurname() {
        return surname;
    }

    /**
     * Imposta il valore della proprietà surname.
     * 
     * @param value
     *     allowed object is
     *     {@link TransTextType }
     *     
     * @see #getSurname()
     */
    public void setSurname(TransTextType value) {
        this.surname = value;
    }

    /**
     * Description: This attribute contains all first names of a person. If there is more than one first name, the names are concatenated using a comma.
     * 
     * @return
     *     possible object is
     *     {@link TransTextType }
     *     
     */
    public TransTextType getFirstnames() {
        return firstnames;
    }

    /**
     * Imposta il valore della proprietà firstnames.
     * 
     * @param value
     *     allowed object is
     *     {@link TransTextType }
     *     
     * @see #getFirstnames()
     */
    public void setFirstnames(TransTextType value) {
        this.firstnames = value;
    }

}
