//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.vis.xsd.v3.types.application;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSeeAlso;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per ApplicationApplicantAbstractType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="ApplicationApplicantAbstractType">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="ApplicationNumber" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}ApplicationNumberType"/>
 *         <element name="ApplicantName" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}FullNameGetType"/>
 *         <element name="Sex" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}CT04_GenderType" minOccurs="0"/>
 *         <element name="DateOfBirth" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}PseudodateType"/>
 *         <element name="SurnameAtBirth" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}TransTextType" minOccurs="0"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ApplicationApplicantAbstractType", propOrder = {
    "applicationNumber",
    "applicantName",
    "sex",
    "dateOfBirth",
    "surnameAtBirth"
})
@XmlSeeAlso({
    ResultApplicantOverviewType.class
})
public class ApplicationApplicantAbstractType {

    /**
     * Description: ID of the Application.
     * 
     */
    @XmlElement(name = "ApplicationNumber", required = true)
    protected String applicationNumber;
    /**
     * Description: Name of the Applicant. Composed of the firstname and the surname.
     * 
     */
    @XmlElement(name = "ApplicantName", required = true)
    protected FullNameGetType applicantName;
    /**
     * Description: Gender of the applicant taken from a code table.
     * 
     */
    @XmlElement(name = "Sex")
    protected String sex;
    /**
     * Description: Date of birth.
     * 
     */
    @XmlElement(name = "DateOfBirth", required = true)
    protected String dateOfBirth;
    @XmlElement(name = "SurnameAtBirth")
    protected TransTextType surnameAtBirth;

    /**
     * Description: ID of the Application.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getApplicationNumber() {
        return applicationNumber;
    }

    /**
     * Imposta il valore della proprietà applicationNumber.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     * @see #getApplicationNumber()
     */
    public void setApplicationNumber(String value) {
        this.applicationNumber = value;
    }

    /**
     * Description: Name of the Applicant. Composed of the firstname and the surname.
     * 
     * @return
     *     possible object is
     *     {@link FullNameGetType }
     *     
     */
    public FullNameGetType getApplicantName() {
        return applicantName;
    }

    /**
     * Imposta il valore della proprietà applicantName.
     * 
     * @param value
     *     allowed object is
     *     {@link FullNameGetType }
     *     
     * @see #getApplicantName()
     */
    public void setApplicantName(FullNameGetType value) {
        this.applicantName = value;
    }

    /**
     * Description: Gender of the applicant taken from a code table.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSex() {
        return sex;
    }

    /**
     * Imposta il valore della proprietà sex.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     * @see #getSex()
     */
    public void setSex(String value) {
        this.sex = value;
    }

    /**
     * Description: Date of birth.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDateOfBirth() {
        return dateOfBirth;
    }

    /**
     * Imposta il valore della proprietà dateOfBirth.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     * @see #getDateOfBirth()
     */
    public void setDateOfBirth(String value) {
        this.dateOfBirth = value;
    }

    /**
     * Recupera il valore della proprietà surnameAtBirth.
     * 
     * @return
     *     possible object is
     *     {@link TransTextType }
     *     
     */
    public TransTextType getSurnameAtBirth() {
        return surnameAtBirth;
    }

    /**
     * Imposta il valore della proprietà surnameAtBirth.
     * 
     * @param value
     *     allowed object is
     *     {@link TransTextType }
     *     
     */
    public void setSurnameAtBirth(TransTextType value) {
        this.surnameAtBirth = value;
    }

}
