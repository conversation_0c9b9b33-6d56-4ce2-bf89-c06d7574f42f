//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.vis.xsd.v3.types.common;

import javax.xml.datatype.XMLGregorianCalendar;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlType;


/**
 * Description: Header of the message. 
 * 			In case of a Response message, all the fields that were originally sent are
 * 			set identical in the header except for the DateTime that is updated at the 
 * 			Central Site.
 * 
 * <p>Classe Java per HeaderType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="HeaderType">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="MessageID" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}MessageIDType"/>
 *         <element name="LogicalSessionID" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}LogicalSessionIDType"/>
 *         <element name="DateTime" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}TimeStampType"/>
 *         <element name="EndUserID" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}EndUserIDType" minOccurs="0"/>
 *         <element name="User" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}CT70_UserType"/>
 *         <element name="EndUserRole" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}ST12_EndUserRoleType"/>
 *         <element name="SystemID" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}SystemIDType"/>
 *         <element name="Contract" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}ST18_ContractType"/>
 *         <element name="Operation" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}ST19_OperationType"/>
 *         <element name="RepresentedUser" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}CT70_UserType" minOccurs="0"/>
 *         <element name="Variant" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}ST21_VariantType" minOccurs="0"/>
 *         <element name="TestCaseID" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}TestCaseIDType" minOccurs="0"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "HeaderType", propOrder = {
    "messageID",
    "logicalSessionID",
    "dateTime",
    "endUserID",
    "user",
    "endUserRole",
    "systemID",
    "contract",
    "operation",
    "representedUser",
    "variant",
    "testCaseID"
})
public class HeaderType {

    /**
     * Description: identifier of the Message.
     * 
     */
    @XmlElement(name = "MessageID", required = true)
    protected String messageID;
    /**
     * Description: identifier of the Logical Session.
     * 
     */
    @XmlElement(name = "LogicalSessionID", required = true)
    protected String logicalSessionID;
    /**
     * Description: This element defines the message issuing date time.
     * 
     */
    @XmlElement(name = "DateTime", required = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar dateTime;
    /**
     * Description: The EndUser is the application or officer acting for a USER. The VIS application do not manage the End-Users. It is the responsibility of the USER to authenticate its EndUser. An EndUser-ID may be present in the message header for USER usage only. The USER has the responsibility to provide the appropriate EndUser-ID.
     * 
     */
    @XmlElement(name = "EndUserID")
    protected String endUserID;
    /**
     * Description: Identification of the User issuing the message.
     * 
     */
    @XmlElement(name = "User", required = true)
    protected String user;
    /**
     * Description: EndUserRole.
     * 
     */
    @XmlElement(name = "EndUserRole", required = true)
    protected String endUserRole;
    /**
     * Description: Identification of the system that sends the message.
     * 
     */
    @XmlElement(name = "SystemID", required = true)
    protected String systemID;
    /**
     * Description: Name of the contract.
     * 
     */
    @XmlElement(name = "Contract", required = true)
    protected String contract;
    /**
     * Description: Name of the operation.
     * 
     */
    @XmlElement(name = "Operation", required = true)
    protected String operation;
    /**
     * Description: In case the User acts on behalf of an other User. This other User is called the Represented User and must be fill in this field.
     * 
     */
    @XmlElement(name = "RepresentedUser")
    protected String representedUser;
    /**
     * Description: Variant of the operation.
     * 
     */
    @XmlElement(name = "Variant")
    protected String variant;
    /**
     * Description: This field will be assigned by the MS for incoming messages with the value of the corresponding Test Case ID as indicated by the TDD. For outgoing messages, the value will be assigned by the CS-VIS with the value available in the incoming message. It will serve for the evaluation of the test execution with the log evaluator.
     * 
     */
    @XmlElement(name = "TestCaseID")
    protected String testCaseID;

    /**
     * Description: identifier of the Message.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMessageID() {
        return messageID;
    }

    /**
     * Imposta il valore della proprietà messageID.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     * @see #getMessageID()
     */
    public void setMessageID(String value) {
        this.messageID = value;
    }

    /**
     * Description: identifier of the Logical Session.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLogicalSessionID() {
        return logicalSessionID;
    }

    /**
     * Imposta il valore della proprietà logicalSessionID.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     * @see #getLogicalSessionID()
     */
    public void setLogicalSessionID(String value) {
        this.logicalSessionID = value;
    }

    /**
     * Description: This element defines the message issuing date time.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getDateTime() {
        return dateTime;
    }

    /**
     * Imposta il valore della proprietà dateTime.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     * @see #getDateTime()
     */
    public void setDateTime(XMLGregorianCalendar value) {
        this.dateTime = value;
    }

    /**
     * Description: The EndUser is the application or officer acting for a USER. The VIS application do not manage the End-Users. It is the responsibility of the USER to authenticate its EndUser. An EndUser-ID may be present in the message header for USER usage only. The USER has the responsibility to provide the appropriate EndUser-ID.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEndUserID() {
        return endUserID;
    }

    /**
     * Imposta il valore della proprietà endUserID.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     * @see #getEndUserID()
     */
    public void setEndUserID(String value) {
        this.endUserID = value;
    }

    /**
     * Description: Identification of the User issuing the message.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUser() {
        return user;
    }

    /**
     * Imposta il valore della proprietà user.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     * @see #getUser()
     */
    public void setUser(String value) {
        this.user = value;
    }

    /**
     * Description: EndUserRole.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEndUserRole() {
        return endUserRole;
    }

    /**
     * Imposta il valore della proprietà endUserRole.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     * @see #getEndUserRole()
     */
    public void setEndUserRole(String value) {
        this.endUserRole = value;
    }

    /**
     * Description: Identification of the system that sends the message.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSystemID() {
        return systemID;
    }

    /**
     * Imposta il valore della proprietà systemID.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     * @see #getSystemID()
     */
    public void setSystemID(String value) {
        this.systemID = value;
    }

    /**
     * Description: Name of the contract.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getContract() {
        return contract;
    }

    /**
     * Imposta il valore della proprietà contract.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     * @see #getContract()
     */
    public void setContract(String value) {
        this.contract = value;
    }

    /**
     * Description: Name of the operation.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getOperation() {
        return operation;
    }

    /**
     * Imposta il valore della proprietà operation.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     * @see #getOperation()
     */
    public void setOperation(String value) {
        this.operation = value;
    }

    /**
     * Description: In case the User acts on behalf of an other User. This other User is called the Represented User and must be fill in this field.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRepresentedUser() {
        return representedUser;
    }

    /**
     * Imposta il valore della proprietà representedUser.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     * @see #getRepresentedUser()
     */
    public void setRepresentedUser(String value) {
        this.representedUser = value;
    }

    /**
     * Description: Variant of the operation.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getVariant() {
        return variant;
    }

    /**
     * Imposta il valore della proprietà variant.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     * @see #getVariant()
     */
    public void setVariant(String value) {
        this.variant = value;
    }

    /**
     * Description: This field will be assigned by the MS for incoming messages with the value of the corresponding Test Case ID as indicated by the TDD. For outgoing messages, the value will be assigned by the CS-VIS with the value available in the incoming message. It will serve for the evaluation of the test execution with the log evaluator.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTestCaseID() {
        return testCaseID;
    }

    /**
     * Imposta il valore della proprietà testCaseID.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     * @see #getTestCaseID()
     */
    public void setTestCaseID(String value) {
        this.testCaseID = value;
    }

}
