//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.vis.xsd.v3.types.application;

import java.util.ArrayList;
import java.util.List;
import javax.xml.datatype.XMLGregorianCalendar;
import eu.europa.schengen.vis.xsd.v3.types.common.AuthorityResponseType;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlType;


/**
 * Description: Overview on the information about an asylum responsibility.
 * 
 * <p>Classe Java per ResultIdentificationOverviewType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="ResultIdentificationOverviewType">
 *   <complexContent>
 *     <extension base="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}ResultOverviewBaseType">
 *       <sequence>
 *         <element name="ApplicationAuthority" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}AuthorityResponseType"/>
 *         <element name="ApplicationStatus" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}ST31_ApplicationStatusType"/>
 *         <element name="DateOfApplication" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}TimeStampType" minOccurs="0"/>
 *         <element name="MemberStatesOfDestination" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}MemberStatesOfDestinationType" minOccurs="0"/>
 *         <element name="NationalityForApplication" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}CT02_CountryOfNationalityType" minOccurs="0"/>
 *         <element name="MainPurposesOfJourney" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}MainPurposesOfJourneyGetType" minOccurs="0"/>
 *         <element name="VisaStickerNumber" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}VisaStickerNumberType" minOccurs="0"/>
 *         <element name="VisaType" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}VisaTypeType" minOccurs="0"/>
 *         <element name="GroupSynopses" minOccurs="0">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="GroupSynopsis" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}GroupSynopsisType" maxOccurs="unbounded"/>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *       </sequence>
 *     </extension>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ResultIdentificationOverviewType", propOrder = {
    "applicationAuthority",
    "applicationStatus",
    "dateOfApplication",
    "memberStatesOfDestination",
    "nationalityForApplication",
    "mainPurposesOfJourney",
    "visaStickerNumber",
    "visaType",
    "groupSynopses"
})
public class ResultIdentificationOverviewType
    extends ResultOverviewBaseType
{

    /**
     * Description: The authority which lodged the Application.
     * 
     */
    @XmlElement(name = "ApplicationAuthority", required = true)
    protected AuthorityResponseType applicationAuthority;
    /**
     * Description: Status of the application (code table value). This Status is the outcome of the decision history.
     * 
     */
    @XmlElement(name = "ApplicationStatus", required = true)
    protected String applicationStatus;
    /**
     * Description: The date of request is taken from the application form.
     * 
     */
    @XmlElement(name = "DateOfApplication")
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar dateOfApplication;
    /**
     * Description: The member states destination is taken from the application form.
     * 
     */
    @XmlElement(name = "MemberStatesOfDestination")
    protected MemberStatesOfDestinationType memberStatesOfDestination;
    /**
     * Description: This nationality is used for the application.
     * 
     */
    @XmlElement(name = "NationalityForApplication")
    protected String nationalityForApplication;
    @XmlElement(name = "MainPurposesOfJourney")
    protected MainPurposesOfJourneyGetType mainPurposesOfJourney;
    /**
     * Description: This attribute uniquely identifies a visa sticker. 
     * A visa sticker number consists of one to three characters indicating the country and a number that consists of up to 30 digits.
     * 
     */
    @XmlElement(name = "VisaStickerNumber")
    protected String visaStickerNumber;
    /**
     * Description: The visa type as printed on the visa sticker.
     * 
     */
    @XmlElement(name = "VisaType")
    protected VisaTypeType visaType;
    @XmlElement(name = "GroupSynopses")
    protected ResultIdentificationOverviewType.GroupSynopses groupSynopses;

    /**
     * Description: The authority which lodged the Application.
     * 
     * @return
     *     possible object is
     *     {@link AuthorityResponseType }
     *     
     */
    public AuthorityResponseType getApplicationAuthority() {
        return applicationAuthority;
    }

    /**
     * Imposta il valore della proprietà applicationAuthority.
     * 
     * @param value
     *     allowed object is
     *     {@link AuthorityResponseType }
     *     
     * @see #getApplicationAuthority()
     */
    public void setApplicationAuthority(AuthorityResponseType value) {
        this.applicationAuthority = value;
    }

    /**
     * Description: Status of the application (code table value). This Status is the outcome of the decision history.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getApplicationStatus() {
        return applicationStatus;
    }

    /**
     * Imposta il valore della proprietà applicationStatus.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     * @see #getApplicationStatus()
     */
    public void setApplicationStatus(String value) {
        this.applicationStatus = value;
    }

    /**
     * Description: The date of request is taken from the application form.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getDateOfApplication() {
        return dateOfApplication;
    }

    /**
     * Imposta il valore della proprietà dateOfApplication.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     * @see #getDateOfApplication()
     */
    public void setDateOfApplication(XMLGregorianCalendar value) {
        this.dateOfApplication = value;
    }

    /**
     * Description: The member states destination is taken from the application form.
     * 
     * @return
     *     possible object is
     *     {@link MemberStatesOfDestinationType }
     *     
     */
    public MemberStatesOfDestinationType getMemberStatesOfDestination() {
        return memberStatesOfDestination;
    }

    /**
     * Imposta il valore della proprietà memberStatesOfDestination.
     * 
     * @param value
     *     allowed object is
     *     {@link MemberStatesOfDestinationType }
     *     
     * @see #getMemberStatesOfDestination()
     */
    public void setMemberStatesOfDestination(MemberStatesOfDestinationType value) {
        this.memberStatesOfDestination = value;
    }

    /**
     * Description: This nationality is used for the application.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNationalityForApplication() {
        return nationalityForApplication;
    }

    /**
     * Imposta il valore della proprietà nationalityForApplication.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     * @see #getNationalityForApplication()
     */
    public void setNationalityForApplication(String value) {
        this.nationalityForApplication = value;
    }

    /**
     * Recupera il valore della proprietà mainPurposesOfJourney.
     * 
     * @return
     *     possible object is
     *     {@link MainPurposesOfJourneyGetType }
     *     
     */
    public MainPurposesOfJourneyGetType getMainPurposesOfJourney() {
        return mainPurposesOfJourney;
    }

    /**
     * Imposta il valore della proprietà mainPurposesOfJourney.
     * 
     * @param value
     *     allowed object is
     *     {@link MainPurposesOfJourneyGetType }
     *     
     */
    public void setMainPurposesOfJourney(MainPurposesOfJourneyGetType value) {
        this.mainPurposesOfJourney = value;
    }

    /**
     * Description: This attribute uniquely identifies a visa sticker. 
     * A visa sticker number consists of one to three characters indicating the country and a number that consists of up to 30 digits.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getVisaStickerNumber() {
        return visaStickerNumber;
    }

    /**
     * Imposta il valore della proprietà visaStickerNumber.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     * @see #getVisaStickerNumber()
     */
    public void setVisaStickerNumber(String value) {
        this.visaStickerNumber = value;
    }

    /**
     * Description: The visa type as printed on the visa sticker.
     * 
     * @return
     *     possible object is
     *     {@link VisaTypeType }
     *     
     */
    public VisaTypeType getVisaType() {
        return visaType;
    }

    /**
     * Imposta il valore della proprietà visaType.
     * 
     * @param value
     *     allowed object is
     *     {@link VisaTypeType }
     *     
     * @see #getVisaType()
     */
    public void setVisaType(VisaTypeType value) {
        this.visaType = value;
    }

    /**
     * Recupera il valore della proprietà groupSynopses.
     * 
     * @return
     *     possible object is
     *     {@link ResultIdentificationOverviewType.GroupSynopses }
     *     
     */
    public ResultIdentificationOverviewType.GroupSynopses getGroupSynopses() {
        return groupSynopses;
    }

    /**
     * Imposta il valore della proprietà groupSynopses.
     * 
     * @param value
     *     allowed object is
     *     {@link ResultIdentificationOverviewType.GroupSynopses }
     *     
     */
    public void setGroupSynopses(ResultIdentificationOverviewType.GroupSynopses value) {
        this.groupSynopses = value;
    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="GroupSynopsis" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}GroupSynopsisType" maxOccurs="unbounded"/>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "groupSynopsis"
    })
    public static class GroupSynopses {

        @XmlElement(name = "GroupSynopsis", required = true)
        protected List<GroupSynopsisType> groupSynopsis;

        /**
         * Gets the value of the groupSynopsis property.
         * 
         * <p>This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the groupSynopsis property.</p>
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * </p>
         * <pre>
         * getGroupSynopsis().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link GroupSynopsisType }
         * </p>
         * 
         * 
         * @return
         *     The value of the groupSynopsis property.
         */
        public List<GroupSynopsisType> getGroupSynopsis() {
            if (groupSynopsis == null) {
                groupSynopsis = new ArrayList<>();
            }
            return this.groupSynopsis;
        }

    }

}
