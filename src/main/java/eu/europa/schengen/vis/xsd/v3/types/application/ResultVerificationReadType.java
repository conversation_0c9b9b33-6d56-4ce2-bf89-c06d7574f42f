//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.vis.xsd.v3.types.application;

import java.util.ArrayList;
import java.util.List;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * Description: Result of the retrieve for verification.
 * 
 * <p>Classe Java per ResultVerificationReadType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="ResultVerificationReadType">
 *   <complexContent>
 *     <extension base="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}ResultReadApplicationBaseType">
 *       <sequence>
 *         <element name="ApplicationData">
 *           <complexType>
 *             <complexContent>
 *               <extension base="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}ResultReadCoreDataType">
 *                 <sequence>
 *                   <element name="ApplicationNumber" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}ApplicationNumberType"/>
 *                   <element name="ApplicationStatus" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}ST31_ApplicationStatusType"/>
 *                 </sequence>
 *               </extension>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *         <element name="Decisions" minOccurs="0">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="Decision" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}VerificationDecisionHistGetType" maxOccurs="unbounded"/>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *         <element name="FacialImages" minOccurs="0">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="FacialImage" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}FacialImageGetType" maxOccurs="unbounded"/>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *         <element name="GroupSynopses" minOccurs="0">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="GroupSynopsis" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}GroupSynopsisType" maxOccurs="unbounded"/>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *       </sequence>
 *     </extension>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ResultVerificationReadType", propOrder = {
    "applicationData",
    "decisions",
    "facialImages",
    "groupSynopses"
})
public class ResultVerificationReadType
    extends ResultReadApplicationBaseType
{

    /**
     * Description: This element is a subset of the Application.
     * 
     */
    @XmlElement(name = "ApplicationData", required = true)
    protected ResultVerificationReadType.ApplicationData applicationData;
    /**
     * Description: Decision history of the application.
     * 
     */
    @XmlElement(name = "Decisions")
    protected ResultVerificationReadType.Decisions decisions;
    /**
     * Description: The facial images from the Application.
     * 
     */
    @XmlElement(name = "FacialImages")
    protected ResultVerificationReadType.FacialImages facialImages;
    /**
     * Description: List of the groups the Application belongs to.
     * 
     */
    @XmlElement(name = "GroupSynopses")
    protected ResultVerificationReadType.GroupSynopses groupSynopses;

    /**
     * Description: This element is a subset of the Application.
     * 
     * @return
     *     possible object is
     *     {@link ResultVerificationReadType.ApplicationData }
     *     
     */
    public ResultVerificationReadType.ApplicationData getApplicationData() {
        return applicationData;
    }

    /**
     * Imposta il valore della proprietà applicationData.
     * 
     * @param value
     *     allowed object is
     *     {@link ResultVerificationReadType.ApplicationData }
     *     
     * @see #getApplicationData()
     */
    public void setApplicationData(ResultVerificationReadType.ApplicationData value) {
        this.applicationData = value;
    }

    /**
     * Description: Decision history of the application.
     * 
     * @return
     *     possible object is
     *     {@link ResultVerificationReadType.Decisions }
     *     
     */
    public ResultVerificationReadType.Decisions getDecisions() {
        return decisions;
    }

    /**
     * Imposta il valore della proprietà decisions.
     * 
     * @param value
     *     allowed object is
     *     {@link ResultVerificationReadType.Decisions }
     *     
     * @see #getDecisions()
     */
    public void setDecisions(ResultVerificationReadType.Decisions value) {
        this.decisions = value;
    }

    /**
     * Description: The facial images from the Application.
     * 
     * @return
     *     possible object is
     *     {@link ResultVerificationReadType.FacialImages }
     *     
     */
    public ResultVerificationReadType.FacialImages getFacialImages() {
        return facialImages;
    }

    /**
     * Imposta il valore della proprietà facialImages.
     * 
     * @param value
     *     allowed object is
     *     {@link ResultVerificationReadType.FacialImages }
     *     
     * @see #getFacialImages()
     */
    public void setFacialImages(ResultVerificationReadType.FacialImages value) {
        this.facialImages = value;
    }

    /**
     * Description: List of the groups the Application belongs to.
     * 
     * @return
     *     possible object is
     *     {@link ResultVerificationReadType.GroupSynopses }
     *     
     */
    public ResultVerificationReadType.GroupSynopses getGroupSynopses() {
        return groupSynopses;
    }

    /**
     * Imposta il valore della proprietà groupSynopses.
     * 
     * @param value
     *     allowed object is
     *     {@link ResultVerificationReadType.GroupSynopses }
     *     
     * @see #getGroupSynopses()
     */
    public void setGroupSynopses(ResultVerificationReadType.GroupSynopses value) {
        this.groupSynopses = value;
    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <extension base="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}ResultReadCoreDataType">
     *       <sequence>
     *         <element name="ApplicationNumber" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}ApplicationNumberType"/>
     *         <element name="ApplicationStatus" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}ST31_ApplicationStatusType"/>
     *       </sequence>
     *     </extension>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "applicationNumber",
        "applicationStatus"
    })
    public static class ApplicationData
        extends ResultReadCoreDataType
    {

        /**
         * Description: Unique identifier of an application. The application number is generated and submitted by a User.
         * 
         */
        @XmlElement(name = "ApplicationNumber", required = true)
        protected String applicationNumber;
        /**
         * Description: Status of the application (code table value). This Status is the outcome of the decision history.
         * 
         */
        @XmlElement(name = "ApplicationStatus", required = true)
        protected String applicationStatus;

        /**
         * Description: Unique identifier of an application. The application number is generated and submitted by a User.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getApplicationNumber() {
            return applicationNumber;
        }

        /**
         * Imposta il valore della proprietà applicationNumber.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         * @see #getApplicationNumber()
         */
        public void setApplicationNumber(String value) {
            this.applicationNumber = value;
        }

        /**
         * Description: Status of the application (code table value). This Status is the outcome of the decision history.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getApplicationStatus() {
            return applicationStatus;
        }

        /**
         * Imposta il valore della proprietà applicationStatus.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         * @see #getApplicationStatus()
         */
        public void setApplicationStatus(String value) {
            this.applicationStatus = value;
        }

    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="Decision" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}VerificationDecisionHistGetType" maxOccurs="unbounded"/>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "decision"
    })
    public static class Decisions {

        @XmlElement(name = "Decision", required = true)
        protected List<VerificationDecisionHistGetType> decision;

        /**
         * Gets the value of the decision property.
         * 
         * <p>This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the decision property.</p>
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * </p>
         * <pre>
         * getDecision().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link VerificationDecisionHistGetType }
         * </p>
         * 
         * 
         * @return
         *     The value of the decision property.
         */
        public List<VerificationDecisionHistGetType> getDecision() {
            if (decision == null) {
                decision = new ArrayList<>();
            }
            return this.decision;
        }

    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="FacialImage" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}FacialImageGetType" maxOccurs="unbounded"/>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "facialImage"
    })
    public static class FacialImages {

        @XmlElement(name = "FacialImage", required = true)
        protected List<FacialImageGetType> facialImage;

        /**
         * Gets the value of the facialImage property.
         * 
         * <p>This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the facialImage property.</p>
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * </p>
         * <pre>
         * getFacialImage().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link FacialImageGetType }
         * </p>
         * 
         * 
         * @return
         *     The value of the facialImage property.
         */
        public List<FacialImageGetType> getFacialImage() {
            if (facialImage == null) {
                facialImage = new ArrayList<>();
            }
            return this.facialImage;
        }

    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="GroupSynopsis" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}GroupSynopsisType" maxOccurs="unbounded"/>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "groupSynopsis"
    })
    public static class GroupSynopses {

        @XmlElement(name = "GroupSynopsis", required = true)
        protected List<GroupSynopsisType> groupSynopsis;

        /**
         * Gets the value of the groupSynopsis property.
         * 
         * <p>This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the groupSynopsis property.</p>
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * </p>
         * <pre>
         * getGroupSynopsis().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link GroupSynopsisType }
         * </p>
         * 
         * 
         * @return
         *     The value of the groupSynopsis property.
         */
        public List<GroupSynopsisType> getGroupSynopsis() {
            if (groupSynopsis == null) {
                groupSynopsis = new ArrayList<>();
            }
            return this.groupSynopsis;
        }

    }

}
