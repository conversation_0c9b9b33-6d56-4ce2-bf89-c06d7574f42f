//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.vis.xsd.v3.types.application;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSeeAlso;
import jakarta.xml.bind.annotation.XmlType;


/**
 * Description: Result of the retrieval of an application.
 * 
 * <p>Classe Java per ResultApplicationFileType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="ResultApplicationFileType">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="ApplicationNumber" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}ApplicationNumberType"/>
 *         <element name="Application" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}ApplicationGetType"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ResultApplicationFileType", propOrder = {
    "applicationNumber",
    "application"
})
@XmlSeeAlso({
    ResultApplicationWithFullDecisionType.class
})
public class ResultApplicationFileType {

    /**
     * Description: ID of the Application.
     * 
     */
    @XmlElement(name = "ApplicationNumber", required = true)
    protected String applicationNumber;
    /**
     * Description: This element contains the full information on the Application.
     * 
     */
    @XmlElement(name = "Application", required = true)
    protected ApplicationGetType application;

    /**
     * Description: ID of the Application.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getApplicationNumber() {
        return applicationNumber;
    }

    /**
     * Imposta il valore della proprietà applicationNumber.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     * @see #getApplicationNumber()
     */
    public void setApplicationNumber(String value) {
        this.applicationNumber = value;
    }

    /**
     * Description: This element contains the full information on the Application.
     * 
     * @return
     *     possible object is
     *     {@link ApplicationGetType }
     *     
     */
    public ApplicationGetType getApplication() {
        return application;
    }

    /**
     * Imposta il valore della proprietà application.
     * 
     * @param value
     *     allowed object is
     *     {@link ApplicationGetType }
     *     
     * @see #getApplication()
     */
    public void setApplication(ApplicationGetType value) {
        this.application = value;
    }

}
