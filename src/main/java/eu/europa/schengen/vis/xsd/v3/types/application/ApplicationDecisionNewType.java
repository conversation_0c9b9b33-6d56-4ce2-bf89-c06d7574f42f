//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.vis.xsd.v3.types.application;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * Description: Type used for the creation of an application decision.
 * 
 * <p>Classe Java per ApplicationDecisionNewType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="ApplicationDecisionNewType">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <choice>
 *         <element name="RefuseVisa" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}RefuseVisaNewType"/>
 *         <element name="DiscontinueExamination" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}DiscontinueExaminationNewType"/>
 *         <element name="CloseApplication" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}CloseApplicationNewType"/>
 *         <element name="GrantVisa" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}GrantVisaNewType"/>
 *       </choice>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ApplicationDecisionNewType", propOrder = {
    "refuseVisa",
    "discontinueExamination",
    "closeApplication",
    "grantVisa"
})
public class ApplicationDecisionNewType {

    /**
     * Description: All information needed for the decision "REFUSE VISA".
     * 
     */
    @XmlElement(name = "RefuseVisa")
    protected RefuseVisaNewType refuseVisa;
    /**
     * Description: All information needed for the decision "RefuseExamination".
     * 
     */
    @XmlElement(name = "DiscontinueExamination")
    protected DiscontinueExaminationNewType discontinueExamination;
    /**
     * Description: All information needed for the decision "WithDrawApplication".
     * 
     */
    @XmlElement(name = "CloseApplication")
    protected CloseApplicationNewType closeApplication;
    /**
     * Description: Decision GrantVisa.
     * 
     */
    @XmlElement(name = "GrantVisa")
    protected GrantVisaNewType grantVisa;

    /**
     * Description: All information needed for the decision "REFUSE VISA".
     * 
     * @return
     *     possible object is
     *     {@link RefuseVisaNewType }
     *     
     */
    public RefuseVisaNewType getRefuseVisa() {
        return refuseVisa;
    }

    /**
     * Imposta il valore della proprietà refuseVisa.
     * 
     * @param value
     *     allowed object is
     *     {@link RefuseVisaNewType }
     *     
     * @see #getRefuseVisa()
     */
    public void setRefuseVisa(RefuseVisaNewType value) {
        this.refuseVisa = value;
    }

    /**
     * Description: All information needed for the decision "RefuseExamination".
     * 
     * @return
     *     possible object is
     *     {@link DiscontinueExaminationNewType }
     *     
     */
    public DiscontinueExaminationNewType getDiscontinueExamination() {
        return discontinueExamination;
    }

    /**
     * Imposta il valore della proprietà discontinueExamination.
     * 
     * @param value
     *     allowed object is
     *     {@link DiscontinueExaminationNewType }
     *     
     * @see #getDiscontinueExamination()
     */
    public void setDiscontinueExamination(DiscontinueExaminationNewType value) {
        this.discontinueExamination = value;
    }

    /**
     * Description: All information needed for the decision "WithDrawApplication".
     * 
     * @return
     *     possible object is
     *     {@link CloseApplicationNewType }
     *     
     */
    public CloseApplicationNewType getCloseApplication() {
        return closeApplication;
    }

    /**
     * Imposta il valore della proprietà closeApplication.
     * 
     * @param value
     *     allowed object is
     *     {@link CloseApplicationNewType }
     *     
     * @see #getCloseApplication()
     */
    public void setCloseApplication(CloseApplicationNewType value) {
        this.closeApplication = value;
    }

    /**
     * Description: Decision GrantVisa.
     * 
     * @return
     *     possible object is
     *     {@link GrantVisaNewType }
     *     
     */
    public GrantVisaNewType getGrantVisa() {
        return grantVisa;
    }

    /**
     * Imposta il valore della proprietà grantVisa.
     * 
     * @param value
     *     allowed object is
     *     {@link GrantVisaNewType }
     *     
     * @see #getGrantVisa()
     */
    public void setGrantVisa(GrantVisaNewType value) {
        this.grantVisa = value;
    }

}
