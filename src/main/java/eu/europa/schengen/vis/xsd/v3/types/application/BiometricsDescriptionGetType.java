//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.vis.xsd.v3.types.application;

import java.util.ArrayList;
import java.util.List;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * Description: Response for a Application examination retrieving.
 * 
 * <p>Classe Java per BiometricsDescriptionGetType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="BiometricsDescriptionGetType">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="FacialImages" minOccurs="0">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="FacialImage" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}FacialImageReturnType" maxOccurs="unbounded"/>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *         <element name="FingerprintSets" minOccurs="0">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="FingerprintSet" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}FingerprintSetReturnType" maxOccurs="unbounded"/>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "BiometricsDescriptionGetType", propOrder = {
    "facialImages",
    "fingerprintSets"
})
public class BiometricsDescriptionGetType {

    /**
     * Description: Facial Images from the Application.
     * 
     */
    @XmlElement(name = "FacialImages")
    protected BiometricsDescriptionGetType.FacialImages facialImages;
    /**
     * Description: Fingerprint sets from the Application.
     * 
     */
    @XmlElement(name = "FingerprintSets")
    protected BiometricsDescriptionGetType.FingerprintSets fingerprintSets;

    /**
     * Description: Facial Images from the Application.
     * 
     * @return
     *     possible object is
     *     {@link BiometricsDescriptionGetType.FacialImages }
     *     
     */
    public BiometricsDescriptionGetType.FacialImages getFacialImages() {
        return facialImages;
    }

    /**
     * Imposta il valore della proprietà facialImages.
     * 
     * @param value
     *     allowed object is
     *     {@link BiometricsDescriptionGetType.FacialImages }
     *     
     * @see #getFacialImages()
     */
    public void setFacialImages(BiometricsDescriptionGetType.FacialImages value) {
        this.facialImages = value;
    }

    /**
     * Description: Fingerprint sets from the Application.
     * 
     * @return
     *     possible object is
     *     {@link BiometricsDescriptionGetType.FingerprintSets }
     *     
     */
    public BiometricsDescriptionGetType.FingerprintSets getFingerprintSets() {
        return fingerprintSets;
    }

    /**
     * Imposta il valore della proprietà fingerprintSets.
     * 
     * @param value
     *     allowed object is
     *     {@link BiometricsDescriptionGetType.FingerprintSets }
     *     
     * @see #getFingerprintSets()
     */
    public void setFingerprintSets(BiometricsDescriptionGetType.FingerprintSets value) {
        this.fingerprintSets = value;
    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="FacialImage" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}FacialImageReturnType" maxOccurs="unbounded"/>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "facialImage"
    })
    public static class FacialImages {

        @XmlElement(name = "FacialImage", required = true)
        protected List<FacialImageReturnType> facialImage;

        /**
         * Gets the value of the facialImage property.
         * 
         * <p>This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the facialImage property.</p>
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * </p>
         * <pre>
         * getFacialImage().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link FacialImageReturnType }
         * </p>
         * 
         * 
         * @return
         *     The value of the facialImage property.
         */
        public List<FacialImageReturnType> getFacialImage() {
            if (facialImage == null) {
                facialImage = new ArrayList<>();
            }
            return this.facialImage;
        }

    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="FingerprintSet" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}FingerprintSetReturnType" maxOccurs="unbounded"/>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "fingerprintSet"
    })
    public static class FingerprintSets {

        @XmlElement(name = "FingerprintSet", required = true)
        protected List<FingerprintSetReturnType> fingerprintSet;

        /**
         * Gets the value of the fingerprintSet property.
         * 
         * <p>This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the fingerprintSet property.</p>
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * </p>
         * <pre>
         * getFingerprintSet().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link FingerprintSetReturnType }
         * </p>
         * 
         * 
         * @return
         *     The value of the fingerprintSet property.
         */
        public List<FingerprintSetReturnType> getFingerprintSet() {
            if (fingerprintSet == null) {
                fingerprintSet = new ArrayList<>();
            }
            return this.fingerprintSet;
        }

    }

}
