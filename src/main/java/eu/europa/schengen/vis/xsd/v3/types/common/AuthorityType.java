//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.vis.xsd.v3.types.common;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * Description: The authority which lodged an application or created a decision.
 * 
 * <p>Classe Java per AuthorityType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="AuthorityType">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="AuthorityUniqueID" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}AuthorityUniqueIDType"/>
 *         <element name="AuthorityLocation" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}AuthorityPlaceType"/>
 *         <element name="AuthorityName" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}AuthorityNameType"/>
 *         <element name="AuthorityType" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}CT10_AuthorityTypeType"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "AuthorityType", propOrder = {
    "authorityUniqueID",
    "authorityLocation",
    "authorityName",
    "authorityType"
})
public class AuthorityType {

    /**
     * Description: Specific code provided by MS, uniquely identify the authority.
     * 
     */
    @XmlElement(name = "AuthorityUniqueID", required = true)
    protected String authorityUniqueID;
    /**
     * Description: The place where the authority is officially located. Consists of a country code and a AuthorityLocation code.
     * 
     */
    @XmlElement(name = "AuthorityLocation", required = true)
    protected AuthorityPlaceType authorityLocation;
    /**
     * Description: The name of the authority.
     * 
     */
    @XmlElement(name = "AuthorityName", required = true)
    protected String authorityName;
    /**
     * Description: A value that indicates the type of authority.
     * 
     */
    @XmlElement(name = "AuthorityType", required = true)
    protected String authorityType;

    /**
     * Description: Specific code provided by MS, uniquely identify the authority.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAuthorityUniqueID() {
        return authorityUniqueID;
    }

    /**
     * Imposta il valore della proprietà authorityUniqueID.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     * @see #getAuthorityUniqueID()
     */
    public void setAuthorityUniqueID(String value) {
        this.authorityUniqueID = value;
    }

    /**
     * Description: The place where the authority is officially located. Consists of a country code and a AuthorityLocation code.
     * 
     * @return
     *     possible object is
     *     {@link AuthorityPlaceType }
     *     
     */
    public AuthorityPlaceType getAuthorityLocation() {
        return authorityLocation;
    }

    /**
     * Imposta il valore della proprietà authorityLocation.
     * 
     * @param value
     *     allowed object is
     *     {@link AuthorityPlaceType }
     *     
     * @see #getAuthorityLocation()
     */
    public void setAuthorityLocation(AuthorityPlaceType value) {
        this.authorityLocation = value;
    }

    /**
     * Description: The name of the authority.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAuthorityName() {
        return authorityName;
    }

    /**
     * Imposta il valore della proprietà authorityName.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     * @see #getAuthorityName()
     */
    public void setAuthorityName(String value) {
        this.authorityName = value;
    }

    /**
     * Description: A value that indicates the type of authority.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAuthorityType() {
        return authorityType;
    }

    /**
     * Imposta il valore della proprietà authorityType.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     * @see #getAuthorityType()
     */
    public void setAuthorityType(String value) {
        this.authorityType = value;
    }

}
