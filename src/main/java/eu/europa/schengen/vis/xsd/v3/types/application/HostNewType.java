//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.vis.xsd.v3.types.application;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSeeAlso;
import jakarta.xml.bind.annotation.XmlType;


/**
 * Description: General information for a host.
 * 
 * <p>Classe Java per HostNewType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="HostNewType">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="Address" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}AddressNewType" minOccurs="0"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "HostNewType", propOrder = {
    "address"
})
@XmlSeeAlso({
    HostOrganisationNewType.class,
    HostPersonNewType.class
})
public class HostNewType {

    /**
     * Description: Address of the host.
     * 
     */
    @XmlElement(name = "Address")
    protected AddressNewType address;

    /**
     * Description: Address of the host.
     * 
     * @return
     *     possible object is
     *     {@link AddressNewType }
     *     
     */
    public AddressNewType getAddress() {
        return address;
    }

    /**
     * Imposta il valore della proprietà address.
     * 
     * @param value
     *     allowed object is
     *     {@link AddressNewType }
     *     
     * @see #getAddress()
     */
    public void setAddress(AddressNewType value) {
        this.address = value;
    }

}
