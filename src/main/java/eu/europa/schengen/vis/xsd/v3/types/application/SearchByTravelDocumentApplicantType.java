//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.vis.xsd.v3.types.application;

import eu.europa.schengen.vis.xsd.v3.types.common.SearchDateWeightType;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlAttribute;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;
import jakarta.xml.bind.annotation.XmlValue;


/**
 * Description: Criteria for the searching of an application.
 * 
 * <p>Classe Java per SearchByTravelDocumentApplicantType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="SearchByTravelDocumentApplicantType">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="Surname" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}SearchType"/>
 *         <element name="Firstnames" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}SearchType" minOccurs="0"/>
 *         <element name="Sex">
 *           <complexType>
 *             <simpleContent>
 *               <extension base="<http://www.europa.eu/schengen/vis/xsd/v3/types/Application>CT04_GenderType">
 *                 <attribute name="Weight" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}WeightType" />
 *               </extension>
 *             </simpleContent>
 *           </complexType>
 *         </element>
 *         <element name="DateOfBirth" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}SearchDateWeightType"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SearchByTravelDocumentApplicantType", propOrder = {
    "surname",
    "firstnames",
    "sex",
    "dateOfBirth"
})
public class SearchByTravelDocumentApplicantType {

    /**
     * Description: Surname of the applicant.
     * 
     */
    @XmlElement(name = "Surname", required = true)
    protected SearchType surname;
    /**
     * Description: Firstname of the Applicant.
     * 
     */
    @XmlElement(name = "Firstnames")
    protected SearchType firstnames;
    /**
     * Description: The gender of the applicant.
     * 
     */
    @XmlElement(name = "Sex", required = true)
    protected SearchByTravelDocumentApplicantType.Sex sex;
    /**
     * Description: Applicant's date of birth.
     * 
     */
    @XmlElement(name = "DateOfBirth", required = true)
    protected SearchDateWeightType dateOfBirth;

    /**
     * Description: Surname of the applicant.
     * 
     * @return
     *     possible object is
     *     {@link SearchType }
     *     
     */
    public SearchType getSurname() {
        return surname;
    }

    /**
     * Imposta il valore della proprietà surname.
     * 
     * @param value
     *     allowed object is
     *     {@link SearchType }
     *     
     * @see #getSurname()
     */
    public void setSurname(SearchType value) {
        this.surname = value;
    }

    /**
     * Description: Firstname of the Applicant.
     * 
     * @return
     *     possible object is
     *     {@link SearchType }
     *     
     */
    public SearchType getFirstnames() {
        return firstnames;
    }

    /**
     * Imposta il valore della proprietà firstnames.
     * 
     * @param value
     *     allowed object is
     *     {@link SearchType }
     *     
     * @see #getFirstnames()
     */
    public void setFirstnames(SearchType value) {
        this.firstnames = value;
    }

    /**
     * Description: The gender of the applicant.
     * 
     * @return
     *     possible object is
     *     {@link SearchByTravelDocumentApplicantType.Sex }
     *     
     */
    public SearchByTravelDocumentApplicantType.Sex getSex() {
        return sex;
    }

    /**
     * Imposta il valore della proprietà sex.
     * 
     * @param value
     *     allowed object is
     *     {@link SearchByTravelDocumentApplicantType.Sex }
     *     
     * @see #getSex()
     */
    public void setSex(SearchByTravelDocumentApplicantType.Sex value) {
        this.sex = value;
    }

    /**
     * Description: Applicant's date of birth.
     * 
     * @return
     *     possible object is
     *     {@link SearchDateWeightType }
     *     
     */
    public SearchDateWeightType getDateOfBirth() {
        return dateOfBirth;
    }

    /**
     * Imposta il valore della proprietà dateOfBirth.
     * 
     * @param value
     *     allowed object is
     *     {@link SearchDateWeightType }
     *     
     * @see #getDateOfBirth()
     */
    public void setDateOfBirth(SearchDateWeightType value) {
        this.dateOfBirth = value;
    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <simpleContent>
     *     <extension base="<http://www.europa.eu/schengen/vis/xsd/v3/types/Application>CT04_GenderType">
     *       <attribute name="Weight" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}WeightType" />
     *     </extension>
     *   </simpleContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "value"
    })
    public static class Sex {

        /**
         * Description: This table defines the list of the gender.
         * 
         */
        @XmlValue
        protected String value;
        /**
         * Description: End-user defined weights
         * 
         */
        @XmlAttribute(name = "Weight")
        protected Short weight;

        /**
         * Description: This table defines the list of the gender.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getValue() {
            return value;
        }

        /**
         * Imposta il valore della proprietà value.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         * @see #getValue()
         */
        public void setValue(String value) {
            this.value = value;
        }

        /**
         * Description: End-user defined weights
         * 
         * @return
         *     possible object is
         *     {@link Short }
         *     
         */
        public Short getWeight() {
            return weight;
        }

        /**
         * Imposta il valore della proprietà weight.
         * 
         * @param value
         *     allowed object is
         *     {@link Short }
         *     
         * @see #getWeight()
         */
        public void setWeight(Short value) {
            this.weight = value;
        }

    }

}
