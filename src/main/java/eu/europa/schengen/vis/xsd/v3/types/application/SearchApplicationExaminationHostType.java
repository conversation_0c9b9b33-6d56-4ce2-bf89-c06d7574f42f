//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.vis.xsd.v3.types.application;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * Decsription: Criteria for the searching of an host.
 * 
 * <p>Classe Java per SearchApplicationExaminationHostType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="SearchApplicationExaminationHostType">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <choice>
 *         <element name="SearchHostOrganisation">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="OrganisationName" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}SearchType" minOccurs="0"/>
 *                   <element name="OrganisationAddress" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}SearchAddressType" minOccurs="0"/>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *         <element name="SearchHostPerson">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="HostAddress" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}SearchAddressType" minOccurs="0"/>
 *                   <element name="HostFirstnames" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}SearchType" minOccurs="0"/>
 *                   <element name="HostSurname" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}SearchType" minOccurs="0"/>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *       </choice>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SearchApplicationExaminationHostType", propOrder = {
    "searchHostOrganisation",
    "searchHostPerson"
})
public class SearchApplicationExaminationHostType {

    @XmlElement(name = "SearchHostOrganisation")
    protected SearchApplicationExaminationHostType.SearchHostOrganisation searchHostOrganisation;
    @XmlElement(name = "SearchHostPerson")
    protected SearchApplicationExaminationHostType.SearchHostPerson searchHostPerson;

    /**
     * Recupera il valore della proprietà searchHostOrganisation.
     * 
     * @return
     *     possible object is
     *     {@link SearchApplicationExaminationHostType.SearchHostOrganisation }
     *     
     */
    public SearchApplicationExaminationHostType.SearchHostOrganisation getSearchHostOrganisation() {
        return searchHostOrganisation;
    }

    /**
     * Imposta il valore della proprietà searchHostOrganisation.
     * 
     * @param value
     *     allowed object is
     *     {@link SearchApplicationExaminationHostType.SearchHostOrganisation }
     *     
     */
    public void setSearchHostOrganisation(SearchApplicationExaminationHostType.SearchHostOrganisation value) {
        this.searchHostOrganisation = value;
    }

    /**
     * Recupera il valore della proprietà searchHostPerson.
     * 
     * @return
     *     possible object is
     *     {@link SearchApplicationExaminationHostType.SearchHostPerson }
     *     
     */
    public SearchApplicationExaminationHostType.SearchHostPerson getSearchHostPerson() {
        return searchHostPerson;
    }

    /**
     * Imposta il valore della proprietà searchHostPerson.
     * 
     * @param value
     *     allowed object is
     *     {@link SearchApplicationExaminationHostType.SearchHostPerson }
     *     
     */
    public void setSearchHostPerson(SearchApplicationExaminationHostType.SearchHostPerson value) {
        this.searchHostPerson = value;
    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="OrganisationName" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}SearchType" minOccurs="0"/>
     *         <element name="OrganisationAddress" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}SearchAddressType" minOccurs="0"/>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "organisationName",
        "organisationAddress"
    })
    public static class SearchHostOrganisation {

        /**
         * Description: The name of the company which invited the applicant.
         * 
         */
        @XmlElement(name = "OrganisationName")
        protected SearchType organisationName;
        /**
         * Description: The adress of the company which invited the applicant.
         * 
         */
        @XmlElement(name = "OrganisationAddress")
        protected SearchAddressType organisationAddress;

        /**
         * Description: The name of the company which invited the applicant.
         * 
         * @return
         *     possible object is
         *     {@link SearchType }
         *     
         */
        public SearchType getOrganisationName() {
            return organisationName;
        }

        /**
         * Imposta il valore della proprietà organisationName.
         * 
         * @param value
         *     allowed object is
         *     {@link SearchType }
         *     
         * @see #getOrganisationName()
         */
        public void setOrganisationName(SearchType value) {
            this.organisationName = value;
        }

        /**
         * Description: The adress of the company which invited the applicant.
         * 
         * @return
         *     possible object is
         *     {@link SearchAddressType }
         *     
         */
        public SearchAddressType getOrganisationAddress() {
            return organisationAddress;
        }

        /**
         * Imposta il valore della proprietà organisationAddress.
         * 
         * @param value
         *     allowed object is
         *     {@link SearchAddressType }
         *     
         * @see #getOrganisationAddress()
         */
        public void setOrganisationAddress(SearchAddressType value) {
            this.organisationAddress = value;
        }

    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="HostAddress" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}SearchAddressType" minOccurs="0"/>
     *         <element name="HostFirstnames" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}SearchType" minOccurs="0"/>
     *         <element name="HostSurname" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}SearchType" minOccurs="0"/>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "hostAddress",
        "hostFirstnames",
        "hostSurname"
    })
    public static class SearchHostPerson {

        /**
         * Description: The name of the person who invited the applicant.
         * 
         */
        @XmlElement(name = "HostAddress")
        protected SearchAddressType hostAddress;
        /**
         * Description: The first name of the host person.
         * 
         */
        @XmlElement(name = "HostFirstnames")
        protected SearchType hostFirstnames;
        /**
         * Description: The surname of the host person.
         * 
         */
        @XmlElement(name = "HostSurname")
        protected SearchType hostSurname;

        /**
         * Description: The name of the person who invited the applicant.
         * 
         * @return
         *     possible object is
         *     {@link SearchAddressType }
         *     
         */
        public SearchAddressType getHostAddress() {
            return hostAddress;
        }

        /**
         * Imposta il valore della proprietà hostAddress.
         * 
         * @param value
         *     allowed object is
         *     {@link SearchAddressType }
         *     
         * @see #getHostAddress()
         */
        public void setHostAddress(SearchAddressType value) {
            this.hostAddress = value;
        }

        /**
         * Description: The first name of the host person.
         * 
         * @return
         *     possible object is
         *     {@link SearchType }
         *     
         */
        public SearchType getHostFirstnames() {
            return hostFirstnames;
        }

        /**
         * Imposta il valore della proprietà hostFirstnames.
         * 
         * @param value
         *     allowed object is
         *     {@link SearchType }
         *     
         * @see #getHostFirstnames()
         */
        public void setHostFirstnames(SearchType value) {
            this.hostFirstnames = value;
        }

        /**
         * Description: The surname of the host person.
         * 
         * @return
         *     possible object is
         *     {@link SearchType }
         *     
         */
        public SearchType getHostSurname() {
            return hostSurname;
        }

        /**
         * Imposta il valore della proprietà hostSurname.
         * 
         * @param value
         *     allowed object is
         *     {@link SearchType }
         *     
         * @see #getHostSurname()
         */
        public void setHostSurname(SearchType value) {
            this.hostSurname = value;
        }

    }

}
