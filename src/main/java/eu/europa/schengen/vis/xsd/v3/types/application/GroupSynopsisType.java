//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.vis.xsd.v3.types.application;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSeeAlso;
import jakarta.xml.bind.annotation.XmlType;


/**
 * Description: Synopsis of a group, it means the ID and the type of the group.
 * 
 * <p>Classe Java per GroupSynopsisType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="GroupSynopsisType">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="GroupID" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}GroupIDType"/>
 *         <element name="GroupType" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}ST02_GroupTypeType"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "GroupSynopsisType", propOrder = {
    "groupID",
    "groupType"
})
@XmlSeeAlso({
    ResultListApplicationsInGroupType.class,
    ResultListVisaInGroupType.class
})
public class GroupSynopsisType {

    /**
     * Description: ID of the group.
     * 
     */
    @XmlElement(name = "GroupID")
    protected long groupID;
    /**
     * Description: Type of the group (code table value).
     * 
     */
    @XmlElement(name = "GroupType", required = true)
    protected String groupType;

    /**
     * Description: ID of the group.
     * 
     */
    public long getGroupID() {
        return groupID;
    }

    /**
     * Imposta il valore della proprietà groupID.
     * 
     */
    public void setGroupID(long value) {
        this.groupID = value;
    }

    /**
     * Description: Type of the group (code table value).
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getGroupType() {
        return groupType;
    }

    /**
     * Imposta il valore della proprietà groupType.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     * @see #getGroupType()
     */
    public void setGroupType(String value) {
        this.groupType = value;
    }

}
