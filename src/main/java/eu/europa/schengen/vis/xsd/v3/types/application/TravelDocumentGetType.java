//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.vis.xsd.v3.types.application;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * Description: Information describing the travel document.
 * 
 * <p>Classe Java per TravelDocumentGetType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="TravelDocumentGetType">
 *   <complexContent>
 *     <extension base="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}TravelDocumentBaseType">
 *       <sequence>
 *         <element name="IssuingAuthorityOfTravelDocument" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}IssuingAuthorityOfTravelDocumentTransTextType" minOccurs="0"/>
 *       </sequence>
 *     </extension>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "TravelDocumentGetType", propOrder = {
    "issuingAuthorityOfTravelDocument"
})
public class TravelDocumentGetType
    extends TravelDocumentBaseType
{

    /**
     * Description: The authority which issued the travel document. Can be also a country or an organization such as UN.
     * 
     */
    @XmlElement(name = "IssuingAuthorityOfTravelDocument")
    protected IssuingAuthorityOfTravelDocumentTransTextType issuingAuthorityOfTravelDocument;

    /**
     * Description: The authority which issued the travel document. Can be also a country or an organization such as UN.
     * 
     * @return
     *     possible object is
     *     {@link IssuingAuthorityOfTravelDocumentTransTextType }
     *     
     */
    public IssuingAuthorityOfTravelDocumentTransTextType getIssuingAuthorityOfTravelDocument() {
        return issuingAuthorityOfTravelDocument;
    }

    /**
     * Imposta il valore della proprietà issuingAuthorityOfTravelDocument.
     * 
     * @param value
     *     allowed object is
     *     {@link IssuingAuthorityOfTravelDocumentTransTextType }
     *     
     * @see #getIssuingAuthorityOfTravelDocument()
     */
    public void setIssuingAuthorityOfTravelDocument(IssuingAuthorityOfTravelDocumentTransTextType value) {
        this.issuingAuthorityOfTravelDocument = value;
    }

}
