//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.vis.xsd.v3.types.application;

import javax.xml.datatype.XMLGregorianCalendar;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per ListVisaInGroupOverviewType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="ListVisaInGroupOverviewType">
 *   <complexContent>
 *     <extension base="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}ListRetrievalBaseType">
 *       <sequence>
 *         <element name="ApplicationStatus" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}ST31_ApplicationStatusType"/>
 *         <element name="DateOfApplication" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}TimeStampType" minOccurs="0"/>
 *         <element name="FacialImage" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}FacialImageListType" minOccurs="0"/>
 *         <element name="MemberStatesOfDestination" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}MemberStatesOfDestinationType" minOccurs="0"/>
 *         <element name="MainPurposesOfJourney" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}MainPurposesOfJourneyGetType" minOccurs="0"/>
 *         <element name="VisaStickerNumber" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}VisaStickerNumberType" minOccurs="0"/>
 *         <element name="VisaType" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}VisaTypeType" minOccurs="0"/>
 *       </sequence>
 *     </extension>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ListVisaInGroupOverviewType", propOrder = {
    "applicationStatus",
    "dateOfApplication",
    "facialImage",
    "memberStatesOfDestination",
    "mainPurposesOfJourney",
    "visaStickerNumber",
    "visaType"
})
public class ListVisaInGroupOverviewType
    extends ListRetrievalBaseType
{

    /**
     * Description: Status of the application (code table value). This Status is the outcome of the decision history.
     * 
     */
    @XmlElement(name = "ApplicationStatus", required = true)
    protected String applicationStatus;
    /**
     * Description: The date of request is taken from the application form.
     * 
     */
    @XmlElement(name = "DateOfApplication")
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar dateOfApplication;
    @XmlElement(name = "FacialImage")
    protected FacialImageListType facialImage;
    /**
     * Description: The member states destination is taken from the application form.
     * 
     */
    @XmlElement(name = "MemberStatesOfDestination")
    protected MemberStatesOfDestinationType memberStatesOfDestination;
    @XmlElement(name = "MainPurposesOfJourney")
    protected MainPurposesOfJourneyGetType mainPurposesOfJourney;
    /**
     * Description: This attribute uniquely identifies a visa sticker. 
     * A visa sticker number consists of one to three characters indicating the country and a number that consists of up to 30 digits.
     * 
     */
    @XmlElement(name = "VisaStickerNumber")
    protected String visaStickerNumber;
    /**
     * Description: The visa type as printed on the visa sticker.
     * 
     */
    @XmlElement(name = "VisaType")
    protected VisaTypeType visaType;

    /**
     * Description: Status of the application (code table value). This Status is the outcome of the decision history.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getApplicationStatus() {
        return applicationStatus;
    }

    /**
     * Imposta il valore della proprietà applicationStatus.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     * @see #getApplicationStatus()
     */
    public void setApplicationStatus(String value) {
        this.applicationStatus = value;
    }

    /**
     * Description: The date of request is taken from the application form.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getDateOfApplication() {
        return dateOfApplication;
    }

    /**
     * Imposta il valore della proprietà dateOfApplication.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     * @see #getDateOfApplication()
     */
    public void setDateOfApplication(XMLGregorianCalendar value) {
        this.dateOfApplication = value;
    }

    /**
     * Recupera il valore della proprietà facialImage.
     * 
     * @return
     *     possible object is
     *     {@link FacialImageListType }
     *     
     */
    public FacialImageListType getFacialImage() {
        return facialImage;
    }

    /**
     * Imposta il valore della proprietà facialImage.
     * 
     * @param value
     *     allowed object is
     *     {@link FacialImageListType }
     *     
     */
    public void setFacialImage(FacialImageListType value) {
        this.facialImage = value;
    }

    /**
     * Description: The member states destination is taken from the application form.
     * 
     * @return
     *     possible object is
     *     {@link MemberStatesOfDestinationType }
     *     
     */
    public MemberStatesOfDestinationType getMemberStatesOfDestination() {
        return memberStatesOfDestination;
    }

    /**
     * Imposta il valore della proprietà memberStatesOfDestination.
     * 
     * @param value
     *     allowed object is
     *     {@link MemberStatesOfDestinationType }
     *     
     * @see #getMemberStatesOfDestination()
     */
    public void setMemberStatesOfDestination(MemberStatesOfDestinationType value) {
        this.memberStatesOfDestination = value;
    }

    /**
     * Recupera il valore della proprietà mainPurposesOfJourney.
     * 
     * @return
     *     possible object is
     *     {@link MainPurposesOfJourneyGetType }
     *     
     */
    public MainPurposesOfJourneyGetType getMainPurposesOfJourney() {
        return mainPurposesOfJourney;
    }

    /**
     * Imposta il valore della proprietà mainPurposesOfJourney.
     * 
     * @param value
     *     allowed object is
     *     {@link MainPurposesOfJourneyGetType }
     *     
     */
    public void setMainPurposesOfJourney(MainPurposesOfJourneyGetType value) {
        this.mainPurposesOfJourney = value;
    }

    /**
     * Description: This attribute uniquely identifies a visa sticker. 
     * A visa sticker number consists of one to three characters indicating the country and a number that consists of up to 30 digits.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getVisaStickerNumber() {
        return visaStickerNumber;
    }

    /**
     * Imposta il valore della proprietà visaStickerNumber.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     * @see #getVisaStickerNumber()
     */
    public void setVisaStickerNumber(String value) {
        this.visaStickerNumber = value;
    }

    /**
     * Description: The visa type as printed on the visa sticker.
     * 
     * @return
     *     possible object is
     *     {@link VisaTypeType }
     *     
     */
    public VisaTypeType getVisaType() {
        return visaType;
    }

    /**
     * Imposta il valore della proprietà visaType.
     * 
     * @param value
     *     allowed object is
     *     {@link VisaTypeType }
     *     
     * @see #getVisaType()
     */
    public void setVisaType(VisaTypeType value) {
        this.visaType = value;
    }

}
