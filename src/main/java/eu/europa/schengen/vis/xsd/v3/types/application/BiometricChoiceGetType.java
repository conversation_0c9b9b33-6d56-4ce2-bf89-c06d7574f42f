//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.vis.xsd.v3.types.application;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * Description: Choice for the retrieval of an attachment.
 * 
 * <p>Classe Java per BiometricChoiceGetType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="BiometricChoiceGetType">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <choice>
 *         <element name="FacialImage" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}FacialImageGetType"/>
 *         <element name="FingerprintSet" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}FingerprintSetGetType"/>
 *       </choice>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "BiometricChoiceGetType", propOrder = {
    "facialImage",
    "fingerprintSet"
})
public class BiometricChoiceGetType {

    /**
     * Description: Information of a facial image.
     * 
     */
    @XmlElement(name = "FacialImage")
    protected FacialImageGetType facialImage;
    /**
     * Description: Information of a fingerprint set.
     * 
     */
    @XmlElement(name = "FingerprintSet")
    protected FingerprintSetGetType fingerprintSet;

    /**
     * Description: Information of a facial image.
     * 
     * @return
     *     possible object is
     *     {@link FacialImageGetType }
     *     
     */
    public FacialImageGetType getFacialImage() {
        return facialImage;
    }

    /**
     * Imposta il valore della proprietà facialImage.
     * 
     * @param value
     *     allowed object is
     *     {@link FacialImageGetType }
     *     
     * @see #getFacialImage()
     */
    public void setFacialImage(FacialImageGetType value) {
        this.facialImage = value;
    }

    /**
     * Description: Information of a fingerprint set.
     * 
     * @return
     *     possible object is
     *     {@link FingerprintSetGetType }
     *     
     */
    public FingerprintSetGetType getFingerprintSet() {
        return fingerprintSet;
    }

    /**
     * Imposta il valore della proprietà fingerprintSet.
     * 
     * @param value
     *     allowed object is
     *     {@link FingerprintSetGetType }
     *     
     * @see #getFingerprintSet()
     */
    public void setFingerprintSet(FingerprintSetGetType value) {
        this.fingerprintSet = value;
    }

}
