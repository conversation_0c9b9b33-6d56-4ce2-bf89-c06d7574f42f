//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.vis.xsd.v3.types.application;

import javax.xml.datatype.XMLGregorianCalendar;
import eu.europa.schengen.vis.xsd.v3.types.common.AuthorityType;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlSeeAlso;
import jakarta.xml.bind.annotation.XmlType;


/**
 * Description: Information describing the core data of an application.
 * 
 * <p>Classe Java per ApplicationCoreDataBaseType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="ApplicationCoreDataBaseType">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="Authority" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}AuthorityType"/>
 *         <element name="IntendedDateOfArrival" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}PseudodateType" minOccurs="0"/>
 *         <element name="IntendedDateOfDeparture" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}PseudodateType" minOccurs="0"/>
 *         <element name="DateOfApplication" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}TimeStampType" minOccurs="0"/>
 *         <element name="DurationOfIntendedStayOrTransit" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}DurationType" minOccurs="0"/>
 *         <element name="VisaTypeRequested" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}CT12_VisaTypeRequestedType" minOccurs="0"/>
 *         <element name="FingerprintsNotRequired" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}YesNoType"/>
 *         <element name="FingerprintsNotApplicable" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}YesNoType"/>
 *         <element name="ReasonForFingerprintNotApplicable" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}ReasonForFingerprintNotApplicableType" minOccurs="0"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ApplicationCoreDataBaseType", propOrder = {
    "authority",
    "intendedDateOfArrival",
    "intendedDateOfDeparture",
    "dateOfApplication",
    "durationOfIntendedStayOrTransit",
    "visaTypeRequested",
    "fingerprintsNotRequired",
    "fingerprintsNotApplicable",
    "reasonForFingerprintNotApplicable"
})
@XmlSeeAlso({
    ApplicationCoreDataNewType.class
})
public class ApplicationCoreDataBaseType {

    /**
     * Description: The authority which lodged the application.
     * 
     */
    @XmlElement(name = "Authority", required = true)
    protected AuthorityType authority;
    /**
     * Description: Date of arrival taken from the application form.
     * 
     */
    @XmlElement(name = "IntendedDateOfArrival")
    protected String intendedDateOfArrival;
    /**
     * Description: The date of departure taken from the application form.
     * 
     */
    @XmlElement(name = "IntendedDateOfDeparture")
    protected String intendedDateOfDeparture;
    /**
     * Description: The date of request taken from the application from.
     * 
     */
    @XmlElement(name = "DateOfApplication")
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar dateOfApplication;
    /**
     * Description: The requested duration of stay or transit taken from the application from.
     * 
     */
    @XmlElement(name = "DurationOfIntendedStayOrTransit")
    protected Long durationOfIntendedStayOrTransit;
    /**
     * Description: The requested visa type is taken from the application from (code table value).
     * 
     */
    @XmlElement(name = "VisaTypeRequested")
    protected String visaTypeRequested;
    /**
     * Description:This pinpoints whether fingerprints are required or not. Fingerprints are required if this boolean is set to FALSE.
     * 
     */
    @XmlElement(name = "FingerprintsNotRequired")
    protected boolean fingerprintsNotRequired;
    /**
     * Description: Indicates whether the fingerprints are not applicable. fingerprints are not applicable when the applicant physically cannot provide them (e.g. when the applicant has no hands) or when the fingerprints cannot be taken for a technical reason (e.g. there is no scanner available).
     * 
     */
    @XmlElement(name = "FingerprintsNotApplicable")
    protected boolean fingerprintsNotApplicable;
    /**
     * Description: A free text field to specify why the fingerprints are not applicable.
     * 
     */
    @XmlElement(name = "ReasonForFingerprintNotApplicable")
    protected String reasonForFingerprintNotApplicable;

    /**
     * Description: The authority which lodged the application.
     * 
     * @return
     *     possible object is
     *     {@link AuthorityType }
     *     
     */
    public AuthorityType getAuthority() {
        return authority;
    }

    /**
     * Imposta il valore della proprietà authority.
     * 
     * @param value
     *     allowed object is
     *     {@link AuthorityType }
     *     
     * @see #getAuthority()
     */
    public void setAuthority(AuthorityType value) {
        this.authority = value;
    }

    /**
     * Description: Date of arrival taken from the application form.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIntendedDateOfArrival() {
        return intendedDateOfArrival;
    }

    /**
     * Imposta il valore della proprietà intendedDateOfArrival.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     * @see #getIntendedDateOfArrival()
     */
    public void setIntendedDateOfArrival(String value) {
        this.intendedDateOfArrival = value;
    }

    /**
     * Description: The date of departure taken from the application form.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIntendedDateOfDeparture() {
        return intendedDateOfDeparture;
    }

    /**
     * Imposta il valore della proprietà intendedDateOfDeparture.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     * @see #getIntendedDateOfDeparture()
     */
    public void setIntendedDateOfDeparture(String value) {
        this.intendedDateOfDeparture = value;
    }

    /**
     * Description: The date of request taken from the application from.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getDateOfApplication() {
        return dateOfApplication;
    }

    /**
     * Imposta il valore della proprietà dateOfApplication.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     * @see #getDateOfApplication()
     */
    public void setDateOfApplication(XMLGregorianCalendar value) {
        this.dateOfApplication = value;
    }

    /**
     * Description: The requested duration of stay or transit taken from the application from.
     * 
     * @return
     *     possible object is
     *     {@link Long }
     *     
     */
    public Long getDurationOfIntendedStayOrTransit() {
        return durationOfIntendedStayOrTransit;
    }

    /**
     * Imposta il valore della proprietà durationOfIntendedStayOrTransit.
     * 
     * @param value
     *     allowed object is
     *     {@link Long }
     *     
     * @see #getDurationOfIntendedStayOrTransit()
     */
    public void setDurationOfIntendedStayOrTransit(Long value) {
        this.durationOfIntendedStayOrTransit = value;
    }

    /**
     * Description: The requested visa type is taken from the application from (code table value).
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getVisaTypeRequested() {
        return visaTypeRequested;
    }

    /**
     * Imposta il valore della proprietà visaTypeRequested.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     * @see #getVisaTypeRequested()
     */
    public void setVisaTypeRequested(String value) {
        this.visaTypeRequested = value;
    }

    /**
     * Description:This pinpoints whether fingerprints are required or not. Fingerprints are required if this boolean is set to FALSE.
     * 
     */
    public boolean isFingerprintsNotRequired() {
        return fingerprintsNotRequired;
    }

    /**
     * Imposta il valore della proprietà fingerprintsNotRequired.
     * 
     */
    public void setFingerprintsNotRequired(boolean value) {
        this.fingerprintsNotRequired = value;
    }

    /**
     * Description: Indicates whether the fingerprints are not applicable. fingerprints are not applicable when the applicant physically cannot provide them (e.g. when the applicant has no hands) or when the fingerprints cannot be taken for a technical reason (e.g. there is no scanner available).
     * 
     */
    public boolean isFingerprintsNotApplicable() {
        return fingerprintsNotApplicable;
    }

    /**
     * Imposta il valore della proprietà fingerprintsNotApplicable.
     * 
     */
    public void setFingerprintsNotApplicable(boolean value) {
        this.fingerprintsNotApplicable = value;
    }

    /**
     * Description: A free text field to specify why the fingerprints are not applicable.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getReasonForFingerprintNotApplicable() {
        return reasonForFingerprintNotApplicable;
    }

    /**
     * Imposta il valore della proprietà reasonForFingerprintNotApplicable.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     * @see #getReasonForFingerprintNotApplicable()
     */
    public void setReasonForFingerprintNotApplicable(String value) {
        this.reasonForFingerprintNotApplicable = value;
    }

}
