//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.vis.xsd.v3.types.application;

import eu.europa.schengen.vis.xsd.v3.types.common.SearchDateWeightType;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlAttribute;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;
import jakarta.xml.bind.annotation.XmlValue;


/**
 * Description: Criteria for the searching of a travel document.
 * 
 * <p>Classe Java per SearchApplicationExaminationTravelDocumentType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="SearchApplicationExaminationTravelDocumentType">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="TypeOfTravelDocument">
 *           <complexType>
 *             <simpleContent>
 *               <extension base="<http://www.europa.eu/schengen/vis/xsd/v3/types/Application>CT11_TravelDocumentTypeType">
 *                 <attribute name="Weight" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}WeightType" />
 *               </extension>
 *             </simpleContent>
 *           </complexType>
 *         </element>
 *         <element name="TravelDocumentNumber">
 *           <complexType>
 *             <simpleContent>
 *               <extension base="<http://www.europa.eu/schengen/vis/xsd/v3/types/Application>TravelDocumentNumberType">
 *                 <attribute name="Weight" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}WeightType" />
 *               </extension>
 *             </simpleContent>
 *           </complexType>
 *         </element>
 *         <element name="IssuingAuthorityOfTravelDocument" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}IssuingAuthorityOfTravelDocumentSearchType"/>
 *         <element name="ExpiringDateOfTravelDocument" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}SearchDateWeightType"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SearchApplicationExaminationTravelDocumentType", propOrder = {
    "typeOfTravelDocument",
    "travelDocumentNumber",
    "issuingAuthorityOfTravelDocument",
    "expiringDateOfTravelDocument"
})
public class SearchApplicationExaminationTravelDocumentType {

    /**
     * Description: The type of the travel document.
     * 
     */
    @XmlElement(name = "TypeOfTravelDocument", required = true)
    protected SearchApplicationExaminationTravelDocumentType.TypeOfTravelDocument typeOfTravelDocument;
    /**
     * Description: The number of the travel document.
     * 
     */
    @XmlElement(name = "TravelDocumentNumber", required = true)
    protected SearchApplicationExaminationTravelDocumentType.TravelDocumentNumber travelDocumentNumber;
    /**
     * Description: The authority which issued the travel document. Can be also a country or an organization such as UN.
     * 
     */
    @XmlElement(name = "IssuingAuthorityOfTravelDocument", required = true)
    protected IssuingAuthorityOfTravelDocumentSearchType issuingAuthorityOfTravelDocument;
    /**
     * Description: The expiring date of the travel document.
     * 
     */
    @XmlElement(name = "ExpiringDateOfTravelDocument", required = true)
    protected SearchDateWeightType expiringDateOfTravelDocument;

    /**
     * Description: The type of the travel document.
     * 
     * @return
     *     possible object is
     *     {@link SearchApplicationExaminationTravelDocumentType.TypeOfTravelDocument }
     *     
     */
    public SearchApplicationExaminationTravelDocumentType.TypeOfTravelDocument getTypeOfTravelDocument() {
        return typeOfTravelDocument;
    }

    /**
     * Imposta il valore della proprietà typeOfTravelDocument.
     * 
     * @param value
     *     allowed object is
     *     {@link SearchApplicationExaminationTravelDocumentType.TypeOfTravelDocument }
     *     
     * @see #getTypeOfTravelDocument()
     */
    public void setTypeOfTravelDocument(SearchApplicationExaminationTravelDocumentType.TypeOfTravelDocument value) {
        this.typeOfTravelDocument = value;
    }

    /**
     * Description: The number of the travel document.
     * 
     * @return
     *     possible object is
     *     {@link SearchApplicationExaminationTravelDocumentType.TravelDocumentNumber }
     *     
     */
    public SearchApplicationExaminationTravelDocumentType.TravelDocumentNumber getTravelDocumentNumber() {
        return travelDocumentNumber;
    }

    /**
     * Imposta il valore della proprietà travelDocumentNumber.
     * 
     * @param value
     *     allowed object is
     *     {@link SearchApplicationExaminationTravelDocumentType.TravelDocumentNumber }
     *     
     * @see #getTravelDocumentNumber()
     */
    public void setTravelDocumentNumber(SearchApplicationExaminationTravelDocumentType.TravelDocumentNumber value) {
        this.travelDocumentNumber = value;
    }

    /**
     * Description: The authority which issued the travel document. Can be also a country or an organization such as UN.
     * 
     * @return
     *     possible object is
     *     {@link IssuingAuthorityOfTravelDocumentSearchType }
     *     
     */
    public IssuingAuthorityOfTravelDocumentSearchType getIssuingAuthorityOfTravelDocument() {
        return issuingAuthorityOfTravelDocument;
    }

    /**
     * Imposta il valore della proprietà issuingAuthorityOfTravelDocument.
     * 
     * @param value
     *     allowed object is
     *     {@link IssuingAuthorityOfTravelDocumentSearchType }
     *     
     * @see #getIssuingAuthorityOfTravelDocument()
     */
    public void setIssuingAuthorityOfTravelDocument(IssuingAuthorityOfTravelDocumentSearchType value) {
        this.issuingAuthorityOfTravelDocument = value;
    }

    /**
     * Description: The expiring date of the travel document.
     * 
     * @return
     *     possible object is
     *     {@link SearchDateWeightType }
     *     
     */
    public SearchDateWeightType getExpiringDateOfTravelDocument() {
        return expiringDateOfTravelDocument;
    }

    /**
     * Imposta il valore della proprietà expiringDateOfTravelDocument.
     * 
     * @param value
     *     allowed object is
     *     {@link SearchDateWeightType }
     *     
     * @see #getExpiringDateOfTravelDocument()
     */
    public void setExpiringDateOfTravelDocument(SearchDateWeightType value) {
        this.expiringDateOfTravelDocument = value;
    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <simpleContent>
     *     <extension base="<http://www.europa.eu/schengen/vis/xsd/v3/types/Application>TravelDocumentNumberType">
     *       <attribute name="Weight" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}WeightType" />
     *     </extension>
     *   </simpleContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "value"
    })
    public static class TravelDocumentNumber {

        /**
         * Description: The travel document number identifying a travel document.
         * 
         */
        @XmlValue
        protected String value;
        /**
         * Description: End-user defined weights
         * 
         */
        @XmlAttribute(name = "Weight")
        protected Short weight;

        /**
         * Description: The travel document number identifying a travel document.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getValue() {
            return value;
        }

        /**
         * Imposta il valore della proprietà value.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         * @see #getValue()
         */
        public void setValue(String value) {
            this.value = value;
        }

        /**
         * Description: End-user defined weights
         * 
         * @return
         *     possible object is
         *     {@link Short }
         *     
         */
        public Short getWeight() {
            return weight;
        }

        /**
         * Imposta il valore della proprietà weight.
         * 
         * @param value
         *     allowed object is
         *     {@link Short }
         *     
         * @see #getWeight()
         */
        public void setWeight(Short value) {
            this.weight = value;
        }

    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <simpleContent>
     *     <extension base="<http://www.europa.eu/schengen/vis/xsd/v3/types/Application>CT11_TravelDocumentTypeType">
     *       <attribute name="Weight" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}WeightType" />
     *     </extension>
     *   </simpleContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "value"
    })
    public static class TypeOfTravelDocument {

        /**
         * Description: This table defines the list of the passport's type.
         * 
         */
        @XmlValue
        protected String value;
        /**
         * Description: End-user defined weights
         * 
         */
        @XmlAttribute(name = "Weight")
        protected Short weight;

        /**
         * Description: This table defines the list of the passport's type.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getValue() {
            return value;
        }

        /**
         * Imposta il valore della proprietà value.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         * @see #getValue()
         */
        public void setValue(String value) {
            this.value = value;
        }

        /**
         * Description: End-user defined weights
         * 
         * @return
         *     possible object is
         *     {@link Short }
         *     
         */
        public Short getWeight() {
            return weight;
        }

        /**
         * Imposta il valore della proprietà weight.
         * 
         * @param value
         *     allowed object is
         *     {@link Short }
         *     
         * @see #getWeight()
         */
        public void setWeight(Short value) {
            this.weight = value;
        }

    }

}
