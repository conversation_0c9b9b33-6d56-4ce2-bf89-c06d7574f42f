//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.vis.xsd.v3.types.application;

import java.util.ArrayList;
import java.util.List;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * Description: All information needed to extend a visa with a new sticker.
 * 
 * <p>Classe Java per ExtendVisaGetType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="ExtendVisaGetType">
 *   <complexContent>
 *     <extension base="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}DecisionGetType">
 *       <sequence>
 *         <element name="VisaSticker" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}VisaStickerGetType"/>
 *         <element name="IssuanceOnSeparateSheet" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}YesNoType"/>
 *         <element name="ExtensionGrounds">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="ExtensionGround" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}CT51_ExtensionGroundType" maxOccurs="unbounded"/>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *       </sequence>
 *     </extension>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ExtendVisaGetType", propOrder = {
    "visaSticker",
    "issuanceOnSeparateSheet",
    "extensionGrounds"
})
public class ExtendVisaGetType
    extends DecisionGetType
{

    /**
     * Description: data of the visa sticker.
     * 
     */
    @XmlElement(name = "VisaSticker", required = true)
    protected VisaStickerGetType visaSticker;
    /**
     * Description: Information indicating that the visa has been issued on a separate sheet in accordance with Regulation (EC) No. 333/2002.
     * 
     */
    @XmlElement(name = "IssuanceOnSeparateSheet")
    protected boolean issuanceOnSeparateSheet;
    /**
     * Description: Grounds for extending the visa as described in [VIS-PEP]. Values are defined within the code table ExtensionGround.
     * 
     */
    @XmlElement(name = "ExtensionGrounds", required = true)
    protected ExtendVisaGetType.ExtensionGrounds extensionGrounds;

    /**
     * Description: data of the visa sticker.
     * 
     * @return
     *     possible object is
     *     {@link VisaStickerGetType }
     *     
     */
    public VisaStickerGetType getVisaSticker() {
        return visaSticker;
    }

    /**
     * Imposta il valore della proprietà visaSticker.
     * 
     * @param value
     *     allowed object is
     *     {@link VisaStickerGetType }
     *     
     * @see #getVisaSticker()
     */
    public void setVisaSticker(VisaStickerGetType value) {
        this.visaSticker = value;
    }

    /**
     * Description: Information indicating that the visa has been issued on a separate sheet in accordance with Regulation (EC) No. 333/2002.
     * 
     */
    public boolean isIssuanceOnSeparateSheet() {
        return issuanceOnSeparateSheet;
    }

    /**
     * Imposta il valore della proprietà issuanceOnSeparateSheet.
     * 
     */
    public void setIssuanceOnSeparateSheet(boolean value) {
        this.issuanceOnSeparateSheet = value;
    }

    /**
     * Description: Grounds for extending the visa as described in [VIS-PEP]. Values are defined within the code table ExtensionGround.
     * 
     * @return
     *     possible object is
     *     {@link ExtendVisaGetType.ExtensionGrounds }
     *     
     */
    public ExtendVisaGetType.ExtensionGrounds getExtensionGrounds() {
        return extensionGrounds;
    }

    /**
     * Imposta il valore della proprietà extensionGrounds.
     * 
     * @param value
     *     allowed object is
     *     {@link ExtendVisaGetType.ExtensionGrounds }
     *     
     * @see #getExtensionGrounds()
     */
    public void setExtensionGrounds(ExtendVisaGetType.ExtensionGrounds value) {
        this.extensionGrounds = value;
    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="ExtensionGround" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}CT51_ExtensionGroundType" maxOccurs="unbounded"/>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "extensionGround"
    })
    public static class ExtensionGrounds {

        @XmlElement(name = "ExtensionGround", required = true)
        protected List<String> extensionGround;

        /**
         * Gets the value of the extensionGround property.
         * 
         * <p>This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the extensionGround property.</p>
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * </p>
         * <pre>
         * getExtensionGround().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link String }
         * </p>
         * 
         * 
         * @return
         *     The value of the extensionGround property.
         */
        public List<String> getExtensionGround() {
            if (extensionGround == null) {
                extensionGround = new ArrayList<>();
            }
            return this.extensionGround;
        }

    }

}
