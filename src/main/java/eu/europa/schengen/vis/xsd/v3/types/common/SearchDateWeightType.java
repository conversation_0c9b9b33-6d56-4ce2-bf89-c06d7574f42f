//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.vis.xsd.v3.types.common;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlAttribute;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per SearchDateWeightType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="SearchDateWeightType">
 *   <complexContent>
 *     <extension base="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}SearchDateType">
 *       <attribute name="Weight" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}WeightType" />
 *     </extension>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SearchDateWeightType")
public class SearchDateWeightType
    extends SearchDateType
{

    /**
     * Description: End-user defined weights
     * 
     */
    @XmlAttribute(name = "Weight")
    protected Short weight;

    /**
     * Description: End-user defined weights
     * 
     * @return
     *     possible object is
     *     {@link Short }
     *     
     */
    public Short getWeight() {
        return weight;
    }

    /**
     * Imposta il valore della proprietà weight.
     * 
     * @param value
     *     allowed object is
     *     {@link Short }
     *     
     * @see #getWeight()
     */
    public void setWeight(Short value) {
        this.weight = value;
    }

}
