//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.vis.xsd.v3.types.application;

import java.util.ArrayList;
import java.util.List;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * Description: Result of the search for asylum responsibility.
 * 
 * <p>Classe Java per ResultAsylumResponsibilityUsageOfDataSearchType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="ResultAsylumResponsibilityUsageOfDataSearchType">
 *   <complexContent>
 *     <extension base="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}ResultFoundDossierType">
 *       <sequence>
 *         <element name="AsylumResponsibilityOverviews">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="AsylumResponsibilityOverview" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}ResultAsylumResponsibilityOverviewResponseType" maxOccurs="unbounded"/>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *       </sequence>
 *     </extension>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ResultAsylumResponsibilityUsageOfDataSearchType", propOrder = {
    "asylumResponsibilityOverviews"
})
public class ResultAsylumResponsibilityUsageOfDataSearchType
    extends ResultFoundDossierType
{

    /**
     * Description: List of found applications in the dossier for the asylum responsibilty.
     * 
     */
    @XmlElement(name = "AsylumResponsibilityOverviews", required = true)
    protected ResultAsylumResponsibilityUsageOfDataSearchType.AsylumResponsibilityOverviews asylumResponsibilityOverviews;

    /**
     * Description: List of found applications in the dossier for the asylum responsibilty.
     * 
     * @return
     *     possible object is
     *     {@link ResultAsylumResponsibilityUsageOfDataSearchType.AsylumResponsibilityOverviews }
     *     
     */
    public ResultAsylumResponsibilityUsageOfDataSearchType.AsylumResponsibilityOverviews getAsylumResponsibilityOverviews() {
        return asylumResponsibilityOverviews;
    }

    /**
     * Imposta il valore della proprietà asylumResponsibilityOverviews.
     * 
     * @param value
     *     allowed object is
     *     {@link ResultAsylumResponsibilityUsageOfDataSearchType.AsylumResponsibilityOverviews }
     *     
     * @see #getAsylumResponsibilityOverviews()
     */
    public void setAsylumResponsibilityOverviews(ResultAsylumResponsibilityUsageOfDataSearchType.AsylumResponsibilityOverviews value) {
        this.asylumResponsibilityOverviews = value;
    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="AsylumResponsibilityOverview" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}ResultAsylumResponsibilityOverviewResponseType" maxOccurs="unbounded"/>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "asylumResponsibilityOverview"
    })
    public static class AsylumResponsibilityOverviews {

        @XmlElement(name = "AsylumResponsibilityOverview", required = true)
        protected List<ResultAsylumResponsibilityOverviewResponseType> asylumResponsibilityOverview;

        /**
         * Gets the value of the asylumResponsibilityOverview property.
         * 
         * <p>This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the asylumResponsibilityOverview property.</p>
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * </p>
         * <pre>
         * getAsylumResponsibilityOverview().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link ResultAsylumResponsibilityOverviewResponseType }
         * </p>
         * 
         * 
         * @return
         *     The value of the asylumResponsibilityOverview property.
         */
        public List<ResultAsylumResponsibilityOverviewResponseType> getAsylumResponsibilityOverview() {
            if (asylumResponsibilityOverview == null) {
                asylumResponsibilityOverview = new ArrayList<>();
            }
            return this.asylumResponsibilityOverview;
        }

    }

}
