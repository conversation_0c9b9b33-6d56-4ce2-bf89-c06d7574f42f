//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.vis.xsd.v3.types.application;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * Description: Information describing a decision from the decision history.
 * 
 * <p>Classe Java per LawEnforcementDecisionHistGetType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="LawEnforcementDecisionHistGetType">
 *   <complexContent>
 *     <extension base="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}LawEnforcementDecisionHistChoiceType">
 *       <sequence>
 *         <element name="DecisionID" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}DecisionIDType"/>
 *       </sequence>
 *     </extension>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "LawEnforcementDecisionHistGetType", propOrder = {
    "decisionID"
})
public class LawEnforcementDecisionHistGetType
    extends LawEnforcementDecisionHistChoiceType
{

    /**
     * Description: ID of the decision.
     * 
     */
    @XmlElement(name = "DecisionID")
    protected long decisionID;

    /**
     * Description: ID of the decision.
     * 
     */
    public long getDecisionID() {
        return decisionID;
    }

    /**
     * Imposta il valore della proprietà decisionID.
     * 
     */
    public void setDecisionID(long value) {
        this.decisionID = value;
    }

}
