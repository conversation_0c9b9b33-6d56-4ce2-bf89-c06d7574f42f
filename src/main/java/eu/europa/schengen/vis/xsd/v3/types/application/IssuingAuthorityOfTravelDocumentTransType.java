//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.vis.xsd.v3.types.application;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * Description: This type is used for a name and its transliterations.
 * 
 * <p>Classe Java per IssuingAuthorityOfTravelDocumentTransType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="IssuingAuthorityOfTravelDocumentTransType">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="CountryOfIssuingAuthority" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}CT02_CountryOfNationalityType"/>
 *         <element name="DescriptionOfIssuingAuthority" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}TransType" minOccurs="0"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "IssuingAuthorityOfTravelDocumentTransType", propOrder = {
    "countryOfIssuingAuthority",
    "descriptionOfIssuingAuthority"
})
public class IssuingAuthorityOfTravelDocumentTransType {

    /**
     * Description: This country is used for the issuing authority.
     * 
     */
    @XmlElement(name = "CountryOfIssuingAuthority", required = true)
    protected String countryOfIssuingAuthority;
    @XmlElement(name = "DescriptionOfIssuingAuthority")
    protected TransType descriptionOfIssuingAuthority;

    /**
     * Description: This country is used for the issuing authority.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCountryOfIssuingAuthority() {
        return countryOfIssuingAuthority;
    }

    /**
     * Imposta il valore della proprietà countryOfIssuingAuthority.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     * @see #getCountryOfIssuingAuthority()
     */
    public void setCountryOfIssuingAuthority(String value) {
        this.countryOfIssuingAuthority = value;
    }

    /**
     * Recupera il valore della proprietà descriptionOfIssuingAuthority.
     * 
     * @return
     *     possible object is
     *     {@link TransType }
     *     
     */
    public TransType getDescriptionOfIssuingAuthority() {
        return descriptionOfIssuingAuthority;
    }

    /**
     * Imposta il valore della proprietà descriptionOfIssuingAuthority.
     * 
     * @param value
     *     allowed object is
     *     {@link TransType }
     *     
     */
    public void setDescriptionOfIssuingAuthority(TransType value) {
        this.descriptionOfIssuingAuthority = value;
    }

}
