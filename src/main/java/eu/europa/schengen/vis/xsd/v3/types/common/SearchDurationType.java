//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.vis.xsd.v3.types.common;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSeeAlso;
import jakarta.xml.bind.annotation.XmlType;


/**
 * Description: From is the lower bound of the number of days.
 * To is the upper bound of the number of days.
 * This search is inclusive.
 * 
 * <p>Classe Java per SearchDurationType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="SearchDurationType">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="From" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}DurationType"/>
 *         <element name="To" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}DurationType"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SearchDurationType", propOrder = {
    "from",
    "to"
})
@XmlSeeAlso({
    eu.europa.schengen.vis.xsd.v3.types.application.LawEnforcementSearchType.DurationOfIntendedStayOrTransit.class
})
public class SearchDurationType {

    /**
     * Description: From must be an integer number, greater than or equal to zero.
     * 
     */
    @XmlElement(name = "From")
    protected long from;
    /**
     * Description: To must be an integer number, greater than or equal to searchFrom and greater than zero.
     * 
     */
    @XmlElement(name = "To")
    protected long to;

    /**
     * Description: From must be an integer number, greater than or equal to zero.
     * 
     */
    public long getFrom() {
        return from;
    }

    /**
     * Imposta il valore della proprietà from.
     * 
     */
    public void setFrom(long value) {
        this.from = value;
    }

    /**
     * Description: To must be an integer number, greater than or equal to searchFrom and greater than zero.
     * 
     */
    public long getTo() {
        return to;
    }

    /**
     * Imposta il valore della proprietà to.
     * 
     */
    public void setTo(long value) {
        this.to = value;
    }

}
