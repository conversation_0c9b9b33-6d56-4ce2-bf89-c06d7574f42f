//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.vis.xsd.v3.types.application;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSeeAlso;
import jakarta.xml.bind.annotation.XmlType;


/**
 * Description: Information describing the decision.
 * 
 * <p>Classe Java per DecisionHistChoiceType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="DecisionHistChoiceType">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <choice>
 *         <element name="ApplicationDecision" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}ApplicationDecisionGetType"/>
 *         <element name="VisaDecision" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}VisaDecisionGetType"/>
 *         <element name="VisaCreationDecision" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}VisaCreationDecisionGetType"/>
 *       </choice>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "DecisionHistChoiceType", propOrder = {
    "applicationDecision",
    "visaDecision",
    "visaCreationDecision"
})
@XmlSeeAlso({
    DecisionHistGetType.class
})
public class DecisionHistChoiceType {

    /**
     * Description: Information of an ApplicationDecision.
     * 
     */
    @XmlElement(name = "ApplicationDecision")
    protected ApplicationDecisionGetType applicationDecision;
    /**
     * Description: Information of an VisaDecision.
     * 
     */
    @XmlElement(name = "VisaDecision")
    protected VisaDecisionGetType visaDecision;
    /**
     * Description: Information of an VisaCreationDecision.
     * 
     */
    @XmlElement(name = "VisaCreationDecision")
    protected VisaCreationDecisionGetType visaCreationDecision;

    /**
     * Description: Information of an ApplicationDecision.
     * 
     * @return
     *     possible object is
     *     {@link ApplicationDecisionGetType }
     *     
     */
    public ApplicationDecisionGetType getApplicationDecision() {
        return applicationDecision;
    }

    /**
     * Imposta il valore della proprietà applicationDecision.
     * 
     * @param value
     *     allowed object is
     *     {@link ApplicationDecisionGetType }
     *     
     * @see #getApplicationDecision()
     */
    public void setApplicationDecision(ApplicationDecisionGetType value) {
        this.applicationDecision = value;
    }

    /**
     * Description: Information of an VisaDecision.
     * 
     * @return
     *     possible object is
     *     {@link VisaDecisionGetType }
     *     
     */
    public VisaDecisionGetType getVisaDecision() {
        return visaDecision;
    }

    /**
     * Imposta il valore della proprietà visaDecision.
     * 
     * @param value
     *     allowed object is
     *     {@link VisaDecisionGetType }
     *     
     * @see #getVisaDecision()
     */
    public void setVisaDecision(VisaDecisionGetType value) {
        this.visaDecision = value;
    }

    /**
     * Description: Information of an VisaCreationDecision.
     * 
     * @return
     *     possible object is
     *     {@link VisaCreationDecisionGetType }
     *     
     */
    public VisaCreationDecisionGetType getVisaCreationDecision() {
        return visaCreationDecision;
    }

    /**
     * Imposta il valore della proprietà visaCreationDecision.
     * 
     * @param value
     *     allowed object is
     *     {@link VisaCreationDecisionGetType }
     *     
     * @see #getVisaCreationDecision()
     */
    public void setVisaCreationDecision(VisaCreationDecisionGetType value) {
        this.visaCreationDecision = value;
    }

}
