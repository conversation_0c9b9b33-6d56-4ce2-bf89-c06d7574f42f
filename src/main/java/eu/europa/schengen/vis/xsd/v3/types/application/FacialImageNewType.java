//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.vis.xsd.v3.types.application;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * Description: Information needed for the creation of a facial image.
 * 
 * <p>Classe Java per FacialImageNewType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="FacialImageNewType">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="MasterData">
 *           <complexType>
 *             <complexContent>
 *               <extension base="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}AttachmentMasterDataBaseType">
 *                 <sequence>
 *                   <element name="ImageType" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}CT14_ImageTypeType"/>
 *                 </sequence>
 *               </extension>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *         <element name="DataSource" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}DataSourceType"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "FacialImageNewType", propOrder = {
    "masterData",
    "dataSource"
})
public class FacialImageNewType {

    /**
     * Description: Master data of the attachment.
     * 
     */
    @XmlElement(name = "MasterData", required = true)
    protected FacialImageNewType.MasterData masterData;
    /**
     * Description: Binary data of the attachment.
     * 
     */
    @XmlElement(name = "DataSource", required = true)
    protected byte[] dataSource;

    /**
     * Description: Master data of the attachment.
     * 
     * @return
     *     possible object is
     *     {@link FacialImageNewType.MasterData }
     *     
     */
    public FacialImageNewType.MasterData getMasterData() {
        return masterData;
    }

    /**
     * Imposta il valore della proprietà masterData.
     * 
     * @param value
     *     allowed object is
     *     {@link FacialImageNewType.MasterData }
     *     
     * @see #getMasterData()
     */
    public void setMasterData(FacialImageNewType.MasterData value) {
        this.masterData = value;
    }

    /**
     * Description: Binary data of the attachment.
     * 
     * @return
     *     possible object is
     *     byte[]
     */
    public byte[] getDataSource() {
        return dataSource;
    }

    /**
     * Imposta il valore della proprietà dataSource.
     * 
     * @param value
     *     allowed object is
     *     byte[]
     * @see #getDataSource()
     */
    public void setDataSource(byte[] value) {
        this.dataSource = value;
    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <extension base="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}AttachmentMasterDataBaseType">
     *       <sequence>
     *         <element name="ImageType" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}CT14_ImageTypeType"/>
     *       </sequence>
     *     </extension>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "imageType"
    })
    public static class MasterData
        extends AttachmentMasterDataBaseType
    {

        /**
         * Description: Type of the image. The value comes from the code table CT14_ImageType.
         * 
         */
        @XmlElement(name = "ImageType", required = true)
        protected String imageType;

        /**
         * Description: Type of the image. The value comes from the code table CT14_ImageType.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getImageType() {
            return imageType;
        }

        /**
         * Imposta il valore della proprietà imageType.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         * @see #getImageType()
         */
        public void setImageType(String value) {
            this.imageType = value;
        }

    }

}
