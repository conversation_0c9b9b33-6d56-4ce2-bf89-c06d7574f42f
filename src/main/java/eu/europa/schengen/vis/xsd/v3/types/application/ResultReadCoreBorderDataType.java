//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.vis.xsd.v3.types.application;

import javax.xml.datatype.XMLGregorianCalendar;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlSeeAlso;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per ResultReadCoreBorderDataType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="ResultReadCoreBorderDataType">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="DateOfApplication" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}TimeStampType" minOccurs="0"/>
 *         <element name="DurationOfIntendedStayOrTransit" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}DurationType" minOccurs="0"/>
 *         <element name="Host" minOccurs="0">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <choice>
 *                   <element name="HostPerson" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}HostPersonGetType"/>
 *                   <element name="HostOrganisation" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}HostOrganisationGetType"/>
 *                 </choice>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *         <element name="MemberStateOfFirstEntry" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}CT70_UserType" minOccurs="0"/>
 *         <element name="IntendedDateOfArrival" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}PseudodateType" minOccurs="0"/>
 *         <element name="IntendedDateOfDeparture" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}PseudodateType" minOccurs="0"/>
 *         <element name="MemberStatesOfDestination" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}MemberStatesOfDestinationType" minOccurs="0"/>
 *         <element name="Occupation" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}OccupationGetType" minOccurs="0"/>
 *         <element name="PlaceOfApplication" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}PlaceGetType" minOccurs="0"/>
 *         <element name="MainPurposesOfJourney" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}MainPurposesOfJourneyGetType" minOccurs="0"/>
 *         <element name="EducationalEstablishment" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}EducationalEstablishmentGetType" minOccurs="0"/>
 *         <element name="TravelDocument" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}TravelDocumentGetType" minOccurs="0"/>
 *         <element name="VisaTypeRequested" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}CT12_VisaTypeRequestedType" minOccurs="0"/>
 *         <element name="FingerprintsNotRequired" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}YesNoType" minOccurs="0"/>
 *         <element name="FingerprintsNotApplicable" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}YesNoType" minOccurs="0"/>
 *         <element name="ApplicantData" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}ApplicantReadType" minOccurs="0"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ResultReadCoreBorderDataType", propOrder = {
    "dateOfApplication",
    "durationOfIntendedStayOrTransit",
    "host",
    "memberStateOfFirstEntry",
    "intendedDateOfArrival",
    "intendedDateOfDeparture",
    "memberStatesOfDestination",
    "occupation",
    "placeOfApplication",
    "mainPurposesOfJourney",
    "educationalEstablishment",
    "travelDocument",
    "visaTypeRequested",
    "fingerprintsNotRequired",
    "fingerprintsNotApplicable",
    "applicantData"
})
@XmlSeeAlso({
    eu.europa.schengen.vis.xsd.v3.types.application.ResultVerificationBorderReadType.ApplicationData.class,
    eu.europa.schengen.vis.xsd.v3.types.application.ResultVerificationBorderControlReadType.ApplicationData.class
})
public class ResultReadCoreBorderDataType {

    /**
     * Description: The date of request is taken from the application form.
     * 
     */
    @XmlElement(name = "DateOfApplication")
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar dateOfApplication;
    /**
     * Description: The duration of intended stay or transit is taken from the application form.
     * The unit is days. This regards short-term visas, which have a maximum validity of 3 months. Therefore, 3 digits are sufficient.
     * 
     */
    @XmlElement(name = "DurationOfIntendedStayOrTransit")
    protected Long durationOfIntendedStayOrTransit;
    /**
     * Description: HostPerson or HostOrganisation.
     * 
     */
    @XmlElement(name = "Host")
    protected ResultReadCoreBorderDataType.Host host;
    @XmlElement(name = "MemberStateOfFirstEntry")
    protected String memberStateOfFirstEntry;
    /**
     * Description: The date of arrival is taken from the application form.
     * 
     */
    @XmlElement(name = "IntendedDateOfArrival")
    protected String intendedDateOfArrival;
    /**
     * Description: The date of departure is taken from the application form.
     * 
     */
    @XmlElement(name = "IntendedDateOfDeparture")
    protected String intendedDateOfDeparture;
    /**
     * Description: The member states destination is taken from the application form.
     * 
     */
    @XmlElement(name = "MemberStatesOfDestination")
    protected MemberStatesOfDestinationType memberStatesOfDestination;
    @XmlElement(name = "Occupation")
    protected OccupationGetType occupation;
    /**
     * Description: The place where the application has been lodged. Consists of a country code and the code for the place. This place could differ from the place of the applicationAuthorityLocation.
     * 
     */
    @XmlElement(name = "PlaceOfApplication")
    protected PlaceGetType placeOfApplication;
    @XmlElement(name = "MainPurposesOfJourney")
    protected MainPurposesOfJourneyGetType mainPurposesOfJourney;
    /**
     * Description: The educational establishment in case the applicant is a student.
     * 
     */
    @XmlElement(name = "EducationalEstablishment")
    protected EducationalEstablishmentGetType educationalEstablishment;
    @XmlElement(name = "TravelDocument")
    protected TravelDocumentGetType travelDocument;
    /**
     * Description: The requested visa type is taken from the application form (code table value).
     * 
     */
    @XmlElement(name = "VisaTypeRequested")
    protected String visaTypeRequested;
    /**
     * Description:This pinpoints whether fingerprints are required or not. Fingerprints are required if this boolean is set to FALSE.
     * 
     */
    @XmlElement(name = "FingerprintsNotRequired")
    protected Boolean fingerprintsNotRequired;
    /**
     * Description: Indicates whether the fingerprints are not applicable. fingerprints are not applicable when the applicant physically cannot provide them (e.g. when the applicant has no hands) or when the fingerprints cannot be taken for a technical reason (e.g. there is no scanner available).
     * 
     */
    @XmlElement(name = "FingerprintsNotApplicable")
    protected Boolean fingerprintsNotApplicable;
    @XmlElement(name = "ApplicantData")
    protected ApplicantReadType applicantData;

    /**
     * Description: The date of request is taken from the application form.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getDateOfApplication() {
        return dateOfApplication;
    }

    /**
     * Imposta il valore della proprietà dateOfApplication.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     * @see #getDateOfApplication()
     */
    public void setDateOfApplication(XMLGregorianCalendar value) {
        this.dateOfApplication = value;
    }

    /**
     * Description: The duration of intended stay or transit is taken from the application form.
     * The unit is days. This regards short-term visas, which have a maximum validity of 3 months. Therefore, 3 digits are sufficient.
     * 
     * @return
     *     possible object is
     *     {@link Long }
     *     
     */
    public Long getDurationOfIntendedStayOrTransit() {
        return durationOfIntendedStayOrTransit;
    }

    /**
     * Imposta il valore della proprietà durationOfIntendedStayOrTransit.
     * 
     * @param value
     *     allowed object is
     *     {@link Long }
     *     
     * @see #getDurationOfIntendedStayOrTransit()
     */
    public void setDurationOfIntendedStayOrTransit(Long value) {
        this.durationOfIntendedStayOrTransit = value;
    }

    /**
     * Description: HostPerson or HostOrganisation.
     * 
     * @return
     *     possible object is
     *     {@link ResultReadCoreBorderDataType.Host }
     *     
     */
    public ResultReadCoreBorderDataType.Host getHost() {
        return host;
    }

    /**
     * Imposta il valore della proprietà host.
     * 
     * @param value
     *     allowed object is
     *     {@link ResultReadCoreBorderDataType.Host }
     *     
     * @see #getHost()
     */
    public void setHost(ResultReadCoreBorderDataType.Host value) {
        this.host = value;
    }

    /**
     * Recupera il valore della proprietà memberStateOfFirstEntry.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMemberStateOfFirstEntry() {
        return memberStateOfFirstEntry;
    }

    /**
     * Imposta il valore della proprietà memberStateOfFirstEntry.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMemberStateOfFirstEntry(String value) {
        this.memberStateOfFirstEntry = value;
    }

    /**
     * Description: The date of arrival is taken from the application form.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIntendedDateOfArrival() {
        return intendedDateOfArrival;
    }

    /**
     * Imposta il valore della proprietà intendedDateOfArrival.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     * @see #getIntendedDateOfArrival()
     */
    public void setIntendedDateOfArrival(String value) {
        this.intendedDateOfArrival = value;
    }

    /**
     * Description: The date of departure is taken from the application form.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIntendedDateOfDeparture() {
        return intendedDateOfDeparture;
    }

    /**
     * Imposta il valore della proprietà intendedDateOfDeparture.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     * @see #getIntendedDateOfDeparture()
     */
    public void setIntendedDateOfDeparture(String value) {
        this.intendedDateOfDeparture = value;
    }

    /**
     * Description: The member states destination is taken from the application form.
     * 
     * @return
     *     possible object is
     *     {@link MemberStatesOfDestinationType }
     *     
     */
    public MemberStatesOfDestinationType getMemberStatesOfDestination() {
        return memberStatesOfDestination;
    }

    /**
     * Imposta il valore della proprietà memberStatesOfDestination.
     * 
     * @param value
     *     allowed object is
     *     {@link MemberStatesOfDestinationType }
     *     
     * @see #getMemberStatesOfDestination()
     */
    public void setMemberStatesOfDestination(MemberStatesOfDestinationType value) {
        this.memberStatesOfDestination = value;
    }

    /**
     * Recupera il valore della proprietà occupation.
     * 
     * @return
     *     possible object is
     *     {@link OccupationGetType }
     *     
     */
    public OccupationGetType getOccupation() {
        return occupation;
    }

    /**
     * Imposta il valore della proprietà occupation.
     * 
     * @param value
     *     allowed object is
     *     {@link OccupationGetType }
     *     
     */
    public void setOccupation(OccupationGetType value) {
        this.occupation = value;
    }

    /**
     * Description: The place where the application has been lodged. Consists of a country code and the code for the place. This place could differ from the place of the applicationAuthorityLocation.
     * 
     * @return
     *     possible object is
     *     {@link PlaceGetType }
     *     
     */
    public PlaceGetType getPlaceOfApplication() {
        return placeOfApplication;
    }

    /**
     * Imposta il valore della proprietà placeOfApplication.
     * 
     * @param value
     *     allowed object is
     *     {@link PlaceGetType }
     *     
     * @see #getPlaceOfApplication()
     */
    public void setPlaceOfApplication(PlaceGetType value) {
        this.placeOfApplication = value;
    }

    /**
     * Recupera il valore della proprietà mainPurposesOfJourney.
     * 
     * @return
     *     possible object is
     *     {@link MainPurposesOfJourneyGetType }
     *     
     */
    public MainPurposesOfJourneyGetType getMainPurposesOfJourney() {
        return mainPurposesOfJourney;
    }

    /**
     * Imposta il valore della proprietà mainPurposesOfJourney.
     * 
     * @param value
     *     allowed object is
     *     {@link MainPurposesOfJourneyGetType }
     *     
     */
    public void setMainPurposesOfJourney(MainPurposesOfJourneyGetType value) {
        this.mainPurposesOfJourney = value;
    }

    /**
     * Description: The educational establishment in case the applicant is a student.
     * 
     * @return
     *     possible object is
     *     {@link EducationalEstablishmentGetType }
     *     
     */
    public EducationalEstablishmentGetType getEducationalEstablishment() {
        return educationalEstablishment;
    }

    /**
     * Imposta il valore della proprietà educationalEstablishment.
     * 
     * @param value
     *     allowed object is
     *     {@link EducationalEstablishmentGetType }
     *     
     * @see #getEducationalEstablishment()
     */
    public void setEducationalEstablishment(EducationalEstablishmentGetType value) {
        this.educationalEstablishment = value;
    }

    /**
     * Recupera il valore della proprietà travelDocument.
     * 
     * @return
     *     possible object is
     *     {@link TravelDocumentGetType }
     *     
     */
    public TravelDocumentGetType getTravelDocument() {
        return travelDocument;
    }

    /**
     * Imposta il valore della proprietà travelDocument.
     * 
     * @param value
     *     allowed object is
     *     {@link TravelDocumentGetType }
     *     
     */
    public void setTravelDocument(TravelDocumentGetType value) {
        this.travelDocument = value;
    }

    /**
     * Description: The requested visa type is taken from the application form (code table value).
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getVisaTypeRequested() {
        return visaTypeRequested;
    }

    /**
     * Imposta il valore della proprietà visaTypeRequested.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     * @see #getVisaTypeRequested()
     */
    public void setVisaTypeRequested(String value) {
        this.visaTypeRequested = value;
    }

    /**
     * Description:This pinpoints whether fingerprints are required or not. Fingerprints are required if this boolean is set to FALSE.
     * 
     * @return
     *     possible object is
     *     {@link Boolean }
     *     
     */
    public Boolean isFingerprintsNotRequired() {
        return fingerprintsNotRequired;
    }

    /**
     * Imposta il valore della proprietà fingerprintsNotRequired.
     * 
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *     
     * @see #isFingerprintsNotRequired()
     */
    public void setFingerprintsNotRequired(Boolean value) {
        this.fingerprintsNotRequired = value;
    }

    /**
     * Description: Indicates whether the fingerprints are not applicable. fingerprints are not applicable when the applicant physically cannot provide them (e.g. when the applicant has no hands) or when the fingerprints cannot be taken for a technical reason (e.g. there is no scanner available).
     * 
     * @return
     *     possible object is
     *     {@link Boolean }
     *     
     */
    public Boolean isFingerprintsNotApplicable() {
        return fingerprintsNotApplicable;
    }

    /**
     * Imposta il valore della proprietà fingerprintsNotApplicable.
     * 
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *     
     * @see #isFingerprintsNotApplicable()
     */
    public void setFingerprintsNotApplicable(Boolean value) {
        this.fingerprintsNotApplicable = value;
    }

    /**
     * Recupera il valore della proprietà applicantData.
     * 
     * @return
     *     possible object is
     *     {@link ApplicantReadType }
     *     
     */
    public ApplicantReadType getApplicantData() {
        return applicantData;
    }

    /**
     * Imposta il valore della proprietà applicantData.
     * 
     * @param value
     *     allowed object is
     *     {@link ApplicantReadType }
     *     
     */
    public void setApplicantData(ApplicantReadType value) {
        this.applicantData = value;
    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <choice>
     *         <element name="HostPerson" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}HostPersonGetType"/>
     *         <element name="HostOrganisation" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}HostOrganisationGetType"/>
     *       </choice>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "hostPerson",
        "hostOrganisation"
    })
    public static class Host {

        @XmlElement(name = "HostPerson")
        protected HostPersonGetType hostPerson;
        @XmlElement(name = "HostOrganisation")
        protected HostOrganisationGetType hostOrganisation;

        /**
         * Recupera il valore della proprietà hostPerson.
         * 
         * @return
         *     possible object is
         *     {@link HostPersonGetType }
         *     
         */
        public HostPersonGetType getHostPerson() {
            return hostPerson;
        }

        /**
         * Imposta il valore della proprietà hostPerson.
         * 
         * @param value
         *     allowed object is
         *     {@link HostPersonGetType }
         *     
         */
        public void setHostPerson(HostPersonGetType value) {
            this.hostPerson = value;
        }

        /**
         * Recupera il valore della proprietà hostOrganisation.
         * 
         * @return
         *     possible object is
         *     {@link HostOrganisationGetType }
         *     
         */
        public HostOrganisationGetType getHostOrganisation() {
            return hostOrganisation;
        }

        /**
         * Imposta il valore della proprietà hostOrganisation.
         * 
         * @param value
         *     allowed object is
         *     {@link HostOrganisationGetType }
         *     
         */
        public void setHostOrganisation(HostOrganisationGetType value) {
            this.hostOrganisation = value;
        }

    }

}
