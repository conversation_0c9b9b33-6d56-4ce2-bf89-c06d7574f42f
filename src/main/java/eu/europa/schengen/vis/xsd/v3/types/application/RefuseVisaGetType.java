//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.vis.xsd.v3.types.application;

import java.util.ArrayList;
import java.util.List;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * Description: All information needed for the decision "RefuseVisa".
 * 
 * <p>Classe Java per RefuseVisaGetType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="RefuseVisaGetType">
 *   <complexContent>
 *     <extension base="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}DecisionGetType">
 *       <sequence>
 *         <element name="RefusalGrounds">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="RefusalGround" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}CT52_RefusalGroundType" maxOccurs="unbounded"/>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *       </sequence>
 *     </extension>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "RefuseVisaGetType", propOrder = {
    "refusalGrounds"
})
public class RefuseVisaGetType
    extends DecisionGetType
{

    /**
     * Description: Grounds for the refusal of the visa as described in [VIS-PEP]. Values are defined within the code table RefusalGround.
     * 
     */
    @XmlElement(name = "RefusalGrounds", required = true)
    protected RefuseVisaGetType.RefusalGrounds refusalGrounds;

    /**
     * Description: Grounds for the refusal of the visa as described in [VIS-PEP]. Values are defined within the code table RefusalGround.
     * 
     * @return
     *     possible object is
     *     {@link RefuseVisaGetType.RefusalGrounds }
     *     
     */
    public RefuseVisaGetType.RefusalGrounds getRefusalGrounds() {
        return refusalGrounds;
    }

    /**
     * Imposta il valore della proprietà refusalGrounds.
     * 
     * @param value
     *     allowed object is
     *     {@link RefuseVisaGetType.RefusalGrounds }
     *     
     * @see #getRefusalGrounds()
     */
    public void setRefusalGrounds(RefuseVisaGetType.RefusalGrounds value) {
        this.refusalGrounds = value;
    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="RefusalGround" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}CT52_RefusalGroundType" maxOccurs="unbounded"/>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "refusalGround"
    })
    public static class RefusalGrounds {

        @XmlElement(name = "RefusalGround", required = true)
        protected List<String> refusalGround;

        /**
         * Gets the value of the refusalGround property.
         * 
         * <p>This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the refusalGround property.</p>
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * </p>
         * <pre>
         * getRefusalGround().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link String }
         * </p>
         * 
         * 
         * @return
         *     The value of the refusalGround property.
         */
        public List<String> getRefusalGround() {
            if (refusalGround == null) {
                refusalGround = new ArrayList<>();
            }
            return this.refusalGround;
        }

    }

}
