//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.vis.xsd.v3.types.application;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSeeAlso;
import jakarta.xml.bind.annotation.XmlType;


/**
 * Description: Information needed for the creation of a decision.
 * 
 * <p>Classe Java per DecisionNewType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="DecisionNewType">
 *   <complexContent>
 *     <extension base="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}DecisionBaseRequestType">
 *       <sequence>
 *         <element name="DecisionPlace" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}PlaceNewType"/>
 *       </sequence>
 *     </extension>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "DecisionNewType", propOrder = {
    "decisionPlace"
})
@XmlSeeAlso({
    DiscontinueExaminationNewType.class,
    RefuseVisaNewType.class,
    GrantVisaNewType.class,
    IssueVisaNewType.class,
    ExtendVisaCorrectType.class,
    ExtendVisaNewType.class,
    RevokeVisaNewType.class,
    AnnulVisaNewType.class
})
public class DecisionNewType
    extends DecisionBaseRequestType
{

    /**
     * Description: The place where the decision has been made (for example at an airport). Consists of a country code and value for the place.
     * 
     */
    @XmlElement(name = "DecisionPlace", required = true)
    protected PlaceNewType decisionPlace;

    /**
     * Description: The place where the decision has been made (for example at an airport). Consists of a country code and value for the place.
     * 
     * @return
     *     possible object is
     *     {@link PlaceNewType }
     *     
     */
    public PlaceNewType getDecisionPlace() {
        return decisionPlace;
    }

    /**
     * Imposta il valore della proprietà decisionPlace.
     * 
     * @param value
     *     allowed object is
     *     {@link PlaceNewType }
     *     
     * @see #getDecisionPlace()
     */
    public void setDecisionPlace(PlaceNewType value) {
        this.decisionPlace = value;
    }

}
