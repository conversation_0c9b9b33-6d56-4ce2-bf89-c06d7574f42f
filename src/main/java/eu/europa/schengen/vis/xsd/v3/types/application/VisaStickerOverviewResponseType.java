//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.vis.xsd.v3.types.application;

import eu.europa.schengen.vis.xsd.v3.types.common.PeriodType;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per VisaStickerOverviewResponseType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="VisaStickerOverviewResponseType">
 *   <complexContent>
 *     <extension base="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}DecisionOverviewBaseType">
 *       <sequence>
 *         <element name="Owner" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}CT70_UserType" minOccurs="0"/>
 *         <element name="RepresentedUser" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}CT70_UserType" minOccurs="0"/>
 *         <element name="DurationOfStay" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}DurationType"/>
 *         <element name="PeriodOfValidity" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}PeriodType"/>
 *         <element name="VisaType" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}VisaTypeType"/>
 *       </sequence>
 *     </extension>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "VisaStickerOverviewResponseType", propOrder = {
    "owner",
    "representedUser",
    "durationOfStay",
    "periodOfValidity",
    "visaType"
})
public class VisaStickerOverviewResponseType
    extends DecisionOverviewBaseType
{

    /**
     * Description: Owner of the decision.
     * 
     */
    @XmlElement(name = "Owner")
    protected String owner;
    /**
     * Decription: Represented user of the decision.
     * 
     */
    @XmlElement(name = "RepresentedUser")
    protected String representedUser;
    /**
     * Description: The duration of the stay, this is stated in the visa.
     * 
     */
    @XmlElement(name = "DurationOfStay")
    protected long durationOfStay;
    /**
     * Description: The visa's period of validity.
     * 
     */
    @XmlElement(name = "PeriodOfValidity", required = true)
    protected PeriodType periodOfValidity;
    /**
     * Description: The type of the visa.
     * 
     */
    @XmlElement(name = "VisaType", required = true)
    protected VisaTypeType visaType;

    /**
     * Description: Owner of the decision.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getOwner() {
        return owner;
    }

    /**
     * Imposta il valore della proprietà owner.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     * @see #getOwner()
     */
    public void setOwner(String value) {
        this.owner = value;
    }

    /**
     * Decription: Represented user of the decision.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRepresentedUser() {
        return representedUser;
    }

    /**
     * Imposta il valore della proprietà representedUser.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     * @see #getRepresentedUser()
     */
    public void setRepresentedUser(String value) {
        this.representedUser = value;
    }

    /**
     * Description: The duration of the stay, this is stated in the visa.
     * 
     */
    public long getDurationOfStay() {
        return durationOfStay;
    }

    /**
     * Imposta il valore della proprietà durationOfStay.
     * 
     */
    public void setDurationOfStay(long value) {
        this.durationOfStay = value;
    }

    /**
     * Description: The visa's period of validity.
     * 
     * @return
     *     possible object is
     *     {@link PeriodType }
     *     
     */
    public PeriodType getPeriodOfValidity() {
        return periodOfValidity;
    }

    /**
     * Imposta il valore della proprietà periodOfValidity.
     * 
     * @param value
     *     allowed object is
     *     {@link PeriodType }
     *     
     * @see #getPeriodOfValidity()
     */
    public void setPeriodOfValidity(PeriodType value) {
        this.periodOfValidity = value;
    }

    /**
     * Description: The type of the visa.
     * 
     * @return
     *     possible object is
     *     {@link VisaTypeType }
     *     
     */
    public VisaTypeType getVisaType() {
        return visaType;
    }

    /**
     * Imposta il valore della proprietà visaType.
     * 
     * @param value
     *     allowed object is
     *     {@link VisaTypeType }
     *     
     * @see #getVisaType()
     */
    public void setVisaType(VisaTypeType value) {
        this.visaType = value;
    }

}
