//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.vis.xsd.v3.types.application;

import java.util.ArrayList;
import java.util.List;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * Description: Response for an Identification searching.
 * 
 * <p>Classe Java per ResultVerificationSearchType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="ResultVerificationSearchType">
 *   <complexContent>
 *     <extension base="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}ResultFoundDossierType">
 *       <sequence>
 *         <element name="VerificationOverviews">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="VerificationOverview" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}ResultVerificationOverviewType" maxOccurs="unbounded"/>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *       </sequence>
 *     </extension>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ResultVerificationSearchType", propOrder = {
    "verificationOverviews"
})
public class ResultVerificationSearchType
    extends ResultFoundDossierType
{

    /**
     * Description: list of the applications in the dossier.
     * 
     */
    @XmlElement(name = "VerificationOverviews", required = true)
    protected ResultVerificationSearchType.VerificationOverviews verificationOverviews;

    /**
     * Description: list of the applications in the dossier.
     * 
     * @return
     *     possible object is
     *     {@link ResultVerificationSearchType.VerificationOverviews }
     *     
     */
    public ResultVerificationSearchType.VerificationOverviews getVerificationOverviews() {
        return verificationOverviews;
    }

    /**
     * Imposta il valore della proprietà verificationOverviews.
     * 
     * @param value
     *     allowed object is
     *     {@link ResultVerificationSearchType.VerificationOverviews }
     *     
     * @see #getVerificationOverviews()
     */
    public void setVerificationOverviews(ResultVerificationSearchType.VerificationOverviews value) {
        this.verificationOverviews = value;
    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="VerificationOverview" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}ResultVerificationOverviewType" maxOccurs="unbounded"/>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "verificationOverview"
    })
    public static class VerificationOverviews {

        @XmlElement(name = "VerificationOverview", required = true)
        protected List<ResultVerificationOverviewType> verificationOverview;

        /**
         * Gets the value of the verificationOverview property.
         * 
         * <p>This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the verificationOverview property.</p>
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * </p>
         * <pre>
         * getVerificationOverview().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link ResultVerificationOverviewType }
         * </p>
         * 
         * 
         * @return
         *     The value of the verificationOverview property.
         */
        public List<ResultVerificationOverviewType> getVerificationOverview() {
            if (verificationOverview == null) {
                verificationOverview = new ArrayList<>();
            }
            return this.verificationOverview;
        }

    }

}
