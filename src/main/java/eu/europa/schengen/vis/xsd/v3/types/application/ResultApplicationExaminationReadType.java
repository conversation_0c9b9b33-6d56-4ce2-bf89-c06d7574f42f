//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.vis.xsd.v3.types.application;

import java.util.ArrayList;
import java.util.List;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * Description: Result of the retrieval of an application for the purpose of an examination.
 * 
 * <p>Classe Java per ResultApplicationExaminationReadType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="ResultApplicationExaminationReadType">
 *   <complexContent>
 *     <extension base="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}ResultReadApplicationBaseType">
 *       <sequence>
 *         <element name="Owner" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}CT70_UserType"/>
 *         <element name="RepresentedUser" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}CT70_UserType" minOccurs="0"/>
 *         <element name="ApplicationData">
 *           <complexType>
 *             <complexContent>
 *               <extension base="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}ResultReadCoreDataBaseType">
 *                 <sequence>
 *                   <element name="ApplicationStatus" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}ST31_ApplicationStatusType"/>
 *                   <element name="FingerprintsNotRequired" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}YesNoType"/>
 *                   <element name="FingerprintsNotApplicable" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}YesNoType"/>
 *                   <element name="ReasonForFingerprintNotApplicable" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}ReasonForFingerprintNotApplicableType" minOccurs="0"/>
 *                 </sequence>
 *               </extension>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *         <element name="Decisions" minOccurs="0">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="Decision" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}DecisionHistGetType" maxOccurs="unbounded"/>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *         <element name="Attachments" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}BiometricsDescriptionGetType" minOccurs="0"/>
 *         <element name="GroupSynopses" minOccurs="0">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="GroupSynopsis" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}GroupSynopsisType" maxOccurs="unbounded"/>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *       </sequence>
 *     </extension>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ResultApplicationExaminationReadType", propOrder = {
    "owner",
    "representedUser",
    "applicationData",
    "decisions",
    "attachments",
    "groupSynopses"
})
public class ResultApplicationExaminationReadType
    extends ResultReadApplicationBaseType
{

    /**
     * Description: Owner of the Application.
     * 
     */
    @XmlElement(name = "Owner", required = true)
    protected String owner;
    /**
     * Description: The represented user of the Application.
     * 
     */
    @XmlElement(name = "RepresentedUser")
    protected String representedUser;
    /**
     * Description: This element is a subset of the Application.
     * 
     */
    @XmlElement(name = "ApplicationData", required = true)
    protected ResultApplicationExaminationReadType.ApplicationData applicationData;
    @XmlElement(name = "Decisions")
    protected ResultApplicationExaminationReadType.Decisions decisions;
    @XmlElement(name = "Attachments")
    protected BiometricsDescriptionGetType attachments;
    @XmlElement(name = "GroupSynopses")
    protected ResultApplicationExaminationReadType.GroupSynopses groupSynopses;

    /**
     * Description: Owner of the Application.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getOwner() {
        return owner;
    }

    /**
     * Imposta il valore della proprietà owner.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     * @see #getOwner()
     */
    public void setOwner(String value) {
        this.owner = value;
    }

    /**
     * Description: The represented user of the Application.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRepresentedUser() {
        return representedUser;
    }

    /**
     * Imposta il valore della proprietà representedUser.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     * @see #getRepresentedUser()
     */
    public void setRepresentedUser(String value) {
        this.representedUser = value;
    }

    /**
     * Description: This element is a subset of the Application.
     * 
     * @return
     *     possible object is
     *     {@link ResultApplicationExaminationReadType.ApplicationData }
     *     
     */
    public ResultApplicationExaminationReadType.ApplicationData getApplicationData() {
        return applicationData;
    }

    /**
     * Imposta il valore della proprietà applicationData.
     * 
     * @param value
     *     allowed object is
     *     {@link ResultApplicationExaminationReadType.ApplicationData }
     *     
     * @see #getApplicationData()
     */
    public void setApplicationData(ResultApplicationExaminationReadType.ApplicationData value) {
        this.applicationData = value;
    }

    /**
     * Recupera il valore della proprietà decisions.
     * 
     * @return
     *     possible object is
     *     {@link ResultApplicationExaminationReadType.Decisions }
     *     
     */
    public ResultApplicationExaminationReadType.Decisions getDecisions() {
        return decisions;
    }

    /**
     * Imposta il valore della proprietà decisions.
     * 
     * @param value
     *     allowed object is
     *     {@link ResultApplicationExaminationReadType.Decisions }
     *     
     */
    public void setDecisions(ResultApplicationExaminationReadType.Decisions value) {
        this.decisions = value;
    }

    /**
     * Recupera il valore della proprietà attachments.
     * 
     * @return
     *     possible object is
     *     {@link BiometricsDescriptionGetType }
     *     
     */
    public BiometricsDescriptionGetType getAttachments() {
        return attachments;
    }

    /**
     * Imposta il valore della proprietà attachments.
     * 
     * @param value
     *     allowed object is
     *     {@link BiometricsDescriptionGetType }
     *     
     */
    public void setAttachments(BiometricsDescriptionGetType value) {
        this.attachments = value;
    }

    /**
     * Recupera il valore della proprietà groupSynopses.
     * 
     * @return
     *     possible object is
     *     {@link ResultApplicationExaminationReadType.GroupSynopses }
     *     
     */
    public ResultApplicationExaminationReadType.GroupSynopses getGroupSynopses() {
        return groupSynopses;
    }

    /**
     * Imposta il valore della proprietà groupSynopses.
     * 
     * @param value
     *     allowed object is
     *     {@link ResultApplicationExaminationReadType.GroupSynopses }
     *     
     */
    public void setGroupSynopses(ResultApplicationExaminationReadType.GroupSynopses value) {
        this.groupSynopses = value;
    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <extension base="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}ResultReadCoreDataBaseType">
     *       <sequence>
     *         <element name="ApplicationStatus" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}ST31_ApplicationStatusType"/>
     *         <element name="FingerprintsNotRequired" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}YesNoType"/>
     *         <element name="FingerprintsNotApplicable" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}YesNoType"/>
     *         <element name="ReasonForFingerprintNotApplicable" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}ReasonForFingerprintNotApplicableType" minOccurs="0"/>
     *       </sequence>
     *     </extension>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "applicationStatus",
        "fingerprintsNotRequired",
        "fingerprintsNotApplicable",
        "reasonForFingerprintNotApplicable"
    })
    public static class ApplicationData
        extends ResultReadCoreDataBaseType
    {

        /**
         * Description: Status of the application (code table value). This Status is the outcome of the decision history.
         * 
         */
        @XmlElement(name = "ApplicationStatus", required = true)
        protected String applicationStatus;
        /**
         * Description: This pinpoints whether fingerprints are required or not. Fingerprints are required if this Boolean is set to FALSE.
         * 
         */
        @XmlElement(name = "FingerprintsNotRequired")
        protected boolean fingerprintsNotRequired;
        /**
         * Description: Indicates whether the fingerprints are not applicable. fingerprints are not applicable when the applicant physically cannot provide them (e.g. when the applicant has no hands) or when the fingerprints cannot be taken for a technical reason (e.g. there is no scanner available).
         * 
         */
        @XmlElement(name = "FingerprintsNotApplicable")
        protected boolean fingerprintsNotApplicable;
        /**
         * Description: A free text field to specify why the fingerprints are not applicable.
         * 
         */
        @XmlElement(name = "ReasonForFingerprintNotApplicable")
        protected String reasonForFingerprintNotApplicable;

        /**
         * Description: Status of the application (code table value). This Status is the outcome of the decision history.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getApplicationStatus() {
            return applicationStatus;
        }

        /**
         * Imposta il valore della proprietà applicationStatus.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         * @see #getApplicationStatus()
         */
        public void setApplicationStatus(String value) {
            this.applicationStatus = value;
        }

        /**
         * Description: This pinpoints whether fingerprints are required or not. Fingerprints are required if this Boolean is set to FALSE.
         * 
         */
        public boolean isFingerprintsNotRequired() {
            return fingerprintsNotRequired;
        }

        /**
         * Imposta il valore della proprietà fingerprintsNotRequired.
         * 
         */
        public void setFingerprintsNotRequired(boolean value) {
            this.fingerprintsNotRequired = value;
        }

        /**
         * Description: Indicates whether the fingerprints are not applicable. fingerprints are not applicable when the applicant physically cannot provide them (e.g. when the applicant has no hands) or when the fingerprints cannot be taken for a technical reason (e.g. there is no scanner available).
         * 
         */
        public boolean isFingerprintsNotApplicable() {
            return fingerprintsNotApplicable;
        }

        /**
         * Imposta il valore della proprietà fingerprintsNotApplicable.
         * 
         */
        public void setFingerprintsNotApplicable(boolean value) {
            this.fingerprintsNotApplicable = value;
        }

        /**
         * Description: A free text field to specify why the fingerprints are not applicable.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getReasonForFingerprintNotApplicable() {
            return reasonForFingerprintNotApplicable;
        }

        /**
         * Imposta il valore della proprietà reasonForFingerprintNotApplicable.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         * @see #getReasonForFingerprintNotApplicable()
         */
        public void setReasonForFingerprintNotApplicable(String value) {
            this.reasonForFingerprintNotApplicable = value;
        }

    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="Decision" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}DecisionHistGetType" maxOccurs="unbounded"/>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "decision"
    })
    public static class Decisions {

        @XmlElement(name = "Decision", required = true)
        protected List<DecisionHistGetType> decision;

        /**
         * Gets the value of the decision property.
         * 
         * <p>This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the decision property.</p>
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * </p>
         * <pre>
         * getDecision().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link DecisionHistGetType }
         * </p>
         * 
         * 
         * @return
         *     The value of the decision property.
         */
        public List<DecisionHistGetType> getDecision() {
            if (decision == null) {
                decision = new ArrayList<>();
            }
            return this.decision;
        }

    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="GroupSynopsis" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}GroupSynopsisType" maxOccurs="unbounded"/>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "groupSynopsis"
    })
    public static class GroupSynopses {

        @XmlElement(name = "GroupSynopsis", required = true)
        protected List<GroupSynopsisType> groupSynopsis;

        /**
         * Gets the value of the groupSynopsis property.
         * 
         * <p>This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the groupSynopsis property.</p>
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * </p>
         * <pre>
         * getGroupSynopsis().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link GroupSynopsisType }
         * </p>
         * 
         * 
         * @return
         *     The value of the groupSynopsis property.
         */
        public List<GroupSynopsisType> getGroupSynopsis() {
            if (groupSynopsis == null) {
                groupSynopsis = new ArrayList<>();
            }
            return this.groupSynopsis;
        }

    }

}
