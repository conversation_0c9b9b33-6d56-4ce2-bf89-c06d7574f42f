//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.vis.xsd.v3.types.application;

import java.util.ArrayList;
import java.util.List;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per ResultListApplicationsInFamilyGroupType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="ResultListApplicationsInFamilyGroupType">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="GroupID" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}GroupIDType"/>
 *         <element name="NumberOfElements" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}ReturnNumberType"/>
 *         <element name="FamilyGroupOverviews">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="FamilyGroupOverview" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}ListApplicationsInFamilyGroupOverviewType" maxOccurs="unbounded"/>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ResultListApplicationsInFamilyGroupType", propOrder = {
    "groupID",
    "numberOfElements",
    "familyGroupOverviews"
})
public class ResultListApplicationsInFamilyGroupType {

    /**
     * Description: ID of the group.
     * 
     */
    @XmlElement(name = "GroupID")
    protected long groupID;
    /**
     * Description: This field has been added to allow the MS to identify if the result contains 0 reccord or if it is empty due to an error.
     * 
     */
    @XmlElement(name = "NumberOfElements")
    protected long numberOfElements;
    @XmlElement(name = "FamilyGroupOverviews", required = true)
    protected ResultListApplicationsInFamilyGroupType.FamilyGroupOverviews familyGroupOverviews;

    /**
     * Description: ID of the group.
     * 
     */
    public long getGroupID() {
        return groupID;
    }

    /**
     * Imposta il valore della proprietà groupID.
     * 
     */
    public void setGroupID(long value) {
        this.groupID = value;
    }

    /**
     * Description: This field has been added to allow the MS to identify if the result contains 0 reccord or if it is empty due to an error.
     * 
     */
    public long getNumberOfElements() {
        return numberOfElements;
    }

    /**
     * Imposta il valore della proprietà numberOfElements.
     * 
     */
    public void setNumberOfElements(long value) {
        this.numberOfElements = value;
    }

    /**
     * Recupera il valore della proprietà familyGroupOverviews.
     * 
     * @return
     *     possible object is
     *     {@link ResultListApplicationsInFamilyGroupType.FamilyGroupOverviews }
     *     
     */
    public ResultListApplicationsInFamilyGroupType.FamilyGroupOverviews getFamilyGroupOverviews() {
        return familyGroupOverviews;
    }

    /**
     * Imposta il valore della proprietà familyGroupOverviews.
     * 
     * @param value
     *     allowed object is
     *     {@link ResultListApplicationsInFamilyGroupType.FamilyGroupOverviews }
     *     
     */
    public void setFamilyGroupOverviews(ResultListApplicationsInFamilyGroupType.FamilyGroupOverviews value) {
        this.familyGroupOverviews = value;
    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="FamilyGroupOverview" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}ListApplicationsInFamilyGroupOverviewType" maxOccurs="unbounded"/>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "familyGroupOverview"
    })
    public static class FamilyGroupOverviews {

        @XmlElement(name = "FamilyGroupOverview", required = true)
        protected List<ListApplicationsInFamilyGroupOverviewType> familyGroupOverview;

        /**
         * Gets the value of the familyGroupOverview property.
         * 
         * <p>This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the familyGroupOverview property.</p>
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * </p>
         * <pre>
         * getFamilyGroupOverview().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link ListApplicationsInFamilyGroupOverviewType }
         * </p>
         * 
         * 
         * @return
         *     The value of the familyGroupOverview property.
         */
        public List<ListApplicationsInFamilyGroupOverviewType> getFamilyGroupOverview() {
            if (familyGroupOverview == null) {
                familyGroupOverview = new ArrayList<>();
            }
            return this.familyGroupOverview;
        }

    }

}
