//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.vis.xsd.v3.types.application;

import java.util.ArrayList;
import java.util.List;
import javax.xml.datatype.XMLGregorianCalendar;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlType;


/**
 * Description: All information needed for the decision "Revoke Visa".
 * 
 * <p>Classe Java per RevokeVisaNewType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="RevokeVisaNewType">
 *   <complexContent>
 *     <extension base="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}DecisionNewType">
 *       <sequence>
 *         <element name="NewExpiryDate" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}TimeStampType" minOccurs="0"/>
 *         <element name="RevocationGrounds">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="RevocationGround" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}CT54_RevocationGroundType" maxOccurs="unbounded"/>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *       </sequence>
 *     </extension>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "RevokeVisaNewType", propOrder = {
    "newExpiryDate",
    "revocationGrounds"
})
public class RevokeVisaNewType
    extends DecisionNewType
{

    /**
     * Description: The (optional)new expiry date as described in [VIS-PEP].
     * 
     */
    @XmlElement(name = "NewExpiryDate")
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar newExpiryDate;
    /**
     * Description: Grounds for the revocation of the visa as described in the [VIS-PEP]. Values are defined within the code table "RevocationGround".
     * 
     */
    @XmlElement(name = "RevocationGrounds", required = true)
    protected RevokeVisaNewType.RevocationGrounds revocationGrounds;

    /**
     * Description: The (optional)new expiry date as described in [VIS-PEP].
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getNewExpiryDate() {
        return newExpiryDate;
    }

    /**
     * Imposta il valore della proprietà newExpiryDate.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     * @see #getNewExpiryDate()
     */
    public void setNewExpiryDate(XMLGregorianCalendar value) {
        this.newExpiryDate = value;
    }

    /**
     * Description: Grounds for the revocation of the visa as described in the [VIS-PEP]. Values are defined within the code table "RevocationGround".
     * 
     * @return
     *     possible object is
     *     {@link RevokeVisaNewType.RevocationGrounds }
     *     
     */
    public RevokeVisaNewType.RevocationGrounds getRevocationGrounds() {
        return revocationGrounds;
    }

    /**
     * Imposta il valore della proprietà revocationGrounds.
     * 
     * @param value
     *     allowed object is
     *     {@link RevokeVisaNewType.RevocationGrounds }
     *     
     * @see #getRevocationGrounds()
     */
    public void setRevocationGrounds(RevokeVisaNewType.RevocationGrounds value) {
        this.revocationGrounds = value;
    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="RevocationGround" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}CT54_RevocationGroundType" maxOccurs="unbounded"/>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "revocationGround"
    })
    public static class RevocationGrounds {

        @XmlElement(name = "RevocationGround", required = true)
        protected List<String> revocationGround;

        /**
         * Gets the value of the revocationGround property.
         * 
         * <p>This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the revocationGround property.</p>
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * </p>
         * <pre>
         * getRevocationGround().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link String }
         * </p>
         * 
         * 
         * @return
         *     The value of the revocationGround property.
         */
        public List<String> getRevocationGround() {
            if (revocationGround == null) {
                revocationGround = new ArrayList<>();
            }
            return this.revocationGround;
        }

    }

}
