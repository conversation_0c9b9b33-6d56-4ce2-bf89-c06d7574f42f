//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.vis.xsd.v3.types.application;

import java.util.ArrayList;
import java.util.List;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * Description: Response for an identification retrieving.
 * 
 * <p>Classe Java per ResultIdentificationReadType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="ResultIdentificationReadType">
 *   <complexContent>
 *     <extension base="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}ResultReadApplicationBaseType">
 *       <sequence>
 *         <element name="ApplicationData">
 *           <complexType>
 *             <complexContent>
 *               <extension base="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}ResultReadCoreDataBaseType">
 *                 <sequence>
 *                   <element name="ApplicationStatus" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}ST31_ApplicationStatusType"/>
 *                 </sequence>
 *               </extension>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *         <element name="Decisions" minOccurs="0">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="Decision" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}IdentificationDecisionHistGetType" maxOccurs="unbounded"/>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *         <element name="FacialImages" minOccurs="0">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="FacialImage" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}FacialImageGetType" maxOccurs="unbounded"/>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *         <element name="GroupSynopses" minOccurs="0">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="GroupSynopsis" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}GroupSynopsisType" maxOccurs="unbounded"/>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *       </sequence>
 *     </extension>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ResultIdentificationReadType", propOrder = {
    "applicationData",
    "decisions",
    "facialImages",
    "groupSynopses"
})
public class ResultIdentificationReadType
    extends ResultReadApplicationBaseType
{

    /**
     * Description: This element is a subset of the Application.
     * 
     */
    @XmlElement(name = "ApplicationData", required = true)
    protected ResultIdentificationReadType.ApplicationData applicationData;
    @XmlElement(name = "Decisions")
    protected ResultIdentificationReadType.Decisions decisions;
    @XmlElement(name = "FacialImages")
    protected ResultIdentificationReadType.FacialImages facialImages;
    @XmlElement(name = "GroupSynopses")
    protected ResultIdentificationReadType.GroupSynopses groupSynopses;

    /**
     * Description: This element is a subset of the Application.
     * 
     * @return
     *     possible object is
     *     {@link ResultIdentificationReadType.ApplicationData }
     *     
     */
    public ResultIdentificationReadType.ApplicationData getApplicationData() {
        return applicationData;
    }

    /**
     * Imposta il valore della proprietà applicationData.
     * 
     * @param value
     *     allowed object is
     *     {@link ResultIdentificationReadType.ApplicationData }
     *     
     * @see #getApplicationData()
     */
    public void setApplicationData(ResultIdentificationReadType.ApplicationData value) {
        this.applicationData = value;
    }

    /**
     * Recupera il valore della proprietà decisions.
     * 
     * @return
     *     possible object is
     *     {@link ResultIdentificationReadType.Decisions }
     *     
     */
    public ResultIdentificationReadType.Decisions getDecisions() {
        return decisions;
    }

    /**
     * Imposta il valore della proprietà decisions.
     * 
     * @param value
     *     allowed object is
     *     {@link ResultIdentificationReadType.Decisions }
     *     
     */
    public void setDecisions(ResultIdentificationReadType.Decisions value) {
        this.decisions = value;
    }

    /**
     * Recupera il valore della proprietà facialImages.
     * 
     * @return
     *     possible object is
     *     {@link ResultIdentificationReadType.FacialImages }
     *     
     */
    public ResultIdentificationReadType.FacialImages getFacialImages() {
        return facialImages;
    }

    /**
     * Imposta il valore della proprietà facialImages.
     * 
     * @param value
     *     allowed object is
     *     {@link ResultIdentificationReadType.FacialImages }
     *     
     */
    public void setFacialImages(ResultIdentificationReadType.FacialImages value) {
        this.facialImages = value;
    }

    /**
     * Recupera il valore della proprietà groupSynopses.
     * 
     * @return
     *     possible object is
     *     {@link ResultIdentificationReadType.GroupSynopses }
     *     
     */
    public ResultIdentificationReadType.GroupSynopses getGroupSynopses() {
        return groupSynopses;
    }

    /**
     * Imposta il valore della proprietà groupSynopses.
     * 
     * @param value
     *     allowed object is
     *     {@link ResultIdentificationReadType.GroupSynopses }
     *     
     */
    public void setGroupSynopses(ResultIdentificationReadType.GroupSynopses value) {
        this.groupSynopses = value;
    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <extension base="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}ResultReadCoreDataBaseType">
     *       <sequence>
     *         <element name="ApplicationStatus" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}ST31_ApplicationStatusType"/>
     *       </sequence>
     *     </extension>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "applicationStatus"
    })
    public static class ApplicationData
        extends ResultReadCoreDataBaseType
    {

        /**
         * Description: Status of the application (code table value). This Status is the outcome of the decision history.
         * 
         */
        @XmlElement(name = "ApplicationStatus", required = true)
        protected String applicationStatus;

        /**
         * Description: Status of the application (code table value). This Status is the outcome of the decision history.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getApplicationStatus() {
            return applicationStatus;
        }

        /**
         * Imposta il valore della proprietà applicationStatus.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         * @see #getApplicationStatus()
         */
        public void setApplicationStatus(String value) {
            this.applicationStatus = value;
        }

    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="Decision" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}IdentificationDecisionHistGetType" maxOccurs="unbounded"/>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "decision"
    })
    public static class Decisions {

        @XmlElement(name = "Decision", required = true)
        protected List<IdentificationDecisionHistGetType> decision;

        /**
         * Gets the value of the decision property.
         * 
         * <p>This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the decision property.</p>
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * </p>
         * <pre>
         * getDecision().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link IdentificationDecisionHistGetType }
         * </p>
         * 
         * 
         * @return
         *     The value of the decision property.
         */
        public List<IdentificationDecisionHistGetType> getDecision() {
            if (decision == null) {
                decision = new ArrayList<>();
            }
            return this.decision;
        }

    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="FacialImage" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}FacialImageGetType" maxOccurs="unbounded"/>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "facialImage"
    })
    public static class FacialImages {

        @XmlElement(name = "FacialImage", required = true)
        protected List<FacialImageGetType> facialImage;

        /**
         * Gets the value of the facialImage property.
         * 
         * <p>This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the facialImage property.</p>
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * </p>
         * <pre>
         * getFacialImage().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link FacialImageGetType }
         * </p>
         * 
         * 
         * @return
         *     The value of the facialImage property.
         */
        public List<FacialImageGetType> getFacialImage() {
            if (facialImage == null) {
                facialImage = new ArrayList<>();
            }
            return this.facialImage;
        }

    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="GroupSynopsis" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}GroupSynopsisType" maxOccurs="unbounded"/>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "groupSynopsis"
    })
    public static class GroupSynopses {

        @XmlElement(name = "GroupSynopsis", required = true)
        protected List<GroupSynopsisType> groupSynopsis;

        /**
         * Gets the value of the groupSynopsis property.
         * 
         * <p>This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the groupSynopsis property.</p>
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * </p>
         * <pre>
         * getGroupSynopsis().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link GroupSynopsisType }
         * </p>
         * 
         * 
         * @return
         *     The value of the groupSynopsis property.
         */
        public List<GroupSynopsisType> getGroupSynopsis() {
            if (groupSynopsis == null) {
                groupSynopsis = new ArrayList<>();
            }
            return this.groupSynopsis;
        }

    }

}
