//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.vis.xsd.v3.types.application;

import java.util.ArrayList;
import java.util.List;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per ResultListApplicationsInGroupType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="ResultListApplicationsInGroupType">
 *   <complexContent>
 *     <extension base="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}GroupSynopsisType">
 *       <sequence>
 *         <element name="NumberOfElements" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}ReturnNumberType"/>
 *         <element name="GroupOverviews">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="GroupOverview" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}ListApplicationsInGroupOverviewType" maxOccurs="unbounded"/>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *       </sequence>
 *     </extension>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ResultListApplicationsInGroupType", propOrder = {
    "numberOfElements",
    "groupOverviews"
})
public class ResultListApplicationsInGroupType
    extends GroupSynopsisType
{

    /**
     * Description: This field has been added to allow the MS to identify if the result contains 0 reccord or if it is empty due to an error.
     * 
     */
    @XmlElement(name = "NumberOfElements")
    protected long numberOfElements;
    @XmlElement(name = "GroupOverviews", required = true)
    protected ResultListApplicationsInGroupType.GroupOverviews groupOverviews;

    /**
     * Description: This field has been added to allow the MS to identify if the result contains 0 reccord or if it is empty due to an error.
     * 
     */
    public long getNumberOfElements() {
        return numberOfElements;
    }

    /**
     * Imposta il valore della proprietà numberOfElements.
     * 
     */
    public void setNumberOfElements(long value) {
        this.numberOfElements = value;
    }

    /**
     * Recupera il valore della proprietà groupOverviews.
     * 
     * @return
     *     possible object is
     *     {@link ResultListApplicationsInGroupType.GroupOverviews }
     *     
     */
    public ResultListApplicationsInGroupType.GroupOverviews getGroupOverviews() {
        return groupOverviews;
    }

    /**
     * Imposta il valore della proprietà groupOverviews.
     * 
     * @param value
     *     allowed object is
     *     {@link ResultListApplicationsInGroupType.GroupOverviews }
     *     
     */
    public void setGroupOverviews(ResultListApplicationsInGroupType.GroupOverviews value) {
        this.groupOverviews = value;
    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="GroupOverview" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}ListApplicationsInGroupOverviewType" maxOccurs="unbounded"/>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "groupOverview"
    })
    public static class GroupOverviews {

        @XmlElement(name = "GroupOverview", required = true)
        protected List<ListApplicationsInGroupOverviewType> groupOverview;

        /**
         * Gets the value of the groupOverview property.
         * 
         * <p>This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the groupOverview property.</p>
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * </p>
         * <pre>
         * getGroupOverview().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link ListApplicationsInGroupOverviewType }
         * </p>
         * 
         * 
         * @return
         *     The value of the groupOverview property.
         */
        public List<ListApplicationsInGroupOverviewType> getGroupOverview() {
            if (groupOverview == null) {
                groupOverview = new ArrayList<>();
            }
            return this.groupOverview;
        }

    }

}
