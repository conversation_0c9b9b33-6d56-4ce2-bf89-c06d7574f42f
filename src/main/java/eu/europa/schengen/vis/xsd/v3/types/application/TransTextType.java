//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.vis.xsd.v3.types.application;

import java.util.ArrayList;
import java.util.List;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * Description: This type is used for a name and its transliterations.
 * 
 * <p>Classe Java per TransTextType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="TransTextType">
 *   <complexContent>
 *     <extension base="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}TransType">
 *       <sequence>
 *         <element name="TransliteratedValues">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="TransliteratedValue" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}TransBaseType" maxOccurs="5"/>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *       </sequence>
 *     </extension>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "TransTextType", propOrder = {
    "transliteratedValues"
})
public class TransTextType
    extends TransType
{

    /**
     * Description: Transliterations of the source value.
     * 
     */
    @XmlElement(name = "TransliteratedValues", required = true)
    protected TransTextType.TransliteratedValues transliteratedValues;

    /**
     * Description: Transliterations of the source value.
     * 
     * @return
     *     possible object is
     *     {@link TransTextType.TransliteratedValues }
     *     
     */
    public TransTextType.TransliteratedValues getTransliteratedValues() {
        return transliteratedValues;
    }

    /**
     * Imposta il valore della proprietà transliteratedValues.
     * 
     * @param value
     *     allowed object is
     *     {@link TransTextType.TransliteratedValues }
     *     
     * @see #getTransliteratedValues()
     */
    public void setTransliteratedValues(TransTextType.TransliteratedValues value) {
        this.transliteratedValues = value;
    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="TransliteratedValue" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}TransBaseType" maxOccurs="5"/>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "transliteratedValue"
    })
    public static class TransliteratedValues {

        @XmlElement(name = "TransliteratedValue", required = true)
        protected List<String> transliteratedValue;

        /**
         * Gets the value of the transliteratedValue property.
         * 
         * <p>This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the transliteratedValue property.</p>
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * </p>
         * <pre>
         * getTransliteratedValue().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link String }
         * </p>
         * 
         * 
         * @return
         *     The value of the transliteratedValue property.
         */
        public List<String> getTransliteratedValue() {
            if (transliteratedValue == null) {
                transliteratedValue = new ArrayList<>();
            }
            return this.transliteratedValue;
        }

    }

}
