//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.vis.xsd.v3.types.application;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * Description: Information needed for the creation of a VisaCreationDecision.
 * 
 * <p>Classe Java per VisaCreationDecisionNewType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="VisaCreationDecisionNewType">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <choice>
 *         <element name="ExtendVisa" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}ExtendVisaNewType"/>
 *         <element name="IssueVisa" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}IssueVisaNewType"/>
 *       </choice>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "VisaCreationDecisionNewType", propOrder = {
    "extendVisa",
    "issueVisa"
})
public class VisaCreationDecisionNewType {

    /**
     * Description: All information needed to extend a visa with a new sticker.
     * 
     */
    @XmlElement(name = "ExtendVisa")
    protected ExtendVisaNewType extendVisa;
    /**
     * Description: All information needed for the decision "Issue Visa".
     * 
     */
    @XmlElement(name = "IssueVisa")
    protected IssueVisaNewType issueVisa;

    /**
     * Description: All information needed to extend a visa with a new sticker.
     * 
     * @return
     *     possible object is
     *     {@link ExtendVisaNewType }
     *     
     */
    public ExtendVisaNewType getExtendVisa() {
        return extendVisa;
    }

    /**
     * Imposta il valore della proprietà extendVisa.
     * 
     * @param value
     *     allowed object is
     *     {@link ExtendVisaNewType }
     *     
     * @see #getExtendVisa()
     */
    public void setExtendVisa(ExtendVisaNewType value) {
        this.extendVisa = value;
    }

    /**
     * Description: All information needed for the decision "Issue Visa".
     * 
     * @return
     *     possible object is
     *     {@link IssueVisaNewType }
     *     
     */
    public IssueVisaNewType getIssueVisa() {
        return issueVisa;
    }

    /**
     * Imposta il valore della proprietà issueVisa.
     * 
     * @param value
     *     allowed object is
     *     {@link IssueVisaNewType }
     *     
     * @see #getIssueVisa()
     */
    public void setIssueVisa(IssueVisaNewType value) {
        this.issueVisa = value;
    }

}
