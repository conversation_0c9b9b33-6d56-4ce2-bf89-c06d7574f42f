//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.vis.xsd.v3.types.application;

import java.util.ArrayList;
import java.util.List;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * Description: Response for an Asylum examination retrieving.
 * 
 * <p>Classe Java per ResultAsylumExaminationReadType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="ResultAsylumExaminationReadType">
 *   <complexContent>
 *     <extension base="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}ResultReadApplicationBaseType">
 *       <sequence>
 *         <element name="ApplicationData">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="ApplicationNumber" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}ApplicationNumberType"/>
 *                   <element name="NationalityAtBirth" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}CT02_CountryOfNationalityType" minOccurs="0"/>
 *                   <element name="NationalityForApplication" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}CT02_CountryOfNationalityType" minOccurs="0"/>
 *                   <element name="TravelDocument" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}TravelDocumentGetType" minOccurs="0"/>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *         <element name="Decisions" minOccurs="0">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="Decision" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}AsylumExaminationDecisionHistGetType" maxOccurs="unbounded"/>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *         <element name="FacialImages" minOccurs="0">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="FacialImage" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}FacialImageGetType" maxOccurs="unbounded"/>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *         <element name="GroupSynopses" minOccurs="0">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="GroupSynopsis" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}GroupSynopsisType" maxOccurs="unbounded"/>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *       </sequence>
 *     </extension>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ResultAsylumExaminationReadType", propOrder = {
    "applicationData",
    "decisions",
    "facialImages",
    "groupSynopses"
})
public class ResultAsylumExaminationReadType
    extends ResultReadApplicationBaseType
{

    /**
     * Description: This element is a subset of the Application.
     * 
     */
    @XmlElement(name = "ApplicationData", required = true)
    protected ResultAsylumExaminationReadType.ApplicationData applicationData;
    @XmlElement(name = "Decisions")
    protected ResultAsylumExaminationReadType.Decisions decisions;
    @XmlElement(name = "FacialImages")
    protected ResultAsylumExaminationReadType.FacialImages facialImages;
    /**
     * Description: List of the groups thae Application belongs to.
     * 
     */
    @XmlElement(name = "GroupSynopses")
    protected ResultAsylumExaminationReadType.GroupSynopses groupSynopses;

    /**
     * Description: This element is a subset of the Application.
     * 
     * @return
     *     possible object is
     *     {@link ResultAsylumExaminationReadType.ApplicationData }
     *     
     */
    public ResultAsylumExaminationReadType.ApplicationData getApplicationData() {
        return applicationData;
    }

    /**
     * Imposta il valore della proprietà applicationData.
     * 
     * @param value
     *     allowed object is
     *     {@link ResultAsylumExaminationReadType.ApplicationData }
     *     
     * @see #getApplicationData()
     */
    public void setApplicationData(ResultAsylumExaminationReadType.ApplicationData value) {
        this.applicationData = value;
    }

    /**
     * Recupera il valore della proprietà decisions.
     * 
     * @return
     *     possible object is
     *     {@link ResultAsylumExaminationReadType.Decisions }
     *     
     */
    public ResultAsylumExaminationReadType.Decisions getDecisions() {
        return decisions;
    }

    /**
     * Imposta il valore della proprietà decisions.
     * 
     * @param value
     *     allowed object is
     *     {@link ResultAsylumExaminationReadType.Decisions }
     *     
     */
    public void setDecisions(ResultAsylumExaminationReadType.Decisions value) {
        this.decisions = value;
    }

    /**
     * Recupera il valore della proprietà facialImages.
     * 
     * @return
     *     possible object is
     *     {@link ResultAsylumExaminationReadType.FacialImages }
     *     
     */
    public ResultAsylumExaminationReadType.FacialImages getFacialImages() {
        return facialImages;
    }

    /**
     * Imposta il valore della proprietà facialImages.
     * 
     * @param value
     *     allowed object is
     *     {@link ResultAsylumExaminationReadType.FacialImages }
     *     
     */
    public void setFacialImages(ResultAsylumExaminationReadType.FacialImages value) {
        this.facialImages = value;
    }

    /**
     * Description: List of the groups thae Application belongs to.
     * 
     * @return
     *     possible object is
     *     {@link ResultAsylumExaminationReadType.GroupSynopses }
     *     
     */
    public ResultAsylumExaminationReadType.GroupSynopses getGroupSynopses() {
        return groupSynopses;
    }

    /**
     * Imposta il valore della proprietà groupSynopses.
     * 
     * @param value
     *     allowed object is
     *     {@link ResultAsylumExaminationReadType.GroupSynopses }
     *     
     * @see #getGroupSynopses()
     */
    public void setGroupSynopses(ResultAsylumExaminationReadType.GroupSynopses value) {
        this.groupSynopses = value;
    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="ApplicationNumber" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}ApplicationNumberType"/>
     *         <element name="NationalityAtBirth" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}CT02_CountryOfNationalityType" minOccurs="0"/>
     *         <element name="NationalityForApplication" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}CT02_CountryOfNationalityType" minOccurs="0"/>
     *         <element name="TravelDocument" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}TravelDocumentGetType" minOccurs="0"/>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "applicationNumber",
        "nationalityAtBirth",
        "nationalityForApplication",
        "travelDocument"
    })
    public static class ApplicationData {

        /**
         * Description: Unique identifier of an application. The application number is generated and submitted by a User.
         * 
         */
        @XmlElement(name = "ApplicationNumber", required = true)
        protected String applicationNumber;
        /**
         * Description: Nationality at birth of the applicant.
         * 
         */
        @XmlElement(name = "NationalityAtBirth")
        protected String nationalityAtBirth;
        /**
         * Description: This nationality is used for the application.
         * 
         */
        @XmlElement(name = "NationalityForApplication")
        protected String nationalityForApplication;
        @XmlElement(name = "TravelDocument")
        protected TravelDocumentGetType travelDocument;

        /**
         * Description: Unique identifier of an application. The application number is generated and submitted by a User.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getApplicationNumber() {
            return applicationNumber;
        }

        /**
         * Imposta il valore della proprietà applicationNumber.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         * @see #getApplicationNumber()
         */
        public void setApplicationNumber(String value) {
            this.applicationNumber = value;
        }

        /**
         * Description: Nationality at birth of the applicant.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getNationalityAtBirth() {
            return nationalityAtBirth;
        }

        /**
         * Imposta il valore della proprietà nationalityAtBirth.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         * @see #getNationalityAtBirth()
         */
        public void setNationalityAtBirth(String value) {
            this.nationalityAtBirth = value;
        }

        /**
         * Description: This nationality is used for the application.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getNationalityForApplication() {
            return nationalityForApplication;
        }

        /**
         * Imposta il valore della proprietà nationalityForApplication.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         * @see #getNationalityForApplication()
         */
        public void setNationalityForApplication(String value) {
            this.nationalityForApplication = value;
        }

        /**
         * Recupera il valore della proprietà travelDocument.
         * 
         * @return
         *     possible object is
         *     {@link TravelDocumentGetType }
         *     
         */
        public TravelDocumentGetType getTravelDocument() {
            return travelDocument;
        }

        /**
         * Imposta il valore della proprietà travelDocument.
         * 
         * @param value
         *     allowed object is
         *     {@link TravelDocumentGetType }
         *     
         */
        public void setTravelDocument(TravelDocumentGetType value) {
            this.travelDocument = value;
        }

    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="Decision" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}AsylumExaminationDecisionHistGetType" maxOccurs="unbounded"/>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "decision"
    })
    public static class Decisions {

        @XmlElement(name = "Decision", required = true)
        protected List<AsylumExaminationDecisionHistGetType> decision;

        /**
         * Gets the value of the decision property.
         * 
         * <p>This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the decision property.</p>
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * </p>
         * <pre>
         * getDecision().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link AsylumExaminationDecisionHistGetType }
         * </p>
         * 
         * 
         * @return
         *     The value of the decision property.
         */
        public List<AsylumExaminationDecisionHistGetType> getDecision() {
            if (decision == null) {
                decision = new ArrayList<>();
            }
            return this.decision;
        }

    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="FacialImage" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}FacialImageGetType" maxOccurs="unbounded"/>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "facialImage"
    })
    public static class FacialImages {

        @XmlElement(name = "FacialImage", required = true)
        protected List<FacialImageGetType> facialImage;

        /**
         * Gets the value of the facialImage property.
         * 
         * <p>This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the facialImage property.</p>
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * </p>
         * <pre>
         * getFacialImage().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link FacialImageGetType }
         * </p>
         * 
         * 
         * @return
         *     The value of the facialImage property.
         */
        public List<FacialImageGetType> getFacialImage() {
            if (facialImage == null) {
                facialImage = new ArrayList<>();
            }
            return this.facialImage;
        }

    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="GroupSynopsis" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}GroupSynopsisType" maxOccurs="unbounded"/>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "groupSynopsis"
    })
    public static class GroupSynopses {

        @XmlElement(name = "GroupSynopsis", required = true)
        protected List<GroupSynopsisType> groupSynopsis;

        /**
         * Gets the value of the groupSynopsis property.
         * 
         * <p>This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the groupSynopsis property.</p>
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * </p>
         * <pre>
         * getGroupSynopsis().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link GroupSynopsisType }
         * </p>
         * 
         * 
         * @return
         *     The value of the groupSynopsis property.
         */
        public List<GroupSynopsisType> getGroupSynopsis() {
            if (groupSynopsis == null) {
                groupSynopsis = new ArrayList<>();
            }
            return this.groupSynopsis;
        }

    }

}
