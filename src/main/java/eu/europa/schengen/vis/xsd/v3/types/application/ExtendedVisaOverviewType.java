//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.vis.xsd.v3.types.application;

import eu.europa.schengen.vis.xsd.v3.types.common.PeriodType;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per ExtendedVisaOverviewType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="ExtendedVisaOverviewType">
 *   <complexContent>
 *     <extension base="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}DecisionOverviewBaseType">
 *       <sequence>
 *         <element name="AffectedDecisionID" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}DecisionIDType"/>
 *         <element name="ExtendedDuration" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}DurationType"/>
 *         <element name="ExtendedPeriod" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}PeriodType"/>
 *         <element name="VisaType" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}VisaTypeType"/>
 *       </sequence>
 *     </extension>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ExtendedVisaOverviewType", propOrder = {
    "affectedDecisionID",
    "extendedDuration",
    "extendedPeriod",
    "visaType"
})
public class ExtendedVisaOverviewType
    extends DecisionOverviewBaseType
{

    /**
     * Description: A VisaDecision refers to an existing visa sticker. As the sticker is part of a VisaCreationDecision, the affectedDecisionID field refers to the VisaCreationDecision containing the visa sticker.
     * 
     */
    @XmlElement(name = "AffectedDecisionID")
    protected long affectedDecisionID;
    /**
     * Description: The new extended duration of the visa.
     * 
     */
    @XmlElement(name = "ExtendedDuration")
    protected long extendedDuration;
    /**
     * Description: The extended period indicates the period (commencement and expiry dates) of the extended period.
     * 
     */
    @XmlElement(name = "ExtendedPeriod", required = true)
    protected PeriodType extendedPeriod;
    /**
     * Description: The type of the visa.
     * 
     */
    @XmlElement(name = "VisaType", required = true)
    protected VisaTypeType visaType;

    /**
     * Description: A VisaDecision refers to an existing visa sticker. As the sticker is part of a VisaCreationDecision, the affectedDecisionID field refers to the VisaCreationDecision containing the visa sticker.
     * 
     */
    public long getAffectedDecisionID() {
        return affectedDecisionID;
    }

    /**
     * Imposta il valore della proprietà affectedDecisionID.
     * 
     */
    public void setAffectedDecisionID(long value) {
        this.affectedDecisionID = value;
    }

    /**
     * Description: The new extended duration of the visa.
     * 
     */
    public long getExtendedDuration() {
        return extendedDuration;
    }

    /**
     * Imposta il valore della proprietà extendedDuration.
     * 
     */
    public void setExtendedDuration(long value) {
        this.extendedDuration = value;
    }

    /**
     * Description: The extended period indicates the period (commencement and expiry dates) of the extended period.
     * 
     * @return
     *     possible object is
     *     {@link PeriodType }
     *     
     */
    public PeriodType getExtendedPeriod() {
        return extendedPeriod;
    }

    /**
     * Imposta il valore della proprietà extendedPeriod.
     * 
     * @param value
     *     allowed object is
     *     {@link PeriodType }
     *     
     * @see #getExtendedPeriod()
     */
    public void setExtendedPeriod(PeriodType value) {
        this.extendedPeriod = value;
    }

    /**
     * Description: The type of the visa.
     * 
     * @return
     *     possible object is
     *     {@link VisaTypeType }
     *     
     */
    public VisaTypeType getVisaType() {
        return visaType;
    }

    /**
     * Imposta il valore della proprietà visaType.
     * 
     * @param value
     *     allowed object is
     *     {@link VisaTypeType }
     *     
     * @see #getVisaType()
     */
    public void setVisaType(VisaTypeType value) {
        this.visaType = value;
    }

}
