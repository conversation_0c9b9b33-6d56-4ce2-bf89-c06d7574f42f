//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.vis.xsd.v3.types.application;

import java.util.ArrayList;
import java.util.List;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per ListApplicationsInFamilyGroupOverviewType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="ListApplicationsInFamilyGroupOverviewType">
 *   <complexContent>
 *     <extension base="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}ListRetrievalBaseType">
 *       <sequence>
 *         <element name="PlaceOfBirth" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}BirthPlaceGetType" minOccurs="0"/>
 *         <element name="FormerSurnames" minOccurs="0">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="FormerSurname" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}TransTextType" maxOccurs="unbounded"/>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *         <element name="SurnameAtBirth" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}TransTextType" minOccurs="0"/>
 *       </sequence>
 *     </extension>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ListApplicationsInFamilyGroupOverviewType", propOrder = {
    "placeOfBirth",
    "formerSurnames",
    "surnameAtBirth"
})
public class ListApplicationsInFamilyGroupOverviewType
    extends ListRetrievalBaseType
{

    /**
     * Description: Place of birth (country from code table plus free text).
     * 
     */
    @XmlElement(name = "PlaceOfBirth")
    protected BirthPlaceGetType placeOfBirth;
    /**
     * Description: Former surnames of the applicant.
     * 
     */
    @XmlElement(name = "FormerSurnames")
    protected ListApplicationsInFamilyGroupOverviewType.FormerSurnames formerSurnames;
    /**
     * Description: Surname at birth of the applicant.
     * 
     */
    @XmlElement(name = "SurnameAtBirth")
    protected TransTextType surnameAtBirth;

    /**
     * Description: Place of birth (country from code table plus free text).
     * 
     * @return
     *     possible object is
     *     {@link BirthPlaceGetType }
     *     
     */
    public BirthPlaceGetType getPlaceOfBirth() {
        return placeOfBirth;
    }

    /**
     * Imposta il valore della proprietà placeOfBirth.
     * 
     * @param value
     *     allowed object is
     *     {@link BirthPlaceGetType }
     *     
     * @see #getPlaceOfBirth()
     */
    public void setPlaceOfBirth(BirthPlaceGetType value) {
        this.placeOfBirth = value;
    }

    /**
     * Description: Former surnames of the applicant.
     * 
     * @return
     *     possible object is
     *     {@link ListApplicationsInFamilyGroupOverviewType.FormerSurnames }
     *     
     */
    public ListApplicationsInFamilyGroupOverviewType.FormerSurnames getFormerSurnames() {
        return formerSurnames;
    }

    /**
     * Imposta il valore della proprietà formerSurnames.
     * 
     * @param value
     *     allowed object is
     *     {@link ListApplicationsInFamilyGroupOverviewType.FormerSurnames }
     *     
     * @see #getFormerSurnames()
     */
    public void setFormerSurnames(ListApplicationsInFamilyGroupOverviewType.FormerSurnames value) {
        this.formerSurnames = value;
    }

    /**
     * Description: Surname at birth of the applicant.
     * 
     * @return
     *     possible object is
     *     {@link TransTextType }
     *     
     */
    public TransTextType getSurnameAtBirth() {
        return surnameAtBirth;
    }

    /**
     * Imposta il valore della proprietà surnameAtBirth.
     * 
     * @param value
     *     allowed object is
     *     {@link TransTextType }
     *     
     * @see #getSurnameAtBirth()
     */
    public void setSurnameAtBirth(TransTextType value) {
        this.surnameAtBirth = value;
    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="FormerSurname" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}TransTextType" maxOccurs="unbounded"/>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "formerSurname"
    })
    public static class FormerSurnames {

        @XmlElement(name = "FormerSurname", required = true)
        protected List<TransTextType> formerSurname;

        /**
         * Gets the value of the formerSurname property.
         * 
         * <p>This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the formerSurname property.</p>
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * </p>
         * <pre>
         * getFormerSurname().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link TransTextType }
         * </p>
         * 
         * 
         * @return
         *     The value of the formerSurname property.
         */
        public List<TransTextType> getFormerSurname() {
            if (formerSurname == null) {
                formerSurname = new ArrayList<>();
            }
            return this.formerSurname;
        }

    }

}
