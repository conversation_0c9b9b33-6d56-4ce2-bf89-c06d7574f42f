//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.vis.xsd.v3.types.application;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSeeAlso;
import jakarta.xml.bind.annotation.XmlType;


/**
 * Description: Base type for the overview of an applicant.
 * 
 * <p>Classe Java per ResultOverviewBaseType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="ResultOverviewBaseType">
 *   <complexContent>
 *     <extension base="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}ResultApplicantOverviewType">
 *       <sequence>
 *         <element name="Rank" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}ReturnNumberType"/>
 *         <element name="Score" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}ReturnNumberType"/>
 *       </sequence>
 *     </extension>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ResultOverviewBaseType", propOrder = {
    "rank",
    "score"
})
@XmlSeeAlso({
    ResultApplicationExaminationOverviewType.class,
    ResultAsylumExaminationOverviewType.class,
    ResultAsylumResponsibilityOverviewType.class,
    ResultAsylumResponsibilityOverviewResponseType.class,
    ResultIdentificationOverviewType.class,
    ResultVerificationOverviewType.class,
    ResultLawEnforcementOverviewType.class
})
public class ResultOverviewBaseType
    extends ResultApplicantOverviewType
{

    /**
     * Description: Rank of the found record within a FoundDossier. Calculated after sorting.
     * 
     */
    @XmlElement(name = "Rank")
    protected long rank;
    /**
     * Description: The calculated score for this found record.
     * 
     */
    @XmlElement(name = "Score")
    protected long score;

    /**
     * Description: Rank of the found record within a FoundDossier. Calculated after sorting.
     * 
     */
    public long getRank() {
        return rank;
    }

    /**
     * Imposta il valore della proprietà rank.
     * 
     */
    public void setRank(long value) {
        this.rank = value;
    }

    /**
     * Description: The calculated score for this found record.
     * 
     */
    public long getScore() {
        return score;
    }

    /**
     * Imposta il valore della proprietà score.
     * 
     */
    public void setScore(long value) {
        this.score = value;
    }

}
