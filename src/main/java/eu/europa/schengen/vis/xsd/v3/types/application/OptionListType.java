//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.vis.xsd.v3.types.application;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * Description: List the data sources of the attachments which have to be returned for search (if only one application matches) as well as for retrieval without previous search.
 * 
 * <p>Classe Java per OptionListType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="OptionListType">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="FacialImageDataSourceIncluded" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}YesNoType"/>
 *         <element name="FingerprintSetDataSourceIncluded" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}YesNoType"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "OptionListType", propOrder = {
    "facialImageDataSourceIncluded",
    "fingerprintSetDataSourceIncluded"
})
public class OptionListType {

    /**
     * Description: Indicates whether the data source of the facial images should is included or not.
     * 
     */
    @XmlElement(name = "FacialImageDataSourceIncluded")
    protected boolean facialImageDataSourceIncluded;
    /**
     * Description: Indicates whether the data source of the fingerprint set should is included or not.
     * 
     */
    @XmlElement(name = "FingerprintSetDataSourceIncluded")
    protected boolean fingerprintSetDataSourceIncluded;

    /**
     * Description: Indicates whether the data source of the facial images should is included or not.
     * 
     */
    public boolean isFacialImageDataSourceIncluded() {
        return facialImageDataSourceIncluded;
    }

    /**
     * Imposta il valore della proprietà facialImageDataSourceIncluded.
     * 
     */
    public void setFacialImageDataSourceIncluded(boolean value) {
        this.facialImageDataSourceIncluded = value;
    }

    /**
     * Description: Indicates whether the data source of the fingerprint set should is included or not.
     * 
     */
    public boolean isFingerprintSetDataSourceIncluded() {
        return fingerprintSetDataSourceIncluded;
    }

    /**
     * Imposta il valore della proprietà fingerprintSetDataSourceIncluded.
     * 
     */
    public void setFingerprintSetDataSourceIncluded(boolean value) {
        this.fingerprintSetDataSourceIncluded = value;
    }

}
