//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.vis.xsd.v3.types.application;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * Description: All information needed for the decision "Issue Visa".
 * 
 * <p>Classe Java per IssueVisaNewType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="IssueVisaNewType">
 *   <complexContent>
 *     <extension base="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}DecisionNewType">
 *       <sequence>
 *         <element name="VisaSticker" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}VisaStickerNewType"/>
 *         <element name="IssuanceOnSeparateSheet" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}YesNoType"/>
 *         <element name="PersonStatus" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}CT71_PersonStatusType" minOccurs="0"/>
 *         <element name="VLTVIndicator" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}VLTVIndicatorType"/>
 *       </sequence>
 *     </extension>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "IssueVisaNewType", propOrder = {
    "visaSticker",
    "issuanceOnSeparateSheet",
    "personStatus",
    "vltvIndicator"
})
public class IssueVisaNewType
    extends DecisionNewType
{

    /**
     * Description: data of the visa sticker.
     * 
     */
    @XmlElement(name = "VisaSticker", required = true)
    protected VisaStickerNewType visaSticker;
    /**
     * Description: Information indicating that the visa has been issued on a separate sheet in accordance with Regulation (EC) No. 333/2002.
     * 
     */
    @XmlElement(name = "IssuanceOnSeparateSheet")
    protected boolean issuanceOnSeparateSheet;
    /**
     * Description: Indicates that the TCN is a member of the family of a Union citizen to whom Directive 2004/38/EC of the European Parliament and of the Council applies or of a TCN enjoying the right of free movement equivalent to that of Union citizens under an agreement between the Union and its Member States and a third country.
     * 
     */
    @XmlElement(name = "PersonStatus")
    protected String personStatus;
    /**
     * Description: TRUE means that the visa has been issued with limited territorial validity pursuant to Article 25(1)(b) of Regulation (EC) No 810/2009.
     * 
     */
    @XmlElement(name = "VLTVIndicator")
    protected boolean vltvIndicator;

    /**
     * Description: data of the visa sticker.
     * 
     * @return
     *     possible object is
     *     {@link VisaStickerNewType }
     *     
     */
    public VisaStickerNewType getVisaSticker() {
        return visaSticker;
    }

    /**
     * Imposta il valore della proprietà visaSticker.
     * 
     * @param value
     *     allowed object is
     *     {@link VisaStickerNewType }
     *     
     * @see #getVisaSticker()
     */
    public void setVisaSticker(VisaStickerNewType value) {
        this.visaSticker = value;
    }

    /**
     * Description: Information indicating that the visa has been issued on a separate sheet in accordance with Regulation (EC) No. 333/2002.
     * 
     */
    public boolean isIssuanceOnSeparateSheet() {
        return issuanceOnSeparateSheet;
    }

    /**
     * Imposta il valore della proprietà issuanceOnSeparateSheet.
     * 
     */
    public void setIssuanceOnSeparateSheet(boolean value) {
        this.issuanceOnSeparateSheet = value;
    }

    /**
     * Description: Indicates that the TCN is a member of the family of a Union citizen to whom Directive 2004/38/EC of the European Parliament and of the Council applies or of a TCN enjoying the right of free movement equivalent to that of Union citizens under an agreement between the Union and its Member States and a third country.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPersonStatus() {
        return personStatus;
    }

    /**
     * Imposta il valore della proprietà personStatus.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     * @see #getPersonStatus()
     */
    public void setPersonStatus(String value) {
        this.personStatus = value;
    }

    /**
     * Description: TRUE means that the visa has been issued with limited territorial validity pursuant to Article 25(1)(b) of Regulation (EC) No 810/2009.
     * 
     */
    public boolean isVLTVIndicator() {
        return vltvIndicator;
    }

    /**
     * Imposta il valore della proprietà vltvIndicator.
     * 
     */
    public void setVLTVIndicator(boolean value) {
        this.vltvIndicator = value;
    }

}
