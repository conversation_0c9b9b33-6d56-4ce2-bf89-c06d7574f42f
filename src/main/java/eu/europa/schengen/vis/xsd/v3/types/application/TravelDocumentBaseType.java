//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.vis.xsd.v3.types.application;

import javax.xml.datatype.XMLGregorianCalendar;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlSeeAlso;
import jakarta.xml.bind.annotation.XmlType;


/**
 * Description: group of information describing the travel document.
 * 
 * <p>Classe Java per TravelDocumentBaseType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="TravelDocumentBaseType">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="TravelDocumentType" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}CT11_TravelDocumentTypeType"/>
 *         <element name="TravelDocumentNumber" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}TravelDocumentNumberType"/>
 *         <element name="DateOfIssue" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}DateType" minOccurs="0"/>
 *         <element name="ValidUntil" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Common}DateType" minOccurs="0"/>
 *         <element name="OtherTravelDocument" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}TravelDocumentOtherType" minOccurs="0"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "TravelDocumentBaseType", propOrder = {
    "travelDocumentType",
    "travelDocumentNumber",
    "dateOfIssue",
    "validUntil",
    "otherTravelDocument"
})
@XmlSeeAlso({
    TravelDocumentNewType.class,
    TravelDocumentGetType.class
})
public class TravelDocumentBaseType {

    /**
     * Description: Type of the travel document (code table value).
     * 
     */
    @XmlElement(name = "TravelDocumentType", required = true)
    protected String travelDocumentType;
    /**
     * Description: The number of the travel document including the three letter country code.
     * 
     */
    @XmlElement(name = "TravelDocumentNumber", required = true)
    protected String travelDocumentNumber;
    /**
     * Description: Date of the issue.
     * 
     */
    @XmlElement(name = "DateOfIssue")
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar dateOfIssue;
    /**
     * Description: Validity of the travel document.
     * 
     */
    @XmlElement(name = "ValidUntil")
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar validUntil;
    /**
     * Rule: If TravelDocumentType is set to "Other" then a description must be entered here.
     * 
     */
    @XmlElement(name = "OtherTravelDocument")
    protected String otherTravelDocument;

    /**
     * Description: Type of the travel document (code table value).
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTravelDocumentType() {
        return travelDocumentType;
    }

    /**
     * Imposta il valore della proprietà travelDocumentType.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     * @see #getTravelDocumentType()
     */
    public void setTravelDocumentType(String value) {
        this.travelDocumentType = value;
    }

    /**
     * Description: The number of the travel document including the three letter country code.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTravelDocumentNumber() {
        return travelDocumentNumber;
    }

    /**
     * Imposta il valore della proprietà travelDocumentNumber.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     * @see #getTravelDocumentNumber()
     */
    public void setTravelDocumentNumber(String value) {
        this.travelDocumentNumber = value;
    }

    /**
     * Description: Date of the issue.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getDateOfIssue() {
        return dateOfIssue;
    }

    /**
     * Imposta il valore della proprietà dateOfIssue.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     * @see #getDateOfIssue()
     */
    public void setDateOfIssue(XMLGregorianCalendar value) {
        this.dateOfIssue = value;
    }

    /**
     * Description: Validity of the travel document.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getValidUntil() {
        return validUntil;
    }

    /**
     * Imposta il valore della proprietà validUntil.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     * @see #getValidUntil()
     */
    public void setValidUntil(XMLGregorianCalendar value) {
        this.validUntil = value;
    }

    /**
     * Rule: If TravelDocumentType is set to "Other" then a description must be entered here.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getOtherTravelDocument() {
        return otherTravelDocument;
    }

    /**
     * Imposta il valore della proprietà otherTravelDocument.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     * @see #getOtherTravelDocument()
     */
    public void setOtherTravelDocument(String value) {
        this.otherTravelDocument = value;
    }

}
