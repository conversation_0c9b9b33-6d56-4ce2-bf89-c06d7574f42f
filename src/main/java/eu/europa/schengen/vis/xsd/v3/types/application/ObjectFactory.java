//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.vis.xsd.v3.types.application;

import jakarta.xml.bind.annotation.XmlRegistry;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the eu.europa.schengen.vis.xsd.v3.types.application package. 
 * <p>An ObjectFactory allows you to programmatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {


    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: eu.europa.schengen.vis.xsd.v3.types.application
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link TravelDocumentSearchRequestType }
     * 
     * @return
     *     the new instance of {@link TravelDocumentSearchRequestType }
     */
    public TravelDocumentSearchRequestType createTravelDocumentSearchRequestType() {
        return new TravelDocumentSearchRequestType();
    }

    /**
     * Create an instance of {@link SearchByTravelDocumentType }
     * 
     * @return
     *     the new instance of {@link SearchByTravelDocumentType }
     */
    public SearchByTravelDocumentType createSearchByTravelDocumentType() {
        return new SearchByTravelDocumentType();
    }

    /**
     * Create an instance of {@link SearchApplicationExaminationTravelDocumentType }
     * 
     * @return
     *     the new instance of {@link SearchApplicationExaminationTravelDocumentType }
     */
    public SearchApplicationExaminationTravelDocumentType createSearchApplicationExaminationTravelDocumentType() {
        return new SearchApplicationExaminationTravelDocumentType();
    }

    /**
     * Create an instance of {@link SearchByTravelDocumentApplicantType }
     * 
     * @return
     *     the new instance of {@link SearchByTravelDocumentApplicantType }
     */
    public SearchByTravelDocumentApplicantType createSearchByTravelDocumentApplicantType() {
        return new SearchByTravelDocumentApplicantType();
    }

    /**
     * Create an instance of {@link SearchApplicationExaminationApplicantType }
     * 
     * @return
     *     the new instance of {@link SearchApplicationExaminationApplicantType }
     */
    public SearchApplicationExaminationApplicantType createSearchApplicationExaminationApplicantType() {
        return new SearchApplicationExaminationApplicantType();
    }

    /**
     * Create an instance of {@link MemberStatesOfDestinationSearchType }
     * 
     * @return
     *     the new instance of {@link MemberStatesOfDestinationSearchType }
     */
    public MemberStatesOfDestinationSearchType createMemberStatesOfDestinationSearchType() {
        return new MemberStatesOfDestinationSearchType();
    }

    /**
     * Create an instance of {@link TerritorialValidityType }
     * 
     * @return
     *     the new instance of {@link TerritorialValidityType }
     */
    public TerritorialValidityType createTerritorialValidityType() {
        return new TerritorialValidityType();
    }

    /**
     * Create an instance of {@link AnnulVisaGetType }
     * 
     * @return
     *     the new instance of {@link AnnulVisaGetType }
     */
    public AnnulVisaGetType createAnnulVisaGetType() {
        return new AnnulVisaGetType();
    }

    /**
     * Create an instance of {@link AnnulVisaNewType }
     * 
     * @return
     *     the new instance of {@link AnnulVisaNewType }
     */
    public AnnulVisaNewType createAnnulVisaNewType() {
        return new AnnulVisaNewType();
    }

    /**
     * Create an instance of {@link ExtendVisaWithoutNewStickerGetType }
     * 
     * @return
     *     the new instance of {@link ExtendVisaWithoutNewStickerGetType }
     */
    public ExtendVisaWithoutNewStickerGetType createExtendVisaWithoutNewStickerGetType() {
        return new ExtendVisaWithoutNewStickerGetType();
    }

    /**
     * Create an instance of {@link RevokeVisaGetType }
     * 
     * @return
     *     the new instance of {@link RevokeVisaGetType }
     */
    public RevokeVisaGetType createRevokeVisaGetType() {
        return new RevokeVisaGetType();
    }

    /**
     * Create an instance of {@link RevokeVisaNewType }
     * 
     * @return
     *     the new instance of {@link RevokeVisaNewType }
     */
    public RevokeVisaNewType createRevokeVisaNewType() {
        return new RevokeVisaNewType();
    }

    /**
     * Create an instance of {@link ShortenValidityPeriodWithoutNewStickerGetType }
     * 
     * @return
     *     the new instance of {@link ShortenValidityPeriodWithoutNewStickerGetType }
     */
    public ShortenValidityPeriodWithoutNewStickerGetType createShortenValidityPeriodWithoutNewStickerGetType() {
        return new ShortenValidityPeriodWithoutNewStickerGetType();
    }

    /**
     * Create an instance of {@link ShortenValidityPeriodWithNewStickerGetType }
     * 
     * @return
     *     the new instance of {@link ShortenValidityPeriodWithNewStickerGetType }
     */
    public ShortenValidityPeriodWithNewStickerGetType createShortenValidityPeriodWithNewStickerGetType() {
        return new ShortenValidityPeriodWithNewStickerGetType();
    }

    /**
     * Create an instance of {@link ExtendVisaGetType }
     * 
     * @return
     *     the new instance of {@link ExtendVisaGetType }
     */
    public ExtendVisaGetType createExtendVisaGetType() {
        return new ExtendVisaGetType();
    }

    /**
     * Create an instance of {@link ExtendVisaNewType }
     * 
     * @return
     *     the new instance of {@link ExtendVisaNewType }
     */
    public ExtendVisaNewType createExtendVisaNewType() {
        return new ExtendVisaNewType();
    }

    /**
     * Create an instance of {@link ExtendVisaCorrectType }
     * 
     * @return
     *     the new instance of {@link ExtendVisaCorrectType }
     */
    public ExtendVisaCorrectType createExtendVisaCorrectType() {
        return new ExtendVisaCorrectType();
    }

    /**
     * Create an instance of {@link SearchBirthPlaceType }
     * 
     * @return
     *     the new instance of {@link SearchBirthPlaceType }
     */
    public SearchBirthPlaceType createSearchBirthPlaceType() {
        return new SearchBirthPlaceType();
    }

    /**
     * Create an instance of {@link SearchAddressType }
     * 
     * @return
     *     the new instance of {@link SearchAddressType }
     */
    public SearchAddressType createSearchAddressType() {
        return new SearchAddressType();
    }

    /**
     * Create an instance of {@link SearchApplicationExaminationHostType }
     * 
     * @return
     *     the new instance of {@link SearchApplicationExaminationHostType }
     */
    public SearchApplicationExaminationHostType createSearchApplicationExaminationHostType() {
        return new SearchApplicationExaminationHostType();
    }

    /**
     * Create an instance of {@link SearchHostType }
     * 
     * @return
     *     the new instance of {@link SearchHostType }
     */
    public SearchHostType createSearchHostType() {
        return new SearchHostType();
    }

    /**
     * Create an instance of {@link SearchTravelDocumentType }
     * 
     * @return
     *     the new instance of {@link SearchTravelDocumentType }
     */
    public SearchTravelDocumentType createSearchTravelDocumentType() {
        return new SearchTravelDocumentType();
    }

    /**
     * Create an instance of {@link SearchApplicantType }
     * 
     * @return
     *     the new instance of {@link SearchApplicantType }
     */
    public SearchApplicantType createSearchApplicantType() {
        return new SearchApplicantType();
    }

    /**
     * Create an instance of {@link ResultReadApplicationBaseType }
     * 
     * @return
     *     the new instance of {@link ResultReadApplicationBaseType }
     */
    public ResultReadApplicationBaseType createResultReadApplicationBaseType() {
        return new ResultReadApplicationBaseType();
    }

    /**
     * Create an instance of {@link ResultVerificationBorderControlReadType }
     * 
     * @return
     *     the new instance of {@link ResultVerificationBorderControlReadType }
     */
    public ResultVerificationBorderControlReadType createResultVerificationBorderControlReadType() {
        return new ResultVerificationBorderControlReadType();
    }

    /**
     * Create an instance of {@link ResultReadCoreBorderDataType }
     * 
     * @return
     *     the new instance of {@link ResultReadCoreBorderDataType }
     */
    public ResultReadCoreBorderDataType createResultReadCoreBorderDataType() {
        return new ResultReadCoreBorderDataType();
    }

    /**
     * Create an instance of {@link ResultVerificationBorderReadType }
     * 
     * @return
     *     the new instance of {@link ResultVerificationBorderReadType }
     */
    public ResultVerificationBorderReadType createResultVerificationBorderReadType() {
        return new ResultVerificationBorderReadType();
    }

    /**
     * Create an instance of {@link ResultRegistrationReadType }
     * 
     * @return
     *     the new instance of {@link ResultRegistrationReadType }
     */
    public ResultRegistrationReadType createResultRegistrationReadType() {
        return new ResultRegistrationReadType();
    }

    /**
     * Create an instance of {@link ResultReadCoreDataType }
     * 
     * @return
     *     the new instance of {@link ResultReadCoreDataType }
     */
    public ResultReadCoreDataType createResultReadCoreDataType() {
        return new ResultReadCoreDataType();
    }

    /**
     * Create an instance of {@link ResultVerificationReadType }
     * 
     * @return
     *     the new instance of {@link ResultVerificationReadType }
     */
    public ResultVerificationReadType createResultVerificationReadType() {
        return new ResultVerificationReadType();
    }

    /**
     * Create an instance of {@link ListApplicationsInFamilyGroupOverviewType }
     * 
     * @return
     *     the new instance of {@link ListApplicationsInFamilyGroupOverviewType }
     */
    public ListApplicationsInFamilyGroupOverviewType createListApplicationsInFamilyGroupOverviewType() {
        return new ListApplicationsInFamilyGroupOverviewType();
    }

    /**
     * Create an instance of {@link ResultListVisaInGroupType }
     * 
     * @return
     *     the new instance of {@link ResultListVisaInGroupType }
     */
    public ResultListVisaInGroupType createResultListVisaInGroupType() {
        return new ResultListVisaInGroupType();
    }

    /**
     * Create an instance of {@link ResultListApplicationsInGroupType }
     * 
     * @return
     *     the new instance of {@link ResultListApplicationsInGroupType }
     */
    public ResultListApplicationsInGroupType createResultListApplicationsInGroupType() {
        return new ResultListApplicationsInGroupType();
    }

    /**
     * Create an instance of {@link ResultListApplicationsInFamilyGroupType }
     * 
     * @return
     *     the new instance of {@link ResultListApplicationsInFamilyGroupType }
     */
    public ResultListApplicationsInFamilyGroupType createResultListApplicationsInFamilyGroupType() {
        return new ResultListApplicationsInFamilyGroupType();
    }

    /**
     * Create an instance of {@link ResultLawEnforcementSearchType }
     * 
     * @return
     *     the new instance of {@link ResultLawEnforcementSearchType }
     */
    public ResultLawEnforcementSearchType createResultLawEnforcementSearchType() {
        return new ResultLawEnforcementSearchType();
    }

    /**
     * Create an instance of {@link ResultLawEnforcementReadType }
     * 
     * @return
     *     the new instance of {@link ResultLawEnforcementReadType }
     */
    public ResultLawEnforcementReadType createResultLawEnforcementReadType() {
        return new ResultLawEnforcementReadType();
    }

    /**
     * Create an instance of {@link ResultVerificationOverviewType }
     * 
     * @return
     *     the new instance of {@link ResultVerificationOverviewType }
     */
    public ResultVerificationOverviewType createResultVerificationOverviewType() {
        return new ResultVerificationOverviewType();
    }

    /**
     * Create an instance of {@link ResultIdentificationOverviewType }
     * 
     * @return
     *     the new instance of {@link ResultIdentificationOverviewType }
     */
    public ResultIdentificationOverviewType createResultIdentificationOverviewType() {
        return new ResultIdentificationOverviewType();
    }

    /**
     * Create an instance of {@link ResultIdentificationReadType }
     * 
     * @return
     *     the new instance of {@link ResultIdentificationReadType }
     */
    public ResultIdentificationReadType createResultIdentificationReadType() {
        return new ResultIdentificationReadType();
    }

    /**
     * Create an instance of {@link ResultApplicationSearchType }
     * 
     * @return
     *     the new instance of {@link ResultApplicationSearchType }
     */
    public ResultApplicationSearchType createResultApplicationSearchType() {
        return new ResultApplicationSearchType();
    }

    /**
     * Create an instance of {@link ResultVerificationSearchType }
     * 
     * @return
     *     the new instance of {@link ResultVerificationSearchType }
     */
    public ResultVerificationSearchType createResultVerificationSearchType() {
        return new ResultVerificationSearchType();
    }

    /**
     * Create an instance of {@link ResultIdentificationSearchType }
     * 
     * @return
     *     the new instance of {@link ResultIdentificationSearchType }
     */
    public ResultIdentificationSearchType createResultIdentificationSearchType() {
        return new ResultIdentificationSearchType();
    }

    /**
     * Create an instance of {@link LawEnforcementSearchType }
     * 
     * @return
     *     the new instance of {@link LawEnforcementSearchType }
     */
    public LawEnforcementSearchType createLawEnforcementSearchType() {
        return new LawEnforcementSearchType();
    }

    /**
     * Create an instance of {@link ResultAsylumResponsibilityOverviewResponseType }
     * 
     * @return
     *     the new instance of {@link ResultAsylumResponsibilityOverviewResponseType }
     */
    public ResultAsylumResponsibilityOverviewResponseType createResultAsylumResponsibilityOverviewResponseType() {
        return new ResultAsylumResponsibilityOverviewResponseType();
    }

    /**
     * Create an instance of {@link ResultAsylumResponsibilityOverviewType }
     * 
     * @return
     *     the new instance of {@link ResultAsylumResponsibilityOverviewType }
     */
    public ResultAsylumResponsibilityOverviewType createResultAsylumResponsibilityOverviewType() {
        return new ResultAsylumResponsibilityOverviewType();
    }

    /**
     * Create an instance of {@link ResultAsylumResponsibilityUsageOfDataReadType }
     * 
     * @return
     *     the new instance of {@link ResultAsylumResponsibilityUsageOfDataReadType }
     */
    public ResultAsylumResponsibilityUsageOfDataReadType createResultAsylumResponsibilityUsageOfDataReadType() {
        return new ResultAsylumResponsibilityUsageOfDataReadType();
    }

    /**
     * Create an instance of {@link ResultAsylumResponsibilityReadType }
     * 
     * @return
     *     the new instance of {@link ResultAsylumResponsibilityReadType }
     */
    public ResultAsylumResponsibilityReadType createResultAsylumResponsibilityReadType() {
        return new ResultAsylumResponsibilityReadType();
    }

    /**
     * Create an instance of {@link ResultAsylumResponsibilityUsageOfDataSearchType }
     * 
     * @return
     *     the new instance of {@link ResultAsylumResponsibilityUsageOfDataSearchType }
     */
    public ResultAsylumResponsibilityUsageOfDataSearchType createResultAsylumResponsibilityUsageOfDataSearchType() {
        return new ResultAsylumResponsibilityUsageOfDataSearchType();
    }

    /**
     * Create an instance of {@link ResultAsylumResponsibilitySearchType }
     * 
     * @return
     *     the new instance of {@link ResultAsylumResponsibilitySearchType }
     */
    public ResultAsylumResponsibilitySearchType createResultAsylumResponsibilitySearchType() {
        return new ResultAsylumResponsibilitySearchType();
    }

    /**
     * Create an instance of {@link ResultAsylumExaminationOverviewType }
     * 
     * @return
     *     the new instance of {@link ResultAsylumExaminationOverviewType }
     */
    public ResultAsylumExaminationOverviewType createResultAsylumExaminationOverviewType() {
        return new ResultAsylumExaminationOverviewType();
    }

    /**
     * Create an instance of {@link ResultAsylumExaminationReadType }
     * 
     * @return
     *     the new instance of {@link ResultAsylumExaminationReadType }
     */
    public ResultAsylumExaminationReadType createResultAsylumExaminationReadType() {
        return new ResultAsylumExaminationReadType();
    }

    /**
     * Create an instance of {@link ResultAsylumExaminationSearchType }
     * 
     * @return
     *     the new instance of {@link ResultAsylumExaminationSearchType }
     */
    public ResultAsylumExaminationSearchType createResultAsylumExaminationSearchType() {
        return new ResultAsylumExaminationSearchType();
    }

    /**
     * Create an instance of {@link ResultApplicationExaminationOverviewType }
     * 
     * @return
     *     the new instance of {@link ResultApplicationExaminationOverviewType }
     */
    public ResultApplicationExaminationOverviewType createResultApplicationExaminationOverviewType() {
        return new ResultApplicationExaminationOverviewType();
    }

    /**
     * Create an instance of {@link ResultApplicationExaminationSearchType }
     * 
     * @return
     *     the new instance of {@link ResultApplicationExaminationSearchType }
     */
    public ResultApplicationExaminationSearchType createResultApplicationExaminationSearchType() {
        return new ResultApplicationExaminationSearchType();
    }

    /**
     * Create an instance of {@link ResultApplicationExaminationReadType }
     * 
     * @return
     *     the new instance of {@link ResultApplicationExaminationReadType }
     */
    public ResultApplicationExaminationReadType createResultApplicationExaminationReadType() {
        return new ResultApplicationExaminationReadType();
    }

    /**
     * Create an instance of {@link BiometricsDescriptionGetType }
     * 
     * @return
     *     the new instance of {@link BiometricsDescriptionGetType }
     */
    public BiometricsDescriptionGetType createBiometricsDescriptionGetType() {
        return new BiometricsDescriptionGetType();
    }

    /**
     * Create an instance of {@link ResultApplicationWithFullDecisionType }
     * 
     * @return
     *     the new instance of {@link ResultApplicationWithFullDecisionType }
     */
    public ResultApplicationWithFullDecisionType createResultApplicationWithFullDecisionType() {
        return new ResultApplicationWithFullDecisionType();
    }

    /**
     * Create an instance of {@link ResultApplicationWithFullDecisionType.Decisions }
     * 
     * @return
     *     the new instance of {@link ResultApplicationWithFullDecisionType.Decisions }
     */
    public ResultApplicationWithFullDecisionType.Decisions createResultApplicationWithFullDecisionTypeDecisions() {
        return new ResultApplicationWithFullDecisionType.Decisions();
    }

    /**
     * Create an instance of {@link LawEnforcementDecisionHistChoiceType }
     * 
     * @return
     *     the new instance of {@link LawEnforcementDecisionHistChoiceType }
     */
    public LawEnforcementDecisionHistChoiceType createLawEnforcementDecisionHistChoiceType() {
        return new LawEnforcementDecisionHistChoiceType();
    }

    /**
     * Create an instance of {@link IdentificationDecisionHistChoiceType }
     * 
     * @return
     *     the new instance of {@link IdentificationDecisionHistChoiceType }
     */
    public IdentificationDecisionHistChoiceType createIdentificationDecisionHistChoiceType() {
        return new IdentificationDecisionHistChoiceType();
    }

    /**
     * Create an instance of {@link MainPurposesOfJourneySearchType }
     * 
     * @return
     *     the new instance of {@link MainPurposesOfJourneySearchType }
     */
    public MainPurposesOfJourneySearchType createMainPurposesOfJourneySearchType() {
        return new MainPurposesOfJourneySearchType();
    }

    /**
     * Create an instance of {@link FoundDossierCountBorderControlType }
     * 
     * @return
     *     the new instance of {@link FoundDossierCountBorderControlType }
     */
    public FoundDossierCountBorderControlType createFoundDossierCountBorderControlType() {
        return new FoundDossierCountBorderControlType();
    }

    /**
     * Create an instance of {@link FoundDossierCountBorderControlType.Result }
     * 
     * @return
     *     the new instance of {@link FoundDossierCountBorderControlType.Result }
     */
    public FoundDossierCountBorderControlType.Result createFoundDossierCountBorderControlTypeResult() {
        return new FoundDossierCountBorderControlType.Result();
    }

    /**
     * Create an instance of {@link BiometricReferencesGetType }
     * 
     * @return
     *     the new instance of {@link BiometricReferencesGetType }
     */
    public BiometricReferencesGetType createBiometricReferencesGetType() {
        return new BiometricReferencesGetType();
    }

    /**
     * Create an instance of {@link BiometricReferencesType }
     * 
     * @return
     *     the new instance of {@link BiometricReferencesType }
     */
    public BiometricReferencesType createBiometricReferencesType() {
        return new BiometricReferencesType();
    }

    /**
     * Create an instance of {@link FacialImageGetType }
     * 
     * @return
     *     the new instance of {@link FacialImageGetType }
     */
    public FacialImageGetType createFacialImageGetType() {
        return new FacialImageGetType();
    }

    /**
     * Create an instance of {@link AttachmentMasterDataBaseType }
     * 
     * @return
     *     the new instance of {@link AttachmentMasterDataBaseType }
     */
    public AttachmentMasterDataBaseType createAttachmentMasterDataBaseType() {
        return new AttachmentMasterDataBaseType();
    }

    /**
     * Create an instance of {@link FacialImageListType }
     * 
     * @return
     *     the new instance of {@link FacialImageListType }
     */
    public FacialImageListType createFacialImageListType() {
        return new FacialImageListType();
    }

    /**
     * Create an instance of {@link FacialImageReturnType }
     * 
     * @return
     *     the new instance of {@link FacialImageReturnType }
     */
    public FacialImageReturnType createFacialImageReturnType() {
        return new FacialImageReturnType();
    }

    /**
     * Create an instance of {@link FacialImageNewType }
     * 
     * @return
     *     the new instance of {@link FacialImageNewType }
     */
    public FacialImageNewType createFacialImageNewType() {
        return new FacialImageNewType();
    }

    /**
     * Create an instance of {@link FingerprintSetGetType }
     * 
     * @return
     *     the new instance of {@link FingerprintSetGetType }
     */
    public FingerprintSetGetType createFingerprintSetGetType() {
        return new FingerprintSetGetType();
    }

    /**
     * Create an instance of {@link FingerprintSetReturnType }
     * 
     * @return
     *     the new instance of {@link FingerprintSetReturnType }
     */
    public FingerprintSetReturnType createFingerprintSetReturnType() {
        return new FingerprintSetReturnType();
    }

    /**
     * Create an instance of {@link FingerprintSetNewType }
     * 
     * @return
     *     the new instance of {@link FingerprintSetNewType }
     */
    public FingerprintSetNewType createFingerprintSetNewType() {
        return new FingerprintSetNewType();
    }

    /**
     * Create an instance of {@link ApplicantGetType }
     * 
     * @return
     *     the new instance of {@link ApplicantGetType }
     */
    public ApplicantGetType createApplicantGetType() {
        return new ApplicantGetType();
    }

    /**
     * Create an instance of {@link ApplicantNewType }
     * 
     * @return
     *     the new instance of {@link ApplicantNewType }
     */
    public ApplicantNewType createApplicantNewType() {
        return new ApplicantNewType();
    }

    /**
     * Create an instance of {@link ApplicationGetType }
     * 
     * @return
     *     the new instance of {@link ApplicationGetType }
     */
    public ApplicationGetType createApplicationGetType() {
        return new ApplicationGetType();
    }

    /**
     * Create an instance of {@link ApplicationNewType }
     * 
     * @return
     *     the new instance of {@link ApplicationNewType }
     */
    public ApplicationNewType createApplicationNewType() {
        return new ApplicationNewType();
    }

    /**
     * Create an instance of {@link CloseApplicationGetType }
     * 
     * @return
     *     the new instance of {@link CloseApplicationGetType }
     */
    public CloseApplicationGetType createCloseApplicationGetType() {
        return new CloseApplicationGetType();
    }

    /**
     * Create an instance of {@link CloseApplicationNewType }
     * 
     * @return
     *     the new instance of {@link CloseApplicationNewType }
     */
    public CloseApplicationNewType createCloseApplicationNewType() {
        return new CloseApplicationNewType();
    }

    /**
     * Create an instance of {@link RefuseVisaGetType }
     * 
     * @return
     *     the new instance of {@link RefuseVisaGetType }
     */
    public RefuseVisaGetType createRefuseVisaGetType() {
        return new RefuseVisaGetType();
    }

    /**
     * Create an instance of {@link RefuseVisaNewType }
     * 
     * @return
     *     the new instance of {@link RefuseVisaNewType }
     */
    public RefuseVisaNewType createRefuseVisaNewType() {
        return new RefuseVisaNewType();
    }

    /**
     * Create an instance of {@link TransTextType }
     * 
     * @return
     *     the new instance of {@link TransTextType }
     */
    public TransTextType createTransTextType() {
        return new TransTextType();
    }

    /**
     * Create an instance of {@link IssuingAuthorityOfTravelDocumentSearchType }
     * 
     * @return
     *     the new instance of {@link IssuingAuthorityOfTravelDocumentSearchType }
     */
    public IssuingAuthorityOfTravelDocumentSearchType createIssuingAuthorityOfTravelDocumentSearchType() {
        return new IssuingAuthorityOfTravelDocumentSearchType();
    }

    /**
     * Create an instance of {@link SearchType }
     * 
     * @return
     *     the new instance of {@link SearchType }
     */
    public SearchType createSearchType() {
        return new SearchType();
    }

    /**
     * Create an instance of {@link TransType }
     * 
     * @return
     *     the new instance of {@link TransType }
     */
    public TransType createTransType() {
        return new TransType();
    }

    /**
     * Create an instance of {@link SourceValueType }
     * 
     * @return
     *     the new instance of {@link SourceValueType }
     */
    public SourceValueType createSourceValueType() {
        return new SourceValueType();
    }

    /**
     * Create an instance of {@link IssuingAuthorityOfTravelDocumentTransTextType }
     * 
     * @return
     *     the new instance of {@link IssuingAuthorityOfTravelDocumentTransTextType }
     */
    public IssuingAuthorityOfTravelDocumentTransTextType createIssuingAuthorityOfTravelDocumentTransTextType() {
        return new IssuingAuthorityOfTravelDocumentTransTextType();
    }

    /**
     * Create an instance of {@link IssuingAuthorityOfTravelDocumentTransType }
     * 
     * @return
     *     the new instance of {@link IssuingAuthorityOfTravelDocumentTransType }
     */
    public IssuingAuthorityOfTravelDocumentTransType createIssuingAuthorityOfTravelDocumentTransType() {
        return new IssuingAuthorityOfTravelDocumentTransType();
    }

    /**
     * Create an instance of {@link AddressBaseType }
     * 
     * @return
     *     the new instance of {@link AddressBaseType }
     */
    public AddressBaseType createAddressBaseType() {
        return new AddressBaseType();
    }

    /**
     * Create an instance of {@link AddressNewType }
     * 
     * @return
     *     the new instance of {@link AddressNewType }
     */
    public AddressNewType createAddressNewType() {
        return new AddressNewType();
    }

    /**
     * Create an instance of {@link AddressGetType }
     * 
     * @return
     *     the new instance of {@link AddressGetType }
     */
    public AddressGetType createAddressGetType() {
        return new AddressGetType();
    }

    /**
     * Create an instance of {@link ApplicationDecisionNewType }
     * 
     * @return
     *     the new instance of {@link ApplicationDecisionNewType }
     */
    public ApplicationDecisionNewType createApplicationDecisionNewType() {
        return new ApplicationDecisionNewType();
    }

    /**
     * Create an instance of {@link ApplicationDecisionGetType }
     * 
     * @return
     *     the new instance of {@link ApplicationDecisionGetType }
     */
    public ApplicationDecisionGetType createApplicationDecisionGetType() {
        return new ApplicationDecisionGetType();
    }

    /**
     * Create an instance of {@link DiscontinueExaminationNewType }
     * 
     * @return
     *     the new instance of {@link DiscontinueExaminationNewType }
     */
    public DiscontinueExaminationNewType createDiscontinueExaminationNewType() {
        return new DiscontinueExaminationNewType();
    }

    /**
     * Create an instance of {@link DiscontinueExaminationGetType }
     * 
     * @return
     *     the new instance of {@link DiscontinueExaminationGetType }
     */
    public DiscontinueExaminationGetType createDiscontinueExaminationGetType() {
        return new DiscontinueExaminationGetType();
    }

    /**
     * Create an instance of {@link GrantVisaNewType }
     * 
     * @return
     *     the new instance of {@link GrantVisaNewType }
     */
    public GrantVisaNewType createGrantVisaNewType() {
        return new GrantVisaNewType();
    }

    /**
     * Create an instance of {@link GrantVisaGetType }
     * 
     * @return
     *     the new instance of {@link GrantVisaGetType }
     */
    public GrantVisaGetType createGrantVisaGetType() {
        return new GrantVisaGetType();
    }

    /**
     * Create an instance of {@link IdentifierChoiceType }
     * 
     * @return
     *     the new instance of {@link IdentifierChoiceType }
     */
    public IdentifierChoiceType createIdentifierChoiceType() {
        return new IdentifierChoiceType();
    }

    /**
     * Create an instance of {@link ExaminationType }
     * 
     * @return
     *     the new instance of {@link ExaminationType }
     */
    public ExaminationType createExaminationType() {
        return new ExaminationType();
    }

    /**
     * Create an instance of {@link ApplicationExaminationType }
     * 
     * @return
     *     the new instance of {@link ApplicationExaminationType }
     */
    public ApplicationExaminationType createApplicationExaminationType() {
        return new ApplicationExaminationType();
    }

    /**
     * Create an instance of {@link LawEnforcementType }
     * 
     * @return
     *     the new instance of {@link LawEnforcementType }
     */
    public LawEnforcementType createLawEnforcementType() {
        return new LawEnforcementType();
    }

    /**
     * Create an instance of {@link ApplicationCoreDataBaseType }
     * 
     * @return
     *     the new instance of {@link ApplicationCoreDataBaseType }
     */
    public ApplicationCoreDataBaseType createApplicationCoreDataBaseType() {
        return new ApplicationCoreDataBaseType();
    }

    /**
     * Create an instance of {@link ApplicationCoreDataBaseResponseType }
     * 
     * @return
     *     the new instance of {@link ApplicationCoreDataBaseResponseType }
     */
    public ApplicationCoreDataBaseResponseType createApplicationCoreDataBaseResponseType() {
        return new ApplicationCoreDataBaseResponseType();
    }

    /**
     * Create an instance of {@link ApplicationCoreDataNewType }
     * 
     * @return
     *     the new instance of {@link ApplicationCoreDataNewType }
     */
    public ApplicationCoreDataNewType createApplicationCoreDataNewType() {
        return new ApplicationCoreDataNewType();
    }

    /**
     * Create an instance of {@link ApplicationCoreDataGetType }
     * 
     * @return
     *     the new instance of {@link ApplicationCoreDataGetType }
     */
    public ApplicationCoreDataGetType createApplicationCoreDataGetType() {
        return new ApplicationCoreDataGetType();
    }

    /**
     * Create an instance of {@link ApplicationApplicantAbstractType }
     * 
     * @return
     *     the new instance of {@link ApplicationApplicantAbstractType }
     */
    public ApplicationApplicantAbstractType createApplicationApplicantAbstractType() {
        return new ApplicationApplicantAbstractType();
    }

    /**
     * Create an instance of {@link ApplicantBaseNewType }
     * 
     * @return
     *     the new instance of {@link ApplicantBaseNewType }
     */
    public ApplicantBaseNewType createApplicantBaseNewType() {
        return new ApplicantBaseNewType();
    }

    /**
     * Create an instance of {@link ApplicantBaseGetType }
     * 
     * @return
     *     the new instance of {@link ApplicantBaseGetType }
     */
    public ApplicantBaseGetType createApplicantBaseGetType() {
        return new ApplicantBaseGetType();
    }

    /**
     * Create an instance of {@link ApplicantReadType }
     * 
     * @return
     *     the new instance of {@link ApplicantReadType }
     */
    public ApplicantReadType createApplicantReadType() {
        return new ApplicantReadType();
    }

    /**
     * Create an instance of {@link ApplicantPersonNewType }
     * 
     * @return
     *     the new instance of {@link ApplicantPersonNewType }
     */
    public ApplicantPersonNewType createApplicantPersonNewType() {
        return new ApplicantPersonNewType();
    }

    /**
     * Create an instance of {@link ApplicantPersonGetType }
     * 
     * @return
     *     the new instance of {@link ApplicantPersonGetType }
     */
    public ApplicantPersonGetType createApplicantPersonGetType() {
        return new ApplicantPersonGetType();
    }

    /**
     * Create an instance of {@link GenericValuePairType }
     * 
     * @return
     *     the new instance of {@link GenericValuePairType }
     */
    public GenericValuePairType createGenericValuePairType() {
        return new GenericValuePairType();
    }

    /**
     * Create an instance of {@link AttachmentCriteriaType }
     * 
     * @return
     *     the new instance of {@link AttachmentCriteriaType }
     */
    public AttachmentCriteriaType createAttachmentCriteriaType() {
        return new AttachmentCriteriaType();
    }

    /**
     * Create an instance of {@link ListType }
     * 
     * @return
     *     the new instance of {@link ListType }
     */
    public ListType createListType() {
        return new ListType();
    }

    /**
     * Create an instance of {@link BiometricMasterDataGetType }
     * 
     * @return
     *     the new instance of {@link BiometricMasterDataGetType }
     */
    public BiometricMasterDataGetType createBiometricMasterDataGetType() {
        return new BiometricMasterDataGetType();
    }

    /**
     * Create an instance of {@link FingerprintSetTFType }
     * 
     * @return
     *     the new instance of {@link FingerprintSetTFType }
     */
    public FingerprintSetTFType createFingerprintSetTFType() {
        return new FingerprintSetTFType();
    }

    /**
     * Create an instance of {@link FacialImageTFType }
     * 
     * @return
     *     the new instance of {@link FacialImageTFType }
     */
    public FacialImageTFType createFacialImageTFType() {
        return new FacialImageTFType();
    }

    /**
     * Create an instance of {@link FacialImageReferenceType }
     * 
     * @return
     *     the new instance of {@link FacialImageReferenceType }
     */
    public FacialImageReferenceType createFacialImageReferenceType() {
        return new FacialImageReferenceType();
    }

    /**
     * Create an instance of {@link FacialImageReferenceGetType }
     * 
     * @return
     *     the new instance of {@link FacialImageReferenceGetType }
     */
    public FacialImageReferenceGetType createFacialImageReferenceGetType() {
        return new FacialImageReferenceGetType();
    }

    /**
     * Create an instance of {@link FingerprintSetReferenceType }
     * 
     * @return
     *     the new instance of {@link FingerprintSetReferenceType }
     */
    public FingerprintSetReferenceType createFingerprintSetReferenceType() {
        return new FingerprintSetReferenceType();
    }

    /**
     * Create an instance of {@link FingerprintSetReferenceGetType }
     * 
     * @return
     *     the new instance of {@link FingerprintSetReferenceGetType }
     */
    public FingerprintSetReferenceGetType createFingerprintSetReferenceGetType() {
        return new FingerprintSetReferenceGetType();
    }

    /**
     * Create an instance of {@link DecisionBaseResponseType }
     * 
     * @return
     *     the new instance of {@link DecisionBaseResponseType }
     */
    public DecisionBaseResponseType createDecisionBaseResponseType() {
        return new DecisionBaseResponseType();
    }

    /**
     * Create an instance of {@link DecisionBaseRequestType }
     * 
     * @return
     *     the new instance of {@link DecisionBaseRequestType }
     */
    public DecisionBaseRequestType createDecisionBaseRequestType() {
        return new DecisionBaseRequestType();
    }

    /**
     * Create an instance of {@link DecisionNewType }
     * 
     * @return
     *     the new instance of {@link DecisionNewType }
     */
    public DecisionNewType createDecisionNewType() {
        return new DecisionNewType();
    }

    /**
     * Create an instance of {@link DecisionGetType }
     * 
     * @return
     *     the new instance of {@link DecisionGetType }
     */
    public DecisionGetType createDecisionGetType() {
        return new DecisionGetType();
    }

    /**
     * Create an instance of {@link DecisionHistChoiceType }
     * 
     * @return
     *     the new instance of {@link DecisionHistChoiceType }
     */
    public DecisionHistChoiceType createDecisionHistChoiceType() {
        return new DecisionHistChoiceType();
    }

    /**
     * Create an instance of {@link VerificationDecisionHistChoiceType }
     * 
     * @return
     *     the new instance of {@link VerificationDecisionHistChoiceType }
     */
    public VerificationDecisionHistChoiceType createVerificationDecisionHistChoiceType() {
        return new VerificationDecisionHistChoiceType();
    }

    /**
     * Create an instance of {@link AsylumExaminationDecisionHistChoiceType }
     * 
     * @return
     *     the new instance of {@link AsylumExaminationDecisionHistChoiceType }
     */
    public AsylumExaminationDecisionHistChoiceType createAsylumExaminationDecisionHistChoiceType() {
        return new AsylumExaminationDecisionHistChoiceType();
    }

    /**
     * Create an instance of {@link FoundDossierCountType }
     * 
     * @return
     *     the new instance of {@link FoundDossierCountType }
     */
    public FoundDossierCountType createFoundDossierCountType() {
        return new FoundDossierCountType();
    }

    /**
     * Create an instance of {@link OptionListType }
     * 
     * @return
     *     the new instance of {@link OptionListType }
     */
    public OptionListType createOptionListType() {
        return new OptionListType();
    }

    /**
     * Create an instance of {@link FullNameNewType }
     * 
     * @return
     *     the new instance of {@link FullNameNewType }
     */
    public FullNameNewType createFullNameNewType() {
        return new FullNameNewType();
    }

    /**
     * Create an instance of {@link FullNameGetType }
     * 
     * @return
     *     the new instance of {@link FullNameGetType }
     */
    public FullNameGetType createFullNameGetType() {
        return new FullNameGetType();
    }

    /**
     * Create an instance of {@link FullNameSearchType }
     * 
     * @return
     *     the new instance of {@link FullNameSearchType }
     */
    public FullNameSearchType createFullNameSearchType() {
        return new FullNameSearchType();
    }

    /**
     * Create an instance of {@link GroupSynopsisType }
     * 
     * @return
     *     the new instance of {@link GroupSynopsisType }
     */
    public GroupSynopsisType createGroupSynopsisType() {
        return new GroupSynopsisType();
    }

    /**
     * Create an instance of {@link EducationalEstablishmentNewType }
     * 
     * @return
     *     the new instance of {@link EducationalEstablishmentNewType }
     */
    public EducationalEstablishmentNewType createEducationalEstablishmentNewType() {
        return new EducationalEstablishmentNewType();
    }

    /**
     * Create an instance of {@link EducationalEstablishmentGetType }
     * 
     * @return
     *     the new instance of {@link EducationalEstablishmentGetType }
     */
    public EducationalEstablishmentGetType createEducationalEstablishmentGetType() {
        return new EducationalEstablishmentGetType();
    }

    /**
     * Create an instance of {@link HostOrganisationNewType }
     * 
     * @return
     *     the new instance of {@link HostOrganisationNewType }
     */
    public HostOrganisationNewType createHostOrganisationNewType() {
        return new HostOrganisationNewType();
    }

    /**
     * Create an instance of {@link HostNewType }
     * 
     * @return
     *     the new instance of {@link HostNewType }
     */
    public HostNewType createHostNewType() {
        return new HostNewType();
    }

    /**
     * Create an instance of {@link HostOrganisationGetType }
     * 
     * @return
     *     the new instance of {@link HostOrganisationGetType }
     */
    public HostOrganisationGetType createHostOrganisationGetType() {
        return new HostOrganisationGetType();
    }

    /**
     * Create an instance of {@link HostGetType }
     * 
     * @return
     *     the new instance of {@link HostGetType }
     */
    public HostGetType createHostGetType() {
        return new HostGetType();
    }

    /**
     * Create an instance of {@link HostPersonNewType }
     * 
     * @return
     *     the new instance of {@link HostPersonNewType }
     */
    public HostPersonNewType createHostPersonNewType() {
        return new HostPersonNewType();
    }

    /**
     * Create an instance of {@link HostPersonGetType }
     * 
     * @return
     *     the new instance of {@link HostPersonGetType }
     */
    public HostPersonGetType createHostPersonGetType() {
        return new HostPersonGetType();
    }

    /**
     * Create an instance of {@link OccupationBaseType }
     * 
     * @return
     *     the new instance of {@link OccupationBaseType }
     */
    public OccupationBaseType createOccupationBaseType() {
        return new OccupationBaseType();
    }

    /**
     * Create an instance of {@link OccupationNewType }
     * 
     * @return
     *     the new instance of {@link OccupationNewType }
     */
    public OccupationNewType createOccupationNewType() {
        return new OccupationNewType();
    }

    /**
     * Create an instance of {@link OccupationGetType }
     * 
     * @return
     *     the new instance of {@link OccupationGetType }
     */
    public OccupationGetType createOccupationGetType() {
        return new OccupationGetType();
    }

    /**
     * Create an instance of {@link BirthPlaceNewType }
     * 
     * @return
     *     the new instance of {@link BirthPlaceNewType }
     */
    public BirthPlaceNewType createBirthPlaceNewType() {
        return new BirthPlaceNewType();
    }

    /**
     * Create an instance of {@link BirthPlaceGetType }
     * 
     * @return
     *     the new instance of {@link BirthPlaceGetType }
     */
    public BirthPlaceGetType createBirthPlaceGetType() {
        return new BirthPlaceGetType();
    }

    /**
     * Create an instance of {@link PlaceNewType }
     * 
     * @return
     *     the new instance of {@link PlaceNewType }
     */
    public PlaceNewType createPlaceNewType() {
        return new PlaceNewType();
    }

    /**
     * Create an instance of {@link PlaceGetType }
     * 
     * @return
     *     the new instance of {@link PlaceGetType }
     */
    public PlaceGetType createPlaceGetType() {
        return new PlaceGetType();
    }

    /**
     * Create an instance of {@link PlaceSearchType }
     * 
     * @return
     *     the new instance of {@link PlaceSearchType }
     */
    public PlaceSearchType createPlaceSearchType() {
        return new PlaceSearchType();
    }

    /**
     * Create an instance of {@link MainPurposesOfJourneyNewType }
     * 
     * @return
     *     the new instance of {@link MainPurposesOfJourneyNewType }
     */
    public MainPurposesOfJourneyNewType createMainPurposesOfJourneyNewType() {
        return new MainPurposesOfJourneyNewType();
    }

    /**
     * Create an instance of {@link MainPurposesOfJourneyGetType }
     * 
     * @return
     *     the new instance of {@link MainPurposesOfJourneyGetType }
     */
    public MainPurposesOfJourneyGetType createMainPurposesOfJourneyGetType() {
        return new MainPurposesOfJourneyGetType();
    }

    /**
     * Create an instance of {@link ResultFoundDossierType }
     * 
     * @return
     *     the new instance of {@link ResultFoundDossierType }
     */
    public ResultFoundDossierType createResultFoundDossierType() {
        return new ResultFoundDossierType();
    }

    /**
     * Create an instance of {@link DecisionHistGetType }
     * 
     * @return
     *     the new instance of {@link DecisionHistGetType }
     */
    public DecisionHistGetType createDecisionHistGetType() {
        return new DecisionHistGetType();
    }

    /**
     * Create an instance of {@link IdentificationDecisionHistGetType }
     * 
     * @return
     *     the new instance of {@link IdentificationDecisionHistGetType }
     */
    public IdentificationDecisionHistGetType createIdentificationDecisionHistGetType() {
        return new IdentificationDecisionHistGetType();
    }

    /**
     * Create an instance of {@link VerificationDecisionHistGetType }
     * 
     * @return
     *     the new instance of {@link VerificationDecisionHistGetType }
     */
    public VerificationDecisionHistGetType createVerificationDecisionHistGetType() {
        return new VerificationDecisionHistGetType();
    }

    /**
     * Create an instance of {@link AsylumExaminationDecisionHistGetType }
     * 
     * @return
     *     the new instance of {@link AsylumExaminationDecisionHistGetType }
     */
    public AsylumExaminationDecisionHistGetType createAsylumExaminationDecisionHistGetType() {
        return new AsylumExaminationDecisionHistGetType();
    }

    /**
     * Create an instance of {@link LawEnforcementDecisionHistGetType }
     * 
     * @return
     *     the new instance of {@link LawEnforcementDecisionHistGetType }
     */
    public LawEnforcementDecisionHistGetType createLawEnforcementDecisionHistGetType() {
        return new LawEnforcementDecisionHistGetType();
    }

    /**
     * Create an instance of {@link ResultApplicationFileType }
     * 
     * @return
     *     the new instance of {@link ResultApplicationFileType }
     */
    public ResultApplicationFileType createResultApplicationFileType() {
        return new ResultApplicationFileType();
    }

    /**
     * Create an instance of {@link BiometricTFGetType }
     * 
     * @return
     *     the new instance of {@link BiometricTFGetType }
     */
    public BiometricTFGetType createBiometricTFGetType() {
        return new BiometricTFGetType();
    }

    /**
     * Create an instance of {@link BiometricChoiceType }
     * 
     * @return
     *     the new instance of {@link BiometricChoiceType }
     */
    public BiometricChoiceType createBiometricChoiceType() {
        return new BiometricChoiceType();
    }

    /**
     * Create an instance of {@link BiometricChoiceGetType }
     * 
     * @return
     *     the new instance of {@link BiometricChoiceGetType }
     */
    public BiometricChoiceGetType createBiometricChoiceGetType() {
        return new BiometricChoiceGetType();
    }

    /**
     * Create an instance of {@link ResultApplicantOverviewType }
     * 
     * @return
     *     the new instance of {@link ResultApplicantOverviewType }
     */
    public ResultApplicantOverviewType createResultApplicantOverviewType() {
        return new ResultApplicantOverviewType();
    }

    /**
     * Create an instance of {@link ResultOverviewBaseType }
     * 
     * @return
     *     the new instance of {@link ResultOverviewBaseType }
     */
    public ResultOverviewBaseType createResultOverviewBaseType() {
        return new ResultOverviewBaseType();
    }

    /**
     * Create an instance of {@link DecisionOverviewType }
     * 
     * @return
     *     the new instance of {@link DecisionOverviewType }
     */
    public DecisionOverviewType createDecisionOverviewType() {
        return new DecisionOverviewType();
    }

    /**
     * Create an instance of {@link DecisionOverviewResponseType }
     * 
     * @return
     *     the new instance of {@link DecisionOverviewResponseType }
     */
    public DecisionOverviewResponseType createDecisionOverviewResponseType() {
        return new DecisionOverviewResponseType();
    }

    /**
     * Create an instance of {@link DecisionOverviewBaseType }
     * 
     * @return
     *     the new instance of {@link DecisionOverviewBaseType }
     */
    public DecisionOverviewBaseType createDecisionOverviewBaseType() {
        return new DecisionOverviewBaseType();
    }

    /**
     * Create an instance of {@link ExtendedVisaOverviewType }
     * 
     * @return
     *     the new instance of {@link ExtendedVisaOverviewType }
     */
    public ExtendedVisaOverviewType createExtendedVisaOverviewType() {
        return new ExtendedVisaOverviewType();
    }

    /**
     * Create an instance of {@link ExtendedVisaOverviewResponseType }
     * 
     * @return
     *     the new instance of {@link ExtendedVisaOverviewResponseType }
     */
    public ExtendedVisaOverviewResponseType createExtendedVisaOverviewResponseType() {
        return new ExtendedVisaOverviewResponseType();
    }

    /**
     * Create an instance of {@link VisaStickerOverviewType }
     * 
     * @return
     *     the new instance of {@link VisaStickerOverviewType }
     */
    public VisaStickerOverviewType createVisaStickerOverviewType() {
        return new VisaStickerOverviewType();
    }

    /**
     * Create an instance of {@link VisaStickerOverviewResponseType }
     * 
     * @return
     *     the new instance of {@link VisaStickerOverviewResponseType }
     */
    public VisaStickerOverviewResponseType createVisaStickerOverviewResponseType() {
        return new VisaStickerOverviewResponseType();
    }

    /**
     * Create an instance of {@link ResultReadCoreDataBaseType }
     * 
     * @return
     *     the new instance of {@link ResultReadCoreDataBaseType }
     */
    public ResultReadCoreDataBaseType createResultReadCoreDataBaseType() {
        return new ResultReadCoreDataBaseType();
    }

    /**
     * Create an instance of {@link ResultLawEnforcementOverviewType }
     * 
     * @return
     *     the new instance of {@link ResultLawEnforcementOverviewType }
     */
    public ResultLawEnforcementOverviewType createResultLawEnforcementOverviewType() {
        return new ResultLawEnforcementOverviewType();
    }

    /**
     * Create an instance of {@link ListRetrievalBaseType }
     * 
     * @return
     *     the new instance of {@link ListRetrievalBaseType }
     */
    public ListRetrievalBaseType createListRetrievalBaseType() {
        return new ListRetrievalBaseType();
    }

    /**
     * Create an instance of {@link SampleExceptionsType }
     * 
     * @return
     *     the new instance of {@link SampleExceptionsType }
     */
    public SampleExceptionsType createSampleExceptionsType() {
        return new SampleExceptionsType();
    }

    /**
     * Create an instance of {@link ListApplicationsInGroupOverviewType }
     * 
     * @return
     *     the new instance of {@link ListApplicationsInGroupOverviewType }
     */
    public ListApplicationsInGroupOverviewType createListApplicationsInGroupOverviewType() {
        return new ListApplicationsInGroupOverviewType();
    }

    /**
     * Create an instance of {@link ListVisaInGroupOverviewType }
     * 
     * @return
     *     the new instance of {@link ListVisaInGroupOverviewType }
     */
    public ListVisaInGroupOverviewType createListVisaInGroupOverviewType() {
        return new ListVisaInGroupOverviewType();
    }

    /**
     * Create an instance of {@link ListApplicationsInDossierAsylumOverviewType }
     * 
     * @return
     *     the new instance of {@link ListApplicationsInDossierAsylumOverviewType }
     */
    public ListApplicationsInDossierAsylumOverviewType createListApplicationsInDossierAsylumOverviewType() {
        return new ListApplicationsInDossierAsylumOverviewType();
    }

    /**
     * Create an instance of {@link ListApplicationsInDossierApplicationExaminationOverviewType }
     * 
     * @return
     *     the new instance of {@link ListApplicationsInDossierApplicationExaminationOverviewType }
     */
    public ListApplicationsInDossierApplicationExaminationOverviewType createListApplicationsInDossierApplicationExaminationOverviewType() {
        return new ListApplicationsInDossierApplicationExaminationOverviewType();
    }

    /**
     * Create an instance of {@link ListApplicationsInDossierIdentificationBorderOverviewType }
     * 
     * @return
     *     the new instance of {@link ListApplicationsInDossierIdentificationBorderOverviewType }
     */
    public ListApplicationsInDossierIdentificationBorderOverviewType createListApplicationsInDossierIdentificationBorderOverviewType() {
        return new ListApplicationsInDossierIdentificationBorderOverviewType();
    }

    /**
     * Create an instance of {@link ListApplicationsInDossierIdentificationTerritoryOverviewType }
     * 
     * @return
     *     the new instance of {@link ListApplicationsInDossierIdentificationTerritoryOverviewType }
     */
    public ListApplicationsInDossierIdentificationTerritoryOverviewType createListApplicationsInDossierIdentificationTerritoryOverviewType() {
        return new ListApplicationsInDossierIdentificationTerritoryOverviewType();
    }

    /**
     * Create an instance of {@link ListApplicationsInDossierLawEnforcementOverviewType }
     * 
     * @return
     *     the new instance of {@link ListApplicationsInDossierLawEnforcementOverviewType }
     */
    public ListApplicationsInDossierLawEnforcementOverviewType createListApplicationsInDossierLawEnforcementOverviewType() {
        return new ListApplicationsInDossierLawEnforcementOverviewType();
    }

    /**
     * Create an instance of {@link TravelDocumentBaseType }
     * 
     * @return
     *     the new instance of {@link TravelDocumentBaseType }
     */
    public TravelDocumentBaseType createTravelDocumentBaseType() {
        return new TravelDocumentBaseType();
    }

    /**
     * Create an instance of {@link TravelDocumentNewType }
     * 
     * @return
     *     the new instance of {@link TravelDocumentNewType }
     */
    public TravelDocumentNewType createTravelDocumentNewType() {
        return new TravelDocumentNewType();
    }

    /**
     * Create an instance of {@link TravelDocumentGetType }
     * 
     * @return
     *     the new instance of {@link TravelDocumentGetType }
     */
    public TravelDocumentGetType createTravelDocumentGetType() {
        return new TravelDocumentGetType();
    }

    /**
     * Create an instance of {@link SearchTravelDocumentNumberType }
     * 
     * @return
     *     the new instance of {@link SearchTravelDocumentNumberType }
     */
    public SearchTravelDocumentNumberType createSearchTravelDocumentNumberType() {
        return new SearchTravelDocumentNumberType();
    }

    /**
     * Create an instance of {@link VisaCreationDecisionNewType }
     * 
     * @return
     *     the new instance of {@link VisaCreationDecisionNewType }
     */
    public VisaCreationDecisionNewType createVisaCreationDecisionNewType() {
        return new VisaCreationDecisionNewType();
    }

    /**
     * Create an instance of {@link VisaCreationDecisionCorrectType }
     * 
     * @return
     *     the new instance of {@link VisaCreationDecisionCorrectType }
     */
    public VisaCreationDecisionCorrectType createVisaCreationDecisionCorrectType() {
        return new VisaCreationDecisionCorrectType();
    }

    /**
     * Create an instance of {@link VisaCreationDecisionGetType }
     * 
     * @return
     *     the new instance of {@link VisaCreationDecisionGetType }
     */
    public VisaCreationDecisionGetType createVisaCreationDecisionGetType() {
        return new VisaCreationDecisionGetType();
    }

    /**
     * Create an instance of {@link IssueVisaNewType }
     * 
     * @return
     *     the new instance of {@link IssueVisaNewType }
     */
    public IssueVisaNewType createIssueVisaNewType() {
        return new IssueVisaNewType();
    }

    /**
     * Create an instance of {@link IssueVisaGetType }
     * 
     * @return
     *     the new instance of {@link IssueVisaGetType }
     */
    public IssueVisaGetType createIssueVisaGetType() {
        return new IssueVisaGetType();
    }

    /**
     * Create an instance of {@link VisaDecisionNewType }
     * 
     * @return
     *     the new instance of {@link VisaDecisionNewType }
     */
    public VisaDecisionNewType createVisaDecisionNewType() {
        return new VisaDecisionNewType();
    }

    /**
     * Create an instance of {@link VisaDecisionGetType }
     * 
     * @return
     *     the new instance of {@link VisaDecisionGetType }
     */
    public VisaDecisionGetType createVisaDecisionGetType() {
        return new VisaDecisionGetType();
    }

    /**
     * Create an instance of {@link VisaStickerBaseType }
     * 
     * @return
     *     the new instance of {@link VisaStickerBaseType }
     */
    public VisaStickerBaseType createVisaStickerBaseType() {
        return new VisaStickerBaseType();
    }

    /**
     * Create an instance of {@link VisaStickerNewType }
     * 
     * @return
     *     the new instance of {@link VisaStickerNewType }
     */
    public VisaStickerNewType createVisaStickerNewType() {
        return new VisaStickerNewType();
    }

    /**
     * Create an instance of {@link VisaStickerGetType }
     * 
     * @return
     *     the new instance of {@link VisaStickerGetType }
     */
    public VisaStickerGetType createVisaStickerGetType() {
        return new VisaStickerGetType();
    }

    /**
     * Create an instance of {@link MemberStatesOfDestinationType }
     * 
     * @return
     *     the new instance of {@link MemberStatesOfDestinationType }
     */
    public MemberStatesOfDestinationType createMemberStatesOfDestinationType() {
        return new MemberStatesOfDestinationType();
    }

    /**
     * Create an instance of {@link VisaTypeType }
     * 
     * @return
     *     the new instance of {@link VisaTypeType }
     */
    public VisaTypeType createVisaTypeType() {
        return new VisaTypeType();
    }

    /**
     * Create an instance of {@link AuthorityNewType }
     * 
     * @return
     *     the new instance of {@link AuthorityNewType }
     */
    public AuthorityNewType createAuthorityNewType() {
        return new AuthorityNewType();
    }

    /**
     * Create an instance of {@link AuthoritySearchType }
     * 
     * @return
     *     the new instance of {@link AuthoritySearchType }
     */
    public AuthoritySearchType createAuthoritySearchType() {
        return new AuthoritySearchType();
    }

    /**
     * Create an instance of {@link AuthorityGetType }
     * 
     * @return
     *     the new instance of {@link AuthorityGetType }
     */
    public AuthorityGetType createAuthorityGetType() {
        return new AuthorityGetType();
    }

    /**
     * Create an instance of {@link NationalityForApplicationsType }
     * 
     * @return
     *     the new instance of {@link NationalityForApplicationsType }
     */
    public NationalityForApplicationsType createNationalityForApplicationsType() {
        return new NationalityForApplicationsType();
    }

    /**
     * Create an instance of {@link TravelDocumentSearchRequestType.Sex }
     * 
     * @return
     *     the new instance of {@link TravelDocumentSearchRequestType.Sex }
     */
    public TravelDocumentSearchRequestType.Sex createTravelDocumentSearchRequestTypeSex() {
        return new TravelDocumentSearchRequestType.Sex();
    }

    /**
     * Create an instance of {@link TravelDocumentSearchRequestType.TypeOfTravelDocument }
     * 
     * @return
     *     the new instance of {@link TravelDocumentSearchRequestType.TypeOfTravelDocument }
     */
    public TravelDocumentSearchRequestType.TypeOfTravelDocument createTravelDocumentSearchRequestTypeTypeOfTravelDocument() {
        return new TravelDocumentSearchRequestType.TypeOfTravelDocument();
    }

    /**
     * Create an instance of {@link TravelDocumentSearchRequestType.TravelDocumentNumber }
     * 
     * @return
     *     the new instance of {@link TravelDocumentSearchRequestType.TravelDocumentNumber }
     */
    public TravelDocumentSearchRequestType.TravelDocumentNumber createTravelDocumentSearchRequestTypeTravelDocumentNumber() {
        return new TravelDocumentSearchRequestType.TravelDocumentNumber();
    }

    /**
     * Create an instance of {@link TravelDocumentSearchRequestType.NationalityForApplications }
     * 
     * @return
     *     the new instance of {@link TravelDocumentSearchRequestType.NationalityForApplications }
     */
    public TravelDocumentSearchRequestType.NationalityForApplications createTravelDocumentSearchRequestTypeNationalityForApplications() {
        return new TravelDocumentSearchRequestType.NationalityForApplications();
    }

    /**
     * Create an instance of {@link SearchByTravelDocumentType.TypeOfTravelDocument }
     * 
     * @return
     *     the new instance of {@link SearchByTravelDocumentType.TypeOfTravelDocument }
     */
    public SearchByTravelDocumentType.TypeOfTravelDocument createSearchByTravelDocumentTypeTypeOfTravelDocument() {
        return new SearchByTravelDocumentType.TypeOfTravelDocument();
    }

    /**
     * Create an instance of {@link SearchByTravelDocumentType.TravelDocumentNumber }
     * 
     * @return
     *     the new instance of {@link SearchByTravelDocumentType.TravelDocumentNumber }
     */
    public SearchByTravelDocumentType.TravelDocumentNumber createSearchByTravelDocumentTypeTravelDocumentNumber() {
        return new SearchByTravelDocumentType.TravelDocumentNumber();
    }

    /**
     * Create an instance of {@link SearchApplicationExaminationTravelDocumentType.TypeOfTravelDocument }
     * 
     * @return
     *     the new instance of {@link SearchApplicationExaminationTravelDocumentType.TypeOfTravelDocument }
     */
    public SearchApplicationExaminationTravelDocumentType.TypeOfTravelDocument createSearchApplicationExaminationTravelDocumentTypeTypeOfTravelDocument() {
        return new SearchApplicationExaminationTravelDocumentType.TypeOfTravelDocument();
    }

    /**
     * Create an instance of {@link SearchApplicationExaminationTravelDocumentType.TravelDocumentNumber }
     * 
     * @return
     *     the new instance of {@link SearchApplicationExaminationTravelDocumentType.TravelDocumentNumber }
     */
    public SearchApplicationExaminationTravelDocumentType.TravelDocumentNumber createSearchApplicationExaminationTravelDocumentTypeTravelDocumentNumber() {
        return new SearchApplicationExaminationTravelDocumentType.TravelDocumentNumber();
    }

    /**
     * Create an instance of {@link SearchByTravelDocumentApplicantType.Sex }
     * 
     * @return
     *     the new instance of {@link SearchByTravelDocumentApplicantType.Sex }
     */
    public SearchByTravelDocumentApplicantType.Sex createSearchByTravelDocumentApplicantTypeSex() {
        return new SearchByTravelDocumentApplicantType.Sex();
    }

    /**
     * Create an instance of {@link SearchApplicationExaminationApplicantType.Sex }
     * 
     * @return
     *     the new instance of {@link SearchApplicationExaminationApplicantType.Sex }
     */
    public SearchApplicationExaminationApplicantType.Sex createSearchApplicationExaminationApplicantTypeSex() {
        return new SearchApplicationExaminationApplicantType.Sex();
    }

    /**
     * Create an instance of {@link MemberStatesOfDestinationSearchType.MemberStateOfDestination }
     * 
     * @return
     *     the new instance of {@link MemberStatesOfDestinationSearchType.MemberStateOfDestination }
     */
    public MemberStatesOfDestinationSearchType.MemberStateOfDestination createMemberStatesOfDestinationSearchTypeMemberStateOfDestination() {
        return new MemberStatesOfDestinationSearchType.MemberStateOfDestination();
    }

    /**
     * Create an instance of {@link TerritorialValidityType.TravelTerritories }
     * 
     * @return
     *     the new instance of {@link TerritorialValidityType.TravelTerritories }
     */
    public TerritorialValidityType.TravelTerritories createTerritorialValidityTypeTravelTerritories() {
        return new TerritorialValidityType.TravelTerritories();
    }

    /**
     * Create an instance of {@link AnnulVisaGetType.AnnulmentGrounds }
     * 
     * @return
     *     the new instance of {@link AnnulVisaGetType.AnnulmentGrounds }
     */
    public AnnulVisaGetType.AnnulmentGrounds createAnnulVisaGetTypeAnnulmentGrounds() {
        return new AnnulVisaGetType.AnnulmentGrounds();
    }

    /**
     * Create an instance of {@link AnnulVisaNewType.AnnulmentGrounds }
     * 
     * @return
     *     the new instance of {@link AnnulVisaNewType.AnnulmentGrounds }
     */
    public AnnulVisaNewType.AnnulmentGrounds createAnnulVisaNewTypeAnnulmentGrounds() {
        return new AnnulVisaNewType.AnnulmentGrounds();
    }

    /**
     * Create an instance of {@link ExtendVisaWithoutNewStickerGetType.ExtensionGrounds }
     * 
     * @return
     *     the new instance of {@link ExtendVisaWithoutNewStickerGetType.ExtensionGrounds }
     */
    public ExtendVisaWithoutNewStickerGetType.ExtensionGrounds createExtendVisaWithoutNewStickerGetTypeExtensionGrounds() {
        return new ExtendVisaWithoutNewStickerGetType.ExtensionGrounds();
    }

    /**
     * Create an instance of {@link RevokeVisaGetType.RevocationGrounds }
     * 
     * @return
     *     the new instance of {@link RevokeVisaGetType.RevocationGrounds }
     */
    public RevokeVisaGetType.RevocationGrounds createRevokeVisaGetTypeRevocationGrounds() {
        return new RevokeVisaGetType.RevocationGrounds();
    }

    /**
     * Create an instance of {@link RevokeVisaNewType.RevocationGrounds }
     * 
     * @return
     *     the new instance of {@link RevokeVisaNewType.RevocationGrounds }
     */
    public RevokeVisaNewType.RevocationGrounds createRevokeVisaNewTypeRevocationGrounds() {
        return new RevokeVisaNewType.RevocationGrounds();
    }

    /**
     * Create an instance of {@link ShortenValidityPeriodWithoutNewStickerGetType.ReducedDurationGrounds }
     * 
     * @return
     *     the new instance of {@link ShortenValidityPeriodWithoutNewStickerGetType.ReducedDurationGrounds }
     */
    public ShortenValidityPeriodWithoutNewStickerGetType.ReducedDurationGrounds createShortenValidityPeriodWithoutNewStickerGetTypeReducedDurationGrounds() {
        return new ShortenValidityPeriodWithoutNewStickerGetType.ReducedDurationGrounds();
    }

    /**
     * Create an instance of {@link ShortenValidityPeriodWithNewStickerGetType.ReducedDurationGrounds }
     * 
     * @return
     *     the new instance of {@link ShortenValidityPeriodWithNewStickerGetType.ReducedDurationGrounds }
     */
    public ShortenValidityPeriodWithNewStickerGetType.ReducedDurationGrounds createShortenValidityPeriodWithNewStickerGetTypeReducedDurationGrounds() {
        return new ShortenValidityPeriodWithNewStickerGetType.ReducedDurationGrounds();
    }

    /**
     * Create an instance of {@link ExtendVisaGetType.ExtensionGrounds }
     * 
     * @return
     *     the new instance of {@link ExtendVisaGetType.ExtensionGrounds }
     */
    public ExtendVisaGetType.ExtensionGrounds createExtendVisaGetTypeExtensionGrounds() {
        return new ExtendVisaGetType.ExtensionGrounds();
    }

    /**
     * Create an instance of {@link ExtendVisaNewType.ExtensionGrounds }
     * 
     * @return
     *     the new instance of {@link ExtendVisaNewType.ExtensionGrounds }
     */
    public ExtendVisaNewType.ExtensionGrounds createExtendVisaNewTypeExtensionGrounds() {
        return new ExtendVisaNewType.ExtensionGrounds();
    }

    /**
     * Create an instance of {@link ExtendVisaCorrectType.ExtensionGrounds }
     * 
     * @return
     *     the new instance of {@link ExtendVisaCorrectType.ExtensionGrounds }
     */
    public ExtendVisaCorrectType.ExtensionGrounds createExtendVisaCorrectTypeExtensionGrounds() {
        return new ExtendVisaCorrectType.ExtensionGrounds();
    }

    /**
     * Create an instance of {@link SearchBirthPlaceType.Country }
     * 
     * @return
     *     the new instance of {@link SearchBirthPlaceType.Country }
     */
    public SearchBirthPlaceType.Country createSearchBirthPlaceTypeCountry() {
        return new SearchBirthPlaceType.Country();
    }

    /**
     * Create an instance of {@link SearchAddressType.Country }
     * 
     * @return
     *     the new instance of {@link SearchAddressType.Country }
     */
    public SearchAddressType.Country createSearchAddressTypeCountry() {
        return new SearchAddressType.Country();
    }

    /**
     * Create an instance of {@link SearchApplicationExaminationHostType.SearchHostOrganisation }
     * 
     * @return
     *     the new instance of {@link SearchApplicationExaminationHostType.SearchHostOrganisation }
     */
    public SearchApplicationExaminationHostType.SearchHostOrganisation createSearchApplicationExaminationHostTypeSearchHostOrganisation() {
        return new SearchApplicationExaminationHostType.SearchHostOrganisation();
    }

    /**
     * Create an instance of {@link SearchApplicationExaminationHostType.SearchHostPerson }
     * 
     * @return
     *     the new instance of {@link SearchApplicationExaminationHostType.SearchHostPerson }
     */
    public SearchApplicationExaminationHostType.SearchHostPerson createSearchApplicationExaminationHostTypeSearchHostPerson() {
        return new SearchApplicationExaminationHostType.SearchHostPerson();
    }

    /**
     * Create an instance of {@link SearchHostType.SearchHostOrganisation }
     * 
     * @return
     *     the new instance of {@link SearchHostType.SearchHostOrganisation }
     */
    public SearchHostType.SearchHostOrganisation createSearchHostTypeSearchHostOrganisation() {
        return new SearchHostType.SearchHostOrganisation();
    }

    /**
     * Create an instance of {@link SearchHostType.SearchHostPerson }
     * 
     * @return
     *     the new instance of {@link SearchHostType.SearchHostPerson }
     */
    public SearchHostType.SearchHostPerson createSearchHostTypeSearchHostPerson() {
        return new SearchHostType.SearchHostPerson();
    }

    /**
     * Create an instance of {@link SearchTravelDocumentType.TypeOfTravelDocument }
     * 
     * @return
     *     the new instance of {@link SearchTravelDocumentType.TypeOfTravelDocument }
     */
    public SearchTravelDocumentType.TypeOfTravelDocument createSearchTravelDocumentTypeTypeOfTravelDocument() {
        return new SearchTravelDocumentType.TypeOfTravelDocument();
    }

    /**
     * Create an instance of {@link SearchApplicantType.AnyName }
     * 
     * @return
     *     the new instance of {@link SearchApplicantType.AnyName }
     */
    public SearchApplicantType.AnyName createSearchApplicantTypeAnyName() {
        return new SearchApplicantType.AnyName();
    }

    /**
     * Create an instance of {@link SearchApplicantType.FormerSurnames }
     * 
     * @return
     *     the new instance of {@link SearchApplicantType.FormerSurnames }
     */
    public SearchApplicantType.FormerSurnames createSearchApplicantTypeFormerSurnames() {
        return new SearchApplicantType.FormerSurnames();
    }

    /**
     * Create an instance of {@link SearchApplicantType.Sex }
     * 
     * @return
     *     the new instance of {@link SearchApplicantType.Sex }
     */
    public SearchApplicantType.Sex createSearchApplicantTypeSex() {
        return new SearchApplicantType.Sex();
    }

    /**
     * Create an instance of {@link ResultReadApplicationBaseType.FormerSurnames }
     * 
     * @return
     *     the new instance of {@link ResultReadApplicationBaseType.FormerSurnames }
     */
    public ResultReadApplicationBaseType.FormerSurnames createResultReadApplicationBaseTypeFormerSurnames() {
        return new ResultReadApplicationBaseType.FormerSurnames();
    }

    /**
     * Create an instance of {@link ResultVerificationBorderControlReadType.ApplicationData }
     * 
     * @return
     *     the new instance of {@link ResultVerificationBorderControlReadType.ApplicationData }
     */
    public ResultVerificationBorderControlReadType.ApplicationData createResultVerificationBorderControlReadTypeApplicationData() {
        return new ResultVerificationBorderControlReadType.ApplicationData();
    }

    /**
     * Create an instance of {@link ResultVerificationBorderControlReadType.Decisions }
     * 
     * @return
     *     the new instance of {@link ResultVerificationBorderControlReadType.Decisions }
     */
    public ResultVerificationBorderControlReadType.Decisions createResultVerificationBorderControlReadTypeDecisions() {
        return new ResultVerificationBorderControlReadType.Decisions();
    }

    /**
     * Create an instance of {@link ResultVerificationBorderControlReadType.FacialImages }
     * 
     * @return
     *     the new instance of {@link ResultVerificationBorderControlReadType.FacialImages }
     */
    public ResultVerificationBorderControlReadType.FacialImages createResultVerificationBorderControlReadTypeFacialImages() {
        return new ResultVerificationBorderControlReadType.FacialImages();
    }

    /**
     * Create an instance of {@link ResultVerificationBorderControlReadType.GroupSynopses }
     * 
     * @return
     *     the new instance of {@link ResultVerificationBorderControlReadType.GroupSynopses }
     */
    public ResultVerificationBorderControlReadType.GroupSynopses createResultVerificationBorderControlReadTypeGroupSynopses() {
        return new ResultVerificationBorderControlReadType.GroupSynopses();
    }

    /**
     * Create an instance of {@link ResultReadCoreBorderDataType.Host }
     * 
     * @return
     *     the new instance of {@link ResultReadCoreBorderDataType.Host }
     */
    public ResultReadCoreBorderDataType.Host createResultReadCoreBorderDataTypeHost() {
        return new ResultReadCoreBorderDataType.Host();
    }

    /**
     * Create an instance of {@link ResultVerificationBorderReadType.ApplicationData }
     * 
     * @return
     *     the new instance of {@link ResultVerificationBorderReadType.ApplicationData }
     */
    public ResultVerificationBorderReadType.ApplicationData createResultVerificationBorderReadTypeApplicationData() {
        return new ResultVerificationBorderReadType.ApplicationData();
    }

    /**
     * Create an instance of {@link ResultVerificationBorderReadType.Decisions }
     * 
     * @return
     *     the new instance of {@link ResultVerificationBorderReadType.Decisions }
     */
    public ResultVerificationBorderReadType.Decisions createResultVerificationBorderReadTypeDecisions() {
        return new ResultVerificationBorderReadType.Decisions();
    }

    /**
     * Create an instance of {@link ResultVerificationBorderReadType.FacialImages }
     * 
     * @return
     *     the new instance of {@link ResultVerificationBorderReadType.FacialImages }
     */
    public ResultVerificationBorderReadType.FacialImages createResultVerificationBorderReadTypeFacialImages() {
        return new ResultVerificationBorderReadType.FacialImages();
    }

    /**
     * Create an instance of {@link ResultVerificationBorderReadType.GroupSynopses }
     * 
     * @return
     *     the new instance of {@link ResultVerificationBorderReadType.GroupSynopses }
     */
    public ResultVerificationBorderReadType.GroupSynopses createResultVerificationBorderReadTypeGroupSynopses() {
        return new ResultVerificationBorderReadType.GroupSynopses();
    }

    /**
     * Create an instance of {@link ResultRegistrationReadType.ApplicationData }
     * 
     * @return
     *     the new instance of {@link ResultRegistrationReadType.ApplicationData }
     */
    public ResultRegistrationReadType.ApplicationData createResultRegistrationReadTypeApplicationData() {
        return new ResultRegistrationReadType.ApplicationData();
    }

    /**
     * Create an instance of {@link ResultRegistrationReadType.Decisions }
     * 
     * @return
     *     the new instance of {@link ResultRegistrationReadType.Decisions }
     */
    public ResultRegistrationReadType.Decisions createResultRegistrationReadTypeDecisions() {
        return new ResultRegistrationReadType.Decisions();
    }

    /**
     * Create an instance of {@link ResultRegistrationReadType.FacialImages }
     * 
     * @return
     *     the new instance of {@link ResultRegistrationReadType.FacialImages }
     */
    public ResultRegistrationReadType.FacialImages createResultRegistrationReadTypeFacialImages() {
        return new ResultRegistrationReadType.FacialImages();
    }

    /**
     * Create an instance of {@link ResultRegistrationReadType.GroupSynopses }
     * 
     * @return
     *     the new instance of {@link ResultRegistrationReadType.GroupSynopses }
     */
    public ResultRegistrationReadType.GroupSynopses createResultRegistrationReadTypeGroupSynopses() {
        return new ResultRegistrationReadType.GroupSynopses();
    }

    /**
     * Create an instance of {@link ResultReadCoreDataType.Host }
     * 
     * @return
     *     the new instance of {@link ResultReadCoreDataType.Host }
     */
    public ResultReadCoreDataType.Host createResultReadCoreDataTypeHost() {
        return new ResultReadCoreDataType.Host();
    }

    /**
     * Create an instance of {@link ResultVerificationReadType.ApplicationData }
     * 
     * @return
     *     the new instance of {@link ResultVerificationReadType.ApplicationData }
     */
    public ResultVerificationReadType.ApplicationData createResultVerificationReadTypeApplicationData() {
        return new ResultVerificationReadType.ApplicationData();
    }

    /**
     * Create an instance of {@link ResultVerificationReadType.Decisions }
     * 
     * @return
     *     the new instance of {@link ResultVerificationReadType.Decisions }
     */
    public ResultVerificationReadType.Decisions createResultVerificationReadTypeDecisions() {
        return new ResultVerificationReadType.Decisions();
    }

    /**
     * Create an instance of {@link ResultVerificationReadType.FacialImages }
     * 
     * @return
     *     the new instance of {@link ResultVerificationReadType.FacialImages }
     */
    public ResultVerificationReadType.FacialImages createResultVerificationReadTypeFacialImages() {
        return new ResultVerificationReadType.FacialImages();
    }

    /**
     * Create an instance of {@link ResultVerificationReadType.GroupSynopses }
     * 
     * @return
     *     the new instance of {@link ResultVerificationReadType.GroupSynopses }
     */
    public ResultVerificationReadType.GroupSynopses createResultVerificationReadTypeGroupSynopses() {
        return new ResultVerificationReadType.GroupSynopses();
    }

    /**
     * Create an instance of {@link ListApplicationsInFamilyGroupOverviewType.FormerSurnames }
     * 
     * @return
     *     the new instance of {@link ListApplicationsInFamilyGroupOverviewType.FormerSurnames }
     */
    public ListApplicationsInFamilyGroupOverviewType.FormerSurnames createListApplicationsInFamilyGroupOverviewTypeFormerSurnames() {
        return new ListApplicationsInFamilyGroupOverviewType.FormerSurnames();
    }

    /**
     * Create an instance of {@link ResultListVisaInGroupType.VisaGroupOverviews }
     * 
     * @return
     *     the new instance of {@link ResultListVisaInGroupType.VisaGroupOverviews }
     */
    public ResultListVisaInGroupType.VisaGroupOverviews createResultListVisaInGroupTypeVisaGroupOverviews() {
        return new ResultListVisaInGroupType.VisaGroupOverviews();
    }

    /**
     * Create an instance of {@link ResultListApplicationsInGroupType.GroupOverviews }
     * 
     * @return
     *     the new instance of {@link ResultListApplicationsInGroupType.GroupOverviews }
     */
    public ResultListApplicationsInGroupType.GroupOverviews createResultListApplicationsInGroupTypeGroupOverviews() {
        return new ResultListApplicationsInGroupType.GroupOverviews();
    }

    /**
     * Create an instance of {@link ResultListApplicationsInFamilyGroupType.FamilyGroupOverviews }
     * 
     * @return
     *     the new instance of {@link ResultListApplicationsInFamilyGroupType.FamilyGroupOverviews }
     */
    public ResultListApplicationsInFamilyGroupType.FamilyGroupOverviews createResultListApplicationsInFamilyGroupTypeFamilyGroupOverviews() {
        return new ResultListApplicationsInFamilyGroupType.FamilyGroupOverviews();
    }

    /**
     * Create an instance of {@link ResultLawEnforcementSearchType.LawEnforcementOverviews }
     * 
     * @return
     *     the new instance of {@link ResultLawEnforcementSearchType.LawEnforcementOverviews }
     */
    public ResultLawEnforcementSearchType.LawEnforcementOverviews createResultLawEnforcementSearchTypeLawEnforcementOverviews() {
        return new ResultLawEnforcementSearchType.LawEnforcementOverviews();
    }

    /**
     * Create an instance of {@link ResultLawEnforcementReadType.Decisions }
     * 
     * @return
     *     the new instance of {@link ResultLawEnforcementReadType.Decisions }
     */
    public ResultLawEnforcementReadType.Decisions createResultLawEnforcementReadTypeDecisions() {
        return new ResultLawEnforcementReadType.Decisions();
    }

    /**
     * Create an instance of {@link ResultVerificationOverviewType.GroupSynopses }
     * 
     * @return
     *     the new instance of {@link ResultVerificationOverviewType.GroupSynopses }
     */
    public ResultVerificationOverviewType.GroupSynopses createResultVerificationOverviewTypeGroupSynopses() {
        return new ResultVerificationOverviewType.GroupSynopses();
    }

    /**
     * Create an instance of {@link ResultIdentificationOverviewType.GroupSynopses }
     * 
     * @return
     *     the new instance of {@link ResultIdentificationOverviewType.GroupSynopses }
     */
    public ResultIdentificationOverviewType.GroupSynopses createResultIdentificationOverviewTypeGroupSynopses() {
        return new ResultIdentificationOverviewType.GroupSynopses();
    }

    /**
     * Create an instance of {@link ResultIdentificationReadType.ApplicationData }
     * 
     * @return
     *     the new instance of {@link ResultIdentificationReadType.ApplicationData }
     */
    public ResultIdentificationReadType.ApplicationData createResultIdentificationReadTypeApplicationData() {
        return new ResultIdentificationReadType.ApplicationData();
    }

    /**
     * Create an instance of {@link ResultIdentificationReadType.Decisions }
     * 
     * @return
     *     the new instance of {@link ResultIdentificationReadType.Decisions }
     */
    public ResultIdentificationReadType.Decisions createResultIdentificationReadTypeDecisions() {
        return new ResultIdentificationReadType.Decisions();
    }

    /**
     * Create an instance of {@link ResultIdentificationReadType.FacialImages }
     * 
     * @return
     *     the new instance of {@link ResultIdentificationReadType.FacialImages }
     */
    public ResultIdentificationReadType.FacialImages createResultIdentificationReadTypeFacialImages() {
        return new ResultIdentificationReadType.FacialImages();
    }

    /**
     * Create an instance of {@link ResultIdentificationReadType.GroupSynopses }
     * 
     * @return
     *     the new instance of {@link ResultIdentificationReadType.GroupSynopses }
     */
    public ResultIdentificationReadType.GroupSynopses createResultIdentificationReadTypeGroupSynopses() {
        return new ResultIdentificationReadType.GroupSynopses();
    }

    /**
     * Create an instance of {@link ResultApplicationSearchType.Applications }
     * 
     * @return
     *     the new instance of {@link ResultApplicationSearchType.Applications }
     */
    public ResultApplicationSearchType.Applications createResultApplicationSearchTypeApplications() {
        return new ResultApplicationSearchType.Applications();
    }

    /**
     * Create an instance of {@link ResultVerificationSearchType.VerificationOverviews }
     * 
     * @return
     *     the new instance of {@link ResultVerificationSearchType.VerificationOverviews }
     */
    public ResultVerificationSearchType.VerificationOverviews createResultVerificationSearchTypeVerificationOverviews() {
        return new ResultVerificationSearchType.VerificationOverviews();
    }

    /**
     * Create an instance of {@link ResultIdentificationSearchType.IdentificationOverviews }
     * 
     * @return
     *     the new instance of {@link ResultIdentificationSearchType.IdentificationOverviews }
     */
    public ResultIdentificationSearchType.IdentificationOverviews createResultIdentificationSearchTypeIdentificationOverviews() {
        return new ResultIdentificationSearchType.IdentificationOverviews();
    }

    /**
     * Create an instance of {@link LawEnforcementSearchType.DurationOfIntendedStayOrTransit }
     * 
     * @return
     *     the new instance of {@link LawEnforcementSearchType.DurationOfIntendedStayOrTransit }
     */
    public LawEnforcementSearchType.DurationOfIntendedStayOrTransit createLawEnforcementSearchTypeDurationOfIntendedStayOrTransit() {
        return new LawEnforcementSearchType.DurationOfIntendedStayOrTransit();
    }

    /**
     * Create an instance of {@link LawEnforcementSearchType.MemberStateOfFirstEntry }
     * 
     * @return
     *     the new instance of {@link LawEnforcementSearchType.MemberStateOfFirstEntry }
     */
    public LawEnforcementSearchType.MemberStateOfFirstEntry createLawEnforcementSearchTypeMemberStateOfFirstEntry() {
        return new LawEnforcementSearchType.MemberStateOfFirstEntry();
    }

    /**
     * Create an instance of {@link LawEnforcementSearchType.NationalityAtBirth }
     * 
     * @return
     *     the new instance of {@link LawEnforcementSearchType.NationalityAtBirth }
     */
    public LawEnforcementSearchType.NationalityAtBirth createLawEnforcementSearchTypeNationalityAtBirth() {
        return new LawEnforcementSearchType.NationalityAtBirth();
    }

    /**
     * Create an instance of {@link LawEnforcementSearchType.NationalityForApplication }
     * 
     * @return
     *     the new instance of {@link LawEnforcementSearchType.NationalityForApplication }
     */
    public LawEnforcementSearchType.NationalityForApplication createLawEnforcementSearchTypeNationalityForApplication() {
        return new LawEnforcementSearchType.NationalityForApplication();
    }

    /**
     * Create an instance of {@link LawEnforcementSearchType.VisaType }
     * 
     * @return
     *     the new instance of {@link LawEnforcementSearchType.VisaType }
     */
    public LawEnforcementSearchType.VisaType createLawEnforcementSearchTypeVisaType() {
        return new LawEnforcementSearchType.VisaType();
    }

    /**
     * Create an instance of {@link ResultAsylumResponsibilityOverviewResponseType.GroupSynopses }
     * 
     * @return
     *     the new instance of {@link ResultAsylumResponsibilityOverviewResponseType.GroupSynopses }
     */
    public ResultAsylumResponsibilityOverviewResponseType.GroupSynopses createResultAsylumResponsibilityOverviewResponseTypeGroupSynopses() {
        return new ResultAsylumResponsibilityOverviewResponseType.GroupSynopses();
    }

    /**
     * Create an instance of {@link ResultAsylumResponsibilityOverviewType.GroupSynopses }
     * 
     * @return
     *     the new instance of {@link ResultAsylumResponsibilityOverviewType.GroupSynopses }
     */
    public ResultAsylumResponsibilityOverviewType.GroupSynopses createResultAsylumResponsibilityOverviewTypeGroupSynopses() {
        return new ResultAsylumResponsibilityOverviewType.GroupSynopses();
    }

    /**
     * Create an instance of {@link ResultAsylumResponsibilityUsageOfDataReadType.ApplicationData }
     * 
     * @return
     *     the new instance of {@link ResultAsylumResponsibilityUsageOfDataReadType.ApplicationData }
     */
    public ResultAsylumResponsibilityUsageOfDataReadType.ApplicationData createResultAsylumResponsibilityUsageOfDataReadTypeApplicationData() {
        return new ResultAsylumResponsibilityUsageOfDataReadType.ApplicationData();
    }

    /**
     * Create an instance of {@link ResultAsylumResponsibilityUsageOfDataReadType.FacialImages }
     * 
     * @return
     *     the new instance of {@link ResultAsylumResponsibilityUsageOfDataReadType.FacialImages }
     */
    public ResultAsylumResponsibilityUsageOfDataReadType.FacialImages createResultAsylumResponsibilityUsageOfDataReadTypeFacialImages() {
        return new ResultAsylumResponsibilityUsageOfDataReadType.FacialImages();
    }

    /**
     * Create an instance of {@link ResultAsylumResponsibilityUsageOfDataReadType.GroupSynopses }
     * 
     * @return
     *     the new instance of {@link ResultAsylumResponsibilityUsageOfDataReadType.GroupSynopses }
     */
    public ResultAsylumResponsibilityUsageOfDataReadType.GroupSynopses createResultAsylumResponsibilityUsageOfDataReadTypeGroupSynopses() {
        return new ResultAsylumResponsibilityUsageOfDataReadType.GroupSynopses();
    }

    /**
     * Create an instance of {@link ResultAsylumResponsibilityUsageOfDataReadType.DecisionOverviews }
     * 
     * @return
     *     the new instance of {@link ResultAsylumResponsibilityUsageOfDataReadType.DecisionOverviews }
     */
    public ResultAsylumResponsibilityUsageOfDataReadType.DecisionOverviews createResultAsylumResponsibilityUsageOfDataReadTypeDecisionOverviews() {
        return new ResultAsylumResponsibilityUsageOfDataReadType.DecisionOverviews();
    }

    /**
     * Create an instance of {@link ResultAsylumResponsibilityReadType.ApplicationData }
     * 
     * @return
     *     the new instance of {@link ResultAsylumResponsibilityReadType.ApplicationData }
     */
    public ResultAsylumResponsibilityReadType.ApplicationData createResultAsylumResponsibilityReadTypeApplicationData() {
        return new ResultAsylumResponsibilityReadType.ApplicationData();
    }

    /**
     * Create an instance of {@link ResultAsylumResponsibilityReadType.FacialImages }
     * 
     * @return
     *     the new instance of {@link ResultAsylumResponsibilityReadType.FacialImages }
     */
    public ResultAsylumResponsibilityReadType.FacialImages createResultAsylumResponsibilityReadTypeFacialImages() {
        return new ResultAsylumResponsibilityReadType.FacialImages();
    }

    /**
     * Create an instance of {@link ResultAsylumResponsibilityReadType.GroupSynopses }
     * 
     * @return
     *     the new instance of {@link ResultAsylumResponsibilityReadType.GroupSynopses }
     */
    public ResultAsylumResponsibilityReadType.GroupSynopses createResultAsylumResponsibilityReadTypeGroupSynopses() {
        return new ResultAsylumResponsibilityReadType.GroupSynopses();
    }

    /**
     * Create an instance of {@link ResultAsylumResponsibilityReadType.DecisionOverviews }
     * 
     * @return
     *     the new instance of {@link ResultAsylumResponsibilityReadType.DecisionOverviews }
     */
    public ResultAsylumResponsibilityReadType.DecisionOverviews createResultAsylumResponsibilityReadTypeDecisionOverviews() {
        return new ResultAsylumResponsibilityReadType.DecisionOverviews();
    }

    /**
     * Create an instance of {@link ResultAsylumResponsibilityUsageOfDataSearchType.AsylumResponsibilityOverviews }
     * 
     * @return
     *     the new instance of {@link ResultAsylumResponsibilityUsageOfDataSearchType.AsylumResponsibilityOverviews }
     */
    public ResultAsylumResponsibilityUsageOfDataSearchType.AsylumResponsibilityOverviews createResultAsylumResponsibilityUsageOfDataSearchTypeAsylumResponsibilityOverviews() {
        return new ResultAsylumResponsibilityUsageOfDataSearchType.AsylumResponsibilityOverviews();
    }

    /**
     * Create an instance of {@link ResultAsylumResponsibilitySearchType.AsylumResponsibilityOverviews }
     * 
     * @return
     *     the new instance of {@link ResultAsylumResponsibilitySearchType.AsylumResponsibilityOverviews }
     */
    public ResultAsylumResponsibilitySearchType.AsylumResponsibilityOverviews createResultAsylumResponsibilitySearchTypeAsylumResponsibilityOverviews() {
        return new ResultAsylumResponsibilitySearchType.AsylumResponsibilityOverviews();
    }

    /**
     * Create an instance of {@link ResultAsylumExaminationOverviewType.GroupSynopses }
     * 
     * @return
     *     the new instance of {@link ResultAsylumExaminationOverviewType.GroupSynopses }
     */
    public ResultAsylumExaminationOverviewType.GroupSynopses createResultAsylumExaminationOverviewTypeGroupSynopses() {
        return new ResultAsylumExaminationOverviewType.GroupSynopses();
    }

    /**
     * Create an instance of {@link ResultAsylumExaminationReadType.ApplicationData }
     * 
     * @return
     *     the new instance of {@link ResultAsylumExaminationReadType.ApplicationData }
     */
    public ResultAsylumExaminationReadType.ApplicationData createResultAsylumExaminationReadTypeApplicationData() {
        return new ResultAsylumExaminationReadType.ApplicationData();
    }

    /**
     * Create an instance of {@link ResultAsylumExaminationReadType.Decisions }
     * 
     * @return
     *     the new instance of {@link ResultAsylumExaminationReadType.Decisions }
     */
    public ResultAsylumExaminationReadType.Decisions createResultAsylumExaminationReadTypeDecisions() {
        return new ResultAsylumExaminationReadType.Decisions();
    }

    /**
     * Create an instance of {@link ResultAsylumExaminationReadType.FacialImages }
     * 
     * @return
     *     the new instance of {@link ResultAsylumExaminationReadType.FacialImages }
     */
    public ResultAsylumExaminationReadType.FacialImages createResultAsylumExaminationReadTypeFacialImages() {
        return new ResultAsylumExaminationReadType.FacialImages();
    }

    /**
     * Create an instance of {@link ResultAsylumExaminationReadType.GroupSynopses }
     * 
     * @return
     *     the new instance of {@link ResultAsylumExaminationReadType.GroupSynopses }
     */
    public ResultAsylumExaminationReadType.GroupSynopses createResultAsylumExaminationReadTypeGroupSynopses() {
        return new ResultAsylumExaminationReadType.GroupSynopses();
    }

    /**
     * Create an instance of {@link ResultAsylumExaminationSearchType.AsylumExaminationOverviews }
     * 
     * @return
     *     the new instance of {@link ResultAsylumExaminationSearchType.AsylumExaminationOverviews }
     */
    public ResultAsylumExaminationSearchType.AsylumExaminationOverviews createResultAsylumExaminationSearchTypeAsylumExaminationOverviews() {
        return new ResultAsylumExaminationSearchType.AsylumExaminationOverviews();
    }

    /**
     * Create an instance of {@link ResultApplicationExaminationOverviewType.GroupSynopses }
     * 
     * @return
     *     the new instance of {@link ResultApplicationExaminationOverviewType.GroupSynopses }
     */
    public ResultApplicationExaminationOverviewType.GroupSynopses createResultApplicationExaminationOverviewTypeGroupSynopses() {
        return new ResultApplicationExaminationOverviewType.GroupSynopses();
    }

    /**
     * Create an instance of {@link ResultApplicationExaminationSearchType.ApplicationExaminationOverviews }
     * 
     * @return
     *     the new instance of {@link ResultApplicationExaminationSearchType.ApplicationExaminationOverviews }
     */
    public ResultApplicationExaminationSearchType.ApplicationExaminationOverviews createResultApplicationExaminationSearchTypeApplicationExaminationOverviews() {
        return new ResultApplicationExaminationSearchType.ApplicationExaminationOverviews();
    }

    /**
     * Create an instance of {@link ResultApplicationExaminationReadType.ApplicationData }
     * 
     * @return
     *     the new instance of {@link ResultApplicationExaminationReadType.ApplicationData }
     */
    public ResultApplicationExaminationReadType.ApplicationData createResultApplicationExaminationReadTypeApplicationData() {
        return new ResultApplicationExaminationReadType.ApplicationData();
    }

    /**
     * Create an instance of {@link ResultApplicationExaminationReadType.Decisions }
     * 
     * @return
     *     the new instance of {@link ResultApplicationExaminationReadType.Decisions }
     */
    public ResultApplicationExaminationReadType.Decisions createResultApplicationExaminationReadTypeDecisions() {
        return new ResultApplicationExaminationReadType.Decisions();
    }

    /**
     * Create an instance of {@link ResultApplicationExaminationReadType.GroupSynopses }
     * 
     * @return
     *     the new instance of {@link ResultApplicationExaminationReadType.GroupSynopses }
     */
    public ResultApplicationExaminationReadType.GroupSynopses createResultApplicationExaminationReadTypeGroupSynopses() {
        return new ResultApplicationExaminationReadType.GroupSynopses();
    }

    /**
     * Create an instance of {@link BiometricsDescriptionGetType.FacialImages }
     * 
     * @return
     *     the new instance of {@link BiometricsDescriptionGetType.FacialImages }
     */
    public BiometricsDescriptionGetType.FacialImages createBiometricsDescriptionGetTypeFacialImages() {
        return new BiometricsDescriptionGetType.FacialImages();
    }

    /**
     * Create an instance of {@link BiometricsDescriptionGetType.FingerprintSets }
     * 
     * @return
     *     the new instance of {@link BiometricsDescriptionGetType.FingerprintSets }
     */
    public BiometricsDescriptionGetType.FingerprintSets createBiometricsDescriptionGetTypeFingerprintSets() {
        return new BiometricsDescriptionGetType.FingerprintSets();
    }

    /**
     * Create an instance of {@link ResultApplicationWithFullDecisionType.Decisions.Decision }
     * 
     * @return
     *     the new instance of {@link ResultApplicationWithFullDecisionType.Decisions.Decision }
     */
    public ResultApplicationWithFullDecisionType.Decisions.Decision createResultApplicationWithFullDecisionTypeDecisionsDecision() {
        return new ResultApplicationWithFullDecisionType.Decisions.Decision();
    }

    /**
     * Create an instance of {@link LawEnforcementDecisionHistChoiceType.ApplicationDecision }
     * 
     * @return
     *     the new instance of {@link LawEnforcementDecisionHistChoiceType.ApplicationDecision }
     */
    public LawEnforcementDecisionHistChoiceType.ApplicationDecision createLawEnforcementDecisionHistChoiceTypeApplicationDecision() {
        return new LawEnforcementDecisionHistChoiceType.ApplicationDecision();
    }

    /**
     * Create an instance of {@link LawEnforcementDecisionHistChoiceType.VisaDecision }
     * 
     * @return
     *     the new instance of {@link LawEnforcementDecisionHistChoiceType.VisaDecision }
     */
    public LawEnforcementDecisionHistChoiceType.VisaDecision createLawEnforcementDecisionHistChoiceTypeVisaDecision() {
        return new LawEnforcementDecisionHistChoiceType.VisaDecision();
    }

    /**
     * Create an instance of {@link LawEnforcementDecisionHistChoiceType.VisaCreationDecision }
     * 
     * @return
     *     the new instance of {@link LawEnforcementDecisionHistChoiceType.VisaCreationDecision }
     */
    public LawEnforcementDecisionHistChoiceType.VisaCreationDecision createLawEnforcementDecisionHistChoiceTypeVisaCreationDecision() {
        return new LawEnforcementDecisionHistChoiceType.VisaCreationDecision();
    }

    /**
     * Create an instance of {@link IdentificationDecisionHistChoiceType.ApplicationDecision }
     * 
     * @return
     *     the new instance of {@link IdentificationDecisionHistChoiceType.ApplicationDecision }
     */
    public IdentificationDecisionHistChoiceType.ApplicationDecision createIdentificationDecisionHistChoiceTypeApplicationDecision() {
        return new IdentificationDecisionHistChoiceType.ApplicationDecision();
    }

    /**
     * Create an instance of {@link MainPurposesOfJourneySearchType.MainPurposeOfJourney }
     * 
     * @return
     *     the new instance of {@link MainPurposesOfJourneySearchType.MainPurposeOfJourney }
     */
    public MainPurposesOfJourneySearchType.MainPurposeOfJourney createMainPurposesOfJourneySearchTypeMainPurposeOfJourney() {
        return new MainPurposesOfJourneySearchType.MainPurposeOfJourney();
    }

    /**
     * Create an instance of {@link FoundDossierCountBorderControlType.Result.Dossiers }
     * 
     * @return
     *     the new instance of {@link FoundDossierCountBorderControlType.Result.Dossiers }
     */
    public FoundDossierCountBorderControlType.Result.Dossiers createFoundDossierCountBorderControlTypeResultDossiers() {
        return new FoundDossierCountBorderControlType.Result.Dossiers();
    }

    /**
     * Create an instance of {@link BiometricReferencesGetType.FacialImageReferences }
     * 
     * @return
     *     the new instance of {@link BiometricReferencesGetType.FacialImageReferences }
     */
    public BiometricReferencesGetType.FacialImageReferences createBiometricReferencesGetTypeFacialImageReferences() {
        return new BiometricReferencesGetType.FacialImageReferences();
    }

    /**
     * Create an instance of {@link BiometricReferencesGetType.FingerprintSetReferences }
     * 
     * @return
     *     the new instance of {@link BiometricReferencesGetType.FingerprintSetReferences }
     */
    public BiometricReferencesGetType.FingerprintSetReferences createBiometricReferencesGetTypeFingerprintSetReferences() {
        return new BiometricReferencesGetType.FingerprintSetReferences();
    }

    /**
     * Create an instance of {@link BiometricReferencesType.FacialImageAttachmentIDs }
     * 
     * @return
     *     the new instance of {@link BiometricReferencesType.FacialImageAttachmentIDs }
     */
    public BiometricReferencesType.FacialImageAttachmentIDs createBiometricReferencesTypeFacialImageAttachmentIDs() {
        return new BiometricReferencesType.FacialImageAttachmentIDs();
    }

    /**
     * Create an instance of {@link BiometricReferencesType.FingerprintSetAttachmentIDs }
     * 
     * @return
     *     the new instance of {@link BiometricReferencesType.FingerprintSetAttachmentIDs }
     */
    public BiometricReferencesType.FingerprintSetAttachmentIDs createBiometricReferencesTypeFingerprintSetAttachmentIDs() {
        return new BiometricReferencesType.FingerprintSetAttachmentIDs();
    }

    /**
     * Create an instance of {@link FacialImageGetType.MasterData }
     * 
     * @return
     *     the new instance of {@link FacialImageGetType.MasterData }
     */
    public FacialImageGetType.MasterData createFacialImageGetTypeMasterData() {
        return new FacialImageGetType.MasterData();
    }

    /**
     * Create an instance of {@link AttachmentMasterDataBaseType.GenericValuePairs }
     * 
     * @return
     *     the new instance of {@link AttachmentMasterDataBaseType.GenericValuePairs }
     */
    public AttachmentMasterDataBaseType.GenericValuePairs createAttachmentMasterDataBaseTypeGenericValuePairs() {
        return new AttachmentMasterDataBaseType.GenericValuePairs();
    }

    /**
     * Create an instance of {@link FacialImageListType.MasterData }
     * 
     * @return
     *     the new instance of {@link FacialImageListType.MasterData }
     */
    public FacialImageListType.MasterData createFacialImageListTypeMasterData() {
        return new FacialImageListType.MasterData();
    }

    /**
     * Create an instance of {@link FacialImageReturnType.MasterData }
     * 
     * @return
     *     the new instance of {@link FacialImageReturnType.MasterData }
     */
    public FacialImageReturnType.MasterData createFacialImageReturnTypeMasterData() {
        return new FacialImageReturnType.MasterData();
    }

    /**
     * Create an instance of {@link FacialImageNewType.MasterData }
     * 
     * @return
     *     the new instance of {@link FacialImageNewType.MasterData }
     */
    public FacialImageNewType.MasterData createFacialImageNewTypeMasterData() {
        return new FacialImageNewType.MasterData();
    }

    /**
     * Create an instance of {@link FingerprintSetGetType.MasterData }
     * 
     * @return
     *     the new instance of {@link FingerprintSetGetType.MasterData }
     */
    public FingerprintSetGetType.MasterData createFingerprintSetGetTypeMasterData() {
        return new FingerprintSetGetType.MasterData();
    }

    /**
     * Create an instance of {@link FingerprintSetReturnType.MasterData }
     * 
     * @return
     *     the new instance of {@link FingerprintSetReturnType.MasterData }
     */
    public FingerprintSetReturnType.MasterData createFingerprintSetReturnTypeMasterData() {
        return new FingerprintSetReturnType.MasterData();
    }

    /**
     * Create an instance of {@link FingerprintSetNewType.MasterData }
     * 
     * @return
     *     the new instance of {@link FingerprintSetNewType.MasterData }
     */
    public FingerprintSetNewType.MasterData createFingerprintSetNewTypeMasterData() {
        return new FingerprintSetNewType.MasterData();
    }

    /**
     * Create an instance of {@link ApplicantGetType.FormerSurnames }
     * 
     * @return
     *     the new instance of {@link ApplicantGetType.FormerSurnames }
     */
    public ApplicantGetType.FormerSurnames createApplicantGetTypeFormerSurnames() {
        return new ApplicantGetType.FormerSurnames();
    }

    /**
     * Create an instance of {@link ApplicantNewType.FormerSurnames }
     * 
     * @return
     *     the new instance of {@link ApplicantNewType.FormerSurnames }
     */
    public ApplicantNewType.FormerSurnames createApplicantNewTypeFormerSurnames() {
        return new ApplicantNewType.FormerSurnames();
    }

    /**
     * Create an instance of {@link ApplicationGetType.Host }
     * 
     * @return
     *     the new instance of {@link ApplicationGetType.Host }
     */
    public ApplicationGetType.Host createApplicationGetTypeHost() {
        return new ApplicationGetType.Host();
    }

    /**
     * Create an instance of {@link ApplicationNewType.Host }
     * 
     * @return
     *     the new instance of {@link ApplicationNewType.Host }
     */
    public ApplicationNewType.Host createApplicationNewTypeHost() {
        return new ApplicationNewType.Host();
    }

    /**
     * Create an instance of {@link CloseApplicationGetType.ClosingGrounds }
     * 
     * @return
     *     the new instance of {@link CloseApplicationGetType.ClosingGrounds }
     */
    public CloseApplicationGetType.ClosingGrounds createCloseApplicationGetTypeClosingGrounds() {
        return new CloseApplicationGetType.ClosingGrounds();
    }

    /**
     * Create an instance of {@link CloseApplicationNewType.ClosingGrounds }
     * 
     * @return
     *     the new instance of {@link CloseApplicationNewType.ClosingGrounds }
     */
    public CloseApplicationNewType.ClosingGrounds createCloseApplicationNewTypeClosingGrounds() {
        return new CloseApplicationNewType.ClosingGrounds();
    }

    /**
     * Create an instance of {@link RefuseVisaGetType.RefusalGrounds }
     * 
     * @return
     *     the new instance of {@link RefuseVisaGetType.RefusalGrounds }
     */
    public RefuseVisaGetType.RefusalGrounds createRefuseVisaGetTypeRefusalGrounds() {
        return new RefuseVisaGetType.RefusalGrounds();
    }

    /**
     * Create an instance of {@link RefuseVisaNewType.RefusalGrounds }
     * 
     * @return
     *     the new instance of {@link RefuseVisaNewType.RefusalGrounds }
     */
    public RefuseVisaNewType.RefusalGrounds createRefuseVisaNewTypeRefusalGrounds() {
        return new RefuseVisaNewType.RefusalGrounds();
    }

    /**
     * Create an instance of {@link TransTextType.TransliteratedValues }
     * 
     * @return
     *     the new instance of {@link TransTextType.TransliteratedValues }
     */
    public TransTextType.TransliteratedValues createTransTextTypeTransliteratedValues() {
        return new TransTextType.TransliteratedValues();
    }

    /**
     * Create an instance of {@link IssuingAuthorityOfTravelDocumentSearchType.CountryOfIssuingAuthority }
     * 
     * @return
     *     the new instance of {@link IssuingAuthorityOfTravelDocumentSearchType.CountryOfIssuingAuthority }
     */
    public IssuingAuthorityOfTravelDocumentSearchType.CountryOfIssuingAuthority createIssuingAuthorityOfTravelDocumentSearchTypeCountryOfIssuingAuthority() {
        return new IssuingAuthorityOfTravelDocumentSearchType.CountryOfIssuingAuthority();
    }

    /**
     * Create an instance of {@link SearchType.SearchCriteria }
     * 
     * @return
     *     the new instance of {@link SearchType.SearchCriteria }
     */
    public SearchType.SearchCriteria createSearchTypeSearchCriteria() {
        return new SearchType.SearchCriteria();
    }

}
