//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.vis.xsd.v3.types.application;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * Description: Information describing an address.
 * 
 * <p>Classe Java per AddressNewType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="AddressNewType">
 *   <complexContent>
 *     <extension base="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}AddressBaseType">
 *       <sequence>
 *         <element name="Street" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}TransType" minOccurs="0"/>
 *         <element name="PostCode" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}TransType" minOccurs="0"/>
 *         <element name="City" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}TransType" minOccurs="0"/>
 *       </sequence>
 *     </extension>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "AddressNewType", propOrder = {
    "street",
    "postCode",
    "city"
})
public class AddressNewType
    extends AddressBaseType
{

    /**
     * Description: Street.
     * 
     */
    @XmlElement(name = "Street")
    protected TransType street;
    /**
     * Description: ZIP Code.
     * 
     */
    @XmlElement(name = "PostCode")
    protected TransType postCode;
    /**
     * Description: City.
     * 
     */
    @XmlElement(name = "City")
    protected TransType city;

    /**
     * Description: Street.
     * 
     * @return
     *     possible object is
     *     {@link TransType }
     *     
     */
    public TransType getStreet() {
        return street;
    }

    /**
     * Imposta il valore della proprietà street.
     * 
     * @param value
     *     allowed object is
     *     {@link TransType }
     *     
     * @see #getStreet()
     */
    public void setStreet(TransType value) {
        this.street = value;
    }

    /**
     * Description: ZIP Code.
     * 
     * @return
     *     possible object is
     *     {@link TransType }
     *     
     */
    public TransType getPostCode() {
        return postCode;
    }

    /**
     * Imposta il valore della proprietà postCode.
     * 
     * @param value
     *     allowed object is
     *     {@link TransType }
     *     
     * @see #getPostCode()
     */
    public void setPostCode(TransType value) {
        this.postCode = value;
    }

    /**
     * Description: City.
     * 
     * @return
     *     possible object is
     *     {@link TransType }
     *     
     */
    public TransType getCity() {
        return city;
    }

    /**
     * Imposta il valore della proprietà city.
     * 
     * @param value
     *     allowed object is
     *     {@link TransType }
     *     
     * @see #getCity()
     */
    public void setCity(TransType value) {
        this.city = value;
    }

}
