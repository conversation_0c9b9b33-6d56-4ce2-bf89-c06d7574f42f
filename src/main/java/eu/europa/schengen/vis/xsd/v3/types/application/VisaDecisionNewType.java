//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.vis.xsd.v3.types.application;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * Description: Information needed to correct a VisaDecision.
 * 
 * <p>Classe Java per VisaDecisionNewType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="VisaDecisionNewType">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <choice>
 *         <element name="RevokeVisa" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}RevokeVisaNewType"/>
 *         <element name="AnnulVisa" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}AnnulVisaNewType"/>
 *       </choice>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "VisaDecisionNewType", propOrder = {
    "revokeVisa",
    "annulVisa"
})
public class VisaDecisionNewType {

    /**
     * Description: All information needed for the decision "Revoke Visa".
     * 
     */
    @XmlElement(name = "RevokeVisa")
    protected RevokeVisaNewType revokeVisa;
    /**
     * Description: All information needed for the decision "Annul Visa".
     * 
     */
    @XmlElement(name = "AnnulVisa")
    protected AnnulVisaNewType annulVisa;

    /**
     * Description: All information needed for the decision "Revoke Visa".
     * 
     * @return
     *     possible object is
     *     {@link RevokeVisaNewType }
     *     
     */
    public RevokeVisaNewType getRevokeVisa() {
        return revokeVisa;
    }

    /**
     * Imposta il valore della proprietà revokeVisa.
     * 
     * @param value
     *     allowed object is
     *     {@link RevokeVisaNewType }
     *     
     * @see #getRevokeVisa()
     */
    public void setRevokeVisa(RevokeVisaNewType value) {
        this.revokeVisa = value;
    }

    /**
     * Description: All information needed for the decision "Annul Visa".
     * 
     * @return
     *     possible object is
     *     {@link AnnulVisaNewType }
     *     
     */
    public AnnulVisaNewType getAnnulVisa() {
        return annulVisa;
    }

    /**
     * Imposta il valore della proprietà annulVisa.
     * 
     * @param value
     *     allowed object is
     *     {@link AnnulVisaNewType }
     *     
     * @see #getAnnulVisa()
     */
    public void setAnnulVisa(AnnulVisaNewType value) {
        this.annulVisa = value;
    }

}
