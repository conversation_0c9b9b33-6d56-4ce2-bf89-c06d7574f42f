//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.ees.xsd.v1;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per RetrieveTravellerFileResponseMessageType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="RetrieveTravellerFileResponseMessageType">
 *   <complexContent>
 *     <extension base="{http://www.europa.eu/schengen/ees/xsd/v1}BaseMessageResponseType">
 *       <sequence>
 *         <element name="Response" minOccurs="0">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="TravellerFile" minOccurs="0">
 *                     <complexType>
 *                       <complexContent>
 *                         <extension base="{http://www.europa.eu/schengen/ees/xsd/v1}TravellerFileWithHistoryType">
 *                           <sequence>
 *                             <element name="Calculator" type="{http://www.europa.eu/schengen/ees/xsd/v1}ExtendedCalculatorResultType" minOccurs="0"/>
 *                           </sequence>
 *                         </extension>
 *                       </complexContent>
 *                     </complexType>
 *                   </element>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *       </sequence>
 *     </extension>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "RetrieveTravellerFileResponseMessageType", propOrder = {
    "response"
})
public class RetrieveTravellerFileResponseMessageType
    extends BaseMessageResponseType
{

    @XmlElement(name = "Response")
    protected RetrieveTravellerFileResponseMessageType.Response response;

    /**
     * Recupera il valore della proprietà response.
     * 
     * @return
     *     possible object is
     *     {@link RetrieveTravellerFileResponseMessageType.Response }
     *     
     */
    public RetrieveTravellerFileResponseMessageType.Response getResponse() {
        return response;
    }

    /**
     * Imposta il valore della proprietà response.
     * 
     * @param value
     *     allowed object is
     *     {@link RetrieveTravellerFileResponseMessageType.Response }
     *     
     */
    public void setResponse(RetrieveTravellerFileResponseMessageType.Response value) {
        this.response = value;
    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="TravellerFile" minOccurs="0">
     *           <complexType>
     *             <complexContent>
     *               <extension base="{http://www.europa.eu/schengen/ees/xsd/v1}TravellerFileWithHistoryType">
     *                 <sequence>
     *                   <element name="Calculator" type="{http://www.europa.eu/schengen/ees/xsd/v1}ExtendedCalculatorResultType" minOccurs="0"/>
     *                 </sequence>
     *               </extension>
     *             </complexContent>
     *           </complexType>
     *         </element>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "travellerFile"
    })
    public static class Response {

        @XmlElement(name = "TravellerFile")
        protected RetrieveTravellerFileResponseMessageType.Response.TravellerFile travellerFile;

        /**
         * Recupera il valore della proprietà travellerFile.
         * 
         * @return
         *     possible object is
         *     {@link RetrieveTravellerFileResponseMessageType.Response.TravellerFile }
         *     
         */
        public RetrieveTravellerFileResponseMessageType.Response.TravellerFile getTravellerFile() {
            return travellerFile;
        }

        /**
         * Imposta il valore della proprietà travellerFile.
         * 
         * @param value
         *     allowed object is
         *     {@link RetrieveTravellerFileResponseMessageType.Response.TravellerFile }
         *     
         */
        public void setTravellerFile(RetrieveTravellerFileResponseMessageType.Response.TravellerFile value) {
            this.travellerFile = value;
        }


        /**
         * <p>Classe Java per anonymous complex type.</p>
         * 
         * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
         * 
         * <pre>{@code
         * <complexType>
         *   <complexContent>
         *     <extension base="{http://www.europa.eu/schengen/ees/xsd/v1}TravellerFileWithHistoryType">
         *       <sequence>
         *         <element name="Calculator" type="{http://www.europa.eu/schengen/ees/xsd/v1}ExtendedCalculatorResultType" minOccurs="0"/>
         *       </sequence>
         *     </extension>
         *   </complexContent>
         * </complexType>
         * }</pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "calculator"
        })
        public static class TravellerFile
            extends TravellerFileWithHistoryType
        {

            @XmlElement(name = "Calculator")
            protected ExtendedCalculatorResultType calculator;

            /**
             * Recupera il valore della proprietà calculator.
             * 
             * @return
             *     possible object is
             *     {@link ExtendedCalculatorResultType }
             *     
             */
            public ExtendedCalculatorResultType getCalculator() {
                return calculator;
            }

            /**
             * Imposta il valore della proprietà calculator.
             * 
             * @param value
             *     allowed object is
             *     {@link ExtendedCalculatorResultType }
             *     
             */
            public void setCalculator(ExtendedCalculatorResultType value) {
                this.calculator = value;
            }

        }

    }

}
