//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.ees.xsd.v1;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per PagingResponseType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="PagingResponseType">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="TotalNoOfHits" type="{http://www.w3.org/2001/XMLSchema}unsignedInt"/>
 *         <element name="PageNumber">
 *           <simpleType>
 *             <restriction base="{http://www.w3.org/2001/XMLSchema}unsignedInt">
 *               <minInclusive value="1"/>
 *               <maxInclusive value="999"/>
 *             </restriction>
 *           </simpleType>
 *         </element>
 *         <element name="HitsOnPage" type="{http://www.w3.org/2001/XMLSchema}unsignedInt"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "PagingResponseType", propOrder = {
    "totalNoOfHits",
    "pageNumber",
    "hitsOnPage"
})
public class PagingResponseType {

    @XmlElement(name = "TotalNoOfHits")
    @XmlSchemaType(name = "unsignedInt")
    protected long totalNoOfHits;
    @XmlElement(name = "PageNumber")
    protected long pageNumber;
    @XmlElement(name = "HitsOnPage")
    @XmlSchemaType(name = "unsignedInt")
    protected long hitsOnPage;

    /**
     * Recupera il valore della proprietà totalNoOfHits.
     * 
     */
    public long getTotalNoOfHits() {
        return totalNoOfHits;
    }

    /**
     * Imposta il valore della proprietà totalNoOfHits.
     * 
     */
    public void setTotalNoOfHits(long value) {
        this.totalNoOfHits = value;
    }

    /**
     * Recupera il valore della proprietà pageNumber.
     * 
     */
    public long getPageNumber() {
        return pageNumber;
    }

    /**
     * Imposta il valore della proprietà pageNumber.
     * 
     */
    public void setPageNumber(long value) {
        this.pageNumber = value;
    }

    /**
     * Recupera il valore della proprietà hitsOnPage.
     * 
     */
    public long getHitsOnPage() {
        return hitsOnPage;
    }

    /**
     * Imposta il valore della proprietà hitsOnPage.
     * 
     */
    public void setHitsOnPage(long value) {
        this.hitsOnPage = value;
    }

}
