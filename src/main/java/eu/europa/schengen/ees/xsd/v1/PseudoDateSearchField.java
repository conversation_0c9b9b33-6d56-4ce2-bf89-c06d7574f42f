//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.ees.xsd.v1;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlAttribute;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per PseudoDateSearchField complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="PseudoDateSearchField">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <choice>
 *         <element name="Date" type="{http://www.europa.eu/schengen/shared/xsd/v1}PseudodateType"/>
 *         <sequence>
 *           <element name="From" type="{http://www.europa.eu/schengen/shared/xsd/v1}PseudodateType"/>
 *           <element name="To" type="{http://www.europa.eu/schengen/shared/xsd/v1}PseudodateType" minOccurs="0"/>
 *         </sequence>
 *       </choice>
 *       <attribute name="IgnoreInexact" type="{http://www.w3.org/2001/XMLSchema}boolean" default="false" />
 *       <attribute name="weight" type="{http://www.europa.eu/schengen/ees/xsd/v1}WeightType" />
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "PseudoDateSearchField", propOrder = {
    "date",
    "from",
    "to"
})
public class PseudoDateSearchField {

    @XmlElement(name = "Date")
    protected String date;
    @XmlElement(name = "From")
    protected String from;
    /**
     * If value is not provided then it is assumed to be today.
     * 
     */
    @XmlElement(name = "To")
    protected String to;
    /**
     * If this boolean is set to "true", records with inexact dates will be ignored and not be considered in the result set.
     * 					Default value: "false"
     * 
     */
    @XmlAttribute(name = "IgnoreInexact")
    protected Boolean ignoreInexact;
    @XmlAttribute(name = "weight")
    protected Short weight;

    /**
     * Recupera il valore della proprietà date.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDate() {
        return date;
    }

    /**
     * Imposta il valore della proprietà date.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDate(String value) {
        this.date = value;
    }

    /**
     * Recupera il valore della proprietà from.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFrom() {
        return from;
    }

    /**
     * Imposta il valore della proprietà from.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFrom(String value) {
        this.from = value;
    }

    /**
     * If value is not provided then it is assumed to be today.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTo() {
        return to;
    }

    /**
     * Imposta il valore della proprietà to.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     * @see #getTo()
     */
    public void setTo(String value) {
        this.to = value;
    }

    /**
     * If this boolean is set to "true", records with inexact dates will be ignored and not be considered in the result set.
     * 					Default value: "false"
     * 
     * @return
     *     possible object is
     *     {@link Boolean }
     *     
     */
    public boolean isIgnoreInexact() {
        if (ignoreInexact == null) {
            return false;
        } else {
            return ignoreInexact;
        }
    }

    /**
     * Imposta il valore della proprietà ignoreInexact.
     * 
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *     
     * @see #isIgnoreInexact()
     */
    public void setIgnoreInexact(Boolean value) {
        this.ignoreInexact = value;
    }

    /**
     * Recupera il valore della proprietà weight.
     * 
     * @return
     *     possible object is
     *     {@link Short }
     *     
     */
    public Short getWeight() {
        return weight;
    }

    /**
     * Imposta il valore della proprietà weight.
     * 
     * @param value
     *     allowed object is
     *     {@link Short }
     *     
     */
    public void setWeight(Short value) {
        this.weight = value;
    }

}
