//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.ees.xsd.v1;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlSeeAlso;
import jakarta.xml.bind.annotation.XmlType;


/**
 * TravellerFile grouping all TCN data - type for the search response.
 * 
 * <p>Classe Java per TravellerFileSearchResponse2Type complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="TravellerFileSearchResponse2Type">
 *   <complexContent>
 *     <extension base="{http://www.europa.eu/schengen/ees/xsd/v1}TravellerFileWithHistoryType">
 *       <sequence>
 *         <group ref="{http://www.europa.eu/schengen/ees/xsd/v1}RankScoreGroupWithMatchingInterval"/>
 *       </sequence>
 *     </extension>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "TravellerFileSearchResponse2Type", propOrder = {
    "rank",
    "score",
    "matchingInterval"
})
@XmlSeeAlso({
    eu.europa.schengen.ees_ns.xsd.v1.ResponseDataType.EESIdentification.TravellerFile.class,
    eu.europa.schengen.ees_ns.xsd.v1.IdentificationResultResponseDataType.EESIdentification.TravellerFile.class
})
public class TravellerFileSearchResponse2Type
    extends TravellerFileWithHistoryType
{

    @XmlElement(name = "Rank")
    @XmlSchemaType(name = "unsignedInt")
    protected long rank;
    /**
     * Normalised matched score between 0 and 100 for alphanumeric searches. 100 is the highest score for alphanumeric searches.
     * 						Matching score between 0 and 100000 for biometric searches. 100000 is the highest score for biometric searches.
     * 
     */
    @XmlElement(name = "Score")
    @XmlSchemaType(name = "unsignedInt")
    protected long score;
    @XmlElement(name = "MatchingInterval", required = true)
    protected String matchingInterval;

    /**
     * Recupera il valore della proprietà rank.
     * 
     */
    public long getRank() {
        return rank;
    }

    /**
     * Imposta il valore della proprietà rank.
     * 
     */
    public void setRank(long value) {
        this.rank = value;
    }

    /**
     * Normalised matched score between 0 and 100 for alphanumeric searches. 100 is the highest score for alphanumeric searches.
     * 						Matching score between 0 and 100000 for biometric searches. 100000 is the highest score for biometric searches.
     * 
     */
    public long getScore() {
        return score;
    }

    /**
     * Imposta il valore della proprietà score.
     * 
     */
    public void setScore(long value) {
        this.score = value;
    }

    /**
     * Recupera il valore della proprietà matchingInterval.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMatchingInterval() {
        return matchingInterval;
    }

    /**
     * Imposta il valore della proprietà matchingInterval.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMatchingInterval(String value) {
        this.matchingInterval = value;
    }

}
