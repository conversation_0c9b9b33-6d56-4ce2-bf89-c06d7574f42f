//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.ees.xsd.v1;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlType;


/**
 * Entry data - type for the update request.
 * 
 * <p>Classe Java per EntryRecordType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="EntryRecordType">
 *   <complexContent>
 *     <extension base="{http://www.europa.eu/schengen/ees/xsd/v1}EntryRecordBaseType">
 *       <sequence>
 *         <element name="EntryRecordID" type="{http://www.europa.eu/schengen/ees/xsd/v1}IdentifierType"/>
 *       </sequence>
 *     </extension>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "EntryRecordType", propOrder = {
    "entryRecordID"
})
public class EntryRecordType
    extends EntryRecordBaseType
{

    /**
     * Record identifier:
     * 								- unique for the TravellerFile;
     * 								- in case Record is deleted the RecordID must not be reused.
     * 
     */
    @XmlElement(name = "EntryRecordID", required = true)
    @XmlSchemaType(name = "anyURI")
    protected String entryRecordID;

    /**
     * Record identifier:
     * 								- unique for the TravellerFile;
     * 								- in case Record is deleted the RecordID must not be reused.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEntryRecordID() {
        return entryRecordID;
    }

    /**
     * Imposta il valore della proprietà entryRecordID.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     * @see #getEntryRecordID()
     */
    public void setEntryRecordID(String value) {
        this.entryRecordID = value;
    }

}
