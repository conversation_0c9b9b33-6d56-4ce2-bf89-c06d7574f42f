//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.ees.xsd.v1;

import java.util.ArrayList;
import java.util.List;
import eu.europa.schengen.ees_ns.xsd.v1.MessageResponseType;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSeeAlso;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per BaseMessageResponseType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="BaseMessageResponseType">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="ReturnCodes">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="ErrorCodes" minOccurs="0">
 *                     <complexType>
 *                       <complexContent>
 *                         <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           <sequence>
 *                             <element name="ErrorCode" type="{http://www.europa.eu/schengen/shared/xsd/v1}ST14_ErrorCodeType" maxOccurs="unbounded"/>
 *                           </sequence>
 *                         </restriction>
 *                       </complexContent>
 *                     </complexType>
 *                   </element>
 *                   <element name="WarningCodes" minOccurs="0">
 *                     <complexType>
 *                       <complexContent>
 *                         <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           <sequence>
 *                             <element name="WarningCode" type="{http://www.europa.eu/schengen/shared/xsd/v1}ST15_WarningCodeType" maxOccurs="unbounded"/>
 *                           </sequence>
 *                         </restriction>
 *                       </complexContent>
 *                     </complexType>
 *                   </element>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "BaseMessageResponseType", propOrder = {
    "returnCodes"
})
@XmlSeeAlso({
    SearchByVSNResponseMessageType.class,
    SearchByPersonalDataResponseMessageType.class,
    RetrieveTravellerFileResponseMessageType.class,
    SearchByBiometricsResponseMessageType.class,
    MessageResponseType.class
})
public class BaseMessageResponseType {

    @XmlElement(name = "ReturnCodes", required = true)
    protected BaseMessageResponseType.ReturnCodes returnCodes;

    /**
     * Recupera il valore della proprietà returnCodes.
     * 
     * @return
     *     possible object is
     *     {@link BaseMessageResponseType.ReturnCodes }
     *     
     */
    public BaseMessageResponseType.ReturnCodes getReturnCodes() {
        return returnCodes;
    }

    /**
     * Imposta il valore della proprietà returnCodes.
     * 
     * @param value
     *     allowed object is
     *     {@link BaseMessageResponseType.ReturnCodes }
     *     
     */
    public void setReturnCodes(BaseMessageResponseType.ReturnCodes value) {
        this.returnCodes = value;
    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="ErrorCodes" minOccurs="0">
     *           <complexType>
     *             <complexContent>
     *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 <sequence>
     *                   <element name="ErrorCode" type="{http://www.europa.eu/schengen/shared/xsd/v1}ST14_ErrorCodeType" maxOccurs="unbounded"/>
     *                 </sequence>
     *               </restriction>
     *             </complexContent>
     *           </complexType>
     *         </element>
     *         <element name="WarningCodes" minOccurs="0">
     *           <complexType>
     *             <complexContent>
     *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 <sequence>
     *                   <element name="WarningCode" type="{http://www.europa.eu/schengen/shared/xsd/v1}ST15_WarningCodeType" maxOccurs="unbounded"/>
     *                 </sequence>
     *               </restriction>
     *             </complexContent>
     *           </complexType>
     *         </element>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "errorCodes",
        "warningCodes"
    })
    public static class ReturnCodes {

        @XmlElement(name = "ErrorCodes")
        protected BaseMessageResponseType.ReturnCodes.ErrorCodes errorCodes;
        @XmlElement(name = "WarningCodes")
        protected BaseMessageResponseType.ReturnCodes.WarningCodes warningCodes;

        /**
         * Recupera il valore della proprietà errorCodes.
         * 
         * @return
         *     possible object is
         *     {@link BaseMessageResponseType.ReturnCodes.ErrorCodes }
         *     
         */
        public BaseMessageResponseType.ReturnCodes.ErrorCodes getErrorCodes() {
            return errorCodes;
        }

        /**
         * Imposta il valore della proprietà errorCodes.
         * 
         * @param value
         *     allowed object is
         *     {@link BaseMessageResponseType.ReturnCodes.ErrorCodes }
         *     
         */
        public void setErrorCodes(BaseMessageResponseType.ReturnCodes.ErrorCodes value) {
            this.errorCodes = value;
        }

        /**
         * Recupera il valore della proprietà warningCodes.
         * 
         * @return
         *     possible object is
         *     {@link BaseMessageResponseType.ReturnCodes.WarningCodes }
         *     
         */
        public BaseMessageResponseType.ReturnCodes.WarningCodes getWarningCodes() {
            return warningCodes;
        }

        /**
         * Imposta il valore della proprietà warningCodes.
         * 
         * @param value
         *     allowed object is
         *     {@link BaseMessageResponseType.ReturnCodes.WarningCodes }
         *     
         */
        public void setWarningCodes(BaseMessageResponseType.ReturnCodes.WarningCodes value) {
            this.warningCodes = value;
        }


        /**
         * <p>Classe Java per anonymous complex type.</p>
         * 
         * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
         * 
         * <pre>{@code
         * <complexType>
         *   <complexContent>
         *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       <sequence>
         *         <element name="ErrorCode" type="{http://www.europa.eu/schengen/shared/xsd/v1}ST14_ErrorCodeType" maxOccurs="unbounded"/>
         *       </sequence>
         *     </restriction>
         *   </complexContent>
         * </complexType>
         * }</pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "errorCode"
        })
        public static class ErrorCodes {

            @XmlElement(name = "ErrorCode", required = true)
            protected List<String> errorCode;

            /**
             * Gets the value of the errorCode property.
             * 
             * <p>This accessor method returns a reference to the live list,
             * not a snapshot. Therefore any modification you make to the
             * returned list will be present inside the JAXB object.
             * This is why there is not a <CODE>set</CODE> method for the errorCode property.</p>
             * 
             * <p>
             * For example, to add a new item, do as follows:
             * </p>
             * <pre>
             * getErrorCode().add(newItem);
             * </pre>
             * 
             * 
             * <p>
             * Objects of the following type(s) are allowed in the list
             * {@link String }
             * </p>
             * 
             * 
             * @return
             *     The value of the errorCode property.
             */
            public List<String> getErrorCode() {
                if (errorCode == null) {
                    errorCode = new ArrayList<>();
                }
                return this.errorCode;
            }

        }


        /**
         * <p>Classe Java per anonymous complex type.</p>
         * 
         * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
         * 
         * <pre>{@code
         * <complexType>
         *   <complexContent>
         *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       <sequence>
         *         <element name="WarningCode" type="{http://www.europa.eu/schengen/shared/xsd/v1}ST15_WarningCodeType" maxOccurs="unbounded"/>
         *       </sequence>
         *     </restriction>
         *   </complexContent>
         * </complexType>
         * }</pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "warningCode"
        })
        public static class WarningCodes {

            @XmlElement(name = "WarningCode", required = true)
            protected List<String> warningCode;

            /**
             * Gets the value of the warningCode property.
             * 
             * <p>This accessor method returns a reference to the live list,
             * not a snapshot. Therefore any modification you make to the
             * returned list will be present inside the JAXB object.
             * This is why there is not a <CODE>set</CODE> method for the warningCode property.</p>
             * 
             * <p>
             * For example, to add a new item, do as follows:
             * </p>
             * <pre>
             * getWarningCode().add(newItem);
             * </pre>
             * 
             * 
             * <p>
             * Objects of the following type(s) are allowed in the list
             * {@link String }
             * </p>
             * 
             * 
             * @return
             *     The value of the warningCode property.
             */
            public List<String> getWarningCode() {
                if (warningCode == null) {
                    warningCode = new ArrayList<>();
                }
                return this.warningCode;
            }

        }

    }

}
