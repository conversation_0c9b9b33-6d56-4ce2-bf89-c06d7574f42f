//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.ees.xsd.v1;

import javax.xml.datatype.XMLGregorianCalendar;
import eu.europa.schengen.shared.xsd.v1.PeriodType;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlSeeAlso;
import jakarta.xml.bind.annotation.XmlType;


/**
 * Calculator data - type for request.
 * 
 * <p>Classe Java per ExtendedCalculatorScopeType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="ExtendedCalculatorScopeType">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <choice>
 *         <element name="Entry">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="VEIndicator" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
 *                   <element name="EntryDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *                   <choice minOccurs="0">
 *                     <element name="VisaInformation">
 *                       <complexType>
 *                         <complexContent>
 *                           <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                             <choice>
 *                               <element name="VisaStickerNumber" type="{http://www.europa.eu/schengen/shared/xsd/v1}VisaStickerNumberType"/>
 *                               <sequence>
 *                                 <element name="PeriodOfValidity" type="{http://www.europa.eu/schengen/shared/xsd/v1}PeriodType"/>
 *                                 <element name="DurationOfStay" type="{http://www.europa.eu/schengen/shared/xsd/v1}DurationType"/>
 *                                 <element name="NumberOfEntries" type="{http://www.europa.eu/schengen/shared/xsd/v1}CT07_NumberOfEntriesType"/>
 *                                 <element name="VLTVIndicator" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *                               </sequence>
 *                             </choice>
 *                           </restriction>
 *                         </complexContent>
 *                       </complexType>
 *                     </element>
 *                     <element name="FTDInformation">
 *                       <complexType>
 *                         <complexContent>
 *                           <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                             <choice>
 *                               <element name="FTDStickerNumber" type="{http://www.europa.eu/schengen/shared/xsd/v1}VisaStickerNumberType"/>
 *                               <element name="ValidUntil" type="{http://www.w3.org/2001/XMLSchema}date"/>
 *                             </choice>
 *                           </restriction>
 *                         </complexContent>
 *                       </complexType>
 *                     </element>
 *                   </choice>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *         <element name="Exit">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="ExitDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *         <element name="Territory">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="ControlDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *         <element name="Visa">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="IntendedEntryDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *                   <element name="VisaInformation" minOccurs="0">
 *                     <complexType>
 *                       <complexContent>
 *                         <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           <choice>
 *                             <element name="VisaStickerNumber" type="{http://www.europa.eu/schengen/shared/xsd/v1}VisaStickerNumberType"/>
 *                             <sequence>
 *                               <element name="PeriodOfValidity" type="{http://www.europa.eu/schengen/shared/xsd/v1}PeriodType"/>
 *                               <element name="DurationOfStay" type="{http://www.europa.eu/schengen/shared/xsd/v1}DurationType"/>
 *                               <element name="NumberOfEntries" type="{http://www.europa.eu/schengen/shared/xsd/v1}CT07_NumberOfEntriesType"/>
 *                               <element name="VLTVIndicator" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *                             </sequence>
 *                           </choice>
 *                         </restriction>
 *                       </complexContent>
 *                     </complexType>
 *                   </element>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *       </choice>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ExtendedCalculatorScopeType", propOrder = {
    "entry",
    "exit",
    "territory",
    "visa"
})
@XmlSeeAlso({
    eu.europa.schengen.ees_ns.xsd.v1.CalculatorRequestMessageType.Calculator.class
})
public class ExtendedCalculatorScopeType {

    /**
     * Calculator input for option Entry
     * 
     */
    @XmlElement(name = "Entry")
    protected ExtendedCalculatorScopeType.Entry entry;
    /**
     * Calculator input for option Exit
     * 
     */
    @XmlElement(name = "Exit")
    protected ExtendedCalculatorScopeType.Exit exit;
    /**
     * Calculator input for option Territory
     * 
     */
    @XmlElement(name = "Territory")
    protected ExtendedCalculatorScopeType.Territory territory;
    /**
     * Calculator input for option Visa
     * 
     */
    @XmlElement(name = "Visa")
    protected ExtendedCalculatorScopeType.Visa visa;

    /**
     * Calculator input for option Entry
     * 
     * @return
     *     possible object is
     *     {@link ExtendedCalculatorScopeType.Entry }
     *     
     */
    public ExtendedCalculatorScopeType.Entry getEntry() {
        return entry;
    }

    /**
     * Imposta il valore della proprietà entry.
     * 
     * @param value
     *     allowed object is
     *     {@link ExtendedCalculatorScopeType.Entry }
     *     
     * @see #getEntry()
     */
    public void setEntry(ExtendedCalculatorScopeType.Entry value) {
        this.entry = value;
    }

    /**
     * Calculator input for option Exit
     * 
     * @return
     *     possible object is
     *     {@link ExtendedCalculatorScopeType.Exit }
     *     
     */
    public ExtendedCalculatorScopeType.Exit getExit() {
        return exit;
    }

    /**
     * Imposta il valore della proprietà exit.
     * 
     * @param value
     *     allowed object is
     *     {@link ExtendedCalculatorScopeType.Exit }
     *     
     * @see #getExit()
     */
    public void setExit(ExtendedCalculatorScopeType.Exit value) {
        this.exit = value;
    }

    /**
     * Calculator input for option Territory
     * 
     * @return
     *     possible object is
     *     {@link ExtendedCalculatorScopeType.Territory }
     *     
     */
    public ExtendedCalculatorScopeType.Territory getTerritory() {
        return territory;
    }

    /**
     * Imposta il valore della proprietà territory.
     * 
     * @param value
     *     allowed object is
     *     {@link ExtendedCalculatorScopeType.Territory }
     *     
     * @see #getTerritory()
     */
    public void setTerritory(ExtendedCalculatorScopeType.Territory value) {
        this.territory = value;
    }

    /**
     * Calculator input for option Visa
     * 
     * @return
     *     possible object is
     *     {@link ExtendedCalculatorScopeType.Visa }
     *     
     */
    public ExtendedCalculatorScopeType.Visa getVisa() {
        return visa;
    }

    /**
     * Imposta il valore della proprietà visa.
     * 
     * @param value
     *     allowed object is
     *     {@link ExtendedCalculatorScopeType.Visa }
     *     
     * @see #getVisa()
     */
    public void setVisa(ExtendedCalculatorScopeType.Visa value) {
        this.visa = value;
    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="VEIndicator" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
     *         <element name="EntryDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
     *         <choice minOccurs="0">
     *           <element name="VisaInformation">
     *             <complexType>
     *               <complexContent>
     *                 <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                   <choice>
     *                     <element name="VisaStickerNumber" type="{http://www.europa.eu/schengen/shared/xsd/v1}VisaStickerNumberType"/>
     *                     <sequence>
     *                       <element name="PeriodOfValidity" type="{http://www.europa.eu/schengen/shared/xsd/v1}PeriodType"/>
     *                       <element name="DurationOfStay" type="{http://www.europa.eu/schengen/shared/xsd/v1}DurationType"/>
     *                       <element name="NumberOfEntries" type="{http://www.europa.eu/schengen/shared/xsd/v1}CT07_NumberOfEntriesType"/>
     *                       <element name="VLTVIndicator" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
     *                     </sequence>
     *                   </choice>
     *                 </restriction>
     *               </complexContent>
     *             </complexType>
     *           </element>
     *           <element name="FTDInformation">
     *             <complexType>
     *               <complexContent>
     *                 <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                   <choice>
     *                     <element name="FTDStickerNumber" type="{http://www.europa.eu/schengen/shared/xsd/v1}VisaStickerNumberType"/>
     *                     <element name="ValidUntil" type="{http://www.w3.org/2001/XMLSchema}date"/>
     *                   </choice>
     *                 </restriction>
     *               </complexContent>
     *             </complexType>
     *           </element>
     *         </choice>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "veIndicator",
        "entryDate",
        "visaInformation",
        "ftdInformation"
    })
    public static class Entry {

        @XmlElement(name = "VEIndicator")
        protected Boolean veIndicator;
        @XmlElement(name = "EntryDate", required = true)
        @XmlSchemaType(name = "dateTime")
        protected XMLGregorianCalendar entryDate;
        @XmlElement(name = "VisaInformation")
        protected ExtendedCalculatorScopeType.Entry.VisaInformation visaInformation;
        @XmlElement(name = "FTDInformation")
        protected ExtendedCalculatorScopeType.Entry.FTDInformation ftdInformation;

        /**
         * Recupera il valore della proprietà veIndicator.
         * 
         * @return
         *     possible object is
         *     {@link Boolean }
         *     
         */
        public Boolean isVEIndicator() {
            return veIndicator;
        }

        /**
         * Imposta il valore della proprietà veIndicator.
         * 
         * @param value
         *     allowed object is
         *     {@link Boolean }
         *     
         */
        public void setVEIndicator(Boolean value) {
            this.veIndicator = value;
        }

        /**
         * Recupera il valore della proprietà entryDate.
         * 
         * @return
         *     possible object is
         *     {@link XMLGregorianCalendar }
         *     
         */
        public XMLGregorianCalendar getEntryDate() {
            return entryDate;
        }

        /**
         * Imposta il valore della proprietà entryDate.
         * 
         * @param value
         *     allowed object is
         *     {@link XMLGregorianCalendar }
         *     
         */
        public void setEntryDate(XMLGregorianCalendar value) {
            this.entryDate = value;
        }

        /**
         * Recupera il valore della proprietà visaInformation.
         * 
         * @return
         *     possible object is
         *     {@link ExtendedCalculatorScopeType.Entry.VisaInformation }
         *     
         */
        public ExtendedCalculatorScopeType.Entry.VisaInformation getVisaInformation() {
            return visaInformation;
        }

        /**
         * Imposta il valore della proprietà visaInformation.
         * 
         * @param value
         *     allowed object is
         *     {@link ExtendedCalculatorScopeType.Entry.VisaInformation }
         *     
         */
        public void setVisaInformation(ExtendedCalculatorScopeType.Entry.VisaInformation value) {
            this.visaInformation = value;
        }

        /**
         * Recupera il valore della proprietà ftdInformation.
         * 
         * @return
         *     possible object is
         *     {@link ExtendedCalculatorScopeType.Entry.FTDInformation }
         *     
         */
        public ExtendedCalculatorScopeType.Entry.FTDInformation getFTDInformation() {
            return ftdInformation;
        }

        /**
         * Imposta il valore della proprietà ftdInformation.
         * 
         * @param value
         *     allowed object is
         *     {@link ExtendedCalculatorScopeType.Entry.FTDInformation }
         *     
         */
        public void setFTDInformation(ExtendedCalculatorScopeType.Entry.FTDInformation value) {
            this.ftdInformation = value;
        }


        /**
         * <p>Classe Java per anonymous complex type.</p>
         * 
         * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
         * 
         * <pre>{@code
         * <complexType>
         *   <complexContent>
         *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       <choice>
         *         <element name="FTDStickerNumber" type="{http://www.europa.eu/schengen/shared/xsd/v1}VisaStickerNumberType"/>
         *         <element name="ValidUntil" type="{http://www.w3.org/2001/XMLSchema}date"/>
         *       </choice>
         *     </restriction>
         *   </complexContent>
         * </complexType>
         * }</pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "ftdStickerNumber",
            "validUntil"
        })
        public static class FTDInformation {

            /**
             * Description: ID of the FTD sticker.
             * 
             */
            @XmlElement(name = "FTDStickerNumber")
            protected String ftdStickerNumber;
            /**
             * Description: The date of expiry of the validity of the FTD.
             * 
             */
            @XmlElement(name = "ValidUntil")
            @XmlSchemaType(name = "date")
            protected XMLGregorianCalendar validUntil;

            /**
             * Description: ID of the FTD sticker.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getFTDStickerNumber() {
                return ftdStickerNumber;
            }

            /**
             * Imposta il valore della proprietà ftdStickerNumber.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             * @see #getFTDStickerNumber()
             */
            public void setFTDStickerNumber(String value) {
                this.ftdStickerNumber = value;
            }

            /**
             * Description: The date of expiry of the validity of the FTD.
             * 
             * @return
             *     possible object is
             *     {@link XMLGregorianCalendar }
             *     
             */
            public XMLGregorianCalendar getValidUntil() {
                return validUntil;
            }

            /**
             * Imposta il valore della proprietà validUntil.
             * 
             * @param value
             *     allowed object is
             *     {@link XMLGregorianCalendar }
             *     
             * @see #getValidUntil()
             */
            public void setValidUntil(XMLGregorianCalendar value) {
                this.validUntil = value;
            }

        }


        /**
         * <p>Classe Java per anonymous complex type.</p>
         * 
         * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
         * 
         * <pre>{@code
         * <complexType>
         *   <complexContent>
         *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       <choice>
         *         <element name="VisaStickerNumber" type="{http://www.europa.eu/schengen/shared/xsd/v1}VisaStickerNumberType"/>
         *         <sequence>
         *           <element name="PeriodOfValidity" type="{http://www.europa.eu/schengen/shared/xsd/v1}PeriodType"/>
         *           <element name="DurationOfStay" type="{http://www.europa.eu/schengen/shared/xsd/v1}DurationType"/>
         *           <element name="NumberOfEntries" type="{http://www.europa.eu/schengen/shared/xsd/v1}CT07_NumberOfEntriesType"/>
         *           <element name="VLTVIndicator" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
         *         </sequence>
         *       </choice>
         *     </restriction>
         *   </complexContent>
         * </complexType>
         * }</pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "visaStickerNumber",
            "periodOfValidity",
            "durationOfStay",
            "numberOfEntries",
            "vltvIndicator"
        })
        public static class VisaInformation {

            /**
             * Description: ID of the visa sticker.
             * 
             */
            @XmlElement(name = "VisaStickerNumber")
            protected String visaStickerNumber;
            /**
             * Description: The visa's period of validity.
             * 
             */
            @XmlElement(name = "PeriodOfValidity")
            protected PeriodType periodOfValidity;
            /**
             * Description: The duration of stay as printed on the visa sticker.
             * 
             */
            @XmlElement(name = "DurationOfStay")
            @XmlSchemaType(name = "nonNegativeInteger")
            protected Integer durationOfStay;
            /**
             * Description: The number of entries allowed by this visa: one, two or many. The allowed values are defined within the code table NumberOfEntries.
             * 
             */
            @XmlElement(name = "NumberOfEntries")
            protected String numberOfEntries;
            /**
             * Description: TRUE means that the visa has been issued with limited territorial validity pursuant to Article 25(1)(b) of Regulation (EC) No 810/2009.
             * 
             */
            @XmlElement(name = "VLTVIndicator")
            protected Boolean vltvIndicator;

            /**
             * Description: ID of the visa sticker.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getVisaStickerNumber() {
                return visaStickerNumber;
            }

            /**
             * Imposta il valore della proprietà visaStickerNumber.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             * @see #getVisaStickerNumber()
             */
            public void setVisaStickerNumber(String value) {
                this.visaStickerNumber = value;
            }

            /**
             * Description: The visa's period of validity.
             * 
             * @return
             *     possible object is
             *     {@link PeriodType }
             *     
             */
            public PeriodType getPeriodOfValidity() {
                return periodOfValidity;
            }

            /**
             * Imposta il valore della proprietà periodOfValidity.
             * 
             * @param value
             *     allowed object is
             *     {@link PeriodType }
             *     
             * @see #getPeriodOfValidity()
             */
            public void setPeriodOfValidity(PeriodType value) {
                this.periodOfValidity = value;
            }

            /**
             * Description: The duration of stay as printed on the visa sticker.
             * 
             * @return
             *     possible object is
             *     {@link Integer }
             *     
             */
            public Integer getDurationOfStay() {
                return durationOfStay;
            }

            /**
             * Imposta il valore della proprietà durationOfStay.
             * 
             * @param value
             *     allowed object is
             *     {@link Integer }
             *     
             * @see #getDurationOfStay()
             */
            public void setDurationOfStay(Integer value) {
                this.durationOfStay = value;
            }

            /**
             * Description: The number of entries allowed by this visa: one, two or many. The allowed values are defined within the code table NumberOfEntries.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getNumberOfEntries() {
                return numberOfEntries;
            }

            /**
             * Imposta il valore della proprietà numberOfEntries.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             * @see #getNumberOfEntries()
             */
            public void setNumberOfEntries(String value) {
                this.numberOfEntries = value;
            }

            /**
             * Description: TRUE means that the visa has been issued with limited territorial validity pursuant to Article 25(1)(b) of Regulation (EC) No 810/2009.
             * 
             * @return
             *     possible object is
             *     {@link Boolean }
             *     
             */
            public Boolean isVLTVIndicator() {
                return vltvIndicator;
            }

            /**
             * Imposta il valore della proprietà vltvIndicator.
             * 
             * @param value
             *     allowed object is
             *     {@link Boolean }
             *     
             * @see #isVLTVIndicator()
             */
            public void setVLTVIndicator(Boolean value) {
                this.vltvIndicator = value;
            }

        }

    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="ExitDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "exitDate"
    })
    public static class Exit {

        @XmlElement(name = "ExitDate", required = true)
        @XmlSchemaType(name = "dateTime")
        protected XMLGregorianCalendar exitDate;

        /**
         * Recupera il valore della proprietà exitDate.
         * 
         * @return
         *     possible object is
         *     {@link XMLGregorianCalendar }
         *     
         */
        public XMLGregorianCalendar getExitDate() {
            return exitDate;
        }

        /**
         * Imposta il valore della proprietà exitDate.
         * 
         * @param value
         *     allowed object is
         *     {@link XMLGregorianCalendar }
         *     
         */
        public void setExitDate(XMLGregorianCalendar value) {
            this.exitDate = value;
        }

    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="ControlDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "controlDate"
    })
    public static class Territory {

        @XmlElement(name = "ControlDate", required = true)
        @XmlSchemaType(name = "dateTime")
        protected XMLGregorianCalendar controlDate;

        /**
         * Recupera il valore della proprietà controlDate.
         * 
         * @return
         *     possible object is
         *     {@link XMLGregorianCalendar }
         *     
         */
        public XMLGregorianCalendar getControlDate() {
            return controlDate;
        }

        /**
         * Imposta il valore della proprietà controlDate.
         * 
         * @param value
         *     allowed object is
         *     {@link XMLGregorianCalendar }
         *     
         */
        public void setControlDate(XMLGregorianCalendar value) {
            this.controlDate = value;
        }

    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="IntendedEntryDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
     *         <element name="VisaInformation" minOccurs="0">
     *           <complexType>
     *             <complexContent>
     *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 <choice>
     *                   <element name="VisaStickerNumber" type="{http://www.europa.eu/schengen/shared/xsd/v1}VisaStickerNumberType"/>
     *                   <sequence>
     *                     <element name="PeriodOfValidity" type="{http://www.europa.eu/schengen/shared/xsd/v1}PeriodType"/>
     *                     <element name="DurationOfStay" type="{http://www.europa.eu/schengen/shared/xsd/v1}DurationType"/>
     *                     <element name="NumberOfEntries" type="{http://www.europa.eu/schengen/shared/xsd/v1}CT07_NumberOfEntriesType"/>
     *                     <element name="VLTVIndicator" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
     *                   </sequence>
     *                 </choice>
     *               </restriction>
     *             </complexContent>
     *           </complexType>
     *         </element>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "intendedEntryDate",
        "visaInformation"
    })
    public static class Visa {

        @XmlElement(name = "IntendedEntryDate", required = true)
        @XmlSchemaType(name = "dateTime")
        protected XMLGregorianCalendar intendedEntryDate;
        @XmlElement(name = "VisaInformation")
        protected ExtendedCalculatorScopeType.Visa.VisaInformation visaInformation;

        /**
         * Recupera il valore della proprietà intendedEntryDate.
         * 
         * @return
         *     possible object is
         *     {@link XMLGregorianCalendar }
         *     
         */
        public XMLGregorianCalendar getIntendedEntryDate() {
            return intendedEntryDate;
        }

        /**
         * Imposta il valore della proprietà intendedEntryDate.
         * 
         * @param value
         *     allowed object is
         *     {@link XMLGregorianCalendar }
         *     
         */
        public void setIntendedEntryDate(XMLGregorianCalendar value) {
            this.intendedEntryDate = value;
        }

        /**
         * Recupera il valore della proprietà visaInformation.
         * 
         * @return
         *     possible object is
         *     {@link ExtendedCalculatorScopeType.Visa.VisaInformation }
         *     
         */
        public ExtendedCalculatorScopeType.Visa.VisaInformation getVisaInformation() {
            return visaInformation;
        }

        /**
         * Imposta il valore della proprietà visaInformation.
         * 
         * @param value
         *     allowed object is
         *     {@link ExtendedCalculatorScopeType.Visa.VisaInformation }
         *     
         */
        public void setVisaInformation(ExtendedCalculatorScopeType.Visa.VisaInformation value) {
            this.visaInformation = value;
        }


        /**
         * <p>Classe Java per anonymous complex type.</p>
         * 
         * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
         * 
         * <pre>{@code
         * <complexType>
         *   <complexContent>
         *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       <choice>
         *         <element name="VisaStickerNumber" type="{http://www.europa.eu/schengen/shared/xsd/v1}VisaStickerNumberType"/>
         *         <sequence>
         *           <element name="PeriodOfValidity" type="{http://www.europa.eu/schengen/shared/xsd/v1}PeriodType"/>
         *           <element name="DurationOfStay" type="{http://www.europa.eu/schengen/shared/xsd/v1}DurationType"/>
         *           <element name="NumberOfEntries" type="{http://www.europa.eu/schengen/shared/xsd/v1}CT07_NumberOfEntriesType"/>
         *           <element name="VLTVIndicator" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
         *         </sequence>
         *       </choice>
         *     </restriction>
         *   </complexContent>
         * </complexType>
         * }</pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "visaStickerNumber",
            "periodOfValidity",
            "durationOfStay",
            "numberOfEntries",
            "vltvIndicator"
        })
        public static class VisaInformation {

            /**
             * Description: ID of the visa sticker.
             * 
             */
            @XmlElement(name = "VisaStickerNumber")
            protected String visaStickerNumber;
            /**
             * Description: The visa's period of validity.
             * 
             */
            @XmlElement(name = "PeriodOfValidity")
            protected PeriodType periodOfValidity;
            /**
             * Description: The duration of stay as printed on the visa sticker.
             * 
             */
            @XmlElement(name = "DurationOfStay")
            @XmlSchemaType(name = "nonNegativeInteger")
            protected Integer durationOfStay;
            /**
             * Description: The number of entries allowed by this visa: one, two or many. The allowed values are defined within the code table NumberOfEntries.
             * 
             */
            @XmlElement(name = "NumberOfEntries")
            protected String numberOfEntries;
            /**
             * Description: TRUE means that the visa has been issued with limited territorial validity pursuant to Article 25(1)(b) of Regulation (EC) No 810/2009.
             * 
             */
            @XmlElement(name = "VLTVIndicator")
            protected Boolean vltvIndicator;

            /**
             * Description: ID of the visa sticker.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getVisaStickerNumber() {
                return visaStickerNumber;
            }

            /**
             * Imposta il valore della proprietà visaStickerNumber.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             * @see #getVisaStickerNumber()
             */
            public void setVisaStickerNumber(String value) {
                this.visaStickerNumber = value;
            }

            /**
             * Description: The visa's period of validity.
             * 
             * @return
             *     possible object is
             *     {@link PeriodType }
             *     
             */
            public PeriodType getPeriodOfValidity() {
                return periodOfValidity;
            }

            /**
             * Imposta il valore della proprietà periodOfValidity.
             * 
             * @param value
             *     allowed object is
             *     {@link PeriodType }
             *     
             * @see #getPeriodOfValidity()
             */
            public void setPeriodOfValidity(PeriodType value) {
                this.periodOfValidity = value;
            }

            /**
             * Description: The duration of stay as printed on the visa sticker.
             * 
             * @return
             *     possible object is
             *     {@link Integer }
             *     
             */
            public Integer getDurationOfStay() {
                return durationOfStay;
            }

            /**
             * Imposta il valore della proprietà durationOfStay.
             * 
             * @param value
             *     allowed object is
             *     {@link Integer }
             *     
             * @see #getDurationOfStay()
             */
            public void setDurationOfStay(Integer value) {
                this.durationOfStay = value;
            }

            /**
             * Description: The number of entries allowed by this visa: one, two or many. The allowed values are defined within the code table NumberOfEntries.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getNumberOfEntries() {
                return numberOfEntries;
            }

            /**
             * Imposta il valore della proprietà numberOfEntries.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             * @see #getNumberOfEntries()
             */
            public void setNumberOfEntries(String value) {
                this.numberOfEntries = value;
            }

            /**
             * Description: TRUE means that the visa has been issued with limited territorial validity pursuant to Article 25(1)(b) of Regulation (EC) No 810/2009.
             * 
             * @return
             *     possible object is
             *     {@link Boolean }
             *     
             */
            public Boolean isVLTVIndicator() {
                return vltvIndicator;
            }

            /**
             * Imposta il valore della proprietà vltvIndicator.
             * 
             * @param value
             *     allowed object is
             *     {@link Boolean }
             *     
             * @see #isVLTVIndicator()
             */
            public void setVLTVIndicator(Boolean value) {
                this.vltvIndicator = value;
            }

        }

    }

}
