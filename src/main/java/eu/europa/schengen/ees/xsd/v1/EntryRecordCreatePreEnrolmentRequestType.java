//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.ees.xsd.v1;

import javax.xml.datatype.XMLGregorianCalendar;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlType;


/**
 * Entry data - type for the create request with the use of pre-enrolled data (attention: it is not extension of the basic type, so in case basic type changes then here changes have to be applied too).
 * 
 * <p>Classe Java per EntryRecordCreatePreEnrolmentRequestType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="EntryRecordCreatePreEnrolmentRequestType">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="BorderCrossingTimestamp" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         <element name="AuthorisedStayUntil" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/>
 *         <element name="BorderCrossingPoint" type="{http://www.europa.eu/schengen/shared/xsd/v1}AuthorityUniqueIDType"/>
 *         <element name="NationalVisa" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
 *         <element name="AuthorisationChange" type="{http://www.europa.eu/schengen/ees/xsd/v1}AuthorisationChangeWithoutVisaType" minOccurs="0"/>
 *         <element name="PersonStatus" type="{http://www.europa.eu/schengen/shared/xsd/v1}CT71_PersonStatusType"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "EntryRecordCreatePreEnrolmentRequestType", propOrder = {
    "borderCrossingTimestamp",
    "authorisedStayUntil",
    "borderCrossingPoint",
    "nationalVisa",
    "authorisationChange",
    "personStatus"
})
public class EntryRecordCreatePreEnrolmentRequestType {

    /**
     * The date and time of the entry.
     * 
     * 						[Article 16(2)(a)]
     * 
     * 						For visa-exempt third-country nationals, points (a), (b), (c) of Article 16(2)(a), points (a) and (b) of Article 16(3) and Article 16(4) shall apply mutatis mutandis.
     * 
     * 						[Article 17(2)]
     * 
     */
    @XmlElement(name = "BorderCrossingTimestamp", required = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar borderCrossingTimestamp;
    /**
     * The end date (and time) of the maximum duration of the stay as authorised by the short-stay visa, which shall be updated at each entry.
     * 
     * 						[Article 16(2)(d)]
     * 
     * 						In case of authorisation change this value will be updated with:
     * 
     * 						Where applicable, the new expiry date of the authorised stay or the visa.
     * 
     * 						[Article 19(1)(f)]
     * 
     */
    @XmlElement(name = "AuthorisedStayUntil")
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar authorisedStayUntil;
    /**
     * The border crossing point.
     * 
     * 						The border crossing point of the entry and the authority that authorized the entry;
     * 
     * 						[Article 16(2)(b)]
     * 
     * 						For visa-exempt third-country nationals, points (a), (b), (c) of Article 16(2)(a), points (a) and (b) of Article 16(3) and Article 16(4) shall apply mutatis mutandis.
     * 
     * 						[Article 17(2)]
     * 
     * 						Without prejudice to Article 20 of this Regulation and Article 12(3) of Regulation (EU) 2016/399, where the short stay of a third-country national who is present on the territory of a Member State starts directly after a stay based on a residence permit or a long-stay visa and no previous individual file has been created. that third-country national may request the competent authorities referred to in Article 9(2) of this Regulation to create an individual file and an entry/exit record by entering the data referred to in Articles 16(1), (2) and (6) and 17(1) of this Regulation. Instead of the data referred to in point (a) of Article 16(2) of this Regulation, those competent authorities shall insert the date of the start of the short stay and, instead of the data referred to in point (b) of Article 16(2) of this Regulation, they shall insert the name of the authority that inserted those data.
     * 
     * 						[Article 14(8)]
     * 
     */
    @XmlElement(name = "BorderCrossingPoint", required = true)
    protected String borderCrossingPoint;
    /**
     * For the Member States which do not yet apply the Schengen acquis in full but operate the EES, a notification, where applicable, indicating that the third-country national used a national short-stay visa for the entry.
     * 
     * 						[Article 16(2)(g)]
     * 
     */
    @XmlElement(name = "NationalVisa")
    protected Boolean nationalVisa;
    @XmlElement(name = "AuthorisationChange")
    protected AuthorisationChangeWithoutVisaType authorisationChange;
    @XmlElement(name = "PersonStatus", required = true)
    protected String personStatus;

    /**
     * The date and time of the entry.
     * 
     * 						[Article 16(2)(a)]
     * 
     * 						For visa-exempt third-country nationals, points (a), (b), (c) of Article 16(2)(a), points (a) and (b) of Article 16(3) and Article 16(4) shall apply mutatis mutandis.
     * 
     * 						[Article 17(2)]
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getBorderCrossingTimestamp() {
        return borderCrossingTimestamp;
    }

    /**
     * Imposta il valore della proprietà borderCrossingTimestamp.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     * @see #getBorderCrossingTimestamp()
     */
    public void setBorderCrossingTimestamp(XMLGregorianCalendar value) {
        this.borderCrossingTimestamp = value;
    }

    /**
     * The end date (and time) of the maximum duration of the stay as authorised by the short-stay visa, which shall be updated at each entry.
     * 
     * 						[Article 16(2)(d)]
     * 
     * 						In case of authorisation change this value will be updated with:
     * 
     * 						Where applicable, the new expiry date of the authorised stay or the visa.
     * 
     * 						[Article 19(1)(f)]
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getAuthorisedStayUntil() {
        return authorisedStayUntil;
    }

    /**
     * Imposta il valore della proprietà authorisedStayUntil.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     * @see #getAuthorisedStayUntil()
     */
    public void setAuthorisedStayUntil(XMLGregorianCalendar value) {
        this.authorisedStayUntil = value;
    }

    /**
     * The border crossing point.
     * 
     * 						The border crossing point of the entry and the authority that authorized the entry;
     * 
     * 						[Article 16(2)(b)]
     * 
     * 						For visa-exempt third-country nationals, points (a), (b), (c) of Article 16(2)(a), points (a) and (b) of Article 16(3) and Article 16(4) shall apply mutatis mutandis.
     * 
     * 						[Article 17(2)]
     * 
     * 						Without prejudice to Article 20 of this Regulation and Article 12(3) of Regulation (EU) 2016/399, where the short stay of a third-country national who is present on the territory of a Member State starts directly after a stay based on a residence permit or a long-stay visa and no previous individual file has been created. that third-country national may request the competent authorities referred to in Article 9(2) of this Regulation to create an individual file and an entry/exit record by entering the data referred to in Articles 16(1), (2) and (6) and 17(1) of this Regulation. Instead of the data referred to in point (a) of Article 16(2) of this Regulation, those competent authorities shall insert the date of the start of the short stay and, instead of the data referred to in point (b) of Article 16(2) of this Regulation, they shall insert the name of the authority that inserted those data.
     * 
     * 						[Article 14(8)]
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBorderCrossingPoint() {
        return borderCrossingPoint;
    }

    /**
     * Imposta il valore della proprietà borderCrossingPoint.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     * @see #getBorderCrossingPoint()
     */
    public void setBorderCrossingPoint(String value) {
        this.borderCrossingPoint = value;
    }

    /**
     * For the Member States which do not yet apply the Schengen acquis in full but operate the EES, a notification, where applicable, indicating that the third-country national used a national short-stay visa for the entry.
     * 
     * 						[Article 16(2)(g)]
     * 
     * @return
     *     possible object is
     *     {@link Boolean }
     *     
     */
    public Boolean isNationalVisa() {
        return nationalVisa;
    }

    /**
     * Imposta il valore della proprietà nationalVisa.
     * 
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *     
     * @see #isNationalVisa()
     */
    public void setNationalVisa(Boolean value) {
        this.nationalVisa = value;
    }

    /**
     * Recupera il valore della proprietà authorisationChange.
     * 
     * @return
     *     possible object is
     *     {@link AuthorisationChangeWithoutVisaType }
     *     
     */
    public AuthorisationChangeWithoutVisaType getAuthorisationChange() {
        return authorisationChange;
    }

    /**
     * Imposta il valore della proprietà authorisationChange.
     * 
     * @param value
     *     allowed object is
     *     {@link AuthorisationChangeWithoutVisaType }
     *     
     */
    public void setAuthorisationChange(AuthorisationChangeWithoutVisaType value) {
        this.authorisationChange = value;
    }

    /**
     * Recupera il valore della proprietà personStatus.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPersonStatus() {
        return personStatus;
    }

    /**
     * Imposta il valore della proprietà personStatus.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPersonStatus(String value) {
        this.personStatus = value;
    }

}
