//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.ees.xsd.v1;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlAttribute;
import jakarta.xml.bind.annotation.XmlType;
import jakarta.xml.bind.annotation.XmlValue;


/**
 * <p>Classe Java per CT04_GenderSearchField complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="CT04_GenderSearchField">
 *   <simpleContent>
 *     <extension base="<http://www.europa.eu/schengen/shared/xsd/v1>CT04_GenderType">
 *       <attribute name="weight" type="{http://www.europa.eu/schengen/ees/xsd/v1}WeightType" />
 *     </extension>
 *   </simpleContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "CT04_GenderSearchField", propOrder = {
    "value"
})
public class CT04GenderSearchField {

    /**
     * Description: This table defines the list of the gender.
     * 
     */
    @XmlValue
    protected String value;
    @XmlAttribute(name = "weight")
    protected Short weight;

    /**
     * Description: This table defines the list of the gender.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getValue() {
        return value;
    }

    /**
     * Imposta il valore della proprietà value.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     * @see #getValue()
     */
    public void setValue(String value) {
        this.value = value;
    }

    /**
     * Recupera il valore della proprietà weight.
     * 
     * @return
     *     possible object is
     *     {@link Short }
     *     
     */
    public Short getWeight() {
        return weight;
    }

    /**
     * Imposta il valore della proprietà weight.
     * 
     * @param value
     *     allowed object is
     *     {@link Short }
     *     
     */
    public void setWeight(Short value) {
        this.weight = value;
    }

}
