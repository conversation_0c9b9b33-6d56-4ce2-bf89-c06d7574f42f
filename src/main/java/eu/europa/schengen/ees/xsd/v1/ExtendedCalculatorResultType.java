//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.ees.xsd.v1;

import java.math.BigInteger;
import javax.xml.datatype.Duration;
import javax.xml.datatype.XMLGregorianCalendar;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlSeeAlso;
import jakarta.xml.bind.annotation.XmlType;


/**
 * Calculator data - type for response.
 * 
 * <p>Classe Java per ExtendedCalculatorResultType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="ExtendedCalculatorResultType">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <choice minOccurs="0">
 *         <group ref="{http://www.europa.eu/schengen/ees/xsd/v1}CalculatorResultChoicesGroup"/>
 *         <element name="Territory">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="ControlDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *                   <element name="AuthorisedStayUntil" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *                   <choice>
 *                     <element name="RemainingAuthorisedDurationOfStay" type="{http://www.w3.org/2001/XMLSchema}duration"/>
 *                     <element name="DurationOfOverstay" type="{http://www.w3.org/2001/XMLSchema}duration"/>
 *                   </choice>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *         <element name="Visa" type="{http://www.europa.eu/schengen/ees/xsd/v1}VisaEESCalculatorResponseType"/>
 *       </choice>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ExtendedCalculatorResultType", propOrder = {
    "entry",
    "exit",
    "territory",
    "visa"
})
@XmlSeeAlso({
    eu.europa.schengen.ees_ns.xsd.v1.CalculatorResponseMessageType.Response.Calculator.class
})
public class ExtendedCalculatorResultType {

    @XmlElement(name = "Entry")
    protected ExtendedCalculatorResultType.Entry entry;
    @XmlElement(name = "Exit")
    protected ExtendedCalculatorResultType.Exit exit;
    @XmlElement(name = "Territory")
    protected ExtendedCalculatorResultType.Territory territory;
    @XmlElement(name = "Visa")
    protected VisaEESCalculatorResponseType visa;

    /**
     * Recupera il valore della proprietà entry.
     * 
     * @return
     *     possible object is
     *     {@link ExtendedCalculatorResultType.Entry }
     *     
     */
    public ExtendedCalculatorResultType.Entry getEntry() {
        return entry;
    }

    /**
     * Imposta il valore della proprietà entry.
     * 
     * @param value
     *     allowed object is
     *     {@link ExtendedCalculatorResultType.Entry }
     *     
     */
    public void setEntry(ExtendedCalculatorResultType.Entry value) {
        this.entry = value;
    }

    /**
     * Recupera il valore della proprietà exit.
     * 
     * @return
     *     possible object is
     *     {@link ExtendedCalculatorResultType.Exit }
     *     
     */
    public ExtendedCalculatorResultType.Exit getExit() {
        return exit;
    }

    /**
     * Imposta il valore della proprietà exit.
     * 
     * @param value
     *     allowed object is
     *     {@link ExtendedCalculatorResultType.Exit }
     *     
     */
    public void setExit(ExtendedCalculatorResultType.Exit value) {
        this.exit = value;
    }

    /**
     * Recupera il valore della proprietà territory.
     * 
     * @return
     *     possible object is
     *     {@link ExtendedCalculatorResultType.Territory }
     *     
     */
    public ExtendedCalculatorResultType.Territory getTerritory() {
        return territory;
    }

    /**
     * Imposta il valore della proprietà territory.
     * 
     * @param value
     *     allowed object is
     *     {@link ExtendedCalculatorResultType.Territory }
     *     
     */
    public void setTerritory(ExtendedCalculatorResultType.Territory value) {
        this.territory = value;
    }

    /**
     * Recupera il valore della proprietà visa.
     * 
     * @return
     *     possible object is
     *     {@link VisaEESCalculatorResponseType }
     *     
     */
    public VisaEESCalculatorResponseType getVisa() {
        return visa;
    }

    /**
     * Imposta il valore della proprietà visa.
     * 
     * @param value
     *     allowed object is
     *     {@link VisaEESCalculatorResponseType }
     *     
     */
    public void setVisa(VisaEESCalculatorResponseType value) {
        this.visa = value;
    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="EntryDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
     *         <element name="AuthorisedStayUntil" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
     *         <element name="RemainingNumberOfEntries" type="{http://www.w3.org/2001/XMLSchema}nonNegativeInteger" minOccurs="0"/>
     *         <element name="AuthorisedDurationOfStay" type="{http://www.w3.org/2001/XMLSchema}duration"/>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "entryDate",
        "authorisedStayUntil",
        "remainingNumberOfEntries",
        "authorisedDurationOfStay"
    })
    public static class Entry {

        /**
         * Entry date for which authorised stay was calculated
         * 
         */
        @XmlElement(name = "EntryDate", required = true)
        @XmlSchemaType(name = "dateTime")
        protected XMLGregorianCalendar entryDate;
        /**
         * Date until which stay is authorised
         * 
         */
        @XmlElement(name = "AuthorisedStayUntil", required = true)
        @XmlSchemaType(name = "dateTime")
        protected XMLGregorianCalendar authorisedStayUntil;
        /**
         * Remaining number of authorised entries calculated on the basis of existing travel history
         * 
         */
        @XmlElement(name = "RemainingNumberOfEntries")
        @XmlSchemaType(name = "nonNegativeInteger")
        protected BigInteger remainingNumberOfEntries;
        @XmlElement(name = "AuthorisedDurationOfStay", required = true)
        protected Duration authorisedDurationOfStay;

        /**
         * Entry date for which authorised stay was calculated
         * 
         * @return
         *     possible object is
         *     {@link XMLGregorianCalendar }
         *     
         */
        public XMLGregorianCalendar getEntryDate() {
            return entryDate;
        }

        /**
         * Imposta il valore della proprietà entryDate.
         * 
         * @param value
         *     allowed object is
         *     {@link XMLGregorianCalendar }
         *     
         * @see #getEntryDate()
         */
        public void setEntryDate(XMLGregorianCalendar value) {
            this.entryDate = value;
        }

        /**
         * Date until which stay is authorised
         * 
         * @return
         *     possible object is
         *     {@link XMLGregorianCalendar }
         *     
         */
        public XMLGregorianCalendar getAuthorisedStayUntil() {
            return authorisedStayUntil;
        }

        /**
         * Imposta il valore della proprietà authorisedStayUntil.
         * 
         * @param value
         *     allowed object is
         *     {@link XMLGregorianCalendar }
         *     
         * @see #getAuthorisedStayUntil()
         */
        public void setAuthorisedStayUntil(XMLGregorianCalendar value) {
            this.authorisedStayUntil = value;
        }

        /**
         * Remaining number of authorised entries calculated on the basis of existing travel history
         * 
         * @return
         *     possible object is
         *     {@link BigInteger }
         *     
         */
        public BigInteger getRemainingNumberOfEntries() {
            return remainingNumberOfEntries;
        }

        /**
         * Imposta il valore della proprietà remainingNumberOfEntries.
         * 
         * @param value
         *     allowed object is
         *     {@link BigInteger }
         *     
         * @see #getRemainingNumberOfEntries()
         */
        public void setRemainingNumberOfEntries(BigInteger value) {
            this.remainingNumberOfEntries = value;
        }

        /**
         * Recupera il valore della proprietà authorisedDurationOfStay.
         * 
         * @return
         *     possible object is
         *     {@link Duration }
         *     
         */
        public Duration getAuthorisedDurationOfStay() {
            return authorisedDurationOfStay;
        }

        /**
         * Imposta il valore della proprietà authorisedDurationOfStay.
         * 
         * @param value
         *     allowed object is
         *     {@link Duration }
         *     
         */
        public void setAuthorisedDurationOfStay(Duration value) {
            this.authorisedDurationOfStay = value;
        }

    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="ExitDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
     *         <element name="AuthorisedStayUntil" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
     *         <choice>
     *           <element name="RemainingAuthorisedDurationOfStay" type="{http://www.w3.org/2001/XMLSchema}duration"/>
     *           <element name="DurationOfOverstay" type="{http://www.w3.org/2001/XMLSchema}duration"/>
     *         </choice>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "exitDate",
        "authorisedStayUntil",
        "remainingAuthorisedDurationOfStay",
        "durationOfOverstay"
    })
    public static class Exit {

        /**
         * Exit date for which authorised stay was calculated
         * 
         */
        @XmlElement(name = "ExitDate", required = true)
        @XmlSchemaType(name = "dateTime")
        protected XMLGregorianCalendar exitDate;
        /**
         * Date until which stay is authorised
         * 
         */
        @XmlElement(name = "AuthorisedStayUntil", required = true)
        @XmlSchemaType(name = "dateTime")
        protected XMLGregorianCalendar authorisedStayUntil;
        @XmlElement(name = "RemainingAuthorisedDurationOfStay")
        protected Duration remainingAuthorisedDurationOfStay;
        @XmlElement(name = "DurationOfOverstay")
        protected Duration durationOfOverstay;

        /**
         * Exit date for which authorised stay was calculated
         * 
         * @return
         *     possible object is
         *     {@link XMLGregorianCalendar }
         *     
         */
        public XMLGregorianCalendar getExitDate() {
            return exitDate;
        }

        /**
         * Imposta il valore della proprietà exitDate.
         * 
         * @param value
         *     allowed object is
         *     {@link XMLGregorianCalendar }
         *     
         * @see #getExitDate()
         */
        public void setExitDate(XMLGregorianCalendar value) {
            this.exitDate = value;
        }

        /**
         * Date until which stay is authorised
         * 
         * @return
         *     possible object is
         *     {@link XMLGregorianCalendar }
         *     
         */
        public XMLGregorianCalendar getAuthorisedStayUntil() {
            return authorisedStayUntil;
        }

        /**
         * Imposta il valore della proprietà authorisedStayUntil.
         * 
         * @param value
         *     allowed object is
         *     {@link XMLGregorianCalendar }
         *     
         * @see #getAuthorisedStayUntil()
         */
        public void setAuthorisedStayUntil(XMLGregorianCalendar value) {
            this.authorisedStayUntil = value;
        }

        /**
         * Recupera il valore della proprietà remainingAuthorisedDurationOfStay.
         * 
         * @return
         *     possible object is
         *     {@link Duration }
         *     
         */
        public Duration getRemainingAuthorisedDurationOfStay() {
            return remainingAuthorisedDurationOfStay;
        }

        /**
         * Imposta il valore della proprietà remainingAuthorisedDurationOfStay.
         * 
         * @param value
         *     allowed object is
         *     {@link Duration }
         *     
         */
        public void setRemainingAuthorisedDurationOfStay(Duration value) {
            this.remainingAuthorisedDurationOfStay = value;
        }

        /**
         * Recupera il valore della proprietà durationOfOverstay.
         * 
         * @return
         *     possible object is
         *     {@link Duration }
         *     
         */
        public Duration getDurationOfOverstay() {
            return durationOfOverstay;
        }

        /**
         * Imposta il valore della proprietà durationOfOverstay.
         * 
         * @param value
         *     allowed object is
         *     {@link Duration }
         *     
         */
        public void setDurationOfOverstay(Duration value) {
            this.durationOfOverstay = value;
        }

    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="ControlDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
     *         <element name="AuthorisedStayUntil" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
     *         <choice>
     *           <element name="RemainingAuthorisedDurationOfStay" type="{http://www.w3.org/2001/XMLSchema}duration"/>
     *           <element name="DurationOfOverstay" type="{http://www.w3.org/2001/XMLSchema}duration"/>
     *         </choice>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "controlDate",
        "authorisedStayUntil",
        "remainingAuthorisedDurationOfStay",
        "durationOfOverstay"
    })
    public static class Territory {

        /**
         * Entry date for which authorised stay was calculated
         * 
         */
        @XmlElement(name = "ControlDate", required = true)
        @XmlSchemaType(name = "dateTime")
        protected XMLGregorianCalendar controlDate;
        /**
         * Date until which stay is authorised
         * 
         */
        @XmlElement(name = "AuthorisedStayUntil", required = true)
        @XmlSchemaType(name = "dateTime")
        protected XMLGregorianCalendar authorisedStayUntil;
        @XmlElement(name = "RemainingAuthorisedDurationOfStay")
        protected Duration remainingAuthorisedDurationOfStay;
        @XmlElement(name = "DurationOfOverstay")
        protected Duration durationOfOverstay;

        /**
         * Entry date for which authorised stay was calculated
         * 
         * @return
         *     possible object is
         *     {@link XMLGregorianCalendar }
         *     
         */
        public XMLGregorianCalendar getControlDate() {
            return controlDate;
        }

        /**
         * Imposta il valore della proprietà controlDate.
         * 
         * @param value
         *     allowed object is
         *     {@link XMLGregorianCalendar }
         *     
         * @see #getControlDate()
         */
        public void setControlDate(XMLGregorianCalendar value) {
            this.controlDate = value;
        }

        /**
         * Date until which stay is authorised
         * 
         * @return
         *     possible object is
         *     {@link XMLGregorianCalendar }
         *     
         */
        public XMLGregorianCalendar getAuthorisedStayUntil() {
            return authorisedStayUntil;
        }

        /**
         * Imposta il valore della proprietà authorisedStayUntil.
         * 
         * @param value
         *     allowed object is
         *     {@link XMLGregorianCalendar }
         *     
         * @see #getAuthorisedStayUntil()
         */
        public void setAuthorisedStayUntil(XMLGregorianCalendar value) {
            this.authorisedStayUntil = value;
        }

        /**
         * Recupera il valore della proprietà remainingAuthorisedDurationOfStay.
         * 
         * @return
         *     possible object is
         *     {@link Duration }
         *     
         */
        public Duration getRemainingAuthorisedDurationOfStay() {
            return remainingAuthorisedDurationOfStay;
        }

        /**
         * Imposta il valore della proprietà remainingAuthorisedDurationOfStay.
         * 
         * @param value
         *     allowed object is
         *     {@link Duration }
         *     
         */
        public void setRemainingAuthorisedDurationOfStay(Duration value) {
            this.remainingAuthorisedDurationOfStay = value;
        }

        /**
         * Recupera il valore della proprietà durationOfOverstay.
         * 
         * @return
         *     possible object is
         *     {@link Duration }
         *     
         */
        public Duration getDurationOfOverstay() {
            return durationOfOverstay;
        }

        /**
         * Imposta il valore della proprietà durationOfOverstay.
         * 
         * @param value
         *     allowed object is
         *     {@link Duration }
         *     
         */
        public void setDurationOfOverstay(Duration value) {
            this.durationOfOverstay = value;
        }

    }

}
