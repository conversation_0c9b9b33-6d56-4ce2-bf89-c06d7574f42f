//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.ees.xsd.v1;

import javax.xml.datatype.XMLGregorianCalendar;
import eu.europa.schengen.etias.xsd.v1.TravelAuthorisationRequestType;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlType;


/**
 * Entry data - type for ChangeAuthorisation Offline mode (AuthorisationChange
 * 				without DurationOfStay.
 * 
 * <p>Classe Java per EntryRecordWithoutDurationOfStayAndVisaInformationType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="EntryRecordWithoutDurationOfStayAndVisaInformationType">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="BorderCrossingTimestamp" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         <element name="AuthorisedStayUntil" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         <element name="BorderCrossingPoint" type="{http://www.europa.eu/schengen/shared/xsd/v1}AuthorityUniqueIDType"/>
 *         <element name="NationalVisa" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
 *         <element name="AuthorisationChange" type="{http://www.europa.eu/schengen/ees/xsd/v1}AuthorisationChangeWithoutDurationOfStayAndVisaInformationType"/>
 *         <choice minOccurs="0">
 *           <element name="VisaInformation" type="{http://www.europa.eu/schengen/ees/xsd/v1}VisaInformationType"/>
 *           <element name="FTDInformation" type="{http://www.europa.eu/schengen/ees/xsd/v1}FTDInformationType"/>
 *           <element name="TravelAuthorisation" type="{http://www.europa.eu/schengen/etias/xsd/v1}TravelAuthorisationRequestType"/>
 *         </choice>
 *         <element name="PersonStatus" type="{http://www.europa.eu/schengen/shared/xsd/v1}CT71_PersonStatusType"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "EntryRecordWithoutDurationOfStayAndVisaInformationType", propOrder = {
    "borderCrossingTimestamp",
    "authorisedStayUntil",
    "borderCrossingPoint",
    "nationalVisa",
    "authorisationChange",
    "visaInformation",
    "ftdInformation",
    "travelAuthorisation",
    "personStatus"
})
public class EntryRecordWithoutDurationOfStayAndVisaInformationType {

    /**
     * The date and time of the entry.
     * 
     * 						[Article 16(2)(a)]
     * 
     * 						For visa-exempt third-country nationals, points (a), (b), (c) of Article 16(2)(a), points (a) and (b) of Article 16(3) and Article 16(4) shall apply mutatis mutandis.
     * 
     * 						[Article 17(2)]
     * 
     */
    @XmlElement(name = "BorderCrossingTimestamp", required = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar borderCrossingTimestamp;
    /**
     * The end date (and time) of the maximum duration of the stay as authorised by the short-stay visa, which shall be updated at each entry.
     * 
     * 						[Article 16(2)(d)]
     * 
     * 						In case of authorisation change this value will be updated with:
     * 
     * 						Where applicable, the new expiry date of the authorised stay or the visa.
     * 
     * 						[Article 19(1)(f)]
     * 
     */
    @XmlElement(name = "AuthorisedStayUntil", required = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar authorisedStayUntil;
    /**
     * The border crossing point.
     * 
     * 						The border crossing point of the entry and the authority that authorized the entry;
     * 
     * 						[Article 16(2)(b)]
     * 
     * 						For visa-exempt third-country nationals, points (a), (b), (c) of Article 16(2)(a), points (a) and (b) of Article 16(3) and Article 16(4) shall apply mutatis mutandis.
     * 
     * 						[Article 17(2)]
     * 
     * 						Without prejudice to Article 20 of this Regulation and Article 12(3) of Regulation (EU) 2016/399, where the short stay of a third-country national who is present on the territory of a Member State starts directly after a stay based on a residence permit or a long-stay visa and no previous individual file has been created. that third-country national may request the competent authorities referred to in Article 9(2) of this Regulation to create an individual file and an entry/exit record by entering the data referred to in Articles 16(1), (2) and (6) and 17(1) of this Regulation. Instead of the data referred to in point (a) of Article 16(2) of this Regulation, those competent authorities shall insert the date of the start of the short stay and, instead of the data referred to in point (b) of Article 16(2) of this Regulation, they shall insert the name of the authority that inserted those data.
     * 
     * 						[Article 14(8)]
     * 
     */
    @XmlElement(name = "BorderCrossingPoint", required = true)
    protected String borderCrossingPoint;
    /**
     * For the Member States which do not yet apply the Schengen acquis in full but operate the EES, a notification, where applicable, indicating that the third-country national used a national short-stay visa for the entry.
     * 
     * 						[Article 16(2)(g)]
     * 
     */
    @XmlElement(name = "NationalVisa")
    protected Boolean nationalVisa;
    @XmlElement(name = "AuthorisationChange", required = true)
    protected AuthorisationChangeWithoutDurationOfStayAndVisaInformationType authorisationChange;
    @XmlElement(name = "VisaInformation")
    protected VisaInformationType visaInformation;
    @XmlElement(name = "FTDInformation")
    protected FTDInformationType ftdInformation;
    @XmlElement(name = "TravelAuthorisation")
    protected TravelAuthorisationRequestType travelAuthorisation;
    /**
     * Code table value indicating third-country nationals for which calculator is not applicable - who:
     * 
     * 						i) is a member of the family of a Union citizen to whom Directive 2004/38/EC applies or of a national of a third country enjoying the right of free movement equivalent to that of Union citizens under an agreement between the Union and its Member States, on the one hand, and a third country, on the other; and
     * 						ii) does not hold a residence card pursuant to Directive 2004/38/EC or a residence permit pursuant to Regulation (EC) No 1030/2002.
     * 
     * 						[Article 2(4), Article 16(2)(c)]
     * 
     * 						For visa-exempt third-country nationals, points (a), (b) and (c) of Articles 16(2), points (a) and (b) of Article 16(3) and Article 16(4) shall apply mutatis mutandis.
     * 
     * 						[Article 17(2)]
     * 
     */
    @XmlElement(name = "PersonStatus", required = true)
    protected String personStatus;

    /**
     * The date and time of the entry.
     * 
     * 						[Article 16(2)(a)]
     * 
     * 						For visa-exempt third-country nationals, points (a), (b), (c) of Article 16(2)(a), points (a) and (b) of Article 16(3) and Article 16(4) shall apply mutatis mutandis.
     * 
     * 						[Article 17(2)]
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getBorderCrossingTimestamp() {
        return borderCrossingTimestamp;
    }

    /**
     * Imposta il valore della proprietà borderCrossingTimestamp.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     * @see #getBorderCrossingTimestamp()
     */
    public void setBorderCrossingTimestamp(XMLGregorianCalendar value) {
        this.borderCrossingTimestamp = value;
    }

    /**
     * The end date (and time) of the maximum duration of the stay as authorised by the short-stay visa, which shall be updated at each entry.
     * 
     * 						[Article 16(2)(d)]
     * 
     * 						In case of authorisation change this value will be updated with:
     * 
     * 						Where applicable, the new expiry date of the authorised stay or the visa.
     * 
     * 						[Article 19(1)(f)]
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getAuthorisedStayUntil() {
        return authorisedStayUntil;
    }

    /**
     * Imposta il valore della proprietà authorisedStayUntil.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     * @see #getAuthorisedStayUntil()
     */
    public void setAuthorisedStayUntil(XMLGregorianCalendar value) {
        this.authorisedStayUntil = value;
    }

    /**
     * The border crossing point.
     * 
     * 						The border crossing point of the entry and the authority that authorized the entry;
     * 
     * 						[Article 16(2)(b)]
     * 
     * 						For visa-exempt third-country nationals, points (a), (b), (c) of Article 16(2)(a), points (a) and (b) of Article 16(3) and Article 16(4) shall apply mutatis mutandis.
     * 
     * 						[Article 17(2)]
     * 
     * 						Without prejudice to Article 20 of this Regulation and Article 12(3) of Regulation (EU) 2016/399, where the short stay of a third-country national who is present on the territory of a Member State starts directly after a stay based on a residence permit or a long-stay visa and no previous individual file has been created. that third-country national may request the competent authorities referred to in Article 9(2) of this Regulation to create an individual file and an entry/exit record by entering the data referred to in Articles 16(1), (2) and (6) and 17(1) of this Regulation. Instead of the data referred to in point (a) of Article 16(2) of this Regulation, those competent authorities shall insert the date of the start of the short stay and, instead of the data referred to in point (b) of Article 16(2) of this Regulation, they shall insert the name of the authority that inserted those data.
     * 
     * 						[Article 14(8)]
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBorderCrossingPoint() {
        return borderCrossingPoint;
    }

    /**
     * Imposta il valore della proprietà borderCrossingPoint.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     * @see #getBorderCrossingPoint()
     */
    public void setBorderCrossingPoint(String value) {
        this.borderCrossingPoint = value;
    }

    /**
     * For the Member States which do not yet apply the Schengen acquis in full but operate the EES, a notification, where applicable, indicating that the third-country national used a national short-stay visa for the entry.
     * 
     * 						[Article 16(2)(g)]
     * 
     * @return
     *     possible object is
     *     {@link Boolean }
     *     
     */
    public Boolean isNationalVisa() {
        return nationalVisa;
    }

    /**
     * Imposta il valore della proprietà nationalVisa.
     * 
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *     
     * @see #isNationalVisa()
     */
    public void setNationalVisa(Boolean value) {
        this.nationalVisa = value;
    }

    /**
     * Recupera il valore della proprietà authorisationChange.
     * 
     * @return
     *     possible object is
     *     {@link AuthorisationChangeWithoutDurationOfStayAndVisaInformationType }
     *     
     */
    public AuthorisationChangeWithoutDurationOfStayAndVisaInformationType getAuthorisationChange() {
        return authorisationChange;
    }

    /**
     * Imposta il valore della proprietà authorisationChange.
     * 
     * @param value
     *     allowed object is
     *     {@link AuthorisationChangeWithoutDurationOfStayAndVisaInformationType }
     *     
     */
    public void setAuthorisationChange(AuthorisationChangeWithoutDurationOfStayAndVisaInformationType value) {
        this.authorisationChange = value;
    }

    /**
     * Recupera il valore della proprietà visaInformation.
     * 
     * @return
     *     possible object is
     *     {@link VisaInformationType }
     *     
     */
    public VisaInformationType getVisaInformation() {
        return visaInformation;
    }

    /**
     * Imposta il valore della proprietà visaInformation.
     * 
     * @param value
     *     allowed object is
     *     {@link VisaInformationType }
     *     
     */
    public void setVisaInformation(VisaInformationType value) {
        this.visaInformation = value;
    }

    /**
     * Recupera il valore della proprietà ftdInformation.
     * 
     * @return
     *     possible object is
     *     {@link FTDInformationType }
     *     
     */
    public FTDInformationType getFTDInformation() {
        return ftdInformation;
    }

    /**
     * Imposta il valore della proprietà ftdInformation.
     * 
     * @param value
     *     allowed object is
     *     {@link FTDInformationType }
     *     
     */
    public void setFTDInformation(FTDInformationType value) {
        this.ftdInformation = value;
    }

    /**
     * Recupera il valore della proprietà travelAuthorisation.
     * 
     * @return
     *     possible object is
     *     {@link TravelAuthorisationRequestType }
     *     
     */
    public TravelAuthorisationRequestType getTravelAuthorisation() {
        return travelAuthorisation;
    }

    /**
     * Imposta il valore della proprietà travelAuthorisation.
     * 
     * @param value
     *     allowed object is
     *     {@link TravelAuthorisationRequestType }
     *     
     */
    public void setTravelAuthorisation(TravelAuthorisationRequestType value) {
        this.travelAuthorisation = value;
    }

    /**
     * Code table value indicating third-country nationals for which calculator is not applicable - who:
     * 
     * 						i) is a member of the family of a Union citizen to whom Directive 2004/38/EC applies or of a national of a third country enjoying the right of free movement equivalent to that of Union citizens under an agreement between the Union and its Member States, on the one hand, and a third country, on the other; and
     * 						ii) does not hold a residence card pursuant to Directive 2004/38/EC or a residence permit pursuant to Regulation (EC) No 1030/2002.
     * 
     * 						[Article 2(4), Article 16(2)(c)]
     * 
     * 						For visa-exempt third-country nationals, points (a), (b) and (c) of Articles 16(2), points (a) and (b) of Article 16(3) and Article 16(4) shall apply mutatis mutandis.
     * 
     * 						[Article 17(2)]
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPersonStatus() {
        return personStatus;
    }

    /**
     * Imposta il valore della proprietà personStatus.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     * @see #getPersonStatus()
     */
    public void setPersonStatus(String value) {
        this.personStatus = value;
    }

}
