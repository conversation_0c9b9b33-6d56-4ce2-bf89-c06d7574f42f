//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.ees.xsd.v1;

import java.util.ArrayList;
import java.util.List;
import javax.xml.datatype.XMLGregorianCalendar;
import eu.europa.schengen.vis.xsd.v3.types.application.PlaceNewType;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlType;


/**
 * Data when authorisation for short stay is revoked, annulled or extended as per Article 19.
 * 
 * <p>Classe Java per AuthorisationChangeWithoutVisaType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="AuthorisationChangeWithoutVisaType">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="Status" type="{http://www.europa.eu/schengen/ees/xsd/v1}CT505_StatusType"/>
 *         <element name="DecisionAuthority" type="{http://www.europa.eu/schengen/shared/xsd/v1}AuthorityUniqueIDType"/>
 *         <element name="DecisionPlace" type="{http://www.europa.eu/schengen/vis/xsd/v3/types/Application}PlaceNewType"/>
 *         <element name="DecisionDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         <element name="DurationOfStay" type="{http://www.europa.eu/schengen/shared/xsd/v1}DurationType" minOccurs="0"/>
 *         <element name="ChangeGround" type="{http://www.europa.eu/schengen/ees/xsd/v1}CT501_ChangeGroundType" maxOccurs="unbounded"/>
 *         <element name="ChangeOtherGround" type="{http://www.europa.eu/schengen/shared/xsd/v1}RawValueType" minOccurs="0"/>
 *         <element name="BilateralAgreement" type="{http://www.europa.eu/schengen/ees/xsd/v1}CT510_BilateralAgreementType" minOccurs="0"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "AuthorisationChangeWithoutVisaType", propOrder = {
    "status",
    "decisionAuthority",
    "decisionPlace",
    "decisionDate",
    "durationOfStay",
    "changeGround",
    "changeOtherGround",
    "bilateralAgreement"
})
public class AuthorisationChangeWithoutVisaType {

    /**
     * Code table value indicating the status information indicating that the authorisation for short stay or the visa has been changed. The status information indicating that the authorisation for short stay or the visa has been revoked or annulled or that the duration of the authorised stay or the visa has been extended.
     * 
     * 						[Article 19(1)(a)]
     * 
     */
    @XmlElement(name = "Status", required = true)
    protected String status;
    /**
     * The identity of the authority that revoked or annulled the authorisation for short stay or the visa or extended the duration of the authorised stay or the visa;
     * 
     * 						[Article 19(1)(b)]
     * 
     */
    @XmlElement(name = "DecisionAuthority", required = true)
    protected String decisionAuthority;
    /**
     * The place and date of the decision to revoke or annul the authorisation for short stay or the visa or to extend the duration of the authorised stay or the visa;
     * 
     * 						[Article 19(1)(c)]
     * 
     */
    @XmlElement(name = "DecisionPlace", required = true)
    protected PlaceNewType decisionPlace;
    /**
     * The place and date of the decision to revoke or annul the authorisation for short stay or the visa or to extend the duration of the authorised stay or the visa;
     * 
     * 						[Article 19(1)(c)]
     * 
     */
    @XmlElement(name = "DecisionDate", required = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar decisionDate;
    /**
     * Where applicable, the period of the extension of the duration of authorised stay;
     * 
     * 						[Article 19(1)(e)]
     * 
     */
    @XmlElement(name = "DurationOfStay")
    @XmlSchemaType(name = "nonNegativeInteger")
    protected Integer durationOfStay;
    /**
     * Code table indicating grounds for authorisation change.
     * 
     * 						The entry/exit record shall indicate the grounds for revocation or annulment of the short stay, which shall be:
     * 						(a) a return decision adopted pursuant to Directive 2008/115/EC of the European Parliament of the Council;
     * 						(b) any other decision taken by the competent authorities of the Member State, in accordance with national law, resulting in the return, removal or voluntary departure of a third-country national who does not fulfil or no longer fulfils the conditions for the entry into or for the stay on the territory of the Member States.
     * 						(...)
     * 						The entry/exit record shall indicate the grounds for extending the duration of an authorised stay.
     * 
     * 						[Article 19(4)-(5)]
     * 
     */
    @XmlElement(name = "ChangeGround", required = true)
    protected List<String> changeGround;
    /**
     * Free text field in case the value selected for Ground is "Other".
     * 
     * 						The entry/exit record shall indicate the grounds for revocation or annulment of the short stay, which shall be:
     * 						(a) a return decision adopted pursuant to Directive 2008/115/EC of the European Parliament of the Council;
     * 						(b) any other decision taken by the competent authorities of the Member State, in accordance with national law, resulting in the return, removal or voluntary departure of a third-country national who does not fulfil or no longer fulfils the conditions for the entry into or for the stay on the territory of the Member States.
     * 						(...)
     * 						The entry/exit record shall indicate the grounds for extending the duration of an authorised stay.
     * 
     * 						[Article 19(4)-(5)]
     * 
     */
    @XmlElement(name = "ChangeOtherGround")
    protected String changeOtherGround;
    @XmlElement(name = "BilateralAgreement")
    protected String bilateralAgreement;

    /**
     * Code table value indicating the status information indicating that the authorisation for short stay or the visa has been changed. The status information indicating that the authorisation for short stay or the visa has been revoked or annulled or that the duration of the authorised stay or the visa has been extended.
     * 
     * 						[Article 19(1)(a)]
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getStatus() {
        return status;
    }

    /**
     * Imposta il valore della proprietà status.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     * @see #getStatus()
     */
    public void setStatus(String value) {
        this.status = value;
    }

    /**
     * The identity of the authority that revoked or annulled the authorisation for short stay or the visa or extended the duration of the authorised stay or the visa;
     * 
     * 						[Article 19(1)(b)]
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDecisionAuthority() {
        return decisionAuthority;
    }

    /**
     * Imposta il valore della proprietà decisionAuthority.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     * @see #getDecisionAuthority()
     */
    public void setDecisionAuthority(String value) {
        this.decisionAuthority = value;
    }

    /**
     * The place and date of the decision to revoke or annul the authorisation for short stay or the visa or to extend the duration of the authorised stay or the visa;
     * 
     * 						[Article 19(1)(c)]
     * 
     * @return
     *     possible object is
     *     {@link PlaceNewType }
     *     
     */
    public PlaceNewType getDecisionPlace() {
        return decisionPlace;
    }

    /**
     * Imposta il valore della proprietà decisionPlace.
     * 
     * @param value
     *     allowed object is
     *     {@link PlaceNewType }
     *     
     * @see #getDecisionPlace()
     */
    public void setDecisionPlace(PlaceNewType value) {
        this.decisionPlace = value;
    }

    /**
     * The place and date of the decision to revoke or annul the authorisation for short stay or the visa or to extend the duration of the authorised stay or the visa;
     * 
     * 						[Article 19(1)(c)]
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getDecisionDate() {
        return decisionDate;
    }

    /**
     * Imposta il valore della proprietà decisionDate.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     * @see #getDecisionDate()
     */
    public void setDecisionDate(XMLGregorianCalendar value) {
        this.decisionDate = value;
    }

    /**
     * Where applicable, the period of the extension of the duration of authorised stay;
     * 
     * 						[Article 19(1)(e)]
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getDurationOfStay() {
        return durationOfStay;
    }

    /**
     * Imposta il valore della proprietà durationOfStay.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     * @see #getDurationOfStay()
     */
    public void setDurationOfStay(Integer value) {
        this.durationOfStay = value;
    }

    /**
     * Code table indicating grounds for authorisation change.
     * 
     * 						The entry/exit record shall indicate the grounds for revocation or annulment of the short stay, which shall be:
     * 						(a) a return decision adopted pursuant to Directive 2008/115/EC of the European Parliament of the Council;
     * 						(b) any other decision taken by the competent authorities of the Member State, in accordance with national law, resulting in the return, removal or voluntary departure of a third-country national who does not fulfil or no longer fulfils the conditions for the entry into or for the stay on the territory of the Member States.
     * 						(...)
     * 						The entry/exit record shall indicate the grounds for extending the duration of an authorised stay.
     * 
     * 						[Article 19(4)-(5)]
     * 
     * Gets the value of the changeGround property.
     * 
     * <p>This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the changeGround property.</p>
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * </p>
     * <pre>
     * getChangeGround().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link String }
     * </p>
     * 
     * 
     * @return
     *     The value of the changeGround property.
     */
    public List<String> getChangeGround() {
        if (changeGround == null) {
            changeGround = new ArrayList<>();
        }
        return this.changeGround;
    }

    /**
     * Free text field in case the value selected for Ground is "Other".
     * 
     * 						The entry/exit record shall indicate the grounds for revocation or annulment of the short stay, which shall be:
     * 						(a) a return decision adopted pursuant to Directive 2008/115/EC of the European Parliament of the Council;
     * 						(b) any other decision taken by the competent authorities of the Member State, in accordance with national law, resulting in the return, removal or voluntary departure of a third-country national who does not fulfil or no longer fulfils the conditions for the entry into or for the stay on the territory of the Member States.
     * 						(...)
     * 						The entry/exit record shall indicate the grounds for extending the duration of an authorised stay.
     * 
     * 						[Article 19(4)-(5)]
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getChangeOtherGround() {
        return changeOtherGround;
    }

    /**
     * Imposta il valore della proprietà changeOtherGround.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     * @see #getChangeOtherGround()
     */
    public void setChangeOtherGround(String value) {
        this.changeOtherGround = value;
    }

    /**
     * Recupera il valore della proprietà bilateralAgreement.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBilateralAgreement() {
        return bilateralAgreement;
    }

    /**
     * Imposta il valore della proprietà bilateralAgreement.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBilateralAgreement(String value) {
        this.bilateralAgreement = value;
    }

}
