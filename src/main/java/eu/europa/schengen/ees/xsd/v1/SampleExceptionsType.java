//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.ees.xsd.v1;

import java.util.ArrayList;
import java.util.List;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per SampleExceptionsType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="SampleExceptionsType">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="SampleError" type="{http://www.europa.eu/schengen/shared/xsd/v1}ST14_ErrorCodeType" minOccurs="0"/>
 *         <element name="SampleWarning" type="{http://www.europa.eu/schengen/shared/xsd/v1}ST15_WarningCodeType" maxOccurs="unbounded" minOccurs="0"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SampleExceptionsType", propOrder = {
    "sampleError",
    "sampleWarning"
})
public class SampleExceptionsType {

    @XmlElement(name = "SampleError")
    protected String sampleError;
    @XmlElement(name = "SampleWarning")
    protected List<String> sampleWarning;

    /**
     * Recupera il valore della proprietà sampleError.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSampleError() {
        return sampleError;
    }

    /**
     * Imposta il valore della proprietà sampleError.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSampleError(String value) {
        this.sampleError = value;
    }

    /**
     * Gets the value of the sampleWarning property.
     * 
     * <p>This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the sampleWarning property.</p>
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * </p>
     * <pre>
     * getSampleWarning().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link String }
     * </p>
     * 
     * 
     * @return
     *     The value of the sampleWarning property.
     */
    public List<String> getSampleWarning() {
        if (sampleWarning == null) {
            sampleWarning = new ArrayList<>();
        }
        return this.sampleWarning;
    }

}
