//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.ees.xsd.v1;

import java.util.ArrayList;
import java.util.List;
import javax.xml.datatype.XMLGregorianCalendar;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlType;


/**
 * Facial image - type for the response.
 * 
 * <p>Classe Java per FIWithUpdatesLogType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="FIWithUpdatesLogType">
 *   <complexContent>
 *     <extension base="{http://www.europa.eu/schengen/ees/xsd/v1}FIBaseType">
 *       <sequence>
 *         <element name="NISTFile" type="{http://www.europa.eu/schengen/ees/xsd/v1}IdentifierType" minOccurs="0"/>
 *         <group ref="{http://www.europa.eu/schengen/ees/xsd/v1}UpdatesLogAuditGroup"/>
 *         <element name="UpdatesLog" maxOccurs="unbounded" minOccurs="0">
 *           <complexType>
 *             <complexContent>
 *               <extension base="{http://www.europa.eu/schengen/ees/xsd/v1}FIBaseType">
 *                 <sequence>
 *                   <group ref="{http://www.europa.eu/schengen/ees/xsd/v1}UpdatesLogAuditGroup"/>
 *                 </sequence>
 *               </extension>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *       </sequence>
 *     </extension>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "FIWithUpdatesLogType", propOrder = {
    "nistFile",
    "responsible",
    "timestamp",
    "flags",
    "updatesLog"
})
public class FIWithUpdatesLogType
    extends FIBaseType
{

    /**
     * This identifier can be used to retrieve the NIST file using the HTTP/GET RetrieveBinary operation
     * 
     */
    @XmlElement(name = "NISTFile")
    @XmlSchemaType(name = "anyURI")
    protected String nistFile;
    @XmlElement(name = "Responsible", required = true)
    protected ResponsibleType responsible;
    @XmlElement(name = "Timestamp", required = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar timestamp;
    @XmlElement(name = "Flags")
    protected FlagsType flags;
    @XmlElement(name = "UpdatesLog")
    protected List<FIWithUpdatesLogType.UpdatesLog> updatesLog;

    /**
     * This identifier can be used to retrieve the NIST file using the HTTP/GET RetrieveBinary operation
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNISTFile() {
        return nistFile;
    }

    /**
     * Imposta il valore della proprietà nistFile.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     * @see #getNISTFile()
     */
    public void setNISTFile(String value) {
        this.nistFile = value;
    }

    /**
     * Recupera il valore della proprietà responsible.
     * 
     * @return
     *     possible object is
     *     {@link ResponsibleType }
     *     
     */
    public ResponsibleType getResponsible() {
        return responsible;
    }

    /**
     * Imposta il valore della proprietà responsible.
     * 
     * @param value
     *     allowed object is
     *     {@link ResponsibleType }
     *     
     */
    public void setResponsible(ResponsibleType value) {
        this.responsible = value;
    }

    /**
     * Recupera il valore della proprietà timestamp.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getTimestamp() {
        return timestamp;
    }

    /**
     * Imposta il valore della proprietà timestamp.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setTimestamp(XMLGregorianCalendar value) {
        this.timestamp = value;
    }

    /**
     * Recupera il valore della proprietà flags.
     * 
     * @return
     *     possible object is
     *     {@link FlagsType }
     *     
     */
    public FlagsType getFlags() {
        return flags;
    }

    /**
     * Imposta il valore della proprietà flags.
     * 
     * @param value
     *     allowed object is
     *     {@link FlagsType }
     *     
     */
    public void setFlags(FlagsType value) {
        this.flags = value;
    }

    /**
     * Gets the value of the updatesLog property.
     * 
     * <p>This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the updatesLog property.</p>
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * </p>
     * <pre>
     * getUpdatesLog().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link FIWithUpdatesLogType.UpdatesLog }
     * </p>
     * 
     * 
     * @return
     *     The value of the updatesLog property.
     */
    public List<FIWithUpdatesLogType.UpdatesLog> getUpdatesLog() {
        if (updatesLog == null) {
            updatesLog = new ArrayList<>();
        }
        return this.updatesLog;
    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <extension base="{http://www.europa.eu/schengen/ees/xsd/v1}FIBaseType">
     *       <sequence>
     *         <group ref="{http://www.europa.eu/schengen/ees/xsd/v1}UpdatesLogAuditGroup"/>
     *       </sequence>
     *     </extension>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "responsible",
        "timestamp",
        "flags"
    })
    public static class UpdatesLog
        extends FIBaseType
    {

        @XmlElement(name = "Responsible", required = true)
        protected ResponsibleType responsible;
        @XmlElement(name = "Timestamp", required = true)
        @XmlSchemaType(name = "dateTime")
        protected XMLGregorianCalendar timestamp;
        @XmlElement(name = "Flags")
        protected FlagsType flags;

        /**
         * Recupera il valore della proprietà responsible.
         * 
         * @return
         *     possible object is
         *     {@link ResponsibleType }
         *     
         */
        public ResponsibleType getResponsible() {
            return responsible;
        }

        /**
         * Imposta il valore della proprietà responsible.
         * 
         * @param value
         *     allowed object is
         *     {@link ResponsibleType }
         *     
         */
        public void setResponsible(ResponsibleType value) {
            this.responsible = value;
        }

        /**
         * Recupera il valore della proprietà timestamp.
         * 
         * @return
         *     possible object is
         *     {@link XMLGregorianCalendar }
         *     
         */
        public XMLGregorianCalendar getTimestamp() {
            return timestamp;
        }

        /**
         * Imposta il valore della proprietà timestamp.
         * 
         * @param value
         *     allowed object is
         *     {@link XMLGregorianCalendar }
         *     
         */
        public void setTimestamp(XMLGregorianCalendar value) {
            this.timestamp = value;
        }

        /**
         * Recupera il valore della proprietà flags.
         * 
         * @return
         *     possible object is
         *     {@link FlagsType }
         *     
         */
        public FlagsType getFlags() {
            return flags;
        }

        /**
         * Imposta il valore della proprietà flags.
         * 
         * @param value
         *     allowed object is
         *     {@link FlagsType }
         *     
         */
        public void setFlags(FlagsType value) {
            this.flags = value;
        }

    }

}
