//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.ees.xsd.v1;

import javax.xml.datatype.XMLGregorianCalendar;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlSeeAlso;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per HeaderBaseType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="HeaderBaseType">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="TransactionID" type="{http://www.europa.eu/schengen/ees/xsd/v1}TransactionIDType"/>
 *         <element name="Variant" type="{http://www.europa.eu/schengen/ees/xsd/v1}ST504_OperationVariantType"/>
 *         <element name="SystemID" type="{http://www.europa.eu/schengen/shared/xsd/v1}SystemIDType"/>
 *         <element name="Timestamp" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         <element name="EndUserID" type="{http://www.europa.eu/schengen/shared/xsd/v1}EndUserIDType" minOccurs="0"/>
 *         <element name="TestID" type="{http://www.europa.eu/schengen/ees/xsd/v1}TestCaseIDType" minOccurs="0"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "HeaderBaseType", propOrder = {
    "transactionID",
    "variant",
    "systemID",
    "timestamp",
    "endUserID",
    "testID"
})
@XmlSeeAlso({
    eu.europa.schengen.ees_ns.xsd.v1.HeaderResponseType.class,
    eu.europa.schengen.ees_ns.xsd.v1.HeaderRequestType.class,
    eu.europa.schengen.cs_vis_nui_ees.xsd.v1.HeaderRequestType.class,
    eu.europa.schengen.cs_vis_nui_ees.xsd.v1.HeaderResponseType.class
})
public class HeaderBaseType {

    /**
     * The transaction id field used for 2 purposes in EES.
     *                         It must filled by the NS with a unique identifier for the operation call.
     *                         First, for write operations and workflow engine operations, this transaction id is used to
     *                         identify retries of the same transaction and implements the idempotent.  It means that for some
     *                         technical reason, the NS retries the same exact operation call for a given transaction, it must
     *                         the use the same transaction id.
     *                         Additionally, this transaction id is copied in the header of the response sent to the NS which,
     *                         in the case of asynchronous operations, allows the NS to correlate the response message with its
     *                         initial request.
     * 
     */
    @XmlElement(name = "TransactionID", required = true)
    protected String transactionID;
    @XmlElement(name = "Variant", required = true)
    protected String variant;
    /**
     * Identification of the system that sends the message.
     * 
     */
    @XmlElement(name = "SystemID", required = true)
    protected String systemID;
    /**
     * Message creation timestamp. Field to be populated by the system sending the request and to be logged independently from message reception time.
     * 
     */
    @XmlElement(name = "Timestamp", required = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar timestamp;
    /**
     * The EndUser is the application or officer acting for a User. The EES does not manage EndUsers. It is the responsibility of the User to authenticate its EndUser. An EndUserID is present in the message header for User usage only. It is User responsibility to provide the appropriate EndUserID.
     * 
     */
    @XmlElement(name = "EndUserID")
    protected String endUserID;
    @XmlElement(name = "TestID")
    protected String testID;

    /**
     * The transaction id field used for 2 purposes in EES.
     *                         It must filled by the NS with a unique identifier for the operation call.
     *                         First, for write operations and workflow engine operations, this transaction id is used to
     *                         identify retries of the same transaction and implements the idempotent.  It means that for some
     *                         technical reason, the NS retries the same exact operation call for a given transaction, it must
     *                         the use the same transaction id.
     *                         Additionally, this transaction id is copied in the header of the response sent to the NS which,
     *                         in the case of asynchronous operations, allows the NS to correlate the response message with its
     *                         initial request.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTransactionID() {
        return transactionID;
    }

    /**
     * Imposta il valore della proprietà transactionID.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     * @see #getTransactionID()
     */
    public void setTransactionID(String value) {
        this.transactionID = value;
    }

    /**
     * Recupera il valore della proprietà variant.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getVariant() {
        return variant;
    }

    /**
     * Imposta il valore della proprietà variant.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setVariant(String value) {
        this.variant = value;
    }

    /**
     * Identification of the system that sends the message.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSystemID() {
        return systemID;
    }

    /**
     * Imposta il valore della proprietà systemID.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     * @see #getSystemID()
     */
    public void setSystemID(String value) {
        this.systemID = value;
    }

    /**
     * Message creation timestamp. Field to be populated by the system sending the request and to be logged independently from message reception time.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getTimestamp() {
        return timestamp;
    }

    /**
     * Imposta il valore della proprietà timestamp.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     * @see #getTimestamp()
     */
    public void setTimestamp(XMLGregorianCalendar value) {
        this.timestamp = value;
    }

    /**
     * The EndUser is the application or officer acting for a User. The EES does not manage EndUsers. It is the responsibility of the User to authenticate its EndUser. An EndUserID is present in the message header for User usage only. It is User responsibility to provide the appropriate EndUserID.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEndUserID() {
        return endUserID;
    }

    /**
     * Imposta il valore della proprietà endUserID.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     * @see #getEndUserID()
     */
    public void setEndUserID(String value) {
        this.endUserID = value;
    }

    /**
     * Recupera il valore della proprietà testID.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTestID() {
        return testID;
    }

    /**
     * Imposta il valore della proprietà testID.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTestID(String value) {
        this.testID = value;
    }

}
