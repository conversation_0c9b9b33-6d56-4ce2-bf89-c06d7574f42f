//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package eu.europa.schengen.ees.xsd.v1;

import java.util.ArrayList;
import java.util.List;
import javax.xml.datatype.XMLGregorianCalendar;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlSeeAlso;
import jakarta.xml.bind.annotation.XmlType;


/**
 * TravellerFile grouping all TCN data - basic type for the response.
 * 
 * <p>Classe Java per TravellerFileWithHistoryType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="TravellerFileWithHistoryType">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="TravelDocument" type="{http://www.europa.eu/schengen/ees/xsd/v1}TravelDocumentWithUpdatesLogType" maxOccurs="unbounded"/>
 *         <element name="FI" type="{http://www.europa.eu/schengen/ees/xsd/v1}FIWithUpdatesLogType" minOccurs="0"/>
 *         <element name="FP" type="{http://www.europa.eu/schengen/ees/xsd/v1}FPWithUpdatesLogType" minOccurs="0"/>
 *         <element name="NFP" type="{http://www.europa.eu/schengen/ees/xsd/v1}NFPWithUpdatesLogType" maxOccurs="unbounded" minOccurs="0"/>
 *         <element name="EntryRecord" type="{http://www.europa.eu/schengen/ees/xsd/v1}EntryRecordWithUpdatesLogType" maxOccurs="unbounded" minOccurs="0"/>
 *         <element name="ExitRecord" type="{http://www.europa.eu/schengen/ees/xsd/v1}ExitRecordWithUpdatesLogType" maxOccurs="unbounded" minOccurs="0"/>
 *         <element name="RefusalRecord" type="{http://www.europa.eu/schengen/ees/xsd/v1}RefusalRecordWithUpdatesLogType" maxOccurs="unbounded" minOccurs="0"/>
 *         <element name="TravellerFileID" type="{http://www.europa.eu/schengen/ees/xsd/v1}IdentifierType"/>
 *         <group ref="{http://www.europa.eu/schengen/ees/xsd/v1}UpdatesLogAuditGroup"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "TravellerFileWithHistoryType", propOrder = {
    "travelDocument",
    "fi",
    "fp",
    "nfp",
    "entryRecord",
    "exitRecord",
    "refusalRecord",
    "travellerFileID",
    "responsible",
    "timestamp",
    "flags"
})
@XmlSeeAlso({
    eu.europa.schengen.ees.xsd.v1.RetrieveTravellerFileResponseMessageType.Response.TravellerFile.class,
    TravellerFileSearchResponseType.class,
    TravellerFileSearchResponse2Type.class
})
public class TravellerFileWithHistoryType {

    @XmlElement(name = "TravelDocument", required = true)
    protected List<TravelDocumentWithUpdatesLogType> travelDocument;
    @XmlElement(name = "FI")
    protected FIWithUpdatesLogType fi;
    @XmlElement(name = "FP")
    protected FPWithUpdatesLogType fp;
    @XmlElement(name = "NFP")
    protected List<NFPWithUpdatesLogType> nfp;
    @XmlElement(name = "EntryRecord")
    protected List<EntryRecordWithUpdatesLogType> entryRecord;
    @XmlElement(name = "ExitRecord")
    protected List<ExitRecordWithUpdatesLogType> exitRecord;
    @XmlElement(name = "RefusalRecord")
    protected List<RefusalRecordWithUpdatesLogType> refusalRecord;
    @XmlElement(name = "TravellerFileID", required = true)
    @XmlSchemaType(name = "anyURI")
    protected String travellerFileID;
    @XmlElement(name = "Responsible", required = true)
    protected ResponsibleType responsible;
    @XmlElement(name = "Timestamp", required = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar timestamp;
    @XmlElement(name = "Flags")
    protected FlagsType flags;

    /**
     * Gets the value of the travelDocument property.
     * 
     * <p>This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the travelDocument property.</p>
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * </p>
     * <pre>
     * getTravelDocument().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link TravelDocumentWithUpdatesLogType }
     * </p>
     * 
     * 
     * @return
     *     The value of the travelDocument property.
     */
    public List<TravelDocumentWithUpdatesLogType> getTravelDocument() {
        if (travelDocument == null) {
            travelDocument = new ArrayList<>();
        }
        return this.travelDocument;
    }

    /**
     * Recupera il valore della proprietà fi.
     * 
     * @return
     *     possible object is
     *     {@link FIWithUpdatesLogType }
     *     
     */
    public FIWithUpdatesLogType getFI() {
        return fi;
    }

    /**
     * Imposta il valore della proprietà fi.
     * 
     * @param value
     *     allowed object is
     *     {@link FIWithUpdatesLogType }
     *     
     */
    public void setFI(FIWithUpdatesLogType value) {
        this.fi = value;
    }

    /**
     * Recupera il valore della proprietà fp.
     * 
     * @return
     *     possible object is
     *     {@link FPWithUpdatesLogType }
     *     
     */
    public FPWithUpdatesLogType getFP() {
        return fp;
    }

    /**
     * Imposta il valore della proprietà fp.
     * 
     * @param value
     *     allowed object is
     *     {@link FPWithUpdatesLogType }
     *     
     */
    public void setFP(FPWithUpdatesLogType value) {
        this.fp = value;
    }

    /**
     * Gets the value of the nfp property.
     * 
     * <p>This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the nfp property.</p>
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * </p>
     * <pre>
     * getNFP().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link NFPWithUpdatesLogType }
     * </p>
     * 
     * 
     * @return
     *     The value of the nfp property.
     */
    public List<NFPWithUpdatesLogType> getNFP() {
        if (nfp == null) {
            nfp = new ArrayList<>();
        }
        return this.nfp;
    }

    /**
     * Gets the value of the entryRecord property.
     * 
     * <p>This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the entryRecord property.</p>
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * </p>
     * <pre>
     * getEntryRecord().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link EntryRecordWithUpdatesLogType }
     * </p>
     * 
     * 
     * @return
     *     The value of the entryRecord property.
     */
    public List<EntryRecordWithUpdatesLogType> getEntryRecord() {
        if (entryRecord == null) {
            entryRecord = new ArrayList<>();
        }
        return this.entryRecord;
    }

    /**
     * Gets the value of the exitRecord property.
     * 
     * <p>This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the exitRecord property.</p>
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * </p>
     * <pre>
     * getExitRecord().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link ExitRecordWithUpdatesLogType }
     * </p>
     * 
     * 
     * @return
     *     The value of the exitRecord property.
     */
    public List<ExitRecordWithUpdatesLogType> getExitRecord() {
        if (exitRecord == null) {
            exitRecord = new ArrayList<>();
        }
        return this.exitRecord;
    }

    /**
     * Gets the value of the refusalRecord property.
     * 
     * <p>This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the refusalRecord property.</p>
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * </p>
     * <pre>
     * getRefusalRecord().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link RefusalRecordWithUpdatesLogType }
     * </p>
     * 
     * 
     * @return
     *     The value of the refusalRecord property.
     */
    public List<RefusalRecordWithUpdatesLogType> getRefusalRecord() {
        if (refusalRecord == null) {
            refusalRecord = new ArrayList<>();
        }
        return this.refusalRecord;
    }

    /**
     * Recupera il valore della proprietà travellerFileID.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTravellerFileID() {
        return travellerFileID;
    }

    /**
     * Imposta il valore della proprietà travellerFileID.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTravellerFileID(String value) {
        this.travellerFileID = value;
    }

    /**
     * Recupera il valore della proprietà responsible.
     * 
     * @return
     *     possible object is
     *     {@link ResponsibleType }
     *     
     */
    public ResponsibleType getResponsible() {
        return responsible;
    }

    /**
     * Imposta il valore della proprietà responsible.
     * 
     * @param value
     *     allowed object is
     *     {@link ResponsibleType }
     *     
     */
    public void setResponsible(ResponsibleType value) {
        this.responsible = value;
    }

    /**
     * Recupera il valore della proprietà timestamp.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getTimestamp() {
        return timestamp;
    }

    /**
     * Imposta il valore della proprietà timestamp.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setTimestamp(XMLGregorianCalendar value) {
        this.timestamp = value;
    }

    /**
     * Recupera il valore della proprietà flags.
     * 
     * @return
     *     possible object is
     *     {@link FlagsType }
     *     
     */
    public FlagsType getFlags() {
        return flags;
    }

    /**
     * Imposta il valore della proprietà flags.
     * 
     * @param value
     *     allowed object is
     *     {@link FlagsType }
     *     
     */
    public void setFlags(FlagsType value) {
        this.flags = value;
    }

}
