package com.reco.ees.gateway.service;

import com.reco.ees.gateway.controller.PreFileController;
import com.reco.ees.gateway.enums.*;
import com.reco.ees.gateway.generated.sif.response.SifHeaderType;
import com.reco.ees.gateway.kafka.model.AbcEgateMessage;
import com.reco.ees.gateway.mapper.DossierMapper;
import com.reco.ees.gateway.repository.*;
import com.reco.ees.gateway.repository.model.*;
import com.reco.ees.gateway.util.SoapUtils;
import eu.europa.schengen.ees.xsd.v1.*;
import eu.europa.schengen.ees_ns.xsd.v1.*;
import eu.europa.schengen.ees_ns.xsd.v1.ObjectFactory;
import eu.europa.schengen.vis.xsd.v3.types.application.PlaceNewType;
import eu.europa.schengen.vis.xsd.v3.types.application.SourceValueType;
import eu.europa.schengen.vis.xsd.v3.types.application.TransType;
import eu.europa.schengen.vis.xsd.v3.types.application.VisaTypeType;
import eu.europa.schengen.vis.xsd.v3.types.common.PeriodType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.oxm.jaxb.Jaxb2Marshaller;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;

import javax.xml.datatype.DatatypeConfigurationException;
import javax.xml.datatype.DatatypeConstants;
import javax.xml.datatype.DatatypeFactory;
import javax.xml.datatype.XMLGregorianCalendar;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import javax.xml.transform.stream.StreamSource;
import java.io.StringReader;
import java.io.StringWriter;
import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.concurrent.ThreadLocalRandom;

@Slf4j
@Service
public class DossierService {
    @Value("${application.name}")
    private String applicationNameProperty;
    @Value("${is.delete.sensitive.data.active:true}")
    private boolean isDeleteSensitiveDataActive;

    private final DossierMapper dossierMapper;
    public final CopyOnWriteArraySet<String> kioskIdsDoingRunAndWaitSifLoginForSpecificKiosks = new CopyOnWriteArraySet<>();
    private final Environment environment;
    private final LogService logService;
    private final KioskRepository kioskRepository;
    private final DossierRepository dossierRepository;
    private final SifGenericCodeRepository sifGenericCodeRepository;
    private final SifParametersRepository sifParametersRepository;
    private final SifAddDataRequestRepository sifAddDataRequestRepository;
    private final SifCountryCodeRepository sifCountryCodeRepository;
    private final SifAuthService sifAuthService;
    private final DynamicSchedulingService dynamicSchedulingService;
    private final SifAuthRepository sifAuthRepository;
    private final AutoScheduledJobsLockRepository autoScheduledJobsLockRepository;

    public DossierService(DossierMapper dossierMapper, Environment environment, LogService logService, KioskRepository kioskRepository, DossierRepository dossierRepository, SifGenericCodeRepository sifGenericCodeRepository, SifParametersRepository sifParametersRepository, SifAddDataRequestRepository sifAddDataRequestRepository, SifCountryCodeRepository sifCountryCodeRepository, SifAuthService sifAuthService, DynamicSchedulingService dynamicSchedulingService, SifAuthRepository sifAuthRepository, AutoScheduledJobsLockRepository autoScheduledJobsLockRepository) {
        this.dossierMapper = dossierMapper;
        this.environment = environment;
        this.logService = logService;
        this.kioskRepository = kioskRepository;
        this.dossierRepository = dossierRepository;
        this.sifGenericCodeRepository = sifGenericCodeRepository;
        this.sifParametersRepository = sifParametersRepository;
        this.sifAddDataRequestRepository = sifAddDataRequestRepository;
        this.sifCountryCodeRepository = sifCountryCodeRepository;
        this.sifAuthService = sifAuthService;
        this.dynamicSchedulingService = dynamicSchedulingService;
        this.sifAuthRepository = sifAuthRepository;
        this.autoScheduledJobsLockRepository = autoScheduledJobsLockRepository;
    }

    /*@Transactional
    public void resetConcurrency(String idMessage) {
        dossierRepository.findById(idMessage).ifPresent(dossierRepository::delete);
    }*/

    // GET DEI DOSSIER PROCESSATI DA QUESTO GATEWAY E PER CUI NON ABBIAMO GIA' SUPERATO I LIMITI DI CONTATTO IMPOSTI DAL SIF
    public List<Dossier> getDossiersToBeProcessedByDossierResponseJob(String application, List<String> statuses, Integer maxRetries, Date date, int limit) {
        if (limit <= 0) {
            if(date != null) return dossierRepository.findAllByApplicationAndStatusInAndSifInteractionCountLessThanAndLastSifInteractionDateBefore(application, statuses, maxRetries, date);
            else return dossierRepository.findAllByApplicationAndStatusInAndSifInteractionCountLessThan(application, statuses, maxRetries);
        } else {
            int page = 0;
            List<Dossier> results = new ArrayList<>();
            Page<Dossier> pageResult;

            do {
                if(date != null) pageResult = dossierRepository.findAllByApplicationAndStatusInAndSifInteractionCountLessThanAndLastSifInteractionDateBefore(application, statuses, maxRetries, date, PageRequest.of(page, limit));
                else pageResult = dossierRepository.findAllByApplicationAndStatusInAndSifInteractionCountLessThan(application, statuses, maxRetries, PageRequest.of(page, limit));
                results.addAll(pageResult.getContent());
                page++;
            } while (results.size() < limit && pageResult.hasNext());

            return results.subList(0, Math.min(results.size(), limit));
        }
    }

    /*@Transactional
    public void facesContention(String idMessage, String application, String dossierStatusEnumValue) {
        dossierRepository.facesContention(idMessage, application, dossierStatusEnumValue);
    }*/

    public void save(Dossier dossier) {
        dossierRepository.saveAndFlush(dossier);
    }

    /*@Transactional
    public void resetAllApplicationPendingDossiers(String application) {
        dossierRepository.deleteAllByApplicationAndSifTransactionIdNull(application);
    }*/

    @Modifying
    public int deleteDossiersInBatches(Date deleteFromDate, int batchSize) {
        int totalDeletedRecords = 0;
        int page = 0;
        Page<Dossier> dossiersPage;

        do {
            dossiersPage = dossierRepository.findAllByLastSifInteractionDateBeforeOrLastSifInteractionDateIsNullAndCreationDateBefore(deleteFromDate, PageRequest.of(page, batchSize));
            List<Dossier> dossiers = dossiersPage.getContent();
            log.info("Deleting {} Dossier records", dossiers.size());
            if (!dossiers.isEmpty()) {
                dossierRepository.deleteAll(dossiers);
                dossierRepository.flush();
                totalDeletedRecords += dossiers.size();
                log.info("Deleted {} Dossier records", totalDeletedRecords);
            }
            page++;
        } while (!dossiersPage.isLast());

        log.info("Total Dossier deleted records: {}", totalDeletedRecords);
        return totalDeletedRecords;
    }

    public SurveyInsertRequestType generateSurvey(Dossier dossier) {
        SurveyInsertRequestType surveyInsertRequestType = new SurveyInsertRequestType();

        surveyInsertRequestType.setMotivoViaggio(dossier.getSurvey().stream().filter(question -> question.getQuestion()
                        .equalsIgnoreCase(SifSurveyQuestionEnum.MOTIVO_VIAGGIO.toString()))
                .findFirst().map(QuestionSubsection::getAnswer).orElse(null));
        surveyInsertRequestType.setDurata(dossier.getSurvey().stream().filter(question -> question.getQuestion()
                        .equalsIgnoreCase(SifSurveyQuestionEnum.DURATA.toString()))
                .findFirst().map(QuestionSubsection::getAnswer).orElse(""));
        surveyInsertRequestType.setBigliettoRitorno(dossier.getSurvey().stream().filter(question -> question.getQuestion()
                        .equalsIgnoreCase(SifSurveyQuestionEnum.BIGLIETTO_RITORNO.toString()))
                .findFirst().map(question -> question.getAnswer().compareToIgnoreCase("SI") == 0).orElse(false));
        surveyInsertRequestType.setValoreContante(dossier.getSurvey().stream().filter(question -> question.getQuestion()
                        .equalsIgnoreCase(SifSurveyQuestionEnum.VALORE_CONTANTE.toString()))
                .findFirst().map(QuestionSubsection::getAnswer).orElse(""));
        surveyInsertRequestType.setCartaCredito(dossier.getSurvey().stream().filter(question -> question.getQuestion()
                        .equalsIgnoreCase(SifSurveyQuestionEnum.CARTA_CREDITO.toString()))
                .findFirst().map(question -> question.getAnswer().compareToIgnoreCase("SI") == 0).orElse(false));
        surveyInsertRequestType.setPresenzaContante(dossier.getSurvey().stream().filter(question -> question.getQuestion()
                        .equalsIgnoreCase(SifSurveyQuestionEnum.PRESENZA_CONTANTE.toString()))
                .findFirst().map(question -> question.getAnswer().compareToIgnoreCase("SI") == 0).orElse(false));
        // Scelta presa di comune accordo con Giovanni: nei casi in cui non viene richiesto alloggio, impostare stringa vuota per alloggio e valore false per lettera invito e prenotazione
        surveyInsertRequestType.setAlloggio(dossier.getSurvey().stream().filter(question -> question.getQuestion()
                        .equalsIgnoreCase(SifSurveyQuestionEnum.ALLOGGIO.toString()))
                .findFirst().map(QuestionSubsection::getAnswer).orElse(""));
        surveyInsertRequestType.setLetteraInvito(dossier.getSurvey().stream().filter(question -> question.getQuestion()
                        .equalsIgnoreCase(SifSurveyQuestionEnum.LETTERA_INVITO.toString()))
                .findFirst().map(question -> question.getAnswer().compareToIgnoreCase("SI") == 0).orElse(false));
        surveyInsertRequestType.setPrenotazione(dossier.getSurvey().stream().filter(question -> question.getQuestion()
                        .equalsIgnoreCase(SifSurveyQuestionEnum.PRENOTAZIONE.toString()))
                .findFirst().map(question -> question.getAnswer().compareToIgnoreCase("SI") == 0).orElse(false));
        surveyInsertRequestType.setNazione(dossier.getNationality());
        surveyInsertRequestType.setNumeroDocumento(dossier.getCountryIssue().concat(dossier.getDocumentNumber()).toUpperCase());
        surveyInsertRequestType.setVersioneQuestionario(dossier.getSurveyVersion());

        return surveyInsertRequestType;
    }

    /*public void saveAll(List<Dossier> dossiers) {
        dossierRepository.saveAll(dossiers);
    }*/

    public WFETravelDocumentSearchRequestType generateTravelDocument(Dossier dossier) {
        WFETravelDocumentSearchRequestType dossierRequest = new WFETravelDocumentSearchRequestType();

        short weight = Short.parseShort(sifParametersRepository.findByType(SifParametersEnum.WEIGHT.toString()));

        WFEAnyNameSearchField wfeAnyNameSearchField = new WFEAnyNameSearchField();
        AnyNameTypeRestriction anyNameTypeRestriction = new AnyNameTypeRestriction();
        anyNameTypeRestriction.setValue(dossier.getSurname().toUpperCase());
        anyNameTypeRestriction.setAnyName(false);
        anyNameTypeRestriction.setFuzzy(false);
        anyNameTypeRestriction.setWeight(weight);
        wfeAnyNameSearchField.setPrimaryValue(anyNameTypeRestriction);
        dossierRequest.setFamilyName(wfeAnyNameSearchField);

        List<String> names = List.of(dossier.getName().trim().split(" "));
        names.forEach(name -> {
            WFENameSearchField wfeNameSearchField = new WFENameSearchField();
            NameTypeRestriction nameTypeRestriction = new NameTypeRestriction();
            nameTypeRestriction.setValue(name);
            nameTypeRestriction.setFuzzy(false);
            nameTypeRestriction.setWeight(weight);
            wfeNameSearchField.setPrimaryValue(nameTypeRestriction);
            dossierRequest.getFirstName().add(wfeNameSearchField);
        });

        PseudoDateExactSearchField pseudoDateExactSearchField = new PseudoDateExactSearchField();
        LocalDate birthDate = LocalDate.parse(dossier.getBirthdate(), DateTimeFormatter.ofPattern("dd/MM/yyyy"));
        pseudoDateExactSearchField.setDate(birthDate.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        pseudoDateExactSearchField.setWeight(weight);
        dossierRequest.setDateOfBirth(pseudoDateExactSearchField);

        // TODO gestire possibile mancanza di valori di mapping nella tabella SifCodesTranslation -> cosa fare?

        SifCountryCode sifCountryCode = sifCountryCodeRepository.findByKioskValue(dossier.getNationality().toUpperCase());
        String sifCountryValue;
        // se non ho un sif_value per l'alpha-3 del passaporto allora invio al SIF il valore associato alla nazionalità UNKNOWN
        if(sifCountryCode == null || sifCountryCode.getCountrySifValue() == null) {
            log.info("No mapping available for alpha-3 value \"{}\". Unknown sifCountryCode value has been set", dossier.getNationality().toUpperCase());
            sifCountryValue = sifGenericCodeRepository.findByType(SifTypeEnum.UNKNOWN_NATIONALITY.toString()).getSifValue();
        } else sifCountryValue = sifCountryCode.getCountrySifValue();
        CT02CountryOfNationalitySearchField ct02CountryOfNationalitySearchField = new CT02CountryOfNationalitySearchField();
        ct02CountryOfNationalitySearchField.setValue(sifCountryValue); // esempio: 0011.01 "DTTG: Corrisponde a "ARGENTINA" (by scheda "CT02 - COUNTRY OF NATIONALITY")"
        ct02CountryOfNationalitySearchField.setWeight(weight);
        dossierRequest.getNationality().add(ct02CountryOfNationalitySearchField);

        CT04GenderSearchField ct04GenderSearchField = new CT04GenderSearchField();
        String gender = sifGenericCodeRepository.getSifValueByTypeAndKioskValue(SifTypeEnum.GENDER.toString(), dossier.getGender());
        ct04GenderSearchField.setValue(gender); // 0002.01 ad esempio DTTG: Corrisponde a "Female" (by scheda "CT04 - GENDER")
        ct04GenderSearchField.setWeight(weight);
        dossierRequest.setGender(ct04GenderSearchField);

        CT512TravelDocumentTypeSearchField ct512TravelDocumentTypeSearchField = new CT512TravelDocumentTypeSearchField();
        ct512TravelDocumentTypeSearchField.setValue(sifParametersRepository.findByType(SifParametersEnum.TRAVEL_DOCUMENT_TYPE.toString())); //DTTG: Corrisponde a "Ordinary passport" (by scheda "CT11 - TRAVEL_DOCUMENT_TYPE")
        ct512TravelDocumentTypeSearchField.setWeight(weight);
        dossierRequest.setDocumentType(ct512TravelDocumentTypeSearchField);

        WFETravelDocumentSearchRequestType.DocumentNumber documentNumber = new WFETravelDocumentSearchRequestType.DocumentNumber();
        documentNumber.setValue(dossier.getCountryIssue().concat(dossier.getDocumentNumber()).toUpperCase());
        documentNumber.setWeight(weight);
        dossierRequest.setDocumentNumber(documentNumber);

        /*LocalDate localDate = LocalDate.parse(dossier.getExpiryDate(), DateTimeFormatter.ofPattern("dd/MM/yyyy"));
        GregorianCalendar gregorianCalendar = GregorianCalendar.from(localDate.atStartOfDay().atZone(ZoneId.systemDefault()));
        DatatypeFactory datatypeFactory;
        try {
            datatypeFactory = DatatypeFactory.newInstance();
            dossierRequest.setValidUntil(datatypeFactory.newXMLGregorianCalendar(gregorianCalendar));
        } catch (Exception e) {
            dossierRequest.setValidUntil(null);
            log.error("Error during conversion of date to XMLGregorianCalendar for Dossier with id {}: {}", dossier.getIdMessage(), e.getMessage());
        }*/
        LocalDate localDate = LocalDate.parse(dossier.getExpiryDate(), DateTimeFormatter.ofPattern("dd/MM/yyyy"));
        //GregorianCalendar gregorianCalendar = GregorianCalendar.from(localDate.atStartOfDay().atZone(ZoneId.systemDefault()));
        DatatypeFactory datatypeFactory;
        try {
            datatypeFactory = DatatypeFactory.newInstance();
            XMLGregorianCalendar xmlGregorianCalendar = datatypeFactory.newXMLGregorianCalendarDate(
                    localDate.getYear(), localDate.getMonthValue(), localDate.getDayOfMonth(), DatatypeConstants.FIELD_UNDEFINED);
            dossierRequest.setValidUntil(xmlGregorianCalendar);
        } catch (Exception e) {
            dossierRequest.setValidUntil(null);
            log.error("Error during conversion of date to XMLGregorianCalendar for Dossier with id {}: {}", dossier.getIdMessage(), e.getMessage());
        }

        return dossierRequest;
    }

    public Dossier getDossierByPassportData(String documentNumber, String countryIssue) {
        return dossierRepository.findFirstByDocumentNumberAndCountryIssueOrderByCreationDateDesc(documentNumber, countryIssue);
    }

    public Dossier getKioskDossierByPassportData(String documentNumber, String countryIssue) {
        return dossierRepository.findFirstByDocumentNumberAndCountryIssueAndEgateHandledFalseOrEgateHandledIsNullOrderByCreationDateDesc(documentNumber, countryIssue);
    }

    public Dossier getEgateDossierByPassportData(String documentNumber, String countryIssue) {
        return dossierRepository.findFirstByDocumentNumberAndCountryIssueAndEgateHandledTrueOrderByCreationDateDesc(documentNumber, countryIssue);
    }

    public Dossier getEgateDossierByNodeIdAndPassengerId(String nodeId, String passengerId) {
        return dossierRepository.findFirstByNodeIdAndPassengerIdAndEgateHandledTrueOrderByCreationDateDesc(nodeId, passengerId);
    }

    public Dossier deleteSensitiveData(Dossier dossier) { //TODO:  necessario cancellare anche altro?
        if(!isDeleteSensitiveDataActive) return dossier;

        dossier.setSurname(null);
        dossier.setName(null);
        dossier.setGender(null);
        dossier.setBirthdate(null);
        dossier.setNationality(null);
        dossier.setExpiryDate(null);
        dossier.setFaceImage(null);
        dossier.setFaceQuality(null);
        dossier.setFingerprintImage(null);
        dossier.setNotFingerEligibilityAge(null);
        dossier.getNfiq().clear();
        dossier.getSurvey().clear();
        dossier.setSurveyVersion(null);
        return dossier;
    }

    public StartBorderControlRequestMessageType mapDossierToStartBorderControlRequest(Dossier dossier) {
        StartBorderControlRequestMessageType startBorderControlRequestMessageType = new StartBorderControlRequestMessageType();

        if(dossier.getEgateHandled().equals(Boolean.TRUE)) {
            WFEScopeModifiersType scopeModifiers = new WFEScopeModifiersType();
            CalculatorScopeType calculatorScopeType = new CalculatorScopeType();
            Kiosk egate = kioskRepository.findById(dossier.getNodeId()).orElse(null);
            if(egate != null) {
                DatatypeFactory datatypeFactory;
                try {
                    datatypeFactory = DatatypeFactory.newInstance();
                } catch (DatatypeConfigurationException e) {
                    throw new RuntimeException(e);
                }
                ZonedDateTime now = ZonedDateTime.now();
                String dateTimeString = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'")); //TODO se hai problemi sappi che probabilmente dovrai usare qualcosa come "2025-01-17T14:55:13.1532976Z"
                XMLGregorianCalendar xmlGregorianCalendar = datatypeFactory.newXMLGregorianCalendar(dateTimeString);
                if(egate.getIsEntryEgate().equals(Boolean.TRUE)) {
                    CalculatorScopeType.Entry entry = new CalculatorScopeType.Entry();
                    entry.setEntryDate(xmlGregorianCalendar);
                    calculatorScopeType.setEntry(entry);
                } else if(egate.getIsEntryEgate().equals(Boolean.FALSE)) {
                    CalculatorScopeType.Exit exit = new CalculatorScopeType.Exit();
                    exit.setExitDate(xmlGregorianCalendar);
                    calculatorScopeType.setExit(exit);
                }
                scopeModifiers.setCalculator(calculatorScopeType);
                startBorderControlRequestMessageType.setScopeModifiers(scopeModifiers);
            } else {
                log.error("Error during mapping of Dossier with id {}: Egate with id {} not found; calculator not set", dossier.getIdMessage(), dossier.getNodeId());
            }
        }

        startBorderControlRequestMessageType.setTravelDocument(generateTravelDocument(dossier));

        startBorderControlRequestMessageType.setBorderCrossingPoint(sifParametersRepository.findByType(SifParametersEnum.BORDER_CROSSING_POINT.toString()));

        CollectedDataRequestType collectedDataRequestType = new CollectedDataRequestType();
        collectedDataRequestType.setTCNType(sifParametersRepository.findByType(SifParametersEnum.TCN_TYPE.toString()));
        collectedDataRequestType.setPersonStatus(sifParametersRepository.findByType(SifParametersEnum.PERSON_STATUS.toString()));
        startBorderControlRequestMessageType.setCollectedData(collectedDataRequestType);

        return startBorderControlRequestMessageType;
    }

    public AbortBorderControlRequestMessageType mapDossierToAbortBorderControlRequest(Dossier dossier) {
        AbortBorderControlRequestMessageType abortBorderControlRequestMessageType = new AbortBorderControlRequestMessageType();

        abortBorderControlRequestMessageType.setBorderCrossingPoint(sifParametersRepository.findByType(SifParametersEnum.BORDER_CROSSING_POINT.toString()));

        AbortBorderControlRequestMessageType.CollectedData collectedData = new AbortBorderControlRequestMessageType.CollectedData();
        AbortBorderControlRequestMessageType.CollectedData.TravelDocument travelDocument = new AbortBorderControlRequestMessageType.CollectedData.TravelDocument();
        travelDocument.setDocumentNumber(dossier.getCountryIssue().concat(dossier.getDocumentNumber()).toUpperCase());
        collectedData.setTravelDocument(travelDocument);
        abortBorderControlRequestMessageType.setCollectedData(collectedData);

        return abortBorderControlRequestMessageType;
    }

    //@Transactional
    public HeaderAsyncRequestType buildHeaderAsyncRequestType(Dossier dossier) {
        ObjectFactory objectFactory = new ObjectFactory();
        HeaderAsyncRequestType headerAsyncRequestType = objectFactory.createHeaderAsyncRequestType();
        headerAsyncRequestType.setVariant(sifParametersRepository.findByType(SifParametersEnum.VARIANT.toString())); //DTTG: Corrisponde a "Use of data for verification at the borders" - VALORE FISSO PER NOI - (by scheda "ST504 - OPERATION VARIANT")
        headerAsyncRequestType.setSystemID(sifParametersRepository.findByType(SifParametersEnum.SYSTEM_ID.toString())); //DTTG: Identificativo del sistema che invia le richieste. Valorizzare provvisoriamente con "ABC-Immigrazione" (ALMAVIVA fara' un quesito al Sistema Centrale Europeo)
        /*XMLGregorianCalendar xmlGregorianCalendar = null;
        try {
            DatatypeFactory datatypeFactory = DatatypeFactory.newInstance();
            xmlGregorianCalendar = datatypeFactory.newXMLGregorianCalendar(new GregorianCalendar(TimeZone.getTimeZone("UTC")));
            headerAsyncRequestType.setTimestamp(xmlGregorianCalendar);
        } catch (Exception e) {
            headerAsyncRequestType.setTimestamp(null);
            log.error("Error during conversion of date to XMLGregorianCalendar for Dossier with id {}: {}", dossier.getIdMessage(), e.getMessage());
        }*/
        try {
            DatatypeFactory datatypeFactory = DatatypeFactory.newInstance();
            ZonedDateTime now = ZonedDateTime.now();
            String dateTimeString = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'"));
            XMLGregorianCalendar xmlGregorianCalendar = datatypeFactory.newXMLGregorianCalendar(dateTimeString);
            headerAsyncRequestType.setTimestamp(xmlGregorianCalendar);
        } catch (Exception e) {
            headerAsyncRequestType.setTimestamp(null);
            log.error("Error during conversion of date to XMLGregorianCalendar for Dossier with id {}: {}", dossier.getIdMessage(), e.getMessage());
        }

        ResponsibleType responsibleType = new ResponsibleType();
        responsibleType.setAuthority(sifParametersRepository.findByType(SifParametersEnum.AUTHORITY.toString())); //DTTG: Sostituire con "ITAeur22b" come da NOTE
        responsibleType.setEndUserRole(Boolean.TRUE.equals(dossier.getEgateHandled()) ? sifParametersRepository.findByType(SifParametersEnum.END_USER_ROLE_EGATE.toString()) : sifParametersRepository.findByType(SifParametersEnum.END_USER_ROLE_KIOSK.toString())); //DTTG: Il valore dovrebbe essere OK, corrispondendo a "EUR 22B - Automated Border Control" (by scheda "ST12 - ENDUSER ROLE")
        responsibleType.setUser(sifParametersRepository.findByType(SifParametersEnum.USER.toString())); //DTTG: Sostituire con "0014.01" ITALIA (by scheda "CT70 - USER")
        headerAsyncRequestType.setUser(responsibleType);
        SIFHeader sifHeader = buildSifHeader(dossier);
        if(sifHeader.getIdChiamante() != null) headerAsyncRequestType.setTransactionID(SoapUtils.generateSIFTransactionId(sifHeader.getIdChiamante()));
        else headerAsyncRequestType.setTransactionID(null);
        headerAsyncRequestType.setSIFHeader(sifHeader);
        headerAsyncRequestType.setReplyToURL("");
        return headerAsyncRequestType;
    }

    //@Transactional
    public HeaderRequestType buildHeaderRequestType(Dossier dossier) {
        /*HeaderRequestType headerRequestType = new HeaderRequestType();*/
        ObjectFactory objectFactory = new ObjectFactory();HeaderRequestType headerRequestType = objectFactory.createHeaderRequestType();
        headerRequestType.setVariant(sifParametersRepository.findByType(SifParametersEnum.VARIANT.toString())); //DTTG: Corrisponde a "Use of data for verification at the borders" - VALORE FISSO PER NOI - (by scheda "ST504 - OPERATION VARIANT")
        headerRequestType.setSystemID(sifParametersRepository.findByType(SifParametersEnum.SYSTEM_ID.toString())); //DTTG: Idnentificativo del sistema che invia le richieste. Valorizzare provvisoriamente con "ABC-Immigrazione" (ALMAVIVA fara' un quesito al Sistema Centrale Europeo)
        /*XMLGregorianCalendar xmlGregorianCalendar = null;
        try {
            DatatypeFactory datatypeFactory = DatatypeFactory.newInstance();
            xmlGregorianCalendar = datatypeFactory.newXMLGregorianCalendar(new GregorianCalendar(TimeZone.getTimeZone("UTC")));
            headerRequestType.setTimestamp(xmlGregorianCalendar);
        } catch (Exception e) {
            headerRequestType.setTimestamp(null);
            log.error("Error during conversion of date to XMLGregorianCalendar for Dossier with id {}: {}", dossier.getIdMessage(), e.getMessage());
        }*/
        try {
            DatatypeFactory datatypeFactory = DatatypeFactory.newInstance();
            ZonedDateTime now = ZonedDateTime.now();
            String dateTimeString = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'"));
            XMLGregorianCalendar xmlGregorianCalendar = datatypeFactory.newXMLGregorianCalendar(dateTimeString);
            headerRequestType.setTimestamp(xmlGregorianCalendar);
        } catch (Exception e) {
            headerRequestType.setTimestamp(null);
            log.error("Error during conversion of date to XMLGregorianCalendar for Dossier with id {}: {}", dossier.getIdMessage(), e.getMessage());
        }

        ResponsibleType responsibleType = new ResponsibleType();
        responsibleType.setAuthority(sifParametersRepository.findByType(SifParametersEnum.AUTHORITY.toString())); //DTTG: Sostituire con "ITAeur22b" come da NOTE
        responsibleType.setEndUserRole(Boolean.TRUE.equals(dossier.getEgateHandled()) ? sifParametersRepository.findByType(SifParametersEnum.END_USER_ROLE_EGATE.toString()) : sifParametersRepository.findByType(SifParametersEnum.END_USER_ROLE_KIOSK.toString())); //DTTG: Il valore dovrebbe essere OK, corrispondendo a "EUR 22B - Automated Border Control" (by scheda "ST12 - ENDUSER ROLE")
        responsibleType.setUser(sifParametersRepository.findByType(SifParametersEnum.USER.toString())); //DTTG: Sostituire con "0014.01" ITALIA (by scheda "CT70 - USER")
        headerRequestType.setUser(responsibleType);
        SIFHeader sifHeader = buildSifHeader(dossier);
        if(sifHeader.getIdChiamante() != null) headerRequestType.setTransactionID(SoapUtils.generateSIFTransactionId(sifHeader.getIdChiamante()));
        else headerRequestType.setTransactionID(null);
        headerRequestType.setSIFHeader(sifHeader);
        return headerRequestType;
    }

    /*public HeaderAsyncRequestType convertStringToHeaderAsyncRequestType(String xmlString) {
        try {
            JAXBContext jaxbContext = JAXBContext.newInstance(HeaderAsyncRequestType.class);
            Unmarshaller unmarshaller = jaxbContext.createUnmarshaller();
            StringReader reader = new StringReader(xmlString);
            return (HeaderAsyncRequestType) unmarshaller.unmarshal(reader);
        } catch (JAXBException e) {
            e.printStackTrace();
            return null;
        }
    }*/

    public Object convertOutCDataToWFESpecificResponse(String xmlString) {
        try {
            boolean fakeClientsReturns = Boolean.parseBoolean(environment.getProperty("fake.clients.returns")); //inserito qui in questo modo anzichè usare @Value perchè qui non funziona (forse a causa di qualche @Order o roba simile?)
            if(xmlString.isBlank() && Boolean.FALSE.equals(fakeClientsReturns)) return null;
            if(xmlString.isBlank() && Boolean.TRUE.equals(fakeClientsReturns)) {
                /* //when in exception catch clause:
                StackTraceElement[] stackTraceElements = Thread.currentThread().getStackTrace();
                String methodName = stackTraceElements[2].getMethodName();
                if(methodName.equalsIgnoreCase("addDataToBorderControl")) {
                    return new AddDataToBorderControlResponseMessageType();
                } else if(methodName.equalsIgnoreCase("startBorderControl")) {
                    return new StartBorderControlResponseMessageType();
                }*/
                StartBorderControlResponseMessageType startBorderControlResponseMessageType = new StartBorderControlResponseMessageType();
                WFEResponseType wfeResponseType = new WFEResponseType();
                RequiredDataType requiredDataType = new RequiredDataType();
                wfeResponseType.setRequiredData(requiredDataType);
                startBorderControlResponseMessageType.setResponse(wfeResponseType);
                return startBorderControlResponseMessageType; //teoricamente in questo caso non avrò mai bisogno di ritornare in alternativa un AddDataToBorderControlResponseMessageType
            }
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            factory.setNamespaceAware(true);
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document doc = builder.parse(new InputSource(new StringReader(xmlString)));

            Node envelope = doc.getDocumentElement();
            NodeList envelopeChildren = envelope.getChildNodes();
            for (int i = 0; i < envelopeChildren.getLength(); i++) {
                Node child = envelopeChildren.item(i);
                if (child.getNodeType() == Node.TEXT_NODE && child.getTextContent().trim().isEmpty()) continue;
                if (child.getNodeName().equalsIgnoreCase("soap:Body") || child.getLocalName().equalsIgnoreCase("Body")) {
                    Node bodyChild = child.getFirstChild();
                    while (bodyChild != null && (bodyChild.getNodeType() == Node.TEXT_NODE && bodyChild.getTextContent().trim().isEmpty())) {
                        bodyChild = bodyChild.getNextSibling();
                    }
                    if (bodyChild == null) {
                        log.error("convertOutCDataToWFESpecificResponse() - bodyChild is null");
                        return null;
                    }

                    TransformerFactory transformerFactory = TransformerFactory.newInstance();
                    Transformer transformer = transformerFactory.newTransformer();
                    StringWriter writer = new StringWriter();
                    transformer.transform(new DOMSource(bodyChild), new StreamResult(writer));
                    String bodyContent = writer.toString();

                    StringReader reader = new StringReader(bodyContent);
                    Jaxb2Marshaller marshaller = new Jaxb2Marshaller();
                    marshaller.setPackagesToScan("eu.europa.schengen.ees.xsd.v1");
                    if (bodyChild.getLocalName().equalsIgnoreCase("StartBorderControlResponse")) {
                        marshaller.setMappedClass(StartBorderControlResponseMessageType.class);
                    } else if (bodyChild.getLocalName().equalsIgnoreCase("AddDataToBorderControlResponse")) {
                        marshaller.setMappedClass(AddDataToBorderControlResponseMessageType.class);
                    }
                    marshaller.afterPropertiesSet();
                    return marshaller.unmarshal(new StreamSource(reader));
                }
            }
        } catch (Exception e) {
            log.error("convertOutCDataToWFESpecificResponse() - Exception {}", e.getMessage());
        }
        return null;
    }

    public AddDataToBorderControlRequestMessageType mapDossierToAddDataToBorderControlRequest(Dossier dossier, List<String> dataRequests) {
        AddDataToBorderControlRequestMessageType addDataToBorderControlRequestMessageType = new AddDataToBorderControlRequestMessageType();
        AddDataToBorderControlRequestMessageType.CollectedData collectedData = new AddDataToBorderControlRequestMessageType.CollectedData();

        for (String dataRequest : dataRequests) {
            SifAddDataRequest addDataRequest = sifAddDataRequestRepository.findByValue(dataRequest);
            SifAddDataRequestEnum requestEnum = SifAddDataRequestEnum.valueOf(dossier.getEgateHandled().equals(Boolean.TRUE) ? addDataRequest.getRequestForEgate().toUpperCase() : addDataRequest.getRequestForKiosk().toUpperCase());
            switch (requestEnum) {
                case FI_REQUEST -> {
                    FIRequestType fiRequestType = getFiRequestType(dossier);
                    if(fiRequestType == null) fiRequestType = new FIRequestType();
                    collectedData.setFI(fiRequestType);
                    log.info("Added FI data to the collected data object for dossier {}. {}: {}", dossier.getIdMessage(), dataRequest, addDataRequest.getDescription());
                }
                case FP_REQUEST -> {
                    if(dossier.getFingerprintImage() == null || dossier.getFingerprintImage().isBlank()) { //getNotFingerEligibilityAge?
                        FPRequestType fpRequestType = getFpRequestType(dossier);
                        if(fpRequestType == null) fpRequestType = new FPRequestType();
                        collectedData.setFP(fpRequestType);
                        log.info("Added FP data to the collected data object for dossier {}. {}: {}", dossier.getIdMessage(), dataRequest, addDataRequest.getDescription());
                        break;
                    }
                    if(Boolean.TRUE.equals(dossier.getNotFingerEligibilityAge()) || (dossier.getFingerprintImage() != null && !dossier.getFingerprintImage().isBlank())) {
                        FPRequestType fpRequestType = getFpRequestType(dossier);
                        if(fpRequestType == null) fpRequestType = new FPRequestType();
                        collectedData.setFP(fpRequestType);
                        log.info("Added FP data to the collected data object for dossier {}. {}: {}", dossier.getIdMessage(), dataRequest, addDataRequest.getDescription());
                    } else log.info("The following requested code has been ignored for dossier {} due to missing finger information. {}: {}", dossier.getIdMessage(), dataRequest, addDataRequest.getDescription());
                }
                default -> log.info("The following requested code has been ignored for dossier {}. {}: {}", dossier.getIdMessage(), dataRequest, addDataRequest.getDescription());
            }
        }

        AddDataToBorderControlRequestMessageType.CollectedData.TravelDocument travelDocument = new AddDataToBorderControlRequestMessageType.CollectedData.TravelDocument();
        travelDocument.setDocumentNumber(dossier.getCountryIssue().concat(dossier.getDocumentNumber()).toUpperCase());
        collectedData.setTravelDocument(travelDocument);

        addDataToBorderControlRequestMessageType.setCollectedData(collectedData);
        addDataToBorderControlRequestMessageType.setBorderCrossingPoint(sifParametersRepository.findByType(SifParametersEnum.BORDER_CROSSING_POINT.toString()));

        return addDataToBorderControlRequestMessageType;
    }

    private FIRequestType getFiRequestType(Dossier dossier) {
        FIRequestType fiRequestType = new FIRequestType();
        if(dossier.getFaceImage() == null || dossier.getFaceImage().isBlank()) {
            logService.saveEventLog(LogScopeEnum.INTERNAL.getValue(), "Dossier service GetFiRequestType", "Face image not found for dossier " + dossier.getIdMessage(), null);
            return null;
        }
        fiRequestType.setNISTFile(Base64.getDecoder().decode(dossier.getFaceImage()));
        fiRequestType.setImageQualityValue(dossier.getFaceQuality().shortValue());
        String FISource = sifGenericCodeRepository.getSifValueByTypeAndKioskValue(
                SifTypeEnum.FI_DATA_SOURCE.toString(), Boolean.TRUE.equals(dossier.getIsFaceTakenLive()) ? "Image taken live" : "eMRTD");
        fiRequestType.setSource(FISource); // "Indicates the source of the image, if it is from the passport or taken live"
        return fiRequestType;
    }

    private FPRequestType getFpRequestType(Dossier dossier) {
        FPRequestType fpRequestType = new FPRequestType();
        if(dossier.getFingerprintImage() == null || dossier.getFingerprintImage().isBlank()) {
            logService.saveEventLog(LogScopeEnum.INTERNAL.getValue(), "Dossier service GetFpRequestType", "Fingerprint image not found for dossier " + dossier.getIdMessage(), null);
            return null;
        }
        if(Boolean.TRUE.equals(dossier.getNotFingerEligibilityAge())) { // si tratta di un passeggero non eleggibile all'acquisizione delle impronte a causa dell'età
            fpRequestType.setNotProvidedReason(sifParametersRepository.findByType(SifParametersEnum.NO_FP_CHILDREN_UNDER_THE_AGE.toString()));
        } else {
            List<SifGenericCode> sifFingersCodes = sifGenericCodeRepository.findAllByType(SifTypeEnum.FP_CODE.toString());
            for (NfiqSubsection nfiqSubsection : dossier.getNfiq()) {
                Optional<SifGenericCode> sifFingerCode = sifFingersCodes.stream()
                        .filter(sifCodesTrans -> sifCodesTrans.getKioskValue().compareToIgnoreCase(nfiqSubsection.getFinger()) == 0).findFirst();
                if(sifFingerCode.isEmpty()) {
                    logService.saveEventLog(LogScopeEnum.INTERNAL.getValue(), "Dossier service GetFpRequestType", "Fingerprint code not found for dossier " + dossier.getIdMessage() + " and finger " + nfiqSubsection.getFinger(), null);
                    continue;
                }

                FPImageQualityType fpImageQualityType = new FPImageQualityType();
                fpImageQualityType.setFingerPositionCode(sifFingerCode.get().getSifValue());
                fpImageQualityType.setQualityValue(nfiqSubsection.getNfiq().shortValue());
                fpRequestType.getImageQuality().add(fpImageQualityType);
            }

            fpRequestType.setNISTFile(Base64.getDecoder().decode(dossier.getFingerprintImage()));
        }
        return fpRequestType;
    }

    public EndBorderControlRequestMessageType mapDossierToEndBorderControlRequest(Dossier dossier, SifEndBorderControlEnum endBorderControlOperation) {
        EndBorderControlRequestMessageType endBorderControlRequestMessageType = new EndBorderControlRequestMessageType();
        endBorderControlRequestMessageType.setBorderCrossingPoint(sifParametersRepository.findByType(SifParametersEnum.BORDER_CROSSING_POINT.toString()));

        EndBorderControlRequestMessageType.TravellerFile travellerFile = new EndBorderControlRequestMessageType.TravellerFile();

        switch (endBorderControlOperation) {
            case ENTRY -> travellerFile.setEntryRecord(mapEndBorderControlEntryRecord());
            case EXIT -> travellerFile.setExitRecord(mapEndBorderControlExitRecord());
            case REFUSAL -> travellerFile.setRefusalRecord(mapEndBorderControlRefusalRecord());
        }

        /*FIToBeStoredInTravellerFileType fiToBeStoredInTravellerFileType = new FIToBeStoredInTravellerFileType();
        fiToBeStoredInTravellerFileType.setFIToBeStoredInTravellerFile("");
        fiToBeStoredInTravellerFileType.setNotProvidedReason("");
        travellerFile.setFIToBeStoredInTravellerFile(fiToBeStoredInTravellerFileType);*/

        /*FPToBeStoredInTravellerFileType fpToBeStoredInTravellerFileType = new FPToBeStoredInTravellerFileType();
        fpToBeStoredInTravellerFileType.setFPToBeDeletedFromTravellerFile("");
        fpToBeStoredInTravellerFileType.setFPToBeStoredInTravellerFile("");
        fpToBeStoredInTravellerFileType.setNotProvidedReason("");
        travellerFile.setFPToBeStoredInTravellerFile(fpToBeStoredInTravellerFileType);*/

        EndBorderControlRequestMessageType.TravellerFile.TravelDocument travelDocument = new EndBorderControlRequestMessageType.TravellerFile.TravelDocument();
        travelDocument.setDocumentNumber(dossier.getCountryIssue().concat(dossier.getDocumentNumber()).toUpperCase());
        travellerFile.setTravelDocument(travelDocument);
        endBorderControlRequestMessageType.setTravellerFile(travellerFile);

        return endBorderControlRequestMessageType;
    }

    private RefusalRecordCreatePreEnrolmentRequestType mapEndBorderControlRefusalRecord() {
        RefusalRecordCreatePreEnrolmentRequestType refusalRecordCreatePreEnrolmentRequestType = new RefusalRecordCreatePreEnrolmentRequestType();
        refusalRecordCreatePreEnrolmentRequestType.setBorderCrossingPoint(sifParametersRepository.findByType(SifParametersEnum.BORDER_CROSSING_POINT.toString()));

        XMLGregorianCalendar xmlGregorianCalendarRefusalTimestamp = getTodayFormattedXmlGregorianCalendar();
        refusalRecordCreatePreEnrolmentRequestType.setRefusalTimestamp(xmlGregorianCalendarRefusalTimestamp);

        return refusalRecordCreatePreEnrolmentRequestType;
    }

    private EndBorderControlRequestMessageType.TravellerFile.ExitRecord mapEndBorderControlExitRecord() {
        EndBorderControlRequestMessageType.TravellerFile.ExitRecord exitRecord = new EndBorderControlRequestMessageType.TravellerFile.ExitRecord();
        EndBorderControlRequestMessageType.TravellerFile.ExitRecord.EntryRecordData entryRecordData = new EndBorderControlRequestMessageType.TravellerFile.ExitRecord.EntryRecordData(); //serve davvero anche questo?
        /*AuthorisationChangeWithoutVisaType authorisationChangeWithoutVisaType = getAuthorisationChangeWithoutVisaType();
        entryRecordData.setAuthorisationChange(authorisationChangeWithoutVisaType);*/
        XMLGregorianCalendar xmlGregorianCalendarAuthorisedStayUntil = null; //dovrebbe essere il valore tornato da altre BorderControl con campo 'calculate' riempito nelle richieste
        entryRecordData.setAuthorisedStayUntil(xmlGregorianCalendarAuthorisedStayUntil);
        EntryRecordCreatePreEnrolmentRequestType entryRecordCreatePreEnrolmentRequestType = new EntryRecordCreatePreEnrolmentRequestType();
        /*AuthorisationChangeWithoutVisaType authorisationChangeWithoutVisaType2 = getAuthorisationChangeWithoutVisaType();
        entryRecordCreatePreEnrolmentRequestType.setAuthorisationChange(authorisationChangeWithoutVisaType2);*/
        XMLGregorianCalendar xmlGregorianCalendarAuthorisedStayUntil2 = null; //dovrebbe essere il valore tornato da altre BorderControl con campo 'calculate' riempito nelle richieste
        entryRecordCreatePreEnrolmentRequestType.setAuthorisedStayUntil(xmlGregorianCalendarAuthorisedStayUntil2);
        entryRecordCreatePreEnrolmentRequestType.setBorderCrossingPoint(sifParametersRepository.findByType(SifParametersEnum.BORDER_CROSSING_POINT.toString()));
        XMLGregorianCalendar xmlGregorianCalendarBorderCrossingTimestamp = getTodayFormattedXmlGregorianCalendar();
        entryRecordCreatePreEnrolmentRequestType.setBorderCrossingTimestamp(xmlGregorianCalendarBorderCrossingTimestamp);
        entryRecordCreatePreEnrolmentRequestType.setNationalVisa(false); //parametro?
        entryRecordCreatePreEnrolmentRequestType.setPersonStatus(""); //parametro?
        entryRecordData.setCorrespondingEntryRecord(entryRecordCreatePreEnrolmentRequestType);
        exitRecord.setEntryRecordData(entryRecordData);
        exitRecord.setBorderCrossingPoint(sifParametersRepository.findByType(SifParametersEnum.BORDER_CROSSING_POINT.toString()));
        XMLGregorianCalendar xmlGregorianCalendarBorderCrossingTimestamp2 = getTodayFormattedXmlGregorianCalendar();
        exitRecord.setBorderCrossingTimestamp(xmlGregorianCalendarBorderCrossingTimestamp2);
        return exitRecord;
    }

    private EntryRecordCreatePreEnrolmentRequestNPSType mapEndBorderControlEntryRecord() {
        EntryRecordCreatePreEnrolmentRequestNPSType entryRecordCreatePreEnrolmentRequestNPSType = new EntryRecordCreatePreEnrolmentRequestNPSType();
        /*AuthorisationChangeWithoutVisaType authorisationChangeWithoutVisaType = getAuthorisationChangeWithoutVisaType();
        entryRecordCreatePreEnrolmentRequestNPSType.setAuthorisationChange(authorisationChangeWithoutVisaType);*/
        XMLGregorianCalendar xmlGregorianCalendarAuthorisedStayUntil = null; //dovrebbe essere il valore tornato da altre BorderControl con campo 'calculate' riempito nelle richieste
        entryRecordCreatePreEnrolmentRequestNPSType.setAuthorisedStayUntil(xmlGregorianCalendarAuthorisedStayUntil);
        entryRecordCreatePreEnrolmentRequestNPSType.setBorderCrossingPoint(sifParametersRepository.findByType(SifParametersEnum.BORDER_CROSSING_POINT.toString()));

        XMLGregorianCalendar xmlGregorianCalendarBorderCrossingTimestamp = getTodayFormattedXmlGregorianCalendar();
        entryRecordCreatePreEnrolmentRequestNPSType.setBorderCrossingTimestamp(xmlGregorianCalendarBorderCrossingTimestamp);

        entryRecordCreatePreEnrolmentRequestNPSType.setNationalVisa(false); //parametro?
        return entryRecordCreatePreEnrolmentRequestNPSType;
    }

    private static XMLGregorianCalendar getTodayFormattedXmlGregorianCalendar() { //corretto chiamarlo più volte per diversi campi della stessa richiesta? Attento al fatto che sono inclusi anche i secondi!
        DatatypeFactory datatypeFactory;
        try {
            datatypeFactory = DatatypeFactory.newInstance();
        } catch (DatatypeConfigurationException e) {
            throw new RuntimeException(e);
        }
        ZonedDateTime now = ZonedDateTime.now();
        String dateTimeString = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'"));
        return datatypeFactory.newXMLGregorianCalendar(dateTimeString);
    }

    private AuthorisationChangeWithoutVisaType getAuthorisationChangeWithoutVisaType() {
        AuthorisationChangeWithoutVisaType authorisationChangeWithoutVisaType = new AuthorisationChangeWithoutVisaType();
        authorisationChangeWithoutVisaType.setBilateralAgreement("");
        authorisationChangeWithoutVisaType.setChangeOtherGround("");
        authorisationChangeWithoutVisaType.setDecisionAuthority("");
        XMLGregorianCalendar xmlGregorianCalendarDecisionDate = null;
        authorisationChangeWithoutVisaType.setDecisionDate(xmlGregorianCalendarDecisionDate);
        PlaceNewType placeNewType = new PlaceNewType();
        TransType transType = new TransType();
        transType.setRawValue("");
        SourceValueType sourceValueType = new SourceValueType();
        sourceValueType.setPreTransliterated(false);
        sourceValueType.setValue("");
        transType.setSourceValue(sourceValueType);
        placeNewType.setPlace(transType);
        placeNewType.setCountry("");
        authorisationChangeWithoutVisaType.setDecisionPlace(placeNewType);
        authorisationChangeWithoutVisaType.setDurationOfStay(1);
        authorisationChangeWithoutVisaType.setStatus("");
        return authorisationChangeWithoutVisaType;
    }

    @Transactional
    protected SIFHeader buildSifHeader(Dossier dossier) {
        SIFHeader sifHeader = new SIFHeader();
        sifHeader.setIdUser(Boolean.TRUE.equals(dossier.getEgateHandled()) ? sifParametersRepository.findByType(SifParametersEnum.END_USER_ID_EGATE.toString()) : sifParametersRepository.findByType(SifParametersEnum.END_USER_ID_KIOSK.toString()));
        sifHeader.setNumeroDocumento(dossier.getCountryIssue().concat(dossier.getDocumentNumber()).toUpperCase());
        /*sifHeader.setFi();
        sifHeader.setFp();
        sifHeader.setTipoOperazione();
        sifHeader.setStato();*/
        sifHeader.setOperazione("");
        sifHeader.setIcd(sifParametersRepository.findByType(SifParametersEnum.ICD.toString()));

        Optional<Kiosk> kioskOpt = kioskRepository.findById(dossier.getNodeId());

        if (kioskOpt.isPresent()) {
            Kiosk kiosk = kioskOpt.get();
            if (kiosk.getIdMachine() != null && !kiosk.getIdMachine().isBlank()) {
                SifAuth currentSifAuth = sifAuthRepository.findByKiosk(kiosk); // Leggi SifAuth una volta prima del check
                boolean needsLogin = (currentSifAuth == null || sifAuthService.shouldDoSifAuth(kiosk));

                if (needsLogin) {
                    try {
                        Thread.sleep(ThreadLocalRandom.current().nextLong(1, 1000));
                    } catch (InterruptedException e) {
                        log.error("Error sleeping in buildSifHeader for dossier {}", dossier.getIdMessage(), e);
                        Thread.currentThread().interrupt();
                    }

                    AutoScheduledJobsLock autoScheduledJobsLock = autoScheduledJobsLockRepository.findByJobName(AutoScheduledJobsLockEnum.SIF_AUTH_JOB.getAutoScheduledJobsLockEnumValue());
                    while (autoScheduledJobsLock != null && !autoScheduledJobsLock.getApplication().equalsIgnoreCase(applicationNameProperty)) {
                        try {
                            Thread.sleep(1000);
                        } catch (InterruptedException e) {
                            log.error("Error during sleep in buildSifHeader for dossier {}", dossier.getIdMessage(), e);
                            Thread.currentThread().interrupt();
                        }
                        autoScheduledJobsLock = autoScheduledJobsLockRepository.findByJobName(AutoScheduledJobsLockEnum.SIF_AUTH_JOB.getAutoScheduledJobsLockEnumValue());
                    }

                    // Ricarica kiosk per sicurezza, anche se l'ID non dovrebbe cambiare
                    kiosk = kioskRepository.findById(dossier.getNodeId()).orElse(kiosk);

                    boolean loginTriggeredByThisThread = false;
                    try {
                        synchronized (kioskIdsDoingRunAndWaitSifLoginForSpecificKiosks) {
                            // Rileggi SifAuth dentro il blocco synchronized per la condizione più aggiornata
                            SifAuth authCheckInsideLock = sifAuthRepository.findByKiosk(kiosk);
                            if (!isKioskIdPresent(kiosk.getIdKiosk()) && (authCheckInsideLock == null || sifAuthService.shouldDoSifAuth(kiosk))) {
                                addKioskId(kiosk.getIdKiosk());
                                loginTriggeredByThisThread = true;
                            }
                        }

                        if (loginTriggeredByThisThread) {
                            dynamicSchedulingService.runAndWaitSifLoginForSpecificKiosks(List.of(kiosk));
                        } else {
                            while (isKioskIdPresent(kiosk.getIdKiosk())) { // Attendi se un altro thread sta facendo il login
                                try {
                                    Thread.sleep(1000);
                                } catch (InterruptedException e) {
                                    log.error("Error during sleep while waiting for other SIF login in buildSifHeader for dossier {}", dossier.getIdMessage(), e);
                                    Thread.currentThread().interrupt();
                                }
                            }
                        }
                    } finally {
                        if (loginTriggeredByThisThread) {
                            synchronized (kioskIdsDoingRunAndWaitSifLoginForSpecificKiosks) {
                                removeKioskId(kiosk.getIdKiosk());
                            }
                        }
                    }
                    // Dopo il tentativo di login (o l'attesa), ricarica SifAuth
                    currentSifAuth = sifAuthRepository.findByKiosk(kiosk);
                } // Fine if (needsLogin)

                // Verifica finale di Kiosk e SifAuth
                if (kiosk.getIdMachine() == null || kiosk.getIdMachine().isBlank() || currentSifAuth == null || currentSifAuth.getToken() == null /*|| sifAuthService.shouldDoSifAuth(kiosk) // L'ultimo check qui potrebbe essere ridondante se il login è appena avvenuto */) {
                    sifHeader.setIdChiamante(null);
                    sifHeader.setSecurityToken(null);
                    logService.saveEventLog(LogScopeEnum.INTERNAL.getValue(), "Dossier service BuildSifHeader", "Kiosk idMachine or Kiosk token not found or expired for dossier " + dossier.getIdMessage(), null);
                } else {
                    sifHeader.setIdChiamante(kiosk.getIdMachine());
                    if (currentSifAuth.getIdSifAuth() != null) { // Assicurati che SifAuth sia un'entità gestita
                        //entityManager.refresh(currentSifAuth); // Refresh dell'istanza SifAuth
                        sifHeader.setSecurityToken(currentSifAuth.getToken());
                    } else {
                        log.warn("SifAuth for kiosk {} has no ID, cannot refresh. Token might be stale.", kiosk.getIdKiosk());
                        sifHeader.setSecurityToken(currentSifAuth.getToken());
                    }
                }
            } else if (sifAuthService.shouldDoSifAuth(kiosk)) {
                sifHeader.setIdChiamante(null);
                sifHeader.setSecurityToken(null);
                logService.saveEventLog(LogScopeEnum.INTERNAL.getValue(), "Dossier service BuildSifHeader", "Kiosk idMachine blank and SIF Auth needed for dossier " + dossier.getIdMessage(), null);
            }
        } else {
            sifHeader.setIdChiamante(null);
            sifHeader.setSecurityToken(null);
            logService.saveEventLog(LogScopeEnum.INTERNAL.getValue(), "Dossier service BuildSifHeader", "Kiosk " + dossier.getNodeId() + " not found for dossier " + dossier.getIdMessage(), null);
        }
        return sifHeader;
    }

    @Transactional
    public SifHeaderType buildSifHeaderType(Dossier dossier) {
        SifHeaderType sifHeaderType = new SifHeaderType();
        sifHeaderType.setOffline(false);
        sifHeaderType.setIdUser(Boolean.TRUE.equals(dossier.getEgateHandled()) ? sifParametersRepository.findByType(SifParametersEnum.END_USER_ID_EGATE.toString()) : sifParametersRepository.findByType(SifParametersEnum.END_USER_ID_KIOSK.toString()));
        sifHeaderType.setNumeroDocumento(dossier.getCountryIssue().concat(dossier.getDocumentNumber()).toUpperCase());
        /*sifHeaderType.setFi();
        sifHeaderType.setFp();
        sifHeaderType.setTipoOperazione();
        sifHeaderType.setStato();*/
        sifHeaderType.setOperazione("");
        sifHeaderType.setIcd(sifParametersRepository.findByType(SifParametersEnum.ICD.toString()));

        Optional<Kiosk> kioskOpt = kioskRepository.findById(dossier.getNodeId());

        if (kioskOpt.isPresent()) {
            Kiosk kiosk = kioskOpt.get();
            if (kiosk.getIdMachine() != null && !kiosk.getIdMachine().isBlank()) {
                SifAuth currentSifAuth = sifAuthRepository.findByKiosk(kiosk);
                boolean needsLogin = (currentSifAuth == null || sifAuthService.shouldDoSifAuth(kiosk));

                if (needsLogin) {
                    try {
                        Thread.sleep(ThreadLocalRandom.current().nextLong(1, 1000));
                    } catch (InterruptedException e) {
                        log.error("Error sleeping in buildSifHeaderType for dossier {}", dossier.getIdMessage(), e);
                        Thread.currentThread().interrupt();
                    }

                    AutoScheduledJobsLock autoScheduledJobsLock = autoScheduledJobsLockRepository.findByJobName(AutoScheduledJobsLockEnum.SIF_AUTH_JOB.getAutoScheduledJobsLockEnumValue());
                    while (autoScheduledJobsLock != null && !autoScheduledJobsLock.getApplication().equalsIgnoreCase(applicationNameProperty)) {
                        try {
                            Thread.sleep(1000);
                        } catch (InterruptedException e) {
                            log.error("Error during sleep in buildSifHeaderType for dossier {}", dossier.getIdMessage(), e);
                            Thread.currentThread().interrupt();
                        }
                        autoScheduledJobsLock = autoScheduledJobsLockRepository.findByJobName(AutoScheduledJobsLockEnum.SIF_AUTH_JOB.getAutoScheduledJobsLockEnumValue());
                    }

                    kiosk = kioskRepository.findById(dossier.getNodeId()).orElse(kiosk);

                    boolean loginTriggeredByThisThread = false;
                    try {
                        synchronized (kioskIdsDoingRunAndWaitSifLoginForSpecificKiosks) {
                            SifAuth authCheckInsideLock = sifAuthRepository.findByKiosk(kiosk);
                            if (!isKioskIdPresent(kiosk.getIdKiosk()) && (authCheckInsideLock == null || sifAuthService.shouldDoSifAuth(kiosk))) {
                                addKioskId(kiosk.getIdKiosk());
                                loginTriggeredByThisThread = true;
                            }
                        }

                        if (loginTriggeredByThisThread) {
                            dynamicSchedulingService.runAndWaitSifLoginForSpecificKiosks(List.of(kiosk));
                        } else {
                            while (isKioskIdPresent(kiosk.getIdKiosk())) {
                                try {
                                    Thread.sleep(1000);
                                } catch (InterruptedException e) {
                                    log.error("Error during sleep while waiting for other SIF login in buildSifHeaderType for dossier {}", dossier.getIdMessage(), e);
                                    Thread.currentThread().interrupt();
                                }
                            }
                        }
                    } finally {
                        if (loginTriggeredByThisThread) {
                            synchronized (kioskIdsDoingRunAndWaitSifLoginForSpecificKiosks) {
                                removeKioskId(kiosk.getIdKiosk());
                            }
                        }
                    }
                    currentSifAuth = sifAuthRepository.findByKiosk(kiosk);
                }

                if (kiosk.getIdMachine() == null || kiosk.getIdMachine().isBlank() || currentSifAuth == null || currentSifAuth.getToken() == null /*|| sifAuthService.shouldDoSifAuth(kiosk)*/) {
                    sifHeaderType.setIdChiamante(null);
                    sifHeaderType.setSecurityToken(null);
                    logService.saveEventLog(LogScopeEnum.INTERNAL.getValue(), "Dossier service BuildSifHeaderType", "Kiosk idMachine or Kiosk token not found or expired for dossier " + dossier.getIdMessage(), null);
                } else {
                    sifHeaderType.setIdChiamante(kiosk.getIdMachine());
                    if (currentSifAuth.getIdSifAuth() != null) {
                        //entityManager.refresh(currentSifAuth);
                        sifHeaderType.setSecurityToken(currentSifAuth.getToken());
                    } else {
                        log.warn("SifAuth for kiosk {} has no ID, cannot refresh. Token might be stale.", kiosk.getIdKiosk());
                        sifHeaderType.setSecurityToken(currentSifAuth.getToken());
                    }
                }
            } else if (sifAuthService.shouldDoSifAuth(kiosk)) {
                sifHeaderType.setIdChiamante(null);
                sifHeaderType.setSecurityToken(null);
                logService.saveEventLog(LogScopeEnum.INTERNAL.getValue(), "Dossier service BuildSifHeaderType", "Kiosk idMachine blank and SIF Auth needed for dossier " + dossier.getIdMessage(), null);
            }
        } else {
            sifHeaderType.setIdChiamante(null);
            sifHeaderType.setSecurityToken(null);
            logService.saveEventLog(LogScopeEnum.INTERNAL.getValue(), "Dossier service BuildSifHeaderType", "Kiosk " + dossier.getNodeId() + " not found for dossier " + dossier.getIdMessage(), null);
        }
        return sifHeaderType;
    }

    @Transactional
    public void updateDossierStatus(String id, String status) {
        dossierRepository.updateDossierStatus(id, status);
        dossierRepository.flush();
    }

    /*public List<Dossier> findByStatus(List<String> dossierStatusEnumValues) {
        return dossierRepository.findAllByStatusIn(dossierStatusEnumValues);
    }*/

    @Transactional
    public void updateDossierApplication(String oldApplication, String newApplication) {
        dossierRepository.updateDossierApplication(oldApplication, newApplication);
        dossierRepository.flush();
    }

    public List<Dossier> findDossiersByApplicationNameAndStatus(String applicationName, List<String> dossierStatusEnumValue, int limit) {
        if (limit <= 0) {
            return dossierRepository.findAllByApplicationAndStatusIn(applicationName, dossierStatusEnumValue);
        } else {
            int page = 0;
            List<Dossier> results = new ArrayList<>();
            Page<Dossier> pageResult;

            do {
                pageResult = dossierRepository.findAllByApplicationAndStatusIn(applicationName, dossierStatusEnumValue, PageRequest.of(page, limit));
                results.addAll(pageResult.getContent());
                page++;
            } while (results.size() < limit && pageResult.hasNext());

            return results.subList(0, Math.min(results.size(), limit));
        }
    }

    public Optional<Dossier> findByIdMessage(String id) {
        return dossierRepository.findById(id);
    }

    public synchronized boolean isKioskIdPresent(String kioskId) {
        return kioskIdsDoingRunAndWaitSifLoginForSpecificKiosks.contains(kioskId);
    }

    public synchronized void addKioskId(String kioskId) {
        kioskIdsDoingRunAndWaitSifLoginForSpecificKiosks.add(kioskId);
    }

    public synchronized void removeKioskId(String kioskId) {
        kioskIdsDoingRunAndWaitSifLoginForSpecificKiosks.removeIf(id -> id.equals(kioskId));
    }

    /**
     * Crea o aggiorna un Kiosk/eGate e gestisce la relativa autenticazione SIF.
     *
     * @param nodeId ID del nodo (desiderato non null e non vuoto)
     * @param idMachine ID della macchina (desiderato non null e non vuoto)
     * @param isEgate Flag che indica se si tratta di un eGate
     * @param isEntryEgate Flag che indica se è un eGate di entrata (valido solo se isEgate=true)
     * @param source Origine della chiamata (per il logging)
     * @throws RuntimeException se nodeId o idMachine sono mancanti o vuoti
     */
    public void handleNewOrUpdatedKioskOrEgateAndSifAuth(String nodeId, String idMachine,
                                                   Boolean isEgate, Boolean isEntryEgate,
                                                   String source) throws RuntimeException {
        boolean missingNodeId = nodeId == null || nodeId.isBlank();
        boolean missingIdMachine = idMachine == null || idMachine.isBlank();
        if (missingNodeId || missingIdMachine) {
            String detail = missingNodeId && missingIdMachine
                    ? "nodeId and idMachine"
                    : missingNodeId
                    ? "nodeId; idMachine=" + idMachine
                    : "idMachine; nodeId=" + nodeId;
            log.warn("{}: Missing required {}", source, detail);
            throw new RuntimeException("handleNewOrUpdatedKioskOrEgateAndSifAuth() by " + source + ": Missing required " + detail);
        }

        final Kiosk[] kioskRef = new Kiosk[1];

        try {
            kioskRepository.findById(nodeId).ifPresentOrElse(existingKiosk -> {
                boolean needsUpdate = false;
                if (existingKiosk.getIdMachine() == null || existingKiosk.getIdMachine().isBlank()) {
                    existingKiosk.setIdMachine(idMachine);
                    needsUpdate = true;
                }
                if (isEgate != null) {
                    if (existingKiosk.getIsEgate() == null || !existingKiosk.getIsEgate().equals(isEgate)) {
                        existingKiosk.setIsEgate(isEgate);
                        needsUpdate = true;
                    }
                    if (isEntryEgate != null && isEgate && (existingKiosk.getIsEntryEgate() == null || !existingKiosk.getIsEntryEgate().equals(isEntryEgate))) {
                        existingKiosk.setIsEntryEgate(isEntryEgate);
                        needsUpdate = true;
                    }
                }
                if (needsUpdate) {
                    kioskRepository.saveAndFlush(existingKiosk);
                    log.info("{}: {} with nodeId {} updated with idMachine {}",
                            source,
                            isEgate != null && isEgate ? "eGate" : "Kiosk",
                            nodeId,
                            idMachine);
                    logService.saveEventLog(LogScopeEnum.INTERNAL.getValue(),
                            source,
                            (isEgate != null && isEgate ? "eGate" : "Kiosk") + " with nodeId " + nodeId +
                                    " updated with idMachine " + idMachine,
                            null);
                }
                kioskRef[0] = existingKiosk;
            }, () -> {
                Kiosk newKiosk = new Kiosk();
                newKiosk.setIdKiosk(nodeId);
                newKiosk.setIdMachine(idMachine);
                if (isEgate != null) newKiosk.setIsEgate(isEgate);
                if (isEntryEgate != null && isEgate != null && isEgate) newKiosk.setIsEntryEgate(isEntryEgate);
                kioskRepository.saveAndFlush(newKiosk);
                log.info("{}: saved new {} with nodeId {} and idMachine {}", source, isEgate != null && isEgate ? "eGate" : "Kiosk", nodeId, idMachine);
                kioskRef[0] = newKiosk;
            });
        } catch (Exception e) {
            log.error("{}: Error while creating/updating kiosk with nodeId {}: {}", source, nodeId, e.getMessage());
            throw new RuntimeException("handleNewOrUpdatedKioskOrEgateAndSifAuth() by " + source + ": Error while creating/updating kiosk with nodeId " + nodeId + ": " + e.getMessage());
        }
        if (kioskRef[0] != null && (kioskRef[0].getSifAuth() == null || sifAuthService.shouldDoSifAuth(kioskRef[0]))) waitAndDoSifLogin(kioskRef[0], source);
    }

    /**
     * Attende che non ci siano job SIF Auth in esecuzione e poi esegue il login SIF per il kiosk specificato
     * Utilizza il CopyOnWriteArraySet esistente in DossierService per la sincronizzazione
     * @param kiosk Il kiosk/eGate per cui eseguire il login
     * @param source Sorgente della chiamata (per logging)
     */
    private void waitAndDoSifLogin(Kiosk kiosk, String source) {
        String logId = null;
        AutoScheduledJobsLock autoScheduledJobsLock = autoScheduledJobsLockRepository.findByJobName(AutoScheduledJobsLockEnum.SIF_AUTH_JOB.getAutoScheduledJobsLockEnumValue());
        if (autoScheduledJobsLock != null) {
            log.info("{} is waiting to be able to do SifAuth for {} with nodeId {}",
                    source,
                    kiosk.getIsEgate() != null && kiosk.getIsEgate() ? "eGate" : "Kiosk",
                    kiosk.getIdKiosk());
            logId = logService.saveEventLog(LogScopeEnum.INTERNAL.getValue(),
                    source,
                    "Waiting to be able to do SifAuth for " +
                            (kiosk.getIsEgate() != null && kiosk.getIsEgate() ? "eGate" : "Kiosk") +
                            " with nodeId " + kiosk.getIdKiosk(),
                    null);
            while (autoScheduledJobsLock != null) {
                try {
                    Thread.sleep(1000L);
                } catch (InterruptedException e) {
                    log.error("Error sleeping {} thread for 1 second due to: {}", source, e.getMessage());
                    Thread.currentThread().interrupt();
                }
                autoScheduledJobsLock = autoScheduledJobsLockRepository.findByJobName(AutoScheduledJobsLockEnum.SIF_AUTH_JOB.getAutoScheduledJobsLockEnumValue());
            }
        }

        if (!isKioskIdPresent(kiosk.getIdKiosk())) {
            addKioskId(kiosk.getIdKiosk());
            log.info("{} triggered SifAuth for {} with nodeId {}",
                    source,
                    kiosk.getIsEgate() != null && kiosk.getIsEgate() ? "eGate" : "Kiosk",
                    kiosk.getIdKiosk());
            logService.saveEventLog(LogScopeEnum.INTERNAL.getValue(),
                    source,
                    "Triggered SifAuth for " +
                            (kiosk.getIsEgate() != null && kiosk.getIsEgate() ? "eGate" : "Kiosk") +
                            " with nodeId " + kiosk.getIdKiosk(),
                    null,
                    logId);
            dynamicSchedulingService.runAndWaitSifLoginForSpecificKiosks(List.of(kiosk));
            removeKioskId(kiosk.getIdKiosk());
        }
    }

    public Dossier getDossierByNodeIdAndDocumentData(String nodeId, String documentNumber, String countryIssue) {
        return dossierRepository.findFirstByNodeIdAndDocumentNumberAndCountryIssueOrderByCreationDateDesc(nodeId, documentNumber, countryIssue);
    }

    public Dossier getDossierByNodeId(String nodeId) {
        return dossierRepository.findByNodeId(nodeId); //TODO: questo va in errore perche' trova quasi sempre piu' risultati
    }

    @Transactional
    public void updateEgateDossier(Dossier existingEgateDossier, AbcEgateMessage abcEgateMessage) {
        dossierMapper.eventuallySetDossierFingerprint(abcEgateMessage, existingEgateDossier);
        dossierMapper.eventuallySetDossierFace(abcEgateMessage, existingEgateDossier);
        existingEgateDossier.setHasToBeProcessed(Boolean.parseBoolean(abcEgateMessage.getHas_to_be_processed()));
        dossierRepository.saveAndFlush(existingEgateDossier);
        /*dossierRepository.updateEgateDossier(existingEgateDossier);
        dossierRepository.flush();*/
    }

    public SearchByPersonalDataRequestMessageType mapDossierToSearchByPersonalDataRequest(Dossier dossier, PreFileController.PersonalDataSearchFilters filters) {
        //TODO:  da ultimare e correggere quando si saprà cosa fare
        //TODO:  molti campi non devono prendere direttamente valore da Dossier ma da un SIF mapping di questo (es: gender può avere valore 0001.01)
        SearchByPersonalDataRequestMessageType searchByPersonalDataRequestMessageType = new SearchByPersonalDataRequestMessageType();

        PagingRequestType pagingRequestType = new PagingRequestType();
        pagingRequestType.setPageNumber(null);
        pagingRequestType.setPageSize(null);
        searchByPersonalDataRequestMessageType.setPaging(pagingRequestType);

        SearchByPersonalDataRequestMessageType.ScopeModifiers scopeModifiers = new SearchByPersonalDataRequestMessageType.ScopeModifiers();
        NFPScopeModifiersType nfpScopeModifiersType = new NFPScopeModifiersType();
        OpenEndPeriodType openEndPeriodType = new OpenEndPeriodType();
        openEndPeriodType.setFrom(null);
        openEndPeriodType.setTo(null);
        nfpScopeModifiersType.setPeriod(openEndPeriodType);
        scopeModifiers.setNFP(nfpScopeModifiersType);
        TravelHistoryScopeModifiersType travelHistoryScopeModifiersType = new TravelHistoryScopeModifiersType();
        travelHistoryScopeModifiersType.setNoOfRecords(null);
        OpenEndPeriodType openEndPeriodType2 = new OpenEndPeriodType();
        openEndPeriodType2.setFrom(null);
        openEndPeriodType2.setTo(null);
        travelHistoryScopeModifiersType.setPeriod(openEndPeriodType2);
        scopeModifiers.setTravelHistory(travelHistoryScopeModifiersType);
        UpdatesLogScopeModifiersType updatesLogScopeModifiersType = new UpdatesLogScopeModifiersType();
        updatesLogScopeModifiersType.setNoOfRecords(null);
        OpenEndPeriodType openEndPeriodType3 = new OpenEndPeriodType();
        openEndPeriodType3.setFrom(null);
        openEndPeriodType3.setTo(null);
        updatesLogScopeModifiersType.setPeriod(openEndPeriodType3);
        scopeModifiers.setUpdatesLog(updatesLogScopeModifiersType);
        searchByPersonalDataRequestMessageType.setScopeModifiers(scopeModifiers);

        SearchByPersonalDataRequestMessageType.SearchData searchData = new SearchByPersonalDataRequestMessageType.SearchData();
        SearchByPersonalDataRequestMessageType.SearchData.TravellerFile travellerFile = new SearchByPersonalDataRequestMessageType.SearchData.TravellerFile();
        SearchByPersonalDataRequestMessageType.SearchData.TravellerFile.TravelDocument travelDocument = new SearchByPersonalDataRequestMessageType.SearchData.TravellerFile.TravelDocument();
        CT02CountryOfNationalitySearchField countryOfNationalitySearchField = new CT02CountryOfNationalitySearchField();
        countryOfNationalitySearchField.setValue(dossier.getCountryIssue());
        countryOfNationalitySearchField.setWeight(filters.getNazionalita().getWeight());
        travelDocument.setIssuingCountry(countryOfNationalitySearchField);
        PseudoDateSearchField pseudoDateSearchField = new PseudoDateSearchField();
        pseudoDateSearchField.setDate(dossier.getBirthdate());
        pseudoDateSearchField.setFrom(null); //TODO PRENDI DA PARAMETRO ENDPOINT
        pseudoDateSearchField.setTo(null); //TODO PRENDI DA PARAMETRO ENDPOINT
        pseudoDateSearchField.setIgnoreInexact(filters.getDataNascita().isIgnore());
        pseudoDateSearchField.setWeight(filters.getDataNascita().getWeight());
        travelDocument.setDateOfBirth(pseudoDateSearchField);
        NumberSearchField numberSearchField = new NumberSearchField();
        numberSearchField.setValue(dossier.getDocumentNumber());
        numberSearchField.setMode(filters.getNumeroDoc().getMode());
        numberSearchField.setWeight(filters.getNumeroDoc().getWeight()); //questo numeroDoc non ha "ignora" come vedo in maschera
        travelDocument.setDocumentNumber(numberSearchField);
        CT512TravelDocumentTypeSearchField travelDocumentTypeSearchField = new CT512TravelDocumentTypeSearchField();
        travelDocumentTypeSearchField.setValue(null); //TODO dovrei dire "passaporto"
        travelDocumentTypeSearchField.setWeight(filters.getTipoDoc().getWeight());
        travelDocument.setDocumentType(travelDocumentTypeSearchField);
        AnyNameSearchField anyNameSearchField = new AnyNameSearchField();
        anyNameSearchField.setAnyName(filters.isAnyName());
        anyNameSearchField.setMode(filters.getCognome().getMode());
        anyNameSearchField.setWeight(filters.getCognome().getWeight());
        anyNameSearchField.setValue(dossier.getSurname());
        travelDocument.setFamilyName(anyNameSearchField);
        CT04GenderSearchField genderSearchField = new CT04GenderSearchField();
        genderSearchField.setValue(dossier.getGender());
        genderSearchField.setWeight(filters.getSesso().getWeight());
        travelDocument.setGender(genderSearchField);
        PseudoDateSearchField pseudoDateSearchField2 = new PseudoDateSearchField();
        pseudoDateSearchField2.setFrom(dossier.getIssueDate());
        pseudoDateSearchField2.setTo(dossier.getExpiryDate());
        pseudoDateSearchField2.setWeight(null); //TODO PRENDI DA quale PARAMETRO ENDPOINT?
        pseudoDateSearchField2.setDate(dossier.getExpiryDate());
        pseudoDateSearchField2.setIgnoreInexact(null); //TODO PRENDI DA quale PARAMETRO ENDPOINT?
        travelDocument.setValidUntil(pseudoDateSearchField2);
        travellerFile.setTravelDocument(travelDocument);
        searchData.setTravellerFile(travellerFile);
        searchByPersonalDataRequestMessageType.setSearchData(searchData);

        return searchByPersonalDataRequestMessageType;
    }

    public SearchByPersonalDataInVISRequestMessageType mapDossierToSearchByPersonalDataInVISRequest(Dossier dossier, PreFileController.PersonalDataSearchFilters filters) {
        //TODO:  da ultimare e correggere quando si saprà cosa fare
        //TODO:  molti campi non devono prendere direttamente valore da Dossier ma da un SIF mapping di questo (es: gender può avere valore 0001.01)
        SearchByPersonalDataInVISRequestMessageType searchByPersonalDataInVISRequestMessageType = new SearchByPersonalDataInVISRequestMessageType();

        PagingRequestType pagingRequestType = new PagingRequestType();
        pagingRequestType.setPageNumber(null);
        pagingRequestType.setPageSize(null);
        searchByPersonalDataInVISRequestMessageType.setPaging(pagingRequestType);

        SearchByPersonalDataInVISRequestMessageType.ScopeModifiers scopeModifiers = new SearchByPersonalDataInVISRequestMessageType.ScopeModifiers();
        scopeModifiers.setVisaApplications(null);
        searchByPersonalDataInVISRequestMessageType.setScopeModifiers(scopeModifiers);

        SearchByPersonalDataInVISRequestMessageType.SearchData searchData = new SearchByPersonalDataInVISRequestMessageType.SearchData();
        SearchByPersonalDataInVISRequestMessageType.SearchData.TravelDocument travelDocument = new SearchByPersonalDataInVISRequestMessageType.SearchData.TravelDocument();
        CT02CountryOfNationalitySearchField countryOfNationalitySearchField = new CT02CountryOfNationalitySearchField();
        countryOfNationalitySearchField.setValue(dossier.getCountryIssue());
        countryOfNationalitySearchField.setWeight(filters.getNazionalita().getWeight());
        travelDocument.setIssuingCountry(countryOfNationalitySearchField);
        PseudoDateSearchField pseudoDateSearchField = new PseudoDateSearchField();
        pseudoDateSearchField.setDate(dossier.getBirthdate());
        pseudoDateSearchField.setFrom(null); //TODO PRENDI DA PARAMETRO ENDPOINT
        pseudoDateSearchField.setTo(null); //TODO PRENDI DA PARAMETRO ENDPOINT
        pseudoDateSearchField.setIgnoreInexact(filters.getDataNascita().isIgnore());
        pseudoDateSearchField.setWeight(filters.getDataNascita().getWeight());
        travelDocument.setDateOfBirth(pseudoDateSearchField);
        NumberSearchField numberSearchField = new NumberSearchField();
        numberSearchField.setValue(dossier.getDocumentNumber());
        numberSearchField.setMode(filters.getNumeroDoc().getMode());
        numberSearchField.setWeight(filters.getNumeroDoc().getWeight()); //questo numeroDoc non ha "ignora" come vedo in maschera
        travelDocument.setDocumentNumber(numberSearchField);
        CT512TravelDocumentTypeSearchField travelDocumentTypeSearchField = new CT512TravelDocumentTypeSearchField();
        travelDocumentTypeSearchField.setValue(null); //TODO dovrei dire "passaporto"
        travelDocumentTypeSearchField.setWeight(filters.getTipoDoc().getWeight());
        travelDocument.setDocumentType(travelDocumentTypeSearchField);
        AnyNameSearchField anyNameSearchField = new AnyNameSearchField();
        anyNameSearchField.setAnyName(filters.isAnyName());
        anyNameSearchField.setMode(filters.getCognome().getMode());
        anyNameSearchField.setWeight(filters.getCognome().getWeight());
        anyNameSearchField.setValue(dossier.getSurname());
        travelDocument.setFamilyName(anyNameSearchField);
        CT04GenderSearchField genderSearchField = new CT04GenderSearchField();
        genderSearchField.setValue(dossier.getGender());
        genderSearchField.setWeight(filters.getSesso().getWeight());
        travelDocument.setGender(genderSearchField);
        PseudoDateSearchField pseudoDateSearchField2 = new PseudoDateSearchField();
        pseudoDateSearchField2.setFrom(dossier.getIssueDate());
        pseudoDateSearchField2.setTo(dossier.getExpiryDate());
        pseudoDateSearchField2.setWeight(null); //TODO PRENDI DA quale PARAMETRO ENDPOINT?
        pseudoDateSearchField2.setDate(dossier.getExpiryDate());
        pseudoDateSearchField2.setIgnoreInexact(null); //TODO PRENDI DA quale PARAMETRO ENDPOINT?
        travelDocument.setValidUntil(pseudoDateSearchField2);
        searchData.setTravelDocument(travelDocument);

        searchByPersonalDataInVISRequestMessageType.setSearchData(searchData);

        return searchByPersonalDataInVISRequestMessageType;
    }

    public IdentificationResultRequestMessageType mapDossierToIdentificationResultRequest(Dossier dossier, String travellerFileId, String visaStickerNumber, String visaApplication) {
        //TODO:  da ultimare e correggere quando si saprà cosa fare
        //TODO:  molti campi non devono prendere direttamente valore da Dossier ma da un SIF mapping di questo
        IdentificationResultRequestMessageType identificationResultRequestMessageType = new IdentificationResultRequestMessageType();

        identificationResultRequestMessageType.setBorderCrossingPoint(sifParametersRepository.findByType(SifParametersEnum.BORDER_CROSSING_POINT.toString()));

        IdentificationResultRequestMessageType.CollectedData collectedData = new IdentificationResultRequestMessageType.CollectedData();
        IdentificationResultRequestMessageType.CollectedData.TravelDocument travelDocument = new IdentificationResultRequestMessageType.CollectedData.TravelDocument();
        travelDocument.setDocumentNumber(dossier.getDocumentNumber());
        collectedData.setTravelDocument(travelDocument);
        identificationResultRequestMessageType.setCollectedData(collectedData);

        WFEScopeModifiersType wfeScopeModifiersType = new WFEScopeModifiersType();
        CalculatorScopeType calculatorScopeType = new CalculatorScopeType();
        CalculatorScopeType.Entry entry = new CalculatorScopeType.Entry();
        entry.setEntryDate(null);
        calculatorScopeType.setEntry(entry);
        CalculatorScopeType.Exit exit = new CalculatorScopeType.Exit();
        exit.setExitDate(null);
        calculatorScopeType.setExit(exit);
        wfeScopeModifiersType.setCalculator(calculatorScopeType);
        wfeScopeModifiersType.setCollectedData(null);
        WFEScopeModifiersWithoutCalculatorType.ResponseData responseData = new WFEScopeModifiersWithoutCalculatorType.ResponseData();
        NFPScopeModifiersType nfpScopeModifiersType = new NFPScopeModifiersType();
        OpenEndPeriodType openEndPeriodType = new OpenEndPeriodType();
        openEndPeriodType.setFrom(null);
        openEndPeriodType.setTo(null);
        nfpScopeModifiersType.setPeriod(openEndPeriodType);
        responseData.setNFP(nfpScopeModifiersType);
        TravelHistoryScopeModifiersType travelHistoryScopeModifiersType = new TravelHistoryScopeModifiersType();
        travelHistoryScopeModifiersType.setNoOfRecords(null);
        OpenEndPeriodType openEndPeriodType2 = new OpenEndPeriodType();
        openEndPeriodType2.setFrom(null);
        openEndPeriodType2.setTo(null);
        travelHistoryScopeModifiersType.setPeriod(openEndPeriodType2);
        responseData.setTravelHistory(travelHistoryScopeModifiersType);
        UpdatesLogScopeModifiersType updatesLogScopeModifiersType = new UpdatesLogScopeModifiersType();
        updatesLogScopeModifiersType.setNoOfRecords(null);
        OpenEndPeriodType openEndPeriodType3 = new OpenEndPeriodType();
        openEndPeriodType3.setFrom(null);
        openEndPeriodType3.setTo(null);
        updatesLogScopeModifiersType.setPeriod(openEndPeriodType3);
        responseData.setUpdatesLog(updatesLogScopeModifiersType);
        wfeScopeModifiersType.setResponseData(responseData);
        identificationResultRequestMessageType.setScopeModifiers(wfeScopeModifiersType);

        IdentificationResultRequestMessageType.SelectedResponseData selectedResponseData = new IdentificationResultRequestMessageType.SelectedResponseData();
        IdentificationResultRequestMessageType.SelectedResponseData.EESAlphanumericIdentification eesAlphanumericIdentification = new IdentificationResultRequestMessageType.SelectedResponseData.EESAlphanumericIdentification();
        IdentificationResultRequestMessageType.SelectedResponseData.EESAlphanumericIdentification.TravellerFile travellerFile = new IdentificationResultRequestMessageType.SelectedResponseData.EESAlphanumericIdentification.TravellerFile();
        travellerFile.setTravellerFileID(travellerFileId);
        eesAlphanumericIdentification.setTravellerFile(travellerFile);
        selectedResponseData.setEESAlphanumericIdentification(eesAlphanumericIdentification);
        IdentificationResultRequestMessageType.SelectedResponseData.EESIdentification eesIdentification = new IdentificationResultRequestMessageType.SelectedResponseData.EESIdentification();
        IdentificationResultRequestMessageType.SelectedResponseData.EESIdentification.TravellerFile travellerFile2 = new IdentificationResultRequestMessageType.SelectedResponseData.EESIdentification.TravellerFile();
        travellerFile2.setTravellerFileID(travellerFileId);
        eesIdentification.setTravellerFile(travellerFile2);
        selectedResponseData.setEESIdentification(eesIdentification);

        if(visaApplication != null && !visaApplication.isBlank() && visaStickerNumber != null && !visaStickerNumber.isBlank()) {
            responseData.setVisaApplications(visaApplication);
            IdentificationResultRequestMessageType.SelectedResponseData.VISAlphanumericIdentification visAlphanumericIdentification = new IdentificationResultRequestMessageType.SelectedResponseData.VISAlphanumericIdentification();

            DirectVisaInformationType directVisaInformation = new DirectVisaInformationType();
            VisaInformationApplicationType visaInformationApplication = new VisaInformationApplicationType();
            visaInformationApplication.setVisaApplicationNumber(visaApplication);
            directVisaInformation.setVisaInformationApplication(visaInformationApplication);
            VisaInformationType visaInformationType = new VisaInformationType();
            visaInformationType.setDurationOfStay(0);
            visaInformationType.setNumberOfEntries(null);
            PeriodType periodType = new PeriodType();
            periodType.setBegin(null);
            periodType.setEnd(null);
            visaInformationType.setPeriodOfValidity(periodType);
            visaInformationType.setVisaStickerNumber(visaStickerNumber);
            VisaTypeType visaTypeType = new VisaTypeType();
            visaTypeType.setVisaNewType(null);
            visaTypeType.setVisaStickerFilledInManually(false);
            visaInformationType.setVisaType(visaTypeType);
            visaInformationType.setVLTVIndicator(false);
            directVisaInformation.setVisaInformationVSN(visaInformationType);
            visAlphanumericIdentification.setDirectVisaInformation(directVisaInformation);
            selectedResponseData.setVISAlphanumericIdentification(visAlphanumericIdentification);
            IdentificationResultRequestMessageType.SelectedResponseData.VISDirectIdentification visDirectIdentification = new IdentificationResultRequestMessageType.SelectedResponseData.VISDirectIdentification();

            DirectVisaInformationType directVisaInformation2 = new DirectVisaInformationType();
            VisaInformationApplicationType visaInformationApplication2 = new VisaInformationApplicationType();
            visaInformationApplication2.setVisaApplicationNumber(visaApplication);
            directVisaInformation2.setVisaInformationApplication(visaInformationApplication2);
            VisaInformationType visaInformationType2 = new VisaInformationType();
            visaInformationType2.setDurationOfStay(0);
            visaInformationType2.setNumberOfEntries(null);
            PeriodType periodType2 = new PeriodType();
            periodType2.setBegin(null);
            periodType2.setEnd(null);
            visaInformationType2.setPeriodOfValidity(periodType2);
            visaInformationType2.setVisaStickerNumber(visaStickerNumber);
            VisaTypeType visaTypeType2 = new VisaTypeType();
            visaTypeType2.setVisaNewType(null);
            visaTypeType2.setVisaStickerFilledInManually(false);
            visaInformationType2.setVisaType(visaTypeType2);
            visaInformationType2.setVLTVIndicator(false);
            directVisaInformation2.setVisaInformationVSN(visaInformationType2);
            visDirectIdentification.setDirectVisaInformation(directVisaInformation2);
            IdentificationResultRequestMessageType.SelectedResponseData.VISDirectIdentification visDirectIdentification2 = new IdentificationResultRequestMessageType.SelectedResponseData.VISDirectIdentification();

            DirectVisaInformationType directVisaInformation3 = new DirectVisaInformationType();
            VisaInformationApplicationType visaInformationApplication3 = new VisaInformationApplicationType();
            visaInformationApplication3.setVisaApplicationNumber(visaApplication);
            directVisaInformation3.setVisaInformationApplication(visaInformationApplication3);
            VisaInformationType visaInformationType3 = new VisaInformationType();
            visaInformationType3.setDurationOfStay(0);
            visaInformationType3.setNumberOfEntries(null);
            PeriodType periodType3 = new PeriodType();
            periodType3.setBegin(null);
            periodType3.setEnd(null);
            visaInformationType3.setPeriodOfValidity(periodType3);
            visaInformationType3.setVisaStickerNumber(visaStickerNumber);
            VisaTypeType visaTypeType3 = new VisaTypeType();
            visaTypeType3.setVisaNewType(null);
            visaTypeType3.setVisaStickerFilledInManually(false);
            visaInformationType3.setVisaType(visaTypeType3);
            visaInformationType3.setVLTVIndicator(false);
            directVisaInformation3.setVisaInformationVSN(visaInformationType3);
            visDirectIdentification2.setDirectVisaInformation(directVisaInformation3);
            selectedResponseData.setVISDirectIdentification(visDirectIdentification2);

            IdentificationResultRequestMessageType.SelectedResponseData.VISIdentification visIdentification = new IdentificationResultRequestMessageType.SelectedResponseData.VISIdentification();
            IdentificationResultRequestMessageType.SelectedResponseData.VISIdentification.VisaApplication visaApplication2 = new IdentificationResultRequestMessageType.SelectedResponseData.VISIdentification.VisaApplication();
            visaApplication2.setVisaApplicationNumber(visaApplication);
            visaApplication2.setVisaStickerNumber(visaStickerNumber);
            visIdentification.setVisaApplication(visaApplication2);
            selectedResponseData.setVISIdentification(visIdentification);
        }
        identificationResultRequestMessageType.setSelectedResponseData(selectedResponseData);

        return identificationResultRequestMessageType;
    }

    /*public List<Dossier> getFreshDossiersToBeProcessedByDossierResponseJob(String applicationName, String dossierStatusEnumValue, int limit) {
        if (limit <= 0) {
            return dossierRepository.findAllByApplicationAndStatus(applicationName, dossierStatusEnumValue);
        } else {
            int page = 0;
            List<Dossier> results = new ArrayList<>();
            Page<Dossier> pageResult;

            do {
                pageResult = dossierRepository.findAllByApplicationAndStatus(applicationName, dossierStatusEnumValue, PageRequest.of(page, limit));
                results.addAll(pageResult.getContent());
                page++;
            } while (results.size() < limit && pageResult.hasNext());

            return results.subList(0, Math.min(results.size(), limit));
        }
    }*/

    /*private void printStartBorderControlResponse(StartBorderControlResponseMessageType startBorderControlResponseMessageType) {
        WFEResponseType startBorderControlWFEResponseType = startBorderControlResponseMessageType.getResponse();
        startBorderControlWFEResponseType.getBorderCrossingPoint();
        startBorderControlWFEResponseType.getCalculator().getEntry();
        startBorderControlWFEResponseType.getCalculator().getExit();
        startBorderControlWFEResponseType.getCollectedData().getBorderGuardPreEnrolledBiometrics();
        startBorderControlWFEResponseType.getCollectedData().getFTDInformation();
        startBorderControlWFEResponseType.getCollectedData().getPersonStatus();
        startBorderControlWFEResponseType.getCollectedData().getSSSPreEnrolledBiometrics();
        startBorderControlWFEResponseType.getCollectedData().getTravelDocument();
        startBorderControlWFEResponseType.getCollectedData().getTCNType();
        startBorderControlWFEResponseType.getFlags().getFlag();
        startBorderControlWFEResponseType.getRequiredData().getData();
        startBorderControlWFEResponseType.getResponseData().getComparisonResults();
        startBorderControlWFEResponseType.getResponseData().getEESIdentification();
        startBorderControlWFEResponseType.getResponseData().getEESSearchAndVerification();
        startBorderControlWFEResponseType.getResponseData().getETIASSearch();
        startBorderControlWFEResponseType.getResponseData().getVISIdentification();
        startBorderControlWFEResponseType.getResponseData().getVISSearchAndVerification();
        startBorderControlWFEResponseType.getResponsible().getAuthority();
        startBorderControlWFEResponseType.getResponsible().getUser();
        startBorderControlWFEResponseType.getResponsible().getEndUserRole();
        startBorderControlWFEResponseType.getTimestamp().getHour();  //get day, year, etc..
        //the startBorderControlResponseMessageType (or its nested WFEResponseType) does not contain the transaction ID, so I suppose to find it in the header of the received response
    }*/
}
