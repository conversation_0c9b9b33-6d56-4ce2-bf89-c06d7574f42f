package com.reco.ees.gateway.service;

import com.reco.ees.gateway.repository.CertificateRequestStatusRepository;
import com.reco.ees.gateway.repository.model.CertificateRequestStatus;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalTime;
import java.util.Date;

@Service
public class CertificateRequestStatusService {
    @Value("${application.name}")
    private String applicationName;
    /*@Value("${certificates.concurrency.request}")
    private String concurrencyRequest;*/

    private final CertificateRequestStatusRepository certificateRequestStatusRepository;

    public CertificateRequestStatusService(CertificateRequestStatusRepository certificateRequestStatusRepository) {
        this.certificateRequestStatusRepository = certificateRequestStatusRepository;
    }

    @Transactional
    public void saveOrUpdateCertificateRequestStatus(int certificateStatus) {
        CertificateRequestStatus newStatus = new CertificateRequestStatus();
        newStatus.setStatus(certificateStatus);
        newStatus.setDate(new Date());
        newStatus.setApplication(applicationName);
        certificateRequestStatusRepository.saveAndFlush(newStatus);
        /*CertificateRequestStatus existingStatus = certificateRequestStatusRepository.findById(CertificateStatusEnum.getNameByValue(certificateStatus)).orElse(null);
        if (existingStatus == null) {
            CertificateRequestStatus newStatus = new CertificateRequestStatus();
            newStatus.setStatus(certificateStatus);
            newStatus.setDate(new Date());
            newStatus.setApplication(applicationName);
            certificateRequestStatusRepository.save(newStatus);
        } else {
            existingStatus.setDate(new Date());
            existingStatus.setApplication(applicationName);
            certificateRequestStatusRepository.save(existingStatus);
        }*/
        /*if(certificateStatus != CertificateStatusEnum.CERTIFICATE_JOB_WON_CONCURRENCY.getCertificateStatusEnumValue()) {
            certificateRequestStatusRepository.facesContention(concurrencyRequest, certificateStatus, applicationName, new Date());
        } else {
            CertificateRequestStatus existingWonConcurrencyStatus = certificateRequestStatusRepository.findFirstByStatusOrderByDateDesc(certificateStatus);
            if (existingWonConcurrencyStatus != null && new Date().getTime() - existingWonConcurrencyStatus.getDate().getTime() > 3600000) { //c'è ma è più vecchio di 1 ora
                resetConcurrency(existingWonConcurrencyStatus.getIdCertificateRequestStatus());
                certificateRequestStatusRepository.facesContention(concurrencyRequest, certificateStatus, applicationName, new Date());
            } else if (existingWonConcurrencyStatus == null) {
                certificateRequestStatusRepository.facesContention(concurrencyRequest, certificateStatus, applicationName, new Date());
            }
        }*/
    }

    public CertificateRequestStatus getCertificateRequestStatusByStatus(Integer certificateStatus) {
        return certificateRequestStatusRepository.findByStatus(certificateStatus);
    }

    public void deleteAll() {
        certificateRequestStatusRepository.deleteAll();
        certificateRequestStatusRepository.flush();
    }

    public boolean isWithinTimeRange(LocalTime startHour, LocalTime endHour) {
        long offsetSeconds = 30; //30 secondi di tolleranza
        LocalTime startHourWithOffset = startHour.minusSeconds(offsetSeconds);
        LocalTime endHourWithOffset = endHour.plusSeconds(offsetSeconds);
        LocalTime now = LocalTime.now();
        if (startHourWithOffset.isBefore(endHourWithOffset)) return !now.isBefore(startHourWithOffset) && !now.isAfter(endHourWithOffset); // Caso in cui startHour e endHour sono nello stesso giorno
        else return !now.isBefore(startHourWithOffset) || !now.isAfter(endHourWithOffset); // Caso in cui startHour e endHour sono a cavallo di mezzanotte
    }

    public int decreaseDateByHours(int hours) {
        int records = certificateRequestStatusRepository.decreaseDateByHours(hours);
        certificateRequestStatusRepository.flush();
        return records;
    }

    /*@Transactional
    public void resetConcurrency(String id) {
        certificateRequestStatusRepository.deleteAllByIdCertificateRequestStatus(id);
    }*/

    /*@Transactional
    public void updateCertificateApplication(String oldApplication, String newApplication) {
        certificateRequestStatusRepository.updateCertificateApplication(oldApplication, newApplication);
    }*/
}
