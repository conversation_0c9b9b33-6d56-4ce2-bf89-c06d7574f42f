package com.reco.ees.gateway.service;

import com.reco.ees.gateway.enums.ParametersEnum;
import com.reco.ees.gateway.enums.SifAuthStatusEnum;
import com.reco.ees.gateway.job.CertificatesJob;
import com.reco.ees.gateway.job.LuoghiAndDocumentiSDIJob;
import com.reco.ees.gateway.job.SifAuthJob;
import com.reco.ees.gateway.repository.SifAuthRepository;
import com.reco.ees.gateway.repository.model.Kiosk;
import com.reco.ees.gateway.repository.model.SifAuth;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.annotation.Order;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ConcurrentTaskScheduler;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.stereotype.Service;

import java.time.*;
import java.util.List;
import java.util.concurrent.*;

@Slf4j
@Service
@Order(7)
public class DynamicSchedulingService { //TODO: usa eventLog per salvare alcuni log importanti di questa classe
    private boolean restartingApp = false;
    private volatile ScheduledFuture<?> gracefulAutoRestartFuture;
    private ConfigurableApplicationContext context;
    boolean gracefulAutoRestartEnabled = false;
    String gracefulAutoRestartTime = "01:10:15";
    private ExecutorService luoghiAndDocumentiSDIExecutorService;
    private ExecutorService certificatesExecutorService;
    private volatile CompletableFuture<Void> sifAuthFutureTaskCompleted;
    private volatile ScheduledFuture<?> sifAuthFutureTask;
    long sifAuthFixedDelayHours;
    LocalTime certificateStartHour;
    LocalTime certificateEndHour;
    LocalTime luoghiAndDocumentiSDIStartHour;
    LocalTime luoghiAndDocumentiSDIEndHour;
    boolean luoghiAndDocumentiSDIFirstBoot = true;
    boolean certificateFirstBoot = true;
    private volatile CompletableFuture<Void> luoghiAndDocumentiSDIFutureTaskCompleted;
    long luoghiAndDocumentiSDIFixedDelayHours;
    private volatile ScheduledFuture<?> luoghiAndDocumentiSDIFutureTask;
    private volatile CompletableFuture<Void> certificateFutureTaskCompleted;
    long certificateFixedDelayHours;
    private volatile ScheduledFuture<?> certificateFutureTask;
    private ScheduledExecutorService scheduler;

    /*@Autowired
    private EntityManager entityManager;*/
    private final LuoghiAndDocumentiSDIJob luoghiAndDocumentiSDIJob;
    private final CertificatesJob certificatesJob;
    private final SifAuthJob sifAuthJob;
    private TaskScheduler taskScheduler;
    private final ParameterService parameterService;

    @Value("${spring.threads.type:virtual}")
    private String threadsType;
    /*@Autowired
    private SifAuthService sifAuthService;*/
    private final SifAuthRepository sifAuthRepository;
    private final SDIDownloadRequestStatusService sdiDownloadRequestStatusService;
    private final CertificateRequestStatusService certificateRequestStatusService;

    public DynamicSchedulingService(ConfigurableApplicationContext context, LuoghiAndDocumentiSDIJob luoghiAndDocumentiSDIJob, CertificatesJob certificatesJob, SifAuthJob sifAuthJob, TaskScheduler taskScheduler, ParameterService parameterService, SifAuthRepository sifAuthRepository, SDIDownloadRequestStatusService sdiDownloadRequestStatusService, CertificateRequestStatusService certificateRequestStatusService) {
        this.context = context;
        this.luoghiAndDocumentiSDIJob = luoghiAndDocumentiSDIJob;
        this.certificatesJob = certificatesJob;
        this.sifAuthJob = sifAuthJob;
        this.taskScheduler = taskScheduler;
        this.parameterService = parameterService;
        this.sifAuthRepository = sifAuthRepository;
        this.sdiDownloadRequestStatusService = sdiDownloadRequestStatusService;
        this.certificateRequestStatusService = certificateRequestStatusService;
    }

    @PostConstruct
    private void init() {
        if ("virtual".equalsIgnoreCase(threadsType)) {
            taskScheduler = createVirtualThreadScheduler();
            scheduler = Executors.newScheduledThreadPool(3, Thread.ofVirtual().factory());
            luoghiAndDocumentiSDIExecutorService = Executors.newSingleThreadExecutor(Thread.ofVirtual().factory());
            certificatesExecutorService = Executors.newSingleThreadExecutor(Thread.ofVirtual().factory());
        } else {
            taskScheduler = new ThreadPoolTaskScheduler();
            scheduler = Executors.newScheduledThreadPool(3);
            ((ThreadPoolTaskScheduler) taskScheduler).initialize();
            luoghiAndDocumentiSDIExecutorService = Executors.newSingleThreadExecutor();
            certificatesExecutorService = Executors.newSingleThreadExecutor();
        }
        List<SifAuth> sifAuthsKO = sifAuthRepository.findByAppstatus(SifAuthStatusEnum.AUTH_KO.getSifAuthStatusEnumValue());
        if(!sifAuthsKO.isEmpty()) scheduleSifAuthJob(true);
        else scheduleSifAuthJob();
        scheduleCertificatesJob();
        scheduleLuoghiAndDocumentiSDIJob();
    }

    private TaskScheduler createVirtualThreadScheduler() {
        ScheduledExecutorService executor = Executors.newScheduledThreadPool(2, Thread.ofVirtual().factory()); //se hai problemi allora sostituisci il "2" di corePoolSize con "Runtime.getRuntime().availableProcessors()"
        return new ConcurrentTaskScheduler(executor);
    }

    public synchronized void scheduleSifAuthJob(boolean... forceCoreLogicExecution) {
        sifAuthFixedDelayHours = Long.parseLong(parameterService.getParameterById(ParametersEnum.SIF_AUTHENTICATION_REFRESH_FREQUENCY_HOURS.getParametersEnumValue()).getValue());
        Duration delay = Duration.ofHours(sifAuthFixedDelayHours);
        if (sifAuthFutureTask != null) {
            try {
                sifAuthFutureTaskCompleted.get(); //TODO: se hai problemi allora usa synchronized(sifAuthFutureTaskCompleted){} ovunque usato qui
            } catch (InterruptedException | ExecutionException e) {
                log.error("Error in waiting sifAuthFutureTaskCompleted by scheduleSifAuthJob()", e);
            }
            sifAuthFutureTask.cancel(true);
        }
        try {
            sifAuthFutureTask = taskScheduler.scheduleWithFixedDelay(() -> {
                sifAuthFutureTaskCompleted = new CompletableFuture<>();
                log.info("DynamicSchedulingService running sifAuthJob on a {} thread with forceCoreLogicExecution set to {}", Thread.currentThread().isVirtual() ? "virtual" : "platform", (forceCoreLogicExecution != null && forceCoreLogicExecution.length > 0 && forceCoreLogicExecution[0]) ? forceCoreLogicExecution[0] : false); //, forceCoreLogicExecution != null && forceCoreLogicExecution.length > 0 && forceCoreLogicExecution[0]);
                /*if(kiosks != null && !kiosks.isEmpty()) sifAuthJob.sifLogin(kiosks, forceCoreLogicExecution);
                else sifAuthJob.sifLogin(null, forceCoreLogicExecution);*/
                sifAuthJob.sifLogin(null, forceCoreLogicExecution);
                sifAuthFutureTaskCompleted.complete(null);
            }, delay);
        } catch (Exception e) {
            log.error("Error scheduling sifAuthJob", e);
            sifAuthFutureTaskCompleted.completeExceptionally(e);
        }
    }

    @Scheduled(fixedDelayString = "${sif.auth.job.reschedule.parameter.check.frequency.milliseconds:21600000}") //6 hours default
    protected synchronized void rescheduleSifAuthJob() {
        log.info("Checking if sifAuthJob needs to be rescheduled");
        long currentRefreshFrequencyValue = Long.parseLong(parameterService.getParameterById(ParametersEnum.SIF_AUTHENTICATION_REFRESH_FREQUENCY_HOURS.getParametersEnumValue()).getValue());
        if (currentRefreshFrequencyValue != sifAuthFixedDelayHours) {
            log.info("sifAuthJob will be rescheduled now");
            if (sifAuthFutureTask != null) {
                try {
                    sifAuthFutureTaskCompleted.get();
                } catch (InterruptedException | ExecutionException e) {
                    log.error("Error in waiting sifAuthFutureTaskCompleted by rescheduleSifAuthJob()", e);
                }
                sifAuthFutureTask.cancel(true);
            }
            scheduleSifAuthJob();
        }
    }

    public synchronized void runAndWaitSifLoginForSpecificKiosks(List<Kiosk> kiosks) {
        if(kiosks != null && !kiosks.isEmpty()) {
            //scheduleSifAuthJob(kiosks, true);
            if (sifAuthFutureTask != null) {
                try {
                    sifAuthFutureTaskCompleted.get();
                } catch (InterruptedException | ExecutionException e) {
                    log.error("Error in waiting sifAuthFutureTaskCompleted by runAndWaitSifLoginForSpecificKiosks()", e);
                }
                //sifAuthFutureTask.cancel(true);
            }
            sifAuthFutureTaskCompleted = new CompletableFuture<>();
            log.info("DynamicSchedulingService running runAndWaitSifLoginForSpecificKiosks for specific Kiosk(s)/eGate(s)"); // on a {} thread", Thread.currentThread().isVirtual() ? "virtual" : "platform");
            /*entityManager.setFlushMode(FlushModeType.COMMIT);
            doIt(kiosks, true);
            entityManager.setFlushMode(FlushModeType.AUTO);*/
            sifAuthJob.sifLogin(kiosks, true);
            //sifAuthJob.sifLogin(kiosks, true);
            log.info("DynamicSchedulingService completed runAndWaitSifLoginForSpecificKiosks for specific Kiosk(s)/eGate(s)");
            sifAuthFutureTaskCompleted.complete(null);
        }
    }

    /*@Transactional(propagation = Propagation.NEVER)
    public void doIt(List<Kiosk> kiosks, boolean... forceCoreLogicExecution) {
        SessionImplementor sessionImp = (SessionImplementor) entityManager.getDelegate();
        var transaction = sessionImp.getTransaction();
        sifAuthJob.sifLogin(kiosks, true);
        transaction.commit();
    }*/

    public synchronized void scheduleLuoghiAndDocumentiSDIJob(boolean... forceCoreLogicExecution) {
        updateLuoghiAndDocumentiSDIParameters();
        if (luoghiAndDocumentiSDIFutureTask != null && luoghiAndDocumentiSDIFutureTaskCompleted != null) {
            try {
                luoghiAndDocumentiSDIFutureTaskCompleted.get();
            } catch (InterruptedException | ExecutionException e) {
                log.error("Error in waiting luoghiAndDocumentiSDIFutureTaskCompleted by scheduleluoghiAndDocumentiSDIJob()", e);
            }
            luoghiAndDocumentiSDIFutureTask.cancel(true);
        }
        if ((forceCoreLogicExecution.length > 0 && forceCoreLogicExecution[0])) {
            forceLuoghiAndDocumentiSDIDownload(true);
        } else {
            if(luoghiAndDocumentiSDIFirstBoot) forceLuoghiAndDocumentiSDIDownload(false);
            scheduleLuoghiAndDocumentiSDITask();
            //rescheduleForNextValidTime();
            if(luoghiAndDocumentiSDIFirstBoot) {
                luoghiAndDocumentiSDIFirstBoot = false;
            }
        }
    }

    public synchronized void scheduleCertificatesJob(boolean... forceCoreLogicExecution) {
        //certificateFixedDelayHours = Long.parseLong(parameterService.getParameterById(ParametersEnum.CERTIFICATES_REFRESH_FREQUENCY_HOURS.getParametersEnumValue()).getValue());
        updateCertificatesParameters();
        //Duration delay = Duration.ofHours(certificateFixedDelayHours);
        if (certificateFutureTask != null && certificateFutureTaskCompleted != null) {
            try {
                certificateFutureTaskCompleted.get();
            } catch (InterruptedException | ExecutionException e) {
                log.error("Error in waiting certificateFutureTaskCompleted by scheduleCertificatesJob()", e);
            }
            certificateFutureTask.cancel(true);
        }
        if ((forceCoreLogicExecution.length > 0 && forceCoreLogicExecution[0])) {
            forceCertificateDownload(true);
        } else {
            if(certificateFirstBoot) forceCertificateDownload(false);
            scheduleCertificateTask();
            //rescheduleForNextValidTime();
            if(certificateFirstBoot) {
                certificateFirstBoot = false;
                //meglio avere DB di partenza con tabella certificate_request_status piena con date molto vecchie anziche' usare il seguente codice commentato (potrebbe riscaricare i certificati in caso di crash di singola istanza oppure avere concorrenza nel caso di istanze multiple avviate insieme, etc; in ogni caso questo codice sembra bloccare l'esecuzione di HeartbeatJob e il resto)
                /*try(ScheduledExecutorService decreaseDateByHoursScheduler = Executors.newScheduledThreadPool(1, Thread.ofVirtual().factory())) {
                    long certificatesDownloadRetryMinutesInterval = Long.parseLong(parameterService.getParameterById(ParametersEnum.CERTIFICATE_DOWNLOAD_RETRY_MINUTES_INTERVAL.getParametersEnumValue()).getValue());
                    long certificatesDownloadRetriesLimit = Long.parseLong(parameterService.getParameterById(ParametersEnum.CERTIFICATE_DOWNLOAD_RETRIES_LIMIT.getParametersEnumValue()).getValue());
                    decreaseDateByHoursScheduler.schedule(() -> { //possibilmente assicurati che la prossima esecuzione del job dei certificati avverra' dopo (certificatesDownloadRetryMinutesInterval * (certificatesDownloadRetriesLimit + 1)) minuti
                        int updatedRowsForFirstNextSchedule = certificateRequestStatusService.decreaseDateByHours(Math.toIntExact(certificateFixedDelayHours));
                        if (updatedRowsForFirstNextSchedule > 0) log.info("CertificateRequestStatusService.decreaseDateByHours() decreased date by {} hours for {} rows in certificate_request_status table in order to allow the next, first, scheduled execution", certificateFixedDelayHours, updatedRowsForFirstNextSchedule);
                        else log.info("CertificateRequestStatusService.decreaseDateByHours() did not decrease date by {} hours for any row in certificate_request_status table", certificateFixedDelayHours);
                        decreaseDateByHoursScheduler.shutdown();
                    }, certificatesDownloadRetryMinutesInterval * (certificatesDownloadRetriesLimit + 1), TimeUnit.MINUTES);
                    log.info("CertificateRequestStatusService.decreaseDateByHours() will run in {} minutes", certificatesDownloadRetryMinutesInterval * (certificatesDownloadRetriesLimit + 1));
                } catch (Exception e) {
                    log.error("Error scheduling CertificateRequestStatusService.decreaseDateByHours() for first next schedule", e);
                }*/
            }
        }/* else {
            log.info("Skipping certificatesJob execution as it is outside the allowed time range");
        }*/
    }

    private void scheduleLuoghiAndDocumentiSDITask() {
        log.info("luoghiAndDocumentiSDIFixedDelayHours: {}", luoghiAndDocumentiSDIFixedDelayHours);
        try {
            luoghiAndDocumentiSDIFutureTask = scheduler.scheduleAtFixedRate(() -> {
                luoghiAndDocumentiSDIFutureTaskCompleted = new CompletableFuture<>();
                if (isLuoghiAndDocumentiSDIWithinTimeRange() && !luoghiAndDocumentiSDIFirstBoot) {
                    log.info("DynamicSchedulingService running luoghiAndDocumentiSDIJob on a {} thread because scheduleLuoghiAndDocumentiSDIJob() is within time range", Thread.currentThread().isVirtual() ? "virtual" : "platform");
                    luoghiAndDocumentiSDIJob.processLuoghiAndDocumentiSDI();
                } else {
                    log.info("Skipping luoghiAndDocumentiSDIJob execution as it is outside the allowed time range or it is the first boot");
                }
                luoghiAndDocumentiSDIFutureTaskCompleted.complete(null);
            }, getLuoghiAndDocumentiSDIInitialDelay(), TimeUnit.HOURS.toSeconds(luoghiAndDocumentiSDIFixedDelayHours), TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("Error scheduling LuoghiAndDocumentiSDIJob", e);
            luoghiAndDocumentiSDIFutureTaskCompleted.completeExceptionally(e);
        }
    }

    private void scheduleCertificateTask() {
        log.info("certificateFixedDelayHours: {}", certificateFixedDelayHours);
        try {
            certificateFutureTask = scheduler.scheduleAtFixedRate(() -> {
                certificateFutureTaskCompleted = new CompletableFuture<>();
                if (isCertificatesWithinTimeRange() && !certificateFirstBoot) {
                    log.info("DynamicSchedulingService running certificatesJob on a {} thread because scheduleCertificatesJob() is within time range", Thread.currentThread().isVirtual() ? "virtual" : "platform");
                    certificatesJob.processCertificates(); //.processCertificatesAsync()
                    //if(forceCoreLogicExecution.length > 0 && forceCoreLogicExecution[0]) rescheduleForNextValidTime();
                } else {
                    log.info("Skipping certificatesJob execution as it is outside the allowed time range or it is the first boot");
                }
                certificateFutureTaskCompleted.complete(null);
            }, getCertificateInitialDelay(), TimeUnit.HOURS.toSeconds(certificateFixedDelayHours), TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("Error scheduling CertificatesJob", e);
            certificateFutureTaskCompleted.completeExceptionally(e);
        }
    }

    private long getLuoghiAndDocumentiSDIInitialDelay() {
        ZonedDateTime now = ZonedDateTime.now();
        ZonedDateTime nextRun = now.withHour(luoghiAndDocumentiSDIStartHour.getHour()).withMinute(luoghiAndDocumentiSDIStartHour.getMinute()).withSecond(0).withNano(0);
        if (now.isAfter(nextRun)) {
            nextRun = nextRun.plusDays(1);
        }
        long initialDelay = Duration.between(now, nextRun).toSeconds();
        long initialDelayBuffer = 30; //30 secondi di ritardo aggiuntivo per sicurezza
        initialDelay += initialDelayBuffer;
        log.info("Next run of LuoghiAndDocumentiSDI Job will be at {} (so with initial delay of {} seconds)", nextRun, initialDelay);
        return initialDelay;
    }

    private long getCertificateInitialDelay() {
        ZonedDateTime now = ZonedDateTime.now();
        ZonedDateTime nextRun = now.withHour(certificateStartHour.getHour()).withMinute(certificateStartHour.getMinute()).withSecond(0).withNano(0);
        if (now.isAfter(nextRun)) {
            nextRun = nextRun.plusDays(1);
        }
        long initialDelay = Duration.between(now, nextRun).toSeconds();
        long initialDelayBuffer = 30; //30 secondi di ritardo aggiuntivo per sicurezza
        initialDelay += initialDelayBuffer;
        log.info("Next run of Certificates Job will be at {} (so with initial delay of {} seconds)", nextRun, initialDelay);
        return initialDelay;
    }

    private void forceLuoghiAndDocumentiSDIDownload(boolean... forceCoreLogicExecution) {
        if(luoghiAndDocumentiSDIExecutorService.isShutdown()) {
            if ("virtual".equalsIgnoreCase(threadsType)) luoghiAndDocumentiSDIExecutorService = Executors.newSingleThreadExecutor(Thread.ofVirtual().factory());
            else luoghiAndDocumentiSDIExecutorService = Executors.newSingleThreadExecutor();
        }
        Future<?> future = luoghiAndDocumentiSDIExecutorService.submit(() -> luoghiAndDocumentiSDIJob.processLuoghiAndDocumentiSDI(forceCoreLogicExecution));
        try {
            log.info("DynamicSchedulingService running luoghiAndDocumentiSDIJob on a {} thread because scheduleLuoghiAndDocumentiSDIJob() should force it due to other instance crash during the relative process (or the app is starting)", Thread.currentThread().isVirtual() ? "virtual" : "platform");
            future.get();
        } catch (InterruptedException | ExecutionException e) {
            log.error("Error executing luoghiAndDocumentiSDIJob.processLuoghiAndDocumentiSDI with forceCoreLogicExecution", e);
        } finally {
            luoghiAndDocumentiSDIExecutorService.shutdown();
        }
    }

    private void forceCertificateDownload(boolean... forceCoreLogicExecution) {
        if(certificatesExecutorService.isShutdown()) {
            if ("virtual".equalsIgnoreCase(threadsType)) certificatesExecutorService = Executors.newSingleThreadExecutor(Thread.ofVirtual().factory());
            else certificatesExecutorService = Executors.newSingleThreadExecutor();
        }
        Future<?> future = certificatesExecutorService.submit(() -> certificatesJob.processCertificates(forceCoreLogicExecution));
        try {
            log.info("DynamicSchedulingService running certificatesJob on a {} thread because scheduleCertificatesJob() should force it due to other instance crash during the relative process (or the app is starting)", Thread.currentThread().isVirtual() ? "virtual" : "platform");
            future.get();
            //future.complete(null);
        } catch (InterruptedException | ExecutionException e) {
            log.error("Error executing certificatesJob.processCertificates with forceCoreLogicExecution", e);
            //Thread.currentThread().interrupt();
        } finally {
            certificatesExecutorService.shutdown();
        }
    }

    /*private Instant getNextExecutionTime() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime nextValidTime = now;
        if (now.toLocalTime().equals(startHour)) {
            nextValidTime = nextValidTime.plusMinutes(1);
        } else if (now.toLocalTime().equals(endHour)) {
            nextValidTime = nextValidTime.minusMinutes(1);
        }
        return nextValidTime.atZone(ZoneId.systemDefault()).toInstant();
    }*/
    /*private Instant getNextExecutionTime() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime nextValidTime = now;
        int maxIterations = 1000;
        int iterations = 0;

        while (iterations < maxIterations) {
            nextValidTime = nextValidTime.plusHours(certificateFixedDelayHours);
            LocalTime nextTime = nextValidTime.toLocalTime();

            if (startHour.isBefore(endHour)) {
                if (!nextTime.isBefore(startHour) && !nextTime.isAfter(endHour)) {
                    break;
                }
            } else {
                if (nextTime.isAfter(startHour) || nextTime.isBefore(endHour)) {
                    break;
                }
            }
            iterations++;
        }

        if (iterations == maxIterations) {
            log.error("Unable to find a valid next execution time to process certificates within the maximum number of iterations. Scheduling at startHour.");
            nextValidTime = LocalDateTime.of(now.toLocalDate(), startHour); // LocalDateTime.of(now.toLocalDate().plusDays(1), startHour);
        }

        if (now.toLocalTime().equals(startHour)) {
            nextValidTime = nextValidTime.plusMinutes(1);
        } else if (now.toLocalTime().equals(endHour)) {
            nextValidTime = nextValidTime.minusMinutes(1);
        }
        log.info("Next execution time: {}", nextValidTime);
        log.info("Next execution time istant: {}", nextValidTime.atZone(ZoneId.systemDefault()).toInstant());
        log.info("Next execution time instant used ok timezone UTC: {}", nextValidTime.toInstant(ZoneOffset.UTC));
        return nextValidTime.toInstant(ZoneOffset.UTC);
    }*/
    /*private Instant getNextExecutionTime() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime nextValidTime;
        if (startHour.isBefore(endHour)) {
            if (now.toLocalTime().isAfter(endHour)) {
                nextValidTime = now.with(startHour).plusDays(1);
            } else if (now.toLocalTime().isBefore(startHour)) {
                nextValidTime = now.with(startHour);
            } else {
                nextValidTime = now.with(startHour).plusDays(1);
            }
        } else {
            if (now.toLocalTime().isAfter(endHour) && now.toLocalTime().isBefore(startHour)) {
                nextValidTime = now.with(startHour);
            } else {
                nextValidTime = now.with(startHour).plusDays(1);
            }
        }
        return nextValidTime.atZone(ZoneId.systemDefault()).toInstant();
    }*/

    /*private void rescheduleForNextValidTime() {
        Instant nextExecutionTime = getNextExecutionTime();
        log.info("Rescheduling CertificatesJob for next valid time: {}", nextExecutionTime);
        scheduler.schedule(() -> scheduleCertificatesJob(), Duration.between(Instant.now(), nextExecutionTime).toMillis(), TimeUnit.MILLISECONDS);
    }*/

    /*private void rescheduleForNextValidTime() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime nextValidTime;
        startHour = LocalTime.parse(parameterService.getParameterById(ParametersEnum.CERTIFICATE_DOWNLOAD_START_HOUR.getParametersEnumValue()).getValue());
        endHour = LocalTime.parse(parameterService.getParameterById(ParametersEnum.CERTIFICATE_DOWNLOAD_END_HOUR.getParametersEnumValue()).getValue());
        if (startHour.isBefore(endHour)) { // Case where startHour and endHour are on the same day
            if (now.toLocalTime().isAfter(endHour)) nextValidTime = now.with(startHour).plusDays(1); // If the current time is after endHour, schedule for the next startHour of the next day
            else if (now.toLocalTime().isBefore(startHour)) nextValidTime = now.with(startHour); // If the current time is before startHour, schedule for the next startHour of today
            else nextValidTime = now.with(startHour).plusDays(1); // If the current time is between startHour and endHour, schedule for the next startHour of the next day
        } else { // Case where startHour and endHour span across midnight
            if (now.toLocalTime().isAfter(endHour) && now.toLocalTime().isBefore(startHour)) nextValidTime = now.with(startHour); // If the current time is after endHour and before startHour, schedule for the next startHour of today
            else nextValidTime = now.with(startHour).plusDays(1); // If the current time is between startHour and endHour (spanning midnight), schedule for the next startHour of the next day
        }
        Duration delay = Duration.between(now, nextValidTime);
        Instant nextExecutionTime = now.plus(delay).atZone(ZoneId.systemDefault()).toInstant();
        taskScheduler.schedule(this::scheduleCertificatesJob, nextExecutionTime); //ok dato che scheduleCertificatesJob() è synchronized
    }*/

    private boolean isLuoghiAndDocumentiSDIWithinTimeRange() {
        luoghiAndDocumentiSDIStartHour = LocalTime.parse(parameterService.getParameterById(ParametersEnum.SDI_DOWNLOAD_START_HOUR.getParametersEnumValue()).getValue());
        luoghiAndDocumentiSDIEndHour = LocalTime.parse(parameterService.getParameterById(ParametersEnum.SDI_DOWNLOAD_END_HOUR.getParametersEnumValue()).getValue());
        return sdiDownloadRequestStatusService.isWithinTimeRange(luoghiAndDocumentiSDIStartHour, luoghiAndDocumentiSDIEndHour);
    }

    private boolean isCertificatesWithinTimeRange() {
        certificateStartHour = LocalTime.parse(parameterService.getParameterById(ParametersEnum.CERTIFICATE_DOWNLOAD_START_HOUR.getParametersEnumValue()).getValue());
        certificateEndHour = LocalTime.parse(parameterService.getParameterById(ParametersEnum.CERTIFICATE_DOWNLOAD_END_HOUR.getParametersEnumValue()).getValue());
        return certificateRequestStatusService.isWithinTimeRange(certificateStartHour, certificateEndHour);
    }

    @Scheduled(fixedDelayString = "${certificates.job.reschedule.parameter.check.frequency.milliseconds:21600000}") //6 hours default (utile anche per passaggio a ora legale o solare)
    protected synchronized void rescheduleCertificatesJob() {
        log.info("Checking if certificatesJob needs to be rescheduled (due to parameter changes or daylight saving time changes)");
        long currentRefreshFrequencyValue = certificateFixedDelayHours;
        LocalTime currentStartHourValue = certificateStartHour;
        LocalTime currentEndHourValue = certificateEndHour;
        updateCertificatesParameters();
        if (currentRefreshFrequencyValue != certificateFixedDelayHours || !currentStartHourValue.equals(certificateStartHour) || !currentEndHourValue.equals(certificateEndHour)) {
            log.info("certificatesJob will be rescheduled now");
            if (certificateFutureTask != null && certificateFutureTaskCompleted != null) {
                try {
                    certificateFutureTaskCompleted.get();
                } catch (InterruptedException | ExecutionException e) {
                    log.error("Error in waiting certificateFutureTaskCompleted by rescheduleCertificatesJob()", e);
                }
                certificateFutureTask.cancel(true);
            }
            scheduleCertificatesJob();
        }
    }

    @Scheduled(fixedDelayString = "${sdi.download.job.reschedule.parameter.check.frequency.milliseconds:21600000}") //6 hours default (utile anche per passaggio a ora legale o solare)
    protected synchronized void rescheduleLuoghiAndDocumentiSDIJob() {
        log.info("Checking if luoghiAndDocumentiSDIJob needs to be rescheduled (due to parameter changes or daylight saving time changes)");
        long currentRefreshFrequencyValue = luoghiAndDocumentiSDIFixedDelayHours;
        LocalTime currentStartHourValue = luoghiAndDocumentiSDIStartHour;
        LocalTime currentEndHourValue = luoghiAndDocumentiSDIEndHour;
        updateLuoghiAndDocumentiSDIParameters();
        if (currentRefreshFrequencyValue != luoghiAndDocumentiSDIFixedDelayHours || !currentStartHourValue.equals(luoghiAndDocumentiSDIStartHour) || !currentEndHourValue.equals(luoghiAndDocumentiSDIEndHour)) {
            log.info("luoghiAndDocumentiSDIJob will be rescheduled now");
            if (luoghiAndDocumentiSDIFutureTask != null && luoghiAndDocumentiSDIFutureTaskCompleted != null) {
                try {
                    luoghiAndDocumentiSDIFutureTaskCompleted.get();
                } catch (InterruptedException | ExecutionException e) {
                    log.error("Error in waiting luoghiAndDocumentiSDIFutureTaskCompleted by rescheduleLuoghiAndDocumentiSDIJob()", e);
                }
                luoghiAndDocumentiSDIFutureTask.cancel(true);
            }
            scheduleLuoghiAndDocumentiSDIJob();
        }
    }

    private void updateLuoghiAndDocumentiSDIParameters() {
        luoghiAndDocumentiSDIFixedDelayHours = Long.parseLong(parameterService.getParameterById(ParametersEnum.SDI_DOWNLOAD_REFRESH_FREQUENCY_HOURS.getParametersEnumValue()).getValue());
        luoghiAndDocumentiSDIStartHour = LocalTime.parse(parameterService.getParameterById(ParametersEnum.SDI_DOWNLOAD_START_HOUR.getParametersEnumValue()).getValue());
        luoghiAndDocumentiSDIEndHour = LocalTime.parse(parameterService.getParameterById(ParametersEnum.SDI_DOWNLOAD_END_HOUR.getParametersEnumValue()).getValue());

        LocalDate tomorrow = LocalDate.now().plusDays(1);
        boolean willBeNewDay = willBeNewDay(luoghiAndDocumentiSDIFixedDelayHours);
        if(willBeNewDay) {
            if (isStartOfDaylightSavingTime(tomorrow)) {
                luoghiAndDocumentiSDIFixedDelayHours += 1;
                luoghiAndDocumentiSDIStartHour = luoghiAndDocumentiSDIStartHour.plusHours(1);
                luoghiAndDocumentiSDIEndHour = luoghiAndDocumentiSDIEndHour.plusHours(1);
                log.warn("Daylight saving time change detected (tomorrow isStartOfDaylightSavingTime): luoghiAndDocumentiSDIFixedDelayHours increased by 1 hour");
            } else if (isEndOfDaylightSavingTime(tomorrow)) {
                luoghiAndDocumentiSDIFixedDelayHours -= 1;
                luoghiAndDocumentiSDIStartHour = luoghiAndDocumentiSDIStartHour.minusHours(1);
                luoghiAndDocumentiSDIEndHour = luoghiAndDocumentiSDIEndHour.minusHours(1);
                log.warn("Daylight saving time change detected (tomorrow isEndOfDaylightSavingTime): luoghiAndDocumentiSDIFixedDelayHours decreased by 1 hour");
            }
        }
    }

    private void updateCertificatesParameters() {
        certificateFixedDelayHours = Long.parseLong(parameterService.getParameterById(ParametersEnum.CERTIFICATES_REFRESH_FREQUENCY_HOURS.getParametersEnumValue()).getValue());
        certificateStartHour = LocalTime.parse(parameterService.getParameterById(ParametersEnum.CERTIFICATE_DOWNLOAD_START_HOUR.getParametersEnumValue()).getValue());
        certificateEndHour = LocalTime.parse(parameterService.getParameterById(ParametersEnum.CERTIFICATE_DOWNLOAD_END_HOUR.getParametersEnumValue()).getValue());
        /*LocalDate today = LocalDate.now();
        LocalDate tomorrow = today.plusDays(1);
        if (isDaylightSavingTimeChangeDay(today) || isDaylightSavingTimeChangeDay(tomorrow)) {
            if (isStartOfDaylightSavingTime(today) || isStartOfDaylightSavingTime(tomorrow)) {
                certificateFixedDelayHours += 1;
            } else if (isEndOfDaylightSavingTime(today) || isEndOfDaylightSavingTime(tomorrow)) {
                certificateFixedDelayHours -= 1;
            }
        }*/
        LocalDate tomorrow = LocalDate.now().plusDays(1);
        boolean willBeNewDay = willBeNewDay(certificateFixedDelayHours);
        if(willBeNewDay) {
            if (isStartOfDaylightSavingTime(tomorrow)) {
                certificateFixedDelayHours += 1;
                certificateStartHour = certificateStartHour.plusHours(1);
                certificateEndHour = certificateEndHour.plusHours(1);
                log.warn("Daylight saving time change detected (tomorrow isStartOfDaylightSavingTime): certificateFixedDelayHours increased by 1 hour");
            } else if (isEndOfDaylightSavingTime(tomorrow)) {
                certificateFixedDelayHours -= 1;
                certificateStartHour = certificateStartHour.minusHours(1);
                certificateEndHour = certificateEndHour.minusHours(1);
                log.warn("Daylight saving time change detected (tomorrow isEndOfDaylightSavingTime): certificateFixedDelayHours decreased by 1 hour");
            }
        }
    }

    private void updateGracefulAutoRestartParameters() {
        gracefulAutoRestartEnabled = Boolean.parseBoolean(parameterService.getParameterById(ParametersEnum.GRACEFUL_AUTO_RESTART_ENABLED.getParametersEnumValue()).getValue());
        gracefulAutoRestartTime = parameterService.getParameterById(ParametersEnum.GRACEFUL_AUTO_RESTART_TIME.getParametersEnumValue()).getValue();
    }

    /*private boolean isDaylightSavingTimeChangeDay(LocalDate date) {
        return isStartOfDaylightSavingTime(date) || isEndOfDaylightSavingTime(date);
    }*/

    private boolean isStartOfDaylightSavingTime(LocalDate date) {
        return date.getMonth() == Month.MARCH && date.getDayOfWeek() == DayOfWeek.SUNDAY && date.getDayOfMonth() > (31 - 7);
    }

    private boolean isEndOfDaylightSavingTime(LocalDate date) {
        return date.getMonth() == Month.OCTOBER && date.getDayOfWeek() == DayOfWeek.SUNDAY && date.getDayOfMonth() > (31 - 7);
    }

    public boolean willBeNewDay(long hoursToAdd) { //controllo se tra "hoursToAdd" ore ci sara' un nuovo giorno (anche per capire se questa e' l'ultima esecuzione del giorno prima del cambio di ora)
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime futureTime = now.plus(Duration.ofHours(hoursToAdd));
        return futureTime.toLocalDate().isAfter(now.toLocalDate());
    }

    @Scheduled(fixedDelayString = "${graceful.auto.restart.reschedule.parameter.check.frequency.milliseconds:3600000}") //1 hour default
    protected synchronized void rescheduleGracefulAutoRestart() {
        log.info("Checking if graceful auto restart needs to be rescheduled (due to parameter changes) - Currently {}", gracefulAutoRestartEnabled ? "enabled" : "disabled");
        boolean currentGracefulAutoRestartEnabled = gracefulAutoRestartEnabled;
        String currentGracefulAutoRestartTime = gracefulAutoRestartTime;
        updateGracefulAutoRestartParameters();

        if (currentGracefulAutoRestartEnabled != gracefulAutoRestartEnabled || !currentGracefulAutoRestartTime.equals(gracefulAutoRestartTime)) {
            if (gracefulAutoRestartFuture != null) {
                gracefulAutoRestartFuture.cancel(true);
                log.info("gracefulAutoRestartFuture cancelled");
            }
            if (gracefulAutoRestartEnabled) {
                scheduleGracefulAutoRestartTask();
                log.info("gracefulAutoRestartFuture scheduled");
            }
        }
    }

    private void scheduleGracefulAutoRestartTask() {
        gracefulAutoRestartFuture = scheduler.scheduleAtFixedRate(this::appRestart, getGracefulAutoRestartInitialDelay(), TimeUnit.DAYS.toSeconds(1), TimeUnit.SECONDS);
    }

    private long getGracefulAutoRestartInitialDelay() {
        LocalTime restartTime = LocalTime.parse(gracefulAutoRestartTime);
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime nextRun = now.withHour(restartTime.getHour())
                .withMinute(restartTime.getMinute())
                .withSecond(restartTime.getSecond())
                .withNano(0);
        if (now.isAfter(nextRun)) {
            nextRun = nextRun.plusDays(1);
        }
        long initialDelay = Duration.between(now, nextRun).getSeconds();
        log.info("Next run of graceful auto restart will be at {} (so with initial delay of {} seconds)", nextRun, initialDelay);
        return initialDelay;
    }

    public void appRestart() {
        log.info("Avvio del restart graceful programmato. Avvio lo shutdown...");
        /*if (scheduler != null && !scheduler.isShutdown()) {
            scheduler.shutdownNow();
        }*/
        restartingApp = true;
        int exitCode = SpringApplication.exit(context, () -> 0);
        // Si presuppone che un sistema esterno (es. container o manager di processi) riavvii l'app al termine
        System.exit(exitCode);
    }

    @PreDestroy
    public void shutdownExecutor() {
        if (scheduler != null && !restartingApp) {
            try {
                log.info("Shutting down ScheduledExecutor in DynamicSchedulingService");
                scheduler.shutdown();
                if (!scheduler.awaitTermination(60, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                    if (!scheduler.awaitTermination(60, TimeUnit.SECONDS)) {
                        log.error("DynamicSchedulingService Scheduler executor did not terminate");
                    }
                }
            } catch (InterruptedException e) {
                scheduler.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        if (luoghiAndDocumentiSDIExecutorService != null) {
            try {
                log.info("Shutting down luoghiAndDocumentiSDIExecutorService in DynamicSchedulingService");
                luoghiAndDocumentiSDIExecutorService.shutdown();
                if (!luoghiAndDocumentiSDIExecutorService.awaitTermination(60, TimeUnit.SECONDS)) {
                    luoghiAndDocumentiSDIExecutorService.shutdownNow();
                    if (!luoghiAndDocumentiSDIExecutorService.awaitTermination(60, TimeUnit.SECONDS)) {
                        log.error("DynamicSchedulingService luoghiAndDocumentiSDIExecutorService did not terminate");
                    }
                }
            } catch (InterruptedException e) {
                luoghiAndDocumentiSDIExecutorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        if (certificatesExecutorService != null) {
            try {
                log.info("Shutting down certificatesExecutorService in DynamicSchedulingService");
                certificatesExecutorService.shutdown();
                if (!certificatesExecutorService.awaitTermination(60, TimeUnit.SECONDS)) {
                    certificatesExecutorService.shutdownNow();
                    if (!certificatesExecutorService.awaitTermination(60, TimeUnit.SECONDS)) {
                        log.error("DynamicSchedulingService certificatesExecutorService did not terminate");
                    }
                }
            } catch (InterruptedException e) {
                certificatesExecutorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        /*if (taskScheduler instanceof ConcurrentTaskScheduler) {
            ScheduledExecutorService taskSchedulerExecutor = ((ConcurrentTaskScheduler) taskScheduler).getScheduledExecutor();
            if (taskSchedulerExecutor != null) {
                try {
                    taskSchedulerExecutor.shutdown();
                    if (!taskSchedulerExecutor.awaitTermination(60, TimeUnit.SECONDS)) {
                        taskSchedulerExecutor.shutdownNow();
                        if (!taskSchedulerExecutor.awaitTermination(60, TimeUnit.SECONDS)) {
                            log.error("TaskScheduler Executor did not terminate");
                        }
                    }
                } catch (InterruptedException e) {
                    taskSchedulerExecutor.shutdownNow();
                    Thread.currentThread().interrupt();
                }
            }
        }*/
    }
}
