package com.reco.ees.gateway.enums;

public enum CheckPreFileEnum {
    EXIST4EGATE("EXIST4EGATE"), //Il viaggiatore è stato indirizzato verso un ABC e-Gate ed è presente il pre-fascicolo EES, g<PERSON><PERSON> ac<PERSON>, associato al viaggiatore
    MISSING4EGATE("MISSING4EGATE"), //Il viaggiatore è stato indirizzato verso un ABC e-Gate ma non risulta ancora presente (al momento della chiamata del servizio) il relativo pre-fascicolo EES
    FORMANUAL("4MANUAL"), //Il viaggiatore è stato indirizzato verso una postazione passaportista (e non verso un ABC e-Gate), indipendentemente dal perfezionamento o meno del suo pre-fascicolo
    NOTFOUND("NOTFOUND"); //Il viaggiatore non è mai passato da un Self-Service Kiosk
    private final String value;

    CheckPreFileEnum(String value) {
        this.value = value;
    }

    public String getCheckPreFileEnumValue() {
        return value;
    }
}
