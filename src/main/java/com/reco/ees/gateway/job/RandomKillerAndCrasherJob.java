package com.reco.ees.gateway.job;

//import org.springframework.scheduling.annotation.Scheduled;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import java.util.Random;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Slf4j
@ConditionalOnProperty(value = "simulate.random.shutdown.or.crash", havingValue = "true", matchIfMissing = false)
@Profile("development")
@Component
public class RandomKillerAndCrasherJob {
    @Value("${simulate.random.shutdown.or.crash.delay.seconds:600}")
    private int delay;
    private final Random random = new Random();
    private final ExecutorService executorService = Executors.newSingleThreadExecutor();

    @PostConstruct
    public void init() {
        executorService.submit(this::execute);
    }

    public void execute() {
        int randomTime = delay + random.nextInt(1, 31); // Tempo casuale tra 1 e 30, più delay secondi
        boolean willShutdownNormally = random.nextBoolean();
        log.warn("RandomKillerAndCrasherJob will execute in {}s to {}", randomTime, willShutdownNormally ? "shutdown normally!!!" : "force a crash!!!");

        try {
            Thread.sleep(randomTime * 1000L);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("RandomKillerAndCrasherJob interrupted with error: {}", e.getMessage());
            return;
        }

        if (willShutdownNormally) {
            log.warn("RandomKillerAndCrasherJob is shutting down the application normally now.");
            System.exit(0);
        } else {
            log.warn("RandomKillerAndCrasherJob is forcing a crash now.");
            Runtime.getRuntime().halt(1);
        }
    }
}
