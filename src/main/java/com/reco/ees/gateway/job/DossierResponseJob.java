package com.reco.ees.gateway.job;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.reco.ees.gateway.ShutdownManager;
import com.reco.ees.gateway.enums.*;
import com.reco.ees.gateway.generated.sif.response.RetrieveEESAsyncResponse;
import com.reco.ees.gateway.generated.sif.response.SifHeaderType;
import com.reco.ees.gateway.kafka.model.AbcEgateOperazioniAtomicheMessage;
import com.reco.ees.gateway.kafka.model.AbcEgateProblemMessage;
import com.reco.ees.gateway.kafka.model.AbcEgateResponseMessage;
import com.reco.ees.gateway.kafka.producer.KafkaProducer;
import com.reco.ees.gateway.repository.KioskRepository;
import com.reco.ees.gateway.repository.SifAddDataRequestRepository;
import com.reco.ees.gateway.repository.model.Dossier;
import com.reco.ees.gateway.repository.model.Kiosk;
import com.reco.ees.gateway.repository.model.SifAddDataRequest;
import com.reco.ees.gateway.service.DossierService;
import com.reco.ees.gateway.service.LogService;
import com.reco.ees.gateway.service.ParameterService;
import com.reco.ees.gateway.soap_clients.SoapClientAsync;
import com.reco.ees.gateway.soap_clients.SoapClientWfe;
import com.reco.ees.gateway.util.DateUtil;
import com.reco.ees.gateway.util.UtilsService;
import eu.europa.schengen.ees.xsd.v1.FPRequestType;
import eu.europa.schengen.ees_ns.xsd.v1.AddDataToBorderControlRequestMessageType;
import eu.europa.schengen.ees_ns.xsd.v1.AddDataToBorderControlResponseMessageType;
import eu.europa.schengen.ees_ns.xsd.v1.HeaderAsyncRequestType;
import eu.europa.schengen.ees_ns.xsd.v1.StartBorderControlResponseMessageType;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

import static com.reco.ees.gateway.kafka.producer.KafkaProducer.unknownFieldValue;

@Slf4j
@Component
public class DossierResponseJob implements Runnable {
    private final KioskRepository kioskRepository;
    private final ObjectMapper objectMapper;
    @Value("${kafka.sent.message.show:false}")
    private boolean showSentMessage;
    private final KafkaProducer kafkaProducer;
    @Value("${fake.clients.returns:false}")
    private String fakeClientsReturns;
    @Value("${to.be.processed.record.limit:16}")
    private int recordLimit;
    private final LogService logService;
    private final UtilsService utilsService;
    private final ExecutorService virtualThreadExecutor = Executors.newVirtualThreadPerTaskExecutor();
    private final DossierService dossierService;
    private final ParameterService parameterService;
    private final SoapClientAsync soapClientAsync;
    private final SoapClientWfe soapClientWfe;
    private final SifAddDataRequestRepository sifAddDataRequestRepository;

    @Value("${kafka.node.abc.process.topicPattern}")
    private String kafkaNodeAbcProcessTopicPattern;
    @Value("${application.name}")
    private String applicationName;
    /*@Value("${certificates.concurrency.request}")
    private String concurrencyRequest;*/
    public record DossierResponseResult(boolean success, String idMessage) {}

    public DossierResponseJob(UtilsService utilsService, DossierService dossierService, ParameterService parameterService, SoapClientAsync soapClientAsync, SoapClientWfe soapClientWfe, SifAddDataRequestRepository sifAddDataRequestRepository, LogService logService, KafkaProducer kafkaProducer, KioskRepository kioskRepository, ObjectMapper objectMapper) {
        this.utilsService = utilsService;
        this.dossierService = dossierService;
        this.parameterService = parameterService;
        this.soapClientAsync = soapClientAsync;
        this.soapClientWfe = soapClientWfe;
        this.sifAddDataRequestRepository = sifAddDataRequestRepository;
        this.logService = logService;
        this.kafkaProducer = kafkaProducer;
        this.kioskRepository = kioskRepository;
        this.objectMapper = objectMapper;
    }

    @Override
    public void run() {
        log.info("Dossier Response Job started");

        while (ShutdownManager.getIsAppRunning()) {
            ShutdownManager.incrementActiveJobs(ShutdownManager.jobNames.DossierResponseJob.name());
            String checkFrequencySeconds = parameterService.getParameterById(ParametersEnum.DOSSIER_TRANSACTION_CHECK_FREQUENCY_SECONDS.getParametersEnumValue()).getValue();
            Integer maxRetries = Integer.parseInt(parameterService.getParameterById(ParametersEnum.DOSSIER_TRANSACTION_CHECK_MAX_RETRIES.getParametersEnumValue()).getValue());
            //exTODO: DOVRESTI EVITARE QUESTO ERRORE PERCHE DOSSIERSENDER DOVREBBE IMPOSTARE SifInteractionCount=0 quando termina correttamente, altrimenti CORREGGI SUBITO questo errore: se Dossier ha START_BORDER_CONTROL_OK con SifInteractionCount >= maxRetries allora non viene preso in carico da DossierResponseJob (tale controllo dei retries deve valere solo per gli altri stati)
            /*List<String> managedDossierStatuses = List.of(DossierStatusEnum.SMART_BORDER_CONTROL_CALLBACK_OK.getDossierStatusEnumValue(), DossierStatusEnum.SMART_BORDER_CONTROL_CALLBACK_KO.getDossierStatusEnumValue());
            List<Dossier> dossiers = dossierService.getFreshDossiersToBeProcessedByDossierResponseJob(applicationName, DossierStatusEnum.START_BORDER_CONTROL_OK.getDossierStatusEnumValue(), recordLimit);
            List<Dossier> otherDossiers = dossierService.getDossiersToBeProcessedByDossierResponseJob(applicationName, managedDossierStatuses, maxRetries, DateUtil.addDeltaToDate(new Date(), 0, 0, 0, -Integer.parseInt(checkFrequencySeconds), 0), recordLimit);
            dossiers.addAll(otherDossiers);*/
            List<String> managedDossierStatuses = List.of(DossierStatusEnum.START_BORDER_CONTROL_OK.getDossierStatusEnumValue(), DossierStatusEnum.SMART_BORDER_CONTROL_CALLBACK_OK.getDossierStatusEnumValue(), DossierStatusEnum.SMART_BORDER_CONTROL_CALLBACK_KO.getDossierStatusEnumValue());
            List<Dossier> dossiers = dossierService.getDossiersToBeProcessedByDossierResponseJob(applicationName, managedDossierStatuses, maxRetries, DateUtil.addDeltaToDate(new Date(), 0, 0, 0, -Integer.parseInt(checkFrequencySeconds), 0), recordLimit);
            List<String> otherManagedDossierStatuses = List.of(DossierStatusEnum.NEED_EGATE_FACE.getDossierStatusEnumValue(), DossierStatusEnum.NEED_EGATE_FINGER.getDossierStatusEnumValue());
            List<Dossier> otherDossiers = dossierService.getDossiersToBeProcessedByDossierResponseJob(applicationName, otherManagedDossierStatuses, maxRetries, null, recordLimit);
            otherDossiers.removeIf(otherDossier ->
                    (otherDossier.getStatus().equalsIgnoreCase(DossierStatusEnum.NEED_EGATE_FACE.getDossierStatusEnumValue()) && (otherDossier.getFaceImage() == null || otherDossier.getFaceImage().isBlank())) ||
                    (otherDossier.getStatus().equalsIgnoreCase(DossierStatusEnum.NEED_EGATE_FINGER.getDossierStatusEnumValue()) && (otherDossier.getFingerprintImage() == null || otherDossier.getFingerprintImage().isBlank())));
            dossiers.addAll(otherDossiers);
            dossiers.removeIf(dossier -> Boolean.FALSE.equals(dossier.getHasToBeProcessed()));

            if(dossiers.isEmpty()) {
                utilsService.allDoneAndSleep(ShutdownManager.jobNames.DossierResponseJob.name());
                continue;
            }

            List<Future<DossierResponseResult>> futures = new ArrayList<>();
            for (Dossier dossier : dossiers) {
                futures.add(virtualThreadExecutor.submit(() -> processDossier(dossier, maxRetries)));
            }

            ListIterator<Future<DossierResponseResult>> iterator = futures.listIterator(futures.size());
            while (iterator.hasPrevious()) {
                Future<DossierResponseResult> future = iterator.previous();
                try {
                    DossierResponseResult result = future.get();
                    if(result.success) log.info("DossierResponseJob: Dossier with Id {} processed successfully", result.idMessage());
                    else log.warn("DossierResponseJob: Dossier with Id {} processed with problems", result.idMessage());
                } catch (Exception ex) {
                    log.error("DossierResponseJob: Error processing dossier due to: ", ex);
                }
            }

            utilsService.allDoneAndSleep(ShutdownManager.jobNames.DossierResponseJob.name());
        }
    }

    private DossierResponseResult processDossier(Dossier dossier, Integer maxRetries) {
        log.info("Requesting SIF answer for dossier: {}", dossier.getIdMessage());

        SifHeaderType sifHeaderType = dossierService.buildSifHeaderType(dossier);

        dossier.setLastSifInteractionDate(new Date());
        if(dossier.getStatus().equalsIgnoreCase(DossierStatusEnum.START_BORDER_CONTROL_OK.getDossierStatusEnumValue())) dossier.setStatus(DossierStatusEnum.SMART_BORDER_CONTROL_CALLBACK_KO.getDossierStatusEnumValue());
        dossier.setSifInteractionCount(dossier.getSifInteractionCount() + 1); // incremento il contatore di tentativi di contatto con SIF prima di contattarlo veramente casomai vado in crash subito dopo averlo contattato ma prima di aver incrementato il contatore su DB (meglio un tentativo in meno che uno in più dato che non è concesso)
        dossierService.save(dossier);

        if(!Boolean.parseBoolean(fakeClientsReturns) && sifHeaderType.getIdChiamante() == null) {
            log.error("DossierResponseJob: Error processing dossier with Id: {}", dossier.getIdMessage());
            return new DossierResponseResult(false, dossier.getIdMessage());
        }

        String transaction = "Dossier response JOB";
        String logId = null;
        RetrieveEESAsyncResponse retrieveEESAsyncResponse = null;
        if(!dossier.getStatus().equalsIgnoreCase(DossierStatusEnum.NEED_EGATE_FINGER.getDossierStatusEnumValue()) && !dossier.getStatus().equalsIgnoreCase(DossierStatusEnum.NEED_EGATE_FACE.getDossierStatusEnumValue())) {
            logId = logService.saveEventLog(LogScopeEnum.INTERNAL.getValue(), transaction,
                    "Requesting retrieveEESAsyncResponse for dossier " + dossier.getIdMessage(), null);
            retrieveEESAsyncResponse = soapClientAsync.retrieveEESAsyncResponse(dossier.getSifTransactionId(), sifHeaderType, dossier.getIdMessage());
        }

        List<SifAddDataRequest> savedSifAddDataRequests = sifAddDataRequestRepository.findAll();
        List<String> finishedTransactionCodes = savedSifAddDataRequests.stream()
                .filter(sifAddDataRequest -> dossier.getEgateHandled().equals(Boolean.TRUE) ? sifAddDataRequest.getRequestForEgate().compareToIgnoreCase(SifAddDataRequestEnum.TRANSACTION_FINISHED.toString()) == 0 : sifAddDataRequest.getRequestForKiosk().compareToIgnoreCase(SifAddDataRequestEnum.TRANSACTION_FINISHED.toString()) == 0)
                .map(SifAddDataRequest::getValue).toList();

        List<String> requiredAdditionalData = new ArrayList<>();
        List<String> warningCodes = null;
        List<String> errorCodes = null;
        boolean isAsyncResponseAvailable = false;
        boolean isStartBorderControlResult = true;
        Boolean isAtomicOperationRequiredInVIS = null;

        if(retrieveEESAsyncResponse != null) {
            if (retrieveEESAsyncResponse.getOut().contains("AddDataToBorderControlResponse")) {
                isStartBorderControlResult = false;
                AddDataToBorderControlResponseMessageType addDataResponse =
                        (AddDataToBorderControlResponseMessageType) dossierService.convertOutCDataToWFESpecificResponse(retrieveEESAsyncResponse.getOut());
                if (addDataResponse != null) { // Se risultato richiesta asincrona è disponibile
                    isAsyncResponseAvailable = true;
                    if(addDataResponse.getResponse() != null && addDataResponse.getResponse().getResponseData() != null && addDataResponse.getResponse().getResponseData().getVISSearchAndVerification() != null) isAtomicOperationRequiredInVIS = true;
                    if(addDataResponse.getResponse() != null && addDataResponse.getResponse().getRequiredData() != null &&
                            addDataResponse.getResponse().getRequiredData().getData() != null)
                        requiredAdditionalData = addDataResponse.getResponse().getRequiredData().getData();

                    if (addDataResponse.getReturnCodes() != null) {
                        if (addDataResponse.getReturnCodes().getWarningCodes() != null)
                            warningCodes = addDataResponse.getReturnCodes().getWarningCodes().getWarningCode();
                        if (addDataResponse.getReturnCodes().getErrorCodes() != null)
                            errorCodes = addDataResponse.getReturnCodes().getErrorCodes().getErrorCode();
                    }
                }
            } else {
                StartBorderControlResponseMessageType startBorderResponse =
                        (StartBorderControlResponseMessageType) dossierService.convertOutCDataToWFESpecificResponse(retrieveEESAsyncResponse.getOut());
                if (startBorderResponse != null) { // Se risultato richiesta asincrona è disponibile
                    isAsyncResponseAvailable = true;

                    if(startBorderResponse.getResponse() != null && startBorderResponse.getResponse().getCalculator() != null && dossier.getEgateHandled().equals(Boolean.TRUE)) {
                        Kiosk egate = kioskRepository.findById(dossier.getNodeId()).orElse(null);
                        if(egate != null && egate.getIsEgate().equals(Boolean.TRUE)) {
                            int residualDays = 0;
                            if(egate.getIsEntryEgate().equals(Boolean.TRUE)) {
                                LocalDate authorisedStayUntil = startBorderResponse.getResponse().getCalculator().getEntry().getAuthorisedStayUntil().toGregorianCalendar().toZonedDateTime().toLocalDate();
                                LocalDate entryDate = startBorderResponse.getResponse().getCalculator().getEntry().getEntryDate().toGregorianCalendar().toZonedDateTime().toLocalDate();
                                residualDays = (int) ChronoUnit.DAYS.between(entryDate, authorisedStayUntil);
                            } else {
                                LocalDate authorisedStayUntil = startBorderResponse.getResponse().getCalculator().getExit().getAuthorisedStayUntil().toGregorianCalendar().toZonedDateTime().toLocalDate();
                                LocalDate exitDate = startBorderResponse.getResponse().getCalculator().getExit().getExitDate().toGregorianCalendar().toZonedDateTime().toLocalDate();
                                residualDays = (int) ChronoUnit.DAYS.between(exitDate, authorisedStayUntil);
                            }
                            dossier.setResidualDays(residualDays);
                            dossierService.save(dossier);
                            if(startBorderResponse.getResponse().getResponseData() != null && startBorderResponse.getResponse().getResponseData().getVISSearchAndVerification() != null) isAtomicOperationRequiredInVIS = true;
                        }
                    }

                    if(startBorderResponse.getResponse() != null && startBorderResponse.getResponse().getRequiredData() != null && startBorderResponse.getResponse().getRequiredData().getData() != null)
                        requiredAdditionalData = startBorderResponse.getResponse().getRequiredData().getData();

                    if (startBorderResponse.getReturnCodes() != null) {
                        if (startBorderResponse.getReturnCodes().getWarningCodes() != null)
                            warningCodes = startBorderResponse.getReturnCodes().getWarningCodes().getWarningCode();
                        if (startBorderResponse.getReturnCodes().getErrorCodes() != null)
                            errorCodes = startBorderResponse.getReturnCodes().getErrorCodes().getErrorCode();
                    }
                }
            }
        }

        if(!isAsyncResponseAvailable && !dossier.getStatus().equalsIgnoreCase(DossierStatusEnum.NEED_EGATE_FINGER.getDossierStatusEnumValue()) && !dossier.getStatus().equalsIgnoreCase(DossierStatusEnum.NEED_EGATE_FACE.getDossierStatusEnumValue())) {
            if(retrieveEESAsyncResponse != null) logService.saveEventLog(LogScopeEnum.INTERNAL.getValue(), transaction, "Result not available yet", null, logId);
            dossier.setSifInteractionCount(dossier.getSifInteractionCount() + 1);
            dossier.setLastSifInteractionDate(new Date());
            dossier.setStatus(DossierStatusEnum.SMART_BORDER_CONTROL_CALLBACK_KO.getDossierStatusEnumValue());
            dossierService.save(dossier);
            return new DossierResponseResult(false, dossier.getIdMessage());
        }

        if (warningCodes != null && !warningCodes.isEmpty()) {
            log.info("Warnings got for dossier {}. Warning codes: {}", dossier.getIdMessage(), warningCodes);
            logService.saveEventLog(LogScopeEnum.INTERNAL.getValue(), transaction, isStartBorderControlResult ?
                    "Warnings got in StartBorderControlResponse: " + String.join(",", warningCodes) :
                    "Warnings got in AddDataToBorderControlResponse: " + String.join(",", warningCodes), null, logId);
        }

        if (errorCodes != null && !errorCodes.isEmpty()) {
            log.warn("Processing for dossier has been definitively concluded due to errors {} contained in callback response for dossier {}. ",
                    errorCodes, dossier.getIdMessage());
            logService.saveEventLog(LogScopeEnum.INTERNAL.getValue(), transaction, isStartBorderControlResult ?
                    "Errors got in StartBorderControlResponse: " + String.join(",", errorCodes) :
                    "Errors got in AddDataToBorderControlResponse: " + String.join(",", errorCodes), null, logId);
            dossier.setLastSifInteractionDate(new Date());
            // imposto SifInteractionCount a maxRetries in modo che DossierResponseJob non prenda più in carico questo dossier
            dossier.setSifInteractionCount(maxRetries);
            dossierService.save(dossier);
            return new DossierResponseResult(false, dossier.getIdMessage());
        }

        if(dossier.getStatus().equalsIgnoreCase(DossierStatusEnum.NEED_EGATE_FINGER.getDossierStatusEnumValue())) {
            requiredAdditionalData = Collections.singletonList(sifAddDataRequestRepository.findFirstByRequestForEgate(SifAddDataRequestEnum.FP_REQUEST.name()).getValue());
            isStartBorderControlResult = false;
        } else if(dossier.getStatus().equalsIgnoreCase(DossierStatusEnum.NEED_EGATE_FACE.getDossierStatusEnumValue())) {
            requiredAdditionalData = Collections.singletonList(sifAddDataRequestRepository.findFirstByRequestForEgate(SifAddDataRequestEnum.FI_REQUEST.name()).getValue());
            isStartBorderControlResult = false;
        }
        if (requiredAdditionalData != null) {
            boolean isTransactionConcluded = false;
            if (requiredAdditionalData.isEmpty()) isTransactionConcluded = true;
            else log.info("Additional data required for dossier {}. Requested codes: {}", dossier.getIdMessage(), requiredAdditionalData);
            logService.saveEventLog(LogScopeEnum.INTERNAL.getValue(), transaction, isTransactionConcluded ?
                    "No additional data required" : "Additional data required. Requested codes: " + requiredAdditionalData, null, logId);

            // considero transazione completata per il dossier se:
            // 1. requiredAdditionalData vuoto -> non ci vengono richiesti dati aggiuntivi
            // 2. ci viene richiesto almeno un codice che non abbiamo mappato in db
            // 3. ci viene richiesto almeno un codice che abbiamo mappato come transazione conclusa
            HashSet<String> savedSifAddDataRequestsStrings = new HashSet<>(savedSifAddDataRequests.stream().map(SifAddDataRequest::getValue).toList());
            List<String> codesWithUnknownOperation = savedSifAddDataRequests.stream()
                    .filter(sifAddDataRequest -> {
                        String fieldToCheck = dossier.getEgateHandled().equals(Boolean.TRUE) ?
                                sifAddDataRequest.getRequestForEgate() :
                                sifAddDataRequest.getRequestForKiosk();
                        return fieldToCheck == null || fieldToCheck.isBlank();
                    })
                    .map(SifAddDataRequest::getValue)
                    .toList();
            if(requiredAdditionalData.stream().anyMatch(codesWithUnknownOperation::contains)) {
                log.warn("For dossier {}: requested codes for which no mapping exists in db: {}", dossier.getIdMessage(), codesWithUnknownOperation);
                kafkaProducer.sendAbcEgateProblemMessage(dossier);
                if(requiredAdditionalData.stream().noneMatch(finishedTransactionCodes::contains)) { //ho almeno un codice per cui non so cosa fare ma non ho neanche alcun codice per cui posso sicuramente considerare transazione conclusa, quindi considero tutto concluso
                    isTransactionConcluded = true;
                    log.warn("For dossier {}: there are requested codes for which no mapping exists in db and no codes for which a mapping exists and indicates transaction conclusion. Therefore, the transaction is considered completed.", dossier.getIdMessage());
                }
            }
            if (!savedSifAddDataRequestsStrings.containsAll(requiredAdditionalData) || requiredAdditionalData.stream().anyMatch(finishedTransactionCodes::contains)) {
                isTransactionConcluded = true;
                for (String requiredData : requiredAdditionalData) {
                    savedSifAddDataRequests.stream()
                            .filter(sifAddDataRequest -> sifAddDataRequest.getValue().equalsIgnoreCase(requiredData)).findFirst()
                            .ifPresent(addDataRequest -> log.info("The following requested code has been ignored for dossier {}. {}: {}", dossier.getIdMessage(), requiredData, addDataRequest.getDescription()));
                }
            } else {
                HeaderAsyncRequestType headerAsyncRequestType = dossierService.buildHeaderAsyncRequestType(dossier);
                if(!Boolean.parseBoolean(fakeClientsReturns) && (headerAsyncRequestType.getTimestamp() == null || headerAsyncRequestType.getTransactionID() == null)) {
                    log.error("DossierResponseJob: Error processing dossier with Id: {}", dossier.getIdMessage());
                    dossier.setSifInteractionCount(dossier.getSifInteractionCount() + 1);
                    dossier.setLastSifInteractionDate(new Date());
                    dossier.setApplication(applicationName);
                    dossier.setStatus(DossierStatusEnum.SMART_BORDER_CONTROL_CALLBACK_KO.getDossierStatusEnumValue());
                    dossierService.save(dossier);
                    return new DossierResponseResult(false, dossier.getIdMessage());
                }

                AddDataToBorderControlRequestMessageType addDataToBorderControlRequest = dossierService.mapDossierToAddDataToBorderControlRequest(dossier, requiredAdditionalData);

                if(dossier.getEgateHandled().equals(Boolean.TRUE)) {
                    if(isStartBorderControlResult) { //in caso di isStartBorderControlResult se esiste un dossier con egateHandled=true in DB allora ha sicuramente has_to_be_processed=true //ricorda che questi dossier ricevuti da AbcEgteConsumer sono dossier con id diverso da quelli ricevuto da RegistrationConsumer e quindi hanno alcuni campi (face o finger) almeno temporaneamente vuoti in alcuni momenti e devi attendere di avere tutti i dati per inviare addDataToBorderControlRequest
                        //startBorderControlResponse chiede o Face o Finger o niente o "manca prefascicolo"
                        if(addDataToBorderControlRequest.getCollectedData().getFP() != null && addDataToBorderControlRequest.getCollectedData().getFP().getNISTFile() == null) {
                            kafkaProducer.sendAbcEgateResponseMessage(dossier, AbcEgateResponseMessageResultEnum.NEED_FINGER, "");
                            //addDataToBorderControlRequest.getCollectedData().setFP(null);
                            dossier.setStatus(DossierStatusEnum.NEED_EGATE_FINGER.getDossierStatusEnumValue()); //attendi i dati (finger) su abcEgateConsumer per poter fare addData
                        } else if(addDataToBorderControlRequest.getCollectedData().getFI() != null && addDataToBorderControlRequest.getCollectedData().getFI().getNISTFile() == null) {
                            kafkaProducer.sendAbcEgateResponseMessage(dossier, AbcEgateResponseMessageResultEnum.NEED_FACE, "");
                            //addDataToBorderControlRequest.getCollectedData().setFI(null);
                            dossier.setStatus(DossierStatusEnum.NEED_EGATE_FACE.getDossierStatusEnumValue()); //attendi i dati (face) su abcEgateConsumer per poter fare addData
                        }
                        if(addDataToBorderControlRequest.getCollectedData().getTravelDocument().getDocumentNumber().equalsIgnoreCase("manca prefascicolo")) kafkaProducer.sendAbcEgateResponseMessage(dossier, AbcEgateResponseMessageResultEnum.REDIRECT_TO_KIOSK, ""); //TODO  correggi questo if quando saprai decodificare "manca prefascicolo" e assicurati che il dossier assuma lo status corretto (forse EGATE_FINISHED)
                        //if(addDataToBorderControlRequest.getCollectedData().getFP() == null && addDataToBorderControlRequest.getCollectedData().getFI() == null && !dossier.getStatus().equalsIgnoreCase(DossierStatusEnum.NEED_EGATE_FINGER.getDossierStatusEnumValue()) && dossier.getStatus().equalsIgnoreCase(DossierStatusEnum.NEED_EGATE_FACE.getDossierStatusEnumValue())) sendAbcEgateResponseMessage(dossier, AbcEgateResponseMessageResultEnum.COMPLETE, dossier.getResidualDays() != null ? dossier.getResidualDays().toString() : ""); //assicurati che questo if significhi "se non richiede niente (cioè ritorna 5019)" e assicurati che il dossier assuma lo status corretto (forse EGATE_FINISHED), oltre al fatto che questo messaggio deve essere inviato solo quando necessario (re-check test cases)
                        dossier.setSifInteractionCount(0);
                        //dossier.setHasToBeProcessed(false);
                        dossierService.save(dossier);
                        logService.saveEventLog(LogScopeEnum.INTERNAL.getValue(), transaction, "Sent AbcEgateResponseMessage ".concat(dossier.getStatus()), null, logId);
                        return new DossierResponseResult(true, dossier.getIdMessage());
                    } else if(dossier.getHasToBeProcessed().equals(Boolean.TRUE)) { //caso in cui ho un AddDataToBorderControlResponse //TODO  caso particolare da gestire forse: non fare nulla se AddDataToBorderControl chiede ciò che ho appena inviato cioè ad esempio invio Face ma richiede nuovamente Face (attento a non andare in loop inviando sempre lo stesso volto se non mi arriva mai un nuovo volto da AbcEgateConsumer)
                        if(addDataToBorderControlRequest.getCollectedData().getFP() != null && dossier.getFingerprintImage() == null) {
                            kafkaProducer.sendAbcEgateResponseMessage(dossier, AbcEgateResponseMessageResultEnum.NEED_FINGER, "");
                            //addDataToBorderControlRequest.getCollectedData().setFP(null);
                            dossier.setStatus(DossierStatusEnum.NEED_EGATE_FINGER.getDossierStatusEnumValue());
                            dossierService.save(dossier);
                            return new DossierResponseResult(true, dossier.getIdMessage());
                        }
                        if(addDataToBorderControlRequest.getCollectedData().getFI() != null && dossier.getFaceImage() == null) {
                            kafkaProducer.sendAbcEgateResponseMessage(dossier, AbcEgateResponseMessageResultEnum.NEED_FACE, "");
                            //addDataToBorderControlRequest.getCollectedData().setFI(null);
                            dossier.setStatus(DossierStatusEnum.NEED_EGATE_FACE.getDossierStatusEnumValue());
                            dossierService.save(dossier);
                            return new DossierResponseResult(true, dossier.getIdMessage());
                        }
                        if(addDataToBorderControlRequest.getCollectedData().getFP() == null && addDataToBorderControlRequest.getCollectedData().getFI() == null && !dossier.getStatus().equalsIgnoreCase(DossierStatusEnum.NEED_EGATE_FINGER.getDossierStatusEnumValue()) && dossier.getStatus().equalsIgnoreCase(DossierStatusEnum.NEED_EGATE_FACE.getDossierStatusEnumValue())) kafkaProducer.sendAbcEgateResponseMessage(dossier, AbcEgateResponseMessageResultEnum.COMPLETE, dossier.getResidualDays() != null ? dossier.getResidualDays().toString() : ""); //TODO  assicurati che questo if significhi "se non richiede niente (cioè ritorna 5019)" e assicurati che il dossier assuma lo status corretto (forse EGATE_FINISHED), oltre al fatto che questo messaggio deve essere inviato solo quando necessario (re-check test cases)
                        logService.saveEventLog(LogScopeEnum.INTERNAL.getValue(), transaction, "Sent AbcEgateResponseMessage ".concat(dossier.getStatus()), null, logId); //TODO  forse meglio metterlo in ogni if suscritto perche' qui credo sia scorretto (quando facciamo addData request con finger ad esempio, ma re-check test cases)
                    } else {
                        dossier.setStatus(DossierStatusEnum.EGATE_FINISHED.getDossierStatusEnumValue());
                        dossierService.save(dossier);
                        return new DossierResponseResult(true, dossier.getIdMessage());
                    }
                }

                // se metodo precedente ha generato una addData senza allegare dati di FI e FP allora non devo inoltrare la richiesta: si tiene conto del caso di passeggero avente età eleggibbile per lo step di acquisizione delle impronte ma per il quale il gateway non ha ricevuto dal kiosk i dati necessari
                if(addDataToBorderControlRequest.getCollectedData().getFI() != null || addDataToBorderControlRequest.getCollectedData().getFP() != null) {
                    dossier.setSifTransactionId(headerAsyncRequestType.getTransactionID());
                    dossier.setLastSifInteractionDate(new Date());
                    dossier.setSifInteractionCount(dossier.getSifInteractionCount() + 1);
                    dossierService.save(dossier);

                    boolean addDataResult;
                    try {
                        logId = logService.saveEventLog(LogScopeEnum.INTERNAL.getValue(), transaction,
                                "Requesting addDataToBorderControl for dossier " + dossier.getIdMessage(), null);
                        if(dossier.getEgateHandled().equals(Boolean.TRUE) && dossier.getSurveyVersion() != null && !dossier.getSurveyVersion().isEmpty()) { //questo significa che un egate ha ricevuto da endpoint force_processing il comando di processare il dossier e con una causale da aggiungere nella richiesta di AddDataToBorderControl (la cuasale la aggiungiamo solo per fingerprint)
                            if(addDataToBorderControlRequest.getCollectedData() == null) {
                                AddDataToBorderControlRequestMessageType.CollectedData collectedData = new AddDataToBorderControlRequestMessageType.CollectedData();
                                FPRequestType fpRequestType = new FPRequestType();
                                fpRequestType.setNotProvidedReason(dossier.getSurveyVersion());
                                collectedData.setFP(fpRequestType);
                                addDataToBorderControlRequest.setCollectedData(collectedData);
                            } else {
                                if(addDataToBorderControlRequest.getCollectedData().getFP() == null) {
                                    FPRequestType fpRequestType = new FPRequestType();
                                    fpRequestType.setNotProvidedReason(dossier.getSurveyVersion());
                                    addDataToBorderControlRequest.getCollectedData().setFP(fpRequestType);
                                } else {
                                    addDataToBorderControlRequest.getCollectedData().getFP().setNotProvidedReason(dossier.getSurveyVersion());
                                }
                            }
                        }
                        addDataResult = soapClientWfe.performAddDataToBorderControl(addDataToBorderControlRequest, headerAsyncRequestType, dossier.getIdMessage());
                        if(addDataResult && dossier.getEgateHandled().equals(Boolean.TRUE) && dossier.getSurveyVersion() != null && !dossier.getSurveyVersion().isEmpty()) {
                            dossier.setSurveyVersion(null);
                            dossierService.save(dossier);
                        }
                        logService.saveEventLog(LogScopeEnum.INTERNAL.getValue(), transaction, addDataResult ? "Completed successfully" : "Completed with errors", null, logId);
                        dossier.setStatus(addDataResult ? DossierStatusEnum.SMART_BORDER_CONTROL_CALLBACK_OK.getDossierStatusEnumValue() : DossierStatusEnum.SMART_BORDER_CONTROL_CALLBACK_KO.getDossierStatusEnumValue());
                    } catch (InterruptedException | JsonProcessingException ex) {
                        dossier.setStatus(DossierStatusEnum.SMART_BORDER_CONTROL_CALLBACK_KO.getDossierStatusEnumValue());
                        log.error("DossierResponseJob: Error performing addDataToBorderControl for dossier with Id: {} due to {}", dossier.getIdMessage(), ex.getMessage());
                        logService.saveEventLog(LogScopeEnum.INTERNAL.getValue(), transaction, "Error performing addDataToBorderControl due to " + ex.getMessage(), null, logId);
                        return new DossierResponseResult(false, dossier.getIdMessage());
                    }

                    // se sono riuscito a lanciare addData eseguo reset limite di contatto per polling risposta asincrona, altrimenti imposto tale valore a maxRetries in modo che DossierResponseJob non prenda più in carico questo dossier
                    dossier.setSifInteractionCount(addDataResult ? 0 : maxRetries);
                    dossier.setLastSifInteractionDate(new Date());
                    dossierService.save(dossier);
                } else if(dossier.getEgateHandled().equals(Boolean.FALSE)) isTransactionConcluded = true; // leggere commento presente subito prima dell'if
            }

            if (isTransactionConcluded) {
                log.info("No additional data required, the transaction has been completed for dossier {}", dossier.getIdMessage());
                dossier.setSifInteractionCount(0);
                if(dossier.getEgateHandled()) {
                    dossier.setStatus(DossierStatusEnum.EGATE_FINISHED.getDossierStatusEnumValue());
                    if(!requiredAdditionalData.isEmpty() && requiredAdditionalData.contains("5019")) kafkaProducer.sendAbcEgateResponseMessage(dossier, AbcEgateResponseMessageResultEnum.COMPLETE, dossier.getResidualDays() != null ? dossier.getResidualDays().toString() : "");
                    if(warningCodes != null && !warningCodes.isEmpty() && warningCodes.contains("5099")) isAtomicOperationRequiredInVIS = false;
                    else if(isAtomicOperationRequiredInVIS == null) isAtomicOperationRequiredInVIS = false;
                    kafkaProducer.sendAbcEgateOperazioniAtomicheMessage(dossier, isAtomicOperationRequiredInVIS);
                } else dossier.setStatus(DossierStatusEnum.EES_NEEDS_SURVEY.getDossierStatusEnumValue());
                dossierService.save(dossier);
            }
        }/* else {
            log.warn("DossierResponseJob: No additional data required for dossier {}. The transaction has been completed.", dossier.getIdMessage());
            dossier.setSifInteractionCount(0);
            dossier.setStatus(DossierStatusEnum.EES_NEEDS_SURVEY.getDossierStatusEnumValue());
            dossierService.save(dossier);
            return new DossierResponseResult(true, dossier.getIdMessage());
        }*/
        return new DossierResponseResult(true, dossier.getIdMessage());
    }

    @PreDestroy
    public void shutdownExecutor() {
        log.info("Shutting down ExecutorService in DossierResponseJob");
        virtualThreadExecutor.shutdown();
        try {
            if (!virtualThreadExecutor.awaitTermination(60, TimeUnit.SECONDS)) {
                virtualThreadExecutor.shutdownNow();
                if (!virtualThreadExecutor.awaitTermination(60, TimeUnit.SECONDS)) {
                    log.error("DossierResponseJob ExecutorService did not terminate");
                }
            }
        } catch (InterruptedException e) {
            virtualThreadExecutor.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
}
