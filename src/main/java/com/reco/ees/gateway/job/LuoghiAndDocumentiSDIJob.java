package com.reco.ees.gateway.job;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.reco.ees.gateway.ShutdownManager;
import com.reco.ees.gateway.enums.*;
import com.reco.ees.gateway.generated.sif.iae.*;
import com.reco.ees.gateway.kafka.producer.KafkaProducer;
import com.reco.ees.gateway.mapper.SDIDownloadMapper;
import com.reco.ees.gateway.repository.AutoScheduledJobsLockRepository;
import com.reco.ees.gateway.repository.DocumentiSDIRepository;
import com.reco.ees.gateway.repository.LuoghiSDIRepository;
import com.reco.ees.gateway.repository.model.AutoScheduledJobsLock;
import com.reco.ees.gateway.repository.model.SDIDownloadRequestStatus;
import com.reco.ees.gateway.service.LogService;
import com.reco.ees.gateway.service.ParameterService;
import com.reco.ees.gateway.service.SDIDownloadRequestStatusService;
import com.reco.ees.gateway.soap_clients.SoapClientSifSupport;
import com.reco.ees.gateway.util.DateUtil;
import com.reco.ees.gateway.util.EmailUtil;
import com.reco.ees.gateway.util.UtilsService;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.*;

@Slf4j
@Service
@DependsOn({"kafkaAdmin", "kafkaProducerConfig", "kafkaConsumerConfig", "kafkaInit"})
public class LuoghiAndDocumentiSDIJob { //TODO da testare
    private final SDIDownloadRequestStatusService sdiDownloadRequestStatusService;
    private final SoapClientSifSupport soapClientSifSupport;
    private final UtilsService utilsService;
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final LogService logService;
    private final AutoScheduledJobsLockRepository autoScheduledJobsLockRepository;
    private final ParameterService parameterService;
    private final KafkaProducer kafkaProducer;
    private final EmailUtil emailUtil;
    private final DocumentiSDIRepository documentiSDIRepository;
    private final LuoghiSDIRepository luoghiSDIRepository;

    int refreshFrequencyHours = 24;
    public enum DownloadSDITypeEnum {LUOGHI, DOCUMENTI}
    private static final Map<DownloadSDITypeEnum, LuoghiAndDocumentiSDIStatusEnum> SDIDownloadedStatusMap = Map.of(
            DownloadSDITypeEnum.LUOGHI, LuoghiAndDocumentiSDIStatusEnum.DOWNLOADED_NEW_LUOGHI,
            DownloadSDITypeEnum.DOCUMENTI, LuoghiAndDocumentiSDIStatusEnum.DOWNLOADED_NEW_DOCUMENTI
    );
    private static final Map<DownloadSDITypeEnum, LuoghiAndDocumentiSDIStatusEnum> SDIDownloadSentStatusMap = Map.of(
            DownloadSDITypeEnum.LUOGHI, LuoghiAndDocumentiSDIStatusEnum.LUOGHI_SENT_ON_KAFKA_QUEUE,
            DownloadSDITypeEnum.DOCUMENTI, LuoghiAndDocumentiSDIStatusEnum.DOCUMENTI_SENT_ON_KAFKA_QUEUE
    );
    ExecutorService executorService = Executors.newVirtualThreadPerTaskExecutor();
    public record DownloadSDIResult(boolean success, LuoghiAndDocumentiSDIStatusEnum status) {}

    public LuoghiAndDocumentiSDIJob(SDIDownloadRequestStatusService sdiDownloadRequestStatusService, SoapClientSifSupport soapClientSifSupport, UtilsService utilsService, LogService logService, AutoScheduledJobsLockRepository autoScheduledJobsLockRepository, ParameterService parameterService, KafkaProducer kafkaProducer, EmailUtil emailUtil, DocumentiSDIRepository documentiSDIRepository, LuoghiSDIRepository luoghiSDIRepository) {
        this.sdiDownloadRequestStatusService = sdiDownloadRequestStatusService;
        this.soapClientSifSupport = soapClientSifSupport;
        this.utilsService = utilsService;
        this.logService = logService;
        this.autoScheduledJobsLockRepository = autoScheduledJobsLockRepository;
        this.parameterService = parameterService;
        this.kafkaProducer = kafkaProducer;
        this.emailUtil = emailUtil;
        this.documentiSDIRepository = documentiSDIRepository;
        this.luoghiSDIRepository = luoghiSDIRepository;
    }

    @Value("${using.heartbeat:false}")
    private boolean usingHeartbeat;
    @Value("${application.name}")
    private String applicationName;
    @Value("${kafka.sdi.download.topicName}")
    private String topicName;
    @Value("${kafka.sent.message.show:false}")
    private boolean showSentMessage;
    @Value("${autoscheduled.job.locks.offset.seconds:10}")
    private String locksOffsetSeconds;
    @Value("${email.sdi.download.subject}")
    private String emailSubject;
    @Value("${email.sdi.download.success.content}")
    private String emailSuccessContent;
    @Value("${email.sdi.download.failed.content}")
    private String emailFailedContent;

    public synchronized void processLuoghiAndDocumentiSDI(boolean... forceCoreLogicExecution) {
        if(!Boolean.parseBoolean(parameterService.getParameterById(ParametersEnum.SDI_DOWNLOAD_ENABLED.getParametersEnumValue()).getValue())) {
            log.info("LuoghiAndDocumentiSDI skipped by {} because SDI_DOWNLOAD_ENABLED parameter is set to false (hot swappable)", applicationName);
            return;
        }
        log.info("processLuoghiAndDocumentiSDI Job started");
        log.info("processLuoghiAndDocumentiSDIJon running on {} thread", Thread.currentThread().isVirtual() ? "virtual" : "platform");
        refreshFrequencyHours = Integer.parseInt(parameterService.getParameterById(ParametersEnum.SDI_DOWNLOAD_REFRESH_FREQUENCY_HOURS.getParametersEnumValue()).getValue());

        if((shouldDownloadLuoghiAndDocumentiSDI() || (forceCoreLogicExecution.length > 0 && forceCoreLogicExecution[0])) && ShutdownManager.getIsAppRunning()) {
            ShutdownManager.incrementActiveJobs(ShutdownManager.jobNames.LuoghiAndDocumentiSDIJob.name());
            AutoScheduledJobsLock autoScheduledJobsLock = null;
            try {
                if(usingHeartbeat) Thread.sleep(Integer.parseInt(locksOffsetSeconds) * ThreadLocalRandom.current().nextLong(1000, 2000 + 1));
                autoScheduledJobsLock = autoScheduledJobsLockRepository.findByJobName(AutoScheduledJobsLockEnum.LUOGHI_AND_DOCUMENTI_JOB.getAutoScheduledJobsLockEnumValue());
                LocalDateTime thresholdTime = LocalDateTime.now().minusHours(refreshFrequencyHours);
                if (autoScheduledJobsLock == null || autoScheduledJobsLock.getLockedAt().isBefore(thresholdTime) || (forceCoreLogicExecution.length > 0 && forceCoreLogicExecution[0])) {
                    autoScheduledJobsLock = new AutoScheduledJobsLock();
                    autoScheduledJobsLock.setApplication(applicationName);
                    autoScheduledJobsLock.setJobName(AutoScheduledJobsLockEnum.LUOGHI_AND_DOCUMENTI_JOB.getAutoScheduledJobsLockEnumValue());
                    autoScheduledJobsLock.setLockedAt(LocalDateTime.now());
                    autoScheduledJobsLockRepository.saveAndFlush(autoScheduledJobsLock);
                } else {
                    log.info("Lock already exists and is not expired for LuoghiAndDocumentiSDIJob");
                    utilsService.allDoneAndSleep(ShutdownManager.jobNames.LuoghiAndDocumentiSDIJob.name());
                    return;
                }
            } catch (Exception e) {
                log.info("Contention lost by {} for LuoghiAndDocumentiSDIJob", applicationName);
                utilsService.allDoneAndSleep(ShutdownManager.jobNames.LuoghiAndDocumentiSDIJob.name());
                return;
            }
            try {
                log.info("LuoghiAndDocumentiSDI Job started by {}", applicationName);

                List<Future<DownloadSDIResult>> futures = new ArrayList<>();
                for (DownloadSDITypeEnum downloadSDITypeEnum : DownloadSDITypeEnum.values()) {
                    if (shouldDownloadAndSendOneSDIType(downloadSDITypeEnum)) {
                        if (executorService.isShutdown()) executorService = Executors.newVirtualThreadPerTaskExecutor();
                        futures.add(executorService.submit(() -> luoghiAndDocumentiSDIHandler(downloadSDITypeEnum)));
                    }
                }
                ListIterator<Future<DownloadSDIResult>> iterator = futures.listIterator(futures.size());
                List<Boolean> downloadSDIProcessingResults = new ArrayList<>();
                while (iterator.hasPrevious()) {
                    Future<DownloadSDIResult> future = iterator.previous();
                    try {
                        DownloadSDIResult result = future.get(SDIDownloadRetryMinutesInterval() * (SDIDownloadRetriesLimit() + 1), TimeUnit.MINUTES);
                        downloadSDIProcessingResults.add(result.success);
                        if (result.success) log.info("LuoghiAndDocumentiSDIJob: SDI Download with state {} processed successfully", result.status);
                        else log.warn("LuoghiAndDocumentiSDIJob: SDI Download with state {} processed with problems", result.status);
                    } catch (TimeoutException te) {
                        downloadSDIProcessingResults.add(false);
                        log.error("LuoghiAndDocumentiSDI job timeout reached: {}", te.getMessage());
                        future.cancel(true);
                    } catch (InterruptedException ie) {
                        downloadSDIProcessingResults.add(false);
                        Thread.currentThread().interrupt();
                        log.error("LuoghiAndDocumentiSDI job was interrupted: {}", ie.getMessage());
                    } catch (ExecutionException ee) {
                        downloadSDIProcessingResults.add(false);
                        log.error("LuoghiAndDocumentiSDI job encountered an exception: {}", ee.getCause().getMessage());
                    }
                }
                if (downloadSDIProcessingResults.stream().allMatch(Boolean::booleanValue)) {
                    sdiDownloadRequestStatusService.saveOrUpdateSDIDownloadRequestStatus(LuoghiAndDocumentiSDIStatusEnum.LUOGHI_AND_DOCUMENTI_SDI_JOB_COMPLETED_OK.getLuoghiAndDocumentiSDIStatusEnumValue());
                    emailSuccessContent = emailSuccessContent.replace("XXX", new Date().toString());
                    emailUtil.sendEmail(EmailTypeEnum.SDI_DOWNLOAD_EMAIL, emailSubject, emailSuccessContent);
                } else emailUtil.sendEmail(EmailTypeEnum.SDI_DOWNLOAD_EMAIL, emailSubject, emailFailedContent);
            } catch (Exception ex) {  //attento a verificare che questo venga lanciato quando serve cioè prova ad avere errori dopo aver vinto la contesa avendo più di 1 istanza e vedi cosa accade a tutte le istanze per quanto riguarda il ritentativo (forse erroneamente rifatti da tutte le istanze) di scarico luoghi e documenti SDI
                log.error("Error during LuoghiAndDocumentiSDI job", ex);
            }
            autoScheduledJobsLockRepository.delete(autoScheduledJobsLock);
            executorService.shutdown();
            log.info("LuoghiAndDocumentiSDI job finished by {}", applicationName);
            utilsService.allDoneAndSleep(ShutdownManager.jobNames.LuoghiAndDocumentiSDIJob.name());
        } else {
            log.info("LuoghiAndDocumentiSDI skipped by {}", applicationName);
        }
    }

    private DownloadSDIResult luoghiAndDocumentiSDIHandler(DownloadSDITypeEnum downloadSDITypeEnum) {
        DownloadSDIResult downloadSDIResult = null;
        String transaction = "LuoghiAndDocumentiSDI JOB";
        String logId = logService.saveEventLog(LogScopeEnum.INTERNAL.getValue(), transaction,
                "Requesting " + downloadSDITypeEnum, null);
        EsitoType esito = null;
        DocumentiSDIResponse.DocumentiSDIResult documentiSDIResult = null;
        LuoghiSDIResponse.LuoghiSDIResult luoghiSDIResult = null;
        int retries = 0;
        do {
            switch (downloadSDITypeEnum) {
                case LUOGHI -> {
                    LuoghiSDI luoghiSDI = new LuoghiSDI();
                    LuoghiSDI.RicLuoghi ricLuoghi = new LuoghiSDI.RicLuoghi();
                    String attore = parameterService.getParameterById(ParametersEnum.SDI_DOWNLOAD_ACTOR.getParametersEnumValue()).getValue();
                    ricLuoghi.setAttore(attore);
                    luoghiSDI.setRicLuoghi(ricLuoghi);
                    LuoghiSDIResponse getLuoghiSDIResponse = soapClientSifSupport.performGetLuoghiSDI(luoghiSDI);
                    if(getLuoghiSDIResponse != null) {
                        esito = getLuoghiSDIResponse.getLuoghiSDIResult().getEsito();
                        luoghiSDIResult = getLuoghiSDIResponse.getLuoghiSDIResult();
                        luoghiSDIRepository.saveAllAndFlush(SDIDownloadMapper.toLuoghiSDIList(luoghiSDIResult.getLuoghi()));
                    } else {
                        esito = null;
                        luoghiSDIResult = null;
                    }
                }
                case DOCUMENTI -> {
                    DocumentiSDI documentiSDI = new DocumentiSDI();
                    DocumentiSDI.RicDocumenti ricDocumenti = new DocumentiSDI.RicDocumenti();
                    String attore = parameterService.getParameterById(ParametersEnum.SDI_DOWNLOAD_ACTOR.getParametersEnumValue()).getValue();
                    ricDocumenti.setAttore(attore);
                    documentiSDI.setRicDocumenti(ricDocumenti);
                    DocumentiSDIResponse getDocumentiSDIResponse = soapClientSifSupport.performGetDocumentiSDI(documentiSDI);
                    if(getDocumentiSDIResponse != null) {
                        esito = getDocumentiSDIResponse.getDocumentiSDIResult().getEsito();
                        documentiSDIResult = getDocumentiSDIResponse.getDocumentiSDIResult();
                        documentiSDIRepository.saveAllAndFlush(SDIDownloadMapper.toDocumentiSDIList(documentiSDIResult.getDoc()));
                    } else {
                        esito = null;
                        documentiSDIResult = null;
                    }
                }
            }
            if (esito != null && isSDIDownloadError(esito.getCodEsito())) { //TODO ricontrolla questa condizione, altre e flusso generale
                downloadSDIResult = new DownloadSDIResult(false, SDIDownloadedStatusMap.get(downloadSDITypeEnum));
                log.warn("luoghiAndDocumentiSDIHandler({}) will retry SDI download in {} minutes because of error: {}", downloadSDITypeEnum, SDIDownloadRetryMinutesInterval(), esito != null ? esito.getCodEsito() : "marshalSendAndReceive returned null");
                waitToRetrySDIDownload();
                retries++;
            } else {
                downloadSDIResult = new DownloadSDIResult(true, SDIDownloadedStatusMap.get(downloadSDITypeEnum));
                break;
            }
        } while (retries < SDIDownloadRetriesLimit());

        if(esito == null || !isSDIDownloadError(esito.getCodEsito())) sdiDownloadRequestStatusService.saveOrUpdateSDIDownloadRequestStatus(SDIDownloadedStatusMap.get(downloadSDITypeEnum).getLuoghiAndDocumentiSDIStatusEnumValue());

        if(esito != null && isSDIDownloadError(esito.getCodEsito())) return downloadSDIResult;
        if((downloadSDITypeEnum.equals(DownloadSDITypeEnum.DOCUMENTI) && documentiSDIResult != null) || (downloadSDITypeEnum.equals(DownloadSDITypeEnum.LUOGHI) && luoghiSDIResult != null)) {
            if(downloadSDITypeEnum.equals(DownloadSDITypeEnum.DOCUMENTI)) {
                sendToKafkaAndSaveDownloadSDIStatus(documentiSDIResult.getDoc(), downloadSDITypeEnum);
            } else if(downloadSDITypeEnum.equals(DownloadSDITypeEnum.LUOGHI)) {
                sendToKafkaAndSaveDownloadSDIStatus(luoghiSDIResult.getLuoghi(), downloadSDITypeEnum);
            }
            downloadSDIResult = new DownloadSDIResult(true, SDIDownloadSentStatusMap.get(downloadSDITypeEnum)); //attento che questo success true non è sicurissimo perchè sendToKafkaAndSaveDownloadSDIStatus potrebbe andare in exceptionally
            logService.saveEventLog(LogScopeEnum.INTERNAL.getValue(), transaction, "Completed successfully", null, logId);
        } else {
            log.error("{} SDI main content is null or empty", downloadSDITypeEnum);
            downloadSDIResult = new DownloadSDIResult(false, SDIDownloadSentStatusMap.get(downloadSDITypeEnum));
            logService.saveEventLog(LogScopeEnum.INTERNAL.getValue(), transaction, "Completed with errors ", null, logId);
        }
        return downloadSDIResult;
    }

    private void sendToKafkaAndSaveDownloadSDIStatus(Object SDIDownload, DownloadSDITypeEnum downloadSDITypeEnum) {
        if(SDIDownload == null) return;

        objectMapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSSSS"));
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        /*objectMapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.NONE);
        objectMapper.setVisibility(PropertyAccessor.FIELD, JsonAutoDetect.Visibility.ANY);

        objectMapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.NONE);
        objectMapper.setVisibility(PropertyAccessor.FIELD, JsonAutoDetect.Visibility.ANY);
        objectMapper.setVisibility(PropertyAccessor.GETTER, JsonAutoDetect.Visibility.NONE);
        objectMapper.setVisibility(PropertyAccessor.IS_GETTER, JsonAutoDetect.Visibility.NONE);
        objectMapper.configure(MapperFeature.USE_ANNOTATIONS, true);*/

        //objectMapper.setPropertyNamingStrategy(PropertyNamingStrategies.UPPER_CAMEL_CASE);
        /*objectMapper.configure(MapperFeature.USE_ANNOTATIONS, true);
        objectMapper.setPropertyNamingStrategy(new PropertyNamingStrategy() {
            @Override
            public String nameForField(MapperConfig<?> config, AnnotatedField field, String defaultName) {
                return field.getName();
            }

            @Override
            public String nameForGetterMethod(MapperConfig<?> config, AnnotatedMethod method, String defaultName) {
                return method.getName();
            }

            @Override
            public String nameForSetterMethod(MapperConfig<?> config, AnnotatedMethod method, String defaultName) {
                return method.getName();
            }
        });*/

        ObjectNode SDIDownloadJsonMessage = objectMapper.createObjectNode();
        SDIDownloadJsonMessage.put("type", downloadSDITypeEnum.toString());
        /*SDIDownloadJsonMessage.put("content", SDIDownload);*/
        JsonNode contentNode = objectMapper.valueToTree(SDIDownload);
        SDIDownloadJsonMessage.set("content", contentNode);

        try {
            kafkaProducer.sendMessage(topicName, SDIDownloadJsonMessage)
                    .thenAccept(result -> {
                        log.info("{}: sent message=[{}] with offset=[{}]", downloadSDITypeEnum.toString(), showSentMessage ? SDIDownload : "", result.getRecordMetadata().offset());
                        sdiDownloadRequestStatusService.saveOrUpdateSDIDownloadRequestStatus(SDIDownloadSentStatusMap.get(downloadSDITypeEnum).getLuoghiAndDocumentiSDIStatusEnumValue());
                    })
                    .exceptionally(ex -> {
                        //log.info("AAA 5.1 - ERRORE IN INVIO A KAFKA");
                        log.error("{}: unable to send message=[{}] due to : {}", downloadSDITypeEnum.toString(), showSentMessage ? SDIDownload : "", ex.getMessage());
                        return null;
                    });
        } catch (Exception e) {
            log.error("{}: unable to send message=[{}] due to : {}", downloadSDITypeEnum.toString(), showSentMessage ? SDIDownload : "", e.getMessage());
        }
    }

    private static boolean isSDIDownloadError(String codeError) {
        return Arrays.stream(SDIDownloadCodeErrorEnum.values()).anyMatch(sdiDownloadCodeErrorEnum -> Objects.equals(sdiDownloadCodeErrorEnum.getSDIDownloadCodeErrorEnumValue(), codeError));
    }

    private int SDIDownloadRetriesLimit() {
        return Integer.parseInt(parameterService.getParameterById(ParametersEnum.SDI_DOWNLOAD_RETRIES_LIMIT.getParametersEnumValue()).getValue());
    }

    private void waitToRetrySDIDownload() {
        try {
            Thread.sleep(SDIDownloadRetryMinutesInterval() * 60 * 1000);
        } catch (InterruptedException e) {
            log.error("Error during waiting to retry SDI download", e);
        }
    }

    private long SDIDownloadRetryMinutesInterval() {
        return Long.parseLong(parameterService.getParameterById(ParametersEnum.SDI_DOWNLOAD_RETRY_MINUTES_INTERVAL.getParametersEnumValue()).getValue());
    }

    public boolean shouldDownloadAndSendOneSDIType(DownloadSDITypeEnum downloadSDITypeEnum) {
        SDIDownloadRequestStatus SDIDownloadRequestStatus = sdiDownloadRequestStatusService.getSDIDownloadRequestStatusByStatus(SDIDownloadSentStatusMap.get(downloadSDITypeEnum).getLuoghiAndDocumentiSDIStatusEnumValue());
        return SDIDownloadRequestStatus == null || DateUtil.addDeltaToDate(SDIDownloadRequestStatus.getDate(), 0, refreshFrequencyHours-1, 0,0, 0).before(new Date());
        //return true;
    }

    public boolean shouldDownloadLuoghiAndDocumentiSDI() {
        SDIDownloadRequestStatus SDIDownloadRequestStatus = sdiDownloadRequestStatusService.getSDIDownloadRequestStatusByStatus(LuoghiAndDocumentiSDIStatusEnum.LUOGHI_AND_DOCUMENTI_SDI_JOB_COMPLETED_OK.getLuoghiAndDocumentiSDIStatusEnumValue());
        return SDIDownloadRequestStatus == null || DateUtil.addDeltaToDate(SDIDownloadRequestStatus.getDate(), 0, refreshFrequencyHours-1, 0,0, 0).before(new Date());
    }

    @PreDestroy
    public void shutdownExecutor() {
        log.info("Shutting down ExecutorService in LuoghiAndDocumentiSDIJob");
        executorService.shutdown();
        try {
            if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
                executorService.shutdownNow();
                if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
                    log.error("LuoghiAndDocumentiSDIJob ExecutorService did not terminate");
                }
            }
        } catch (InterruptedException e) {
            executorService.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
}
