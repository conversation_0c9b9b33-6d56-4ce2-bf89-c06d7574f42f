package com.reco.ees.gateway.job;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.reco.ees.gateway.enums.ParametersEnum;
import com.reco.ees.gateway.service.ParameterService;
import jakarta.annotation.PostConstruct;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Slf4j
@Component
@ConditionalOnProperty(name = "zabbix.network.monitor.enabled", havingValue = "true", matchIfMissing = false)
public class NetworkMonitor {
    @Value("${zabbix.network.monitor.use.external.zabbix.sender:false}")
    boolean useExternalZabbixSender; //se true allora si presuppone di poter lanciare il comando "zabbix_sender"
    @Value("${zabbix.network.monitor.frequency.ms:60000}")
    private long monitorFrequencyMs;
    private final int timeout = 5000;
    private final ParameterService parameterService;
    private String zabbixHost;
    private int zabbixPort;
    private String zabbixHostName;
    private String zabbixMonitorIpAddressesString;
    private List<String> ipAddresses;
    private final ObjectMapper objectMapper = new ObjectMapper();

    public NetworkMonitor(ParameterService parameterService) {
        this.parameterService = parameterService;
    }

    @PostConstruct
    public void init() {
        zabbixMonitorIpAddressesString = parameterService.getParameterById(ParametersEnum.ZABBIX_MONITOR_IP_ADDRESSES.getParametersEnumValue()).getValue();
        ipAddresses = Arrays.asList(zabbixMonitorIpAddressesString.split(","));
        zabbixHost = parameterService.getParameterById(ParametersEnum.ZABBIX_SERVER_HOST.getParametersEnumValue()).getValue();
        zabbixPort = Integer.parseInt(parameterService.getParameterById(ParametersEnum.ZABBIX_SERVER_PORT.getParametersEnumValue()).getValue());
        zabbixHostName = parameterService.getParameterById(ParametersEnum.ZABBIX_HOST_NAME.getParametersEnumValue()).getValue();
        
        log.info("Monitoraggio inizialmente configurato per gli IP: {}", ipAddresses);
        log.info("Frequenza di monitoraggio: {} ms", monitorFrequencyMs);
        log.info("Server Zabbix: {}:{}", zabbixHost, zabbixPort);
        log.info("Nome host Zabbix: {}", zabbixHostName);
    }

    @Scheduled(fixedRateString = "${zabbix.network.monitor.frequency.ms:60000}")
    public void pingIpAddresses() {
        log.debug("Inizia controllo disponibilità IP...");

        boolean anyFailure = false;
        List<String> failedIps = new ArrayList<>();

        String currentZabbixMonitorIpAddressesString = parameterService.getParameterById(ParametersEnum.ZABBIX_MONITOR_IP_ADDRESSES.getParametersEnumValue()).getValue();
        if (!zabbixMonitorIpAddressesString.equals(currentZabbixMonitorIpAddressesString)) {
            zabbixMonitorIpAddressesString = currentZabbixMonitorIpAddressesString;
            ipAddresses = Arrays.asList(zabbixMonitorIpAddressesString.split(","));
            log.info("Lista IP da monitorare modificata a Runtime: {}", ipAddresses);
        }

        for (String entry : ipAddresses) {
            String host = entry;
            Integer port = null;
            if (entry.contains(":")) {
                String[] parts = entry.split(":", 2);
                host = parts[0];
                try {
                    port = Integer.parseInt(parts[1]);
                } catch (NumberFormatException e) {
                    log.warn("Porta non valida per {}: {}", entry, e.getMessage());
                    continue;
                }
            }

            boolean isReachable = (port != null) ? checkHostPort(host, port) : pingHost(host);

            if (!isReachable) {
                anyFailure = true;
                failedIps.add(entry);
            } else log.debug("Host {} è raggiungibile", entry);

            if (useExternalZabbixSender) {
                sendViaZabbixSender(entry, isReachable);
            } else {
                notifyZabbix(entry, isReachable);
            }
        }

        if (anyFailure) {
            log.warn("Controllo disponibilità IP completato con {} fallimenti: {}",
                    failedIps.size(), String.join(", ", failedIps));
        } else {
            log.debug("Controllo disponibilità IP completato con successo");
        }
    }

    private boolean checkHostPort(String host, int port) {
        try (Socket socket = new Socket()) {
            socket.connect(new InetSocketAddress(host, port), timeout);
            return true;
        } catch (IOException e) {
            log.warn("Errore durante la connessione TCP a {}:{} -> {}", host, port, e.getMessage());
            return false;
        }
    }

    private boolean pingHost(String host) {
        try {
            InetAddress address = InetAddress.getByName(host);
            return address.isReachable(timeout);
        } catch (IOException e) {
            log.warn("Errore durante il ping dell'host {}: {}", host, e.getMessage());
            return false;
        }
    }

    private void sendViaZabbixSender(String ipAddress, boolean isAvailable) {
        List<String> cmd = new ArrayList<>();
        cmd.add("zabbix_sender"); // processo per inviare dati a Zabbix
        cmd.add("-z"); cmd.add(zabbixHost); // host del server Zabbix
        cmd.add("-p"); cmd.add(String.valueOf(zabbixPort)); // porta del server Zabbix
        cmd.add("-s"); cmd.add(zabbixHostName); // nome dell’host cosi' come registrato in Zabbix
        cmd.add("-k"); cmd.add("network.ping[" + ipAddress + "]"); // chiave dell'item che identifica il parametro monitorato
        cmd.add("-o"); cmd.add(isAvailable ? "1" : "0"); // valore da inviare (1 = raggiungibile, 0 = non raggiungibile)

        ProcessBuilder pb = new ProcessBuilder(cmd);
        pb.redirectErrorStream(true);
        try {
            Process p = pb.start();
            String output = new String(p.getInputStream().readAllBytes(), StandardCharsets.UTF_8);
            int code = p.waitFor();
            if (code != 0) {
                log.error("zabbix_sender failed (code={}): {}", code, output);
            } else {
                log.debug("zabbix_sender output: {}", output.trim());
            }
        } catch (IOException | InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Errore in zabbix_sender: {}", e.getMessage());
        }
    }

    private void notifyZabbix(String ipAddress, boolean isAvailable) {
        int maxRetries = 3;
        int retryDelayMs = 1000;

        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            Socket socket = null;
            try {
                zabbixHost = parameterService.getParameterById(ParametersEnum.ZABBIX_SERVER_HOST.getParametersEnumValue()).getValue();
                zabbixPort = Integer.parseInt(parameterService.getParameterById(ParametersEnum.ZABBIX_SERVER_PORT.getParametersEnumValue()).getValue());
                zabbixHostName = parameterService.getParameterById(ParametersEnum.ZABBIX_HOST_NAME.getParametersEnumValue()).getValue();

                log.debug("Tentativo di connessione a Zabbix: {}:{}", zabbixHost, zabbixPort);
                socket = new Socket(zabbixHost, zabbixPort);
                socket.setSoTimeout(timeout*3); //3 volte il timeout usato per le altre connessioni
                
                // Preparazione dei dati
                ZabbixRequest request = new ZabbixRequest();

                ZabbixDataItem dataItem = new ZabbixDataItem();
                dataItem.setHost(zabbixHostName);
                dataItem.setKey("network.ping[" + ipAddress + "]");
                dataItem.setValue(isAvailable ? "1" : "0");

                request.getData().add(dataItem);

                // Serializzazione a JSON
                String jsonData = objectMapper.writeValueAsString(request);
                byte[] jsonBytes = jsonData.getBytes(StandardCharsets.UTF_8);

                // Formato corretto per il protocollo Zabbix (testato con v7.0.11 ma dovrebbe andar bene per zabbix v>=6.X)
                byte[] header = new byte[13];
                header[0] = 'Z';
                header[1] = 'B';
                header[2] = 'X';
                header[3] = 'D';
                header[4] = 1;
                
                // Lunghezza dati (little endian)
                header[5] = (byte) (jsonBytes.length & 0xFF);
                header[6] = (byte) ((jsonBytes.length >> 8) & 0xFF);
                header[7] = (byte) ((jsonBytes.length >> 16) & 0xFF);
                header[8] = (byte) ((jsonBytes.length >> 24) & 0xFF);
                
                // 4 byte riservati (0)
                header[9] = 0;
                header[10] = 0;
                header[11] = 0;
                header[12] = 0;

                OutputStream os = socket.getOutputStream();
                InputStream is = socket.getInputStream();
                
                try {
                    // Invia header e dati
                    os.write(header);
                    os.write(jsonBytes);
                    os.flush();
                    
                    log.debug("Dati inviati a Zabbix per IP {}: {}", ipAddress, isAvailable ? "UP" : "DOWN");
                    
                    // Leggi risposta
                    byte[] responseHeader = new byte[13];
                    int headerBytesRead = 0;
                    int totalHeaderBytesRead = 0;
                    
                    while (totalHeaderBytesRead < 13) {
                        headerBytesRead = is.read(responseHeader, totalHeaderBytesRead, 13 - totalHeaderBytesRead);
                        if (headerBytesRead == -1) {
                            log.warn("Connessione chiusa durante la lettura dell'header");
                            break;
                        }
                        totalHeaderBytesRead += headerBytesRead;
                    }
                    
                    if (totalHeaderBytesRead == 13) {
                        int dataLength = (responseHeader[5] & 0xFF) |
                                        ((responseHeader[6] & 0xFF) << 8) |
                                        ((responseHeader[7] & 0xFF) << 16) |
                                        ((responseHeader[8] & 0xFF) << 24);
                        
                        byte[] responseData = new byte[dataLength];
                        int dataBytesRead = 0;
                        int totalDataBytesRead = 0;
                        
                        while (totalDataBytesRead < dataLength) {
                            dataBytesRead = is.read(responseData, totalDataBytesRead, dataLength - totalDataBytesRead);
                            if (dataBytesRead == -1) {
                                log.warn("Connessione chiusa durante la lettura dei dati");
                                break;
                            }
                            totalDataBytesRead += dataBytesRead;
                        }
                        
                        if (totalDataBytesRead == dataLength) {
                            String jsonResponse = new String(responseData, StandardCharsets.UTF_8);
                            
                            try {
                                ZabbixResponse zabbixResponse = objectMapper.readValue(jsonResponse, ZabbixResponse.class);
                                if (!"success".equals(zabbixResponse.getResponse())) {
                                    log.warn("Risposta non positiva da Zabbix: {}", jsonResponse);
                                }
                            } catch (Exception e) {
                                log.warn("Impossibile parsare la risposta JSON: {}", e.getMessage());
                            }
                        }
                    }
                    
                    return; // Successo
                } finally {
                    try {
                        if (os != null) os.close();
                        if (is != null) is.close();
                    } catch (IOException e) {
                        log.error("Errore durante la chiusura degli stream: {}", e.getMessage());
                    }
                }
            } catch (IOException e) {
                if (attempt < maxRetries) {
                    log.debug("Tentativo {} fallito per notifica Zabbix IP {}: {}. Riprovo tra {}ms",
                            attempt, ipAddress, e.getMessage(), retryDelayMs);
                    try {
                        Thread.sleep(retryDelayMs);
                        retryDelayMs *= 2;
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        log.error("Interrotto durante retry", ie);
                        break;
                    }
                } else {
                    log.error("Errore durante l'invio dei dati a Zabbix dopo {} tentativi per IP {}: {}",
                            maxRetries, ipAddress, e.getMessage());
                }
            } finally {
                if (socket != null && !socket.isClosed()) {
                    try {
                        socket.close();
                    } catch (IOException e) {
                        log.error("Errore durante la chiusura del socket: {}", e.getMessage());
                    }
                }
            }
        }
    }

    @Data
    public static class ZabbixRequest {
        @JsonProperty("request")
        private String request = "sender data";

        @JsonProperty("data")
        private List<ZabbixDataItem> data = new ArrayList<>();
    }

    @Data
    public static class ZabbixDataItem {
        @JsonProperty("host")
        private String host;

        @JsonProperty("key")
        private String key;

        @JsonProperty("value")
        private String value;

        @JsonProperty("clock")
        private long clock = System.currentTimeMillis() / 1000;
    }

    @Data
    public static class ZabbixResponse {
        @JsonProperty("response")
        private String response;

        @JsonProperty("info")
        private String info;
    }

    //DI SEGUITO LA PROCEDURA DA SEGUIRE per USARE IL MONITORAGGIO EFFETTUATO DA QUESTA CLASSE (host, items e triggers):
    /*
    PROCEDURA LATO GATEWAY APP:
    - la tabella parameter di ees_gateway DB deve contenere almeno:
        ZabbixServerHost,**************
        ZabbixServerPort,10051
        ZabbixHostName,eesgateway
        ZabbixMonitorIpAddresses,"*************:443,*************:443,*************:443,************:443,************:443" (se non viene specificata porta tramite IP:PORTA allora verrà effettuato ping, altrimenti verrà effettuato un tentativo di connessione TCP come un telnet su porta specifica)
    Questi parametri vengono creati in automatico se non esistono e impostati ai valori suscritti. Possono essere modificati anche a Runtime.
    Se si compila gateway con properties di produzione, allora di default il servizio di controllo disponibilità IP con invio info a Zabbix sarà abilitato con controllo ogni minuto.

    PROCEDURA LATO ZABBIX DASHBOARD:
    - HOST
    Monitoraggio Host -> Crea Host:
        Nome Host: eesgateway
        Gruppo Host: Linux Servers
        Interfacce: nessuna
        Monitorato da: Server
        Abilitato: true

    - ITEMS
    Monitoraggio Host -> eesgateway -> Items -> Crea Item:
        Nome: Network status for IP ************
        Tipo: Trapper Zabbix
        Chiave: network.ping[************]
        Tipo di informazione: Numerico (senza segno)
        Storico: Store up to 31d
        Trend: Store up to 365d
        Abilitato: true
        Descrizione: Url d’invocazione SIF in ambienti PPE (Pre Produzione) e PRD (Produzione): Servizi WFE, Servizio Questionario e Ricezione Foto, Servizio ricezione risposte Asincrone

    Monitoraggio Host -> eesgateway -> Items -> Crea Item:
        Nome: Network status for IP ************
        Tipo: Trapper Zabbix
        Chiave: network.ping[************]
        Tipo di informazione: Numerico (senza segno)
        Storico: Store up to 31d
        Trend: Store up to 365d
        Abilitato: true
        Descrizione: Url d’invocazione SIF in ambienti PPE (Pre Produzione) e PRD (Produzione): Servizio Autenticazione e di logout

    Monitoraggio Host -> eesgateway -> Items -> Crea Item:
        Nome: Network status for IP *************
        Tipo: Trapper Zabbix
        Chiave: network.ping[*************]
        Tipo di informazione: Numerico (senza segno)
        Storico: Store up to 31d
        Trend: Store up to 365d
        Abilitato: true
        Descrizione: Url d’invocazione SIF in ambiente PGD con MUTUA AUTENTICAZIONE: Servizi WFE, Servizio Questionario e Ricezione Foto, Servizio ricezione risposte Asincrone

    Monitoraggio Host -> eesgateway -> Items -> Crea Item:
        Nome: Network status for IP *************
        Tipo: Trapper Zabbix
        Chiave: network.ping[*************]
        Tipo di informazione: Numerico (senza segno)
        Storico: Store up to 31d
        Trend: Store up to 365d
        Abilitato: true
        Descrizione: Url d’invocazione SIF in ambiente PGD: Servizi WFE, Servizio Questionario e Ricezione Foto, Servizio ricezione risposte Asincrone

    Monitoraggio Host -> eesgateway -> Items -> Crea Item:
        Nome: Network status for IP *************
        Tipo: Trapper Zabbix
        Chiave: network.ping[*************]
        Tipo di informazione: Numerico (senza segno)
        Storico: Store up to 31d
        Trend: Store up to 365d
        Abilitato: true
        Descrizione: Url d’invocazione SIF in ambienti PGD e PGD con MUTUA AUTENTICAZIONE: Servizio Autenticazione e di logout

    - TRIGGERS
    Monitoraggio Host -> eesgateway -> Triggers -> Crea Trigger:
        Nome: SIF IP ************ non raggiungibile
        Severità: Alta
        Espressione: min(/eesgateway/network.ping[************],3m)=0
        Generazione eventi OK: Espressione
        Modalità di generazione eventi PROBLEM: Singolo
        Chiudere eventi in OK: Tutti i problemi
        Abilitato: true

    Monitoraggio Host -> eesgateway -> Triggers -> Crea Trigger:
        Nome: SIF IP ************ non raggiungibile
        Severità: Alta
        Espressione: min(/eesgateway/network.ping[************],3m)=0
        Generazione eventi OK: Espressione
        Modalità di generazione eventi PROBLEM: Singolo
        Chiudere eventi in OK: Tutti i problemi
        Abilitato: true

    Monitoraggio Host -> eesgateway -> Triggers -> Crea Trigger:
        Nome: SIF IP ************* non raggiungibile
        Severità: Alta
        Espressione: min(/eesgateway/network.ping[*************],3m)=0
        Generazione eventi OK: Espressione
        Modalità di generazione eventi PROBLEM: Singolo
        Chiudere eventi in OK: Tutti i problemi
        Abilitato: true

    Monitoraggio Host -> eesgateway -> Triggers -> Crea Trigger:
        Nome: SIF IP ************* non raggiungibile
        Severità: Alta
        Espressione: min(/eesgateway/network.ping[*************],3m)=0
        Generazione eventi OK: Espressione
        Modalità di generazione eventi PROBLEM: Singolo
        Chiudere eventi in OK: Tutti i problemi
        Abilitato: true

    Monitoraggio Host -> eesgateway -> Triggers -> Crea Trigger:
        Nome: SIF IP ************* non raggiungibile
        Severità: Alta
        Espressione: min(/eesgateway/network.ping[*************],3m)=0
        Generazione eventi OK: Espressione
        Modalità di generazione eventi PROBLEM: Singolo
        Chiudere eventi in OK: Tutti i problemi
        Abilitato: true
    */
}
