package com.reco.ees.gateway.soap_clients;

import com.reco.ees.gateway.enums.LogScopeEnum;
import com.reco.ees.gateway.enums.ParametersEnum;
import com.reco.ees.gateway.generated.sif.response.RetrieveEESAsyncRequest;
import com.reco.ees.gateway.generated.sif.response.RetrieveEESAsyncResponse;
import com.reco.ees.gateway.generated.sif.response.SifHeaderType;
import com.reco.ees.gateway.service.LogService;
import com.reco.ees.gateway.service.ParameterService;
import com.reco.ees.gateway.util.SoapUtils;
import com.reco.ees.gateway.util.UtilsService;
import jakarta.xml.soap.MessageFactory;
import jakarta.xml.soap.SOAPConstants;
import jakarta.xml.soap.SOAPException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.oxm.jaxb.Jaxb2Marshaller;
import org.springframework.stereotype.Component;
import org.springframework.ws.client.core.support.WebServiceGatewaySupport;
import org.springframework.ws.client.support.interceptor.ClientInterceptor;
import org.springframework.ws.soap.saaj.SaajSoapMessageFactory;
import org.springframework.ws.transport.http.HttpsUrlConnectionMessageSender;

import java.io.IOException;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.UnrecoverableKeyException;
import java.security.cert.CertificateException;

@Slf4j
@Component
public class SoapClientAsync extends WebServiceGatewaySupport {
    private final SoapClientFake soapClientFake;
    private final LogService logService;
    private final SoapUtils soapUtils;
    final
    SoapInterceptorAsync soapInterceptorAsync;
    private final ParameterService parameterService;
    private final UtilsService utilsService;

    @Value("${generated.sif.async.classes:com.reco.ees.gateway.generated.sif.response}")
    private String sifAsyncPackage;
    @Value("${generated.sif.async.soap.version:1.1}")
    private String sifAsyncSoapVersion;
    /*@Value("${generated.sif.wfe-and-survey.classes}")
    private String eesNsPackage;*/
    @Value("${fake.clients.returns:false}")
    private Boolean fakeClientsReturns;

    public SoapClientAsync(SoapClientFake soapClientFake, LogService logService, SoapUtils soapUtils, SoapInterceptorAsync soapInterceptorAsync, ParameterService parameterService, UtilsService utilsService) {
        this.soapClientFake = soapClientFake;
        this.logService = logService;
        this.soapUtils = soapUtils;
        this.soapInterceptorAsync = soapInterceptorAsync;
        this.parameterService = parameterService;
        this.utilsService = utilsService;
    }

    public SoapClientAsync createSoapClientAsync(SifHeaderType sifHeaderType) {
        this.setDefaultUri(parameterService.getParameterById(ParametersEnum.SIF_ASYNC_URI.getParametersEnumValue()).getValue());
        Jaxb2Marshaller jaxb2Marshaller = new Jaxb2Marshaller();
        //jaxb2Marshaller.setContextPath(sifAsyncPackage);
        //jaxb2Marshaller.setClassesToBeBound(RetrieveEESAsyncRequest.class, RetrieveEESAsyncResponse.class, SifHeaderType.class, ObjectFactory.class, HeaderRequestType.class);
        jaxb2Marshaller.setPackagesToScan(sifAsyncPackage); //eesNsPackage
        try {
            jaxb2Marshaller.afterPropertiesSet();
        } catch (Exception e) {
            log.error("Error during marshaller creation of the Async Soap Client: {}", e.getMessage());
        }
        this.setMarshaller(jaxb2Marshaller);
        this.setUnmarshaller(jaxb2Marshaller);

        HttpsUrlConnectionMessageSender httpsSender = null;
        try {
            httpsSender = soapUtils.prepareSecureChannel();
            log.info("HTTPS ready for the Async Soap Client " + utilsService.getCurrentAndCallerMethodInfo());
        } catch (KeyStoreException | IOException | CertificateException | NoSuchAlgorithmException |
                 UnrecoverableKeyException | KeyManagementException e) {
            log.error("Error during secure channel preparation of the Async Soap Client " + utilsService.getCurrentAndCallerMethodInfo());
            throw new RuntimeException(e);
        }
        this.setMessageSender(httpsSender);
        if(sifAsyncSoapVersion.equals("1.1")) {
            try {
                this.getWebServiceTemplate().setMessageFactory(new SaajSoapMessageFactory(MessageFactory.newInstance(SOAPConstants.SOAP_1_1_PROTOCOL)));
            } catch (SOAPException e) {
                throw new RuntimeException(e);
            }
        } else if(sifAsyncSoapVersion.equals("1.2")) {
            try {
                this.getWebServiceTemplate().setMessageFactory(new SaajSoapMessageFactory(MessageFactory.newInstance(SOAPConstants.SOAP_1_2_PROTOCOL)));
            } catch (SOAPException e) {
                throw new RuntimeException(e);
            }
        }
        soapInterceptorAsync.setRequestHeader(sifHeaderType);
        this.getWebServiceTemplate().setInterceptors(new ClientInterceptor[]{soapInterceptorAsync});

        return this;
    }

    public RetrieveEESAsyncResponse retrieveEESAsyncResponse(String idTransaction, SifHeaderType sifHeaderType, String idDossier) {
        if(fakeClientsReturns) return soapClientFake.fakeRetrieveEESAsyncResponse();

        RetrieveEESAsyncRequest request = new RetrieveEESAsyncRequest();
        request.setIdTransaction(idTransaction);

        SoapClientAsync soapClient = createSoapClientAsync(sifHeaderType);

        String xmlRequest = SoapUtils.marshalToXml(request, RetrieveEESAsyncRequest.class, RetrieveEESAsyncResponse.class);
        String transaction = "RetrieveEESAsync performed by the client " + sifHeaderType.getIdChiamante() + " for dossier " + idDossier;
        String logId = logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction, xmlRequest, null);
        try {
            RetrieveEESAsyncResponse response = (RetrieveEESAsyncResponse) soapClient.getWebServiceTemplate().marshalSendAndReceive(request);
            String responseCode = response.getReturnCodes() != null && response.getReturnCodes().getErrorCodes() != null ? response.getReturnCodes().getErrorCodes().toString() : null;
            String responseXml = SoapUtils.marshalToXml(response, RetrieveEESAsyncRequest.class, RetrieveEESAsyncResponse.class);
            logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction, soapUtils.cutResponseXml(responseXml), responseCode, logId);
            return response;
        } catch (Exception e) {
            log.error("Error during RetrieveEESAsyncResponse marshalSendAndReceive: {}", e.getMessage());
            logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction, e.getMessage(), null, logId);
            return null;
        }
    }
}
