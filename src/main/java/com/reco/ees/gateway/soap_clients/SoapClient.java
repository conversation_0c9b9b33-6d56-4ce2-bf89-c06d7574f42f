/*
package com.reco.ees.gateway.soap_clients;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.reco.ees.gateway.enums.ParametersEnum;
import com.reco.ees.gateway.generated.npkd.certificates.*;
import com.reco.ees.gateway.generated.sif.services.GetLoginAuthenticationClient;
import com.reco.ees.gateway.generated.sif.services.GetLoginAuthenticationClientResponse;
import com.reco.ees.gateway.generated.sif.response.RetrieveEESAsyncRequest;
import com.reco.ees.gateway.generated.sif.response.RetrieveEESAsyncResponse;
import com.reco.ees.gateway.repository.model.Dossier;
import com.reco.ees.gateway.repository.model.Kiosk;
import com.reco.ees.gateway.service.DossierService;
import com.reco.ees.gateway.service.LogService;
import com.reco.ees.gateway.service.ParameterService;
import com.reco.ees.gateway.util.UtilsService;
import eu.europa.schengen.ees_ns.xsd.v1.*;
import jakarta.xml.bind.JAXBElement;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Scope;
import org.springframework.core.io.ClassPathResource;
import org.springframework.oxm.jaxb.Jaxb2Marshaller;
import org.springframework.stereotype.Component;
import org.springframework.ws.client.core.support.WebServiceGatewaySupport;
import org.springframework.ws.client.support.interceptor.ClientInterceptor;
import org.springframework.ws.transport.http.HttpsUrlConnectionMessageSender;

import javax.net.ssl.KeyManagerFactory;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManagerFactory;
import javax.xml.namespace.QName;
import java.io.IOException;
import java.io.InputStream;
import java.security.*;
import java.security.cert.CertificateException;
import java.util.List;

@Slf4j
@Component //exTODO: forse l'app sia riavvia da sola quando arriva un messaggio da nodes.passport.data e inizia una StartBorderControl (forse perchè la getLoginAuthenticationClient usa lo stesso marshaller o roba simile?)
@Scope("prototype") //exTODO: a lungo andare questo satura RAM o SpringBoot gestisce bene il suo ciclo di vita? Spring non lo gestisce ma ho manualmente impostato soapClient a null ogni volta che chiamo il createSoapClient (cosicchè il garbage collector java possa eliminarlo quando vuole). Se hai problemi, pensa (se possibile) a usare diverse classi di SoapClient e SoapHeaderInterceptor specifiche per diversi marshaller/headers/etc.
public class SoapClient extends WebServiceGatewaySupport {
    private SoapHeaderInterceptor soapHeaderInterceptor;
    @Autowired
    private SoapClientFake soapClientFake;
    @Autowired
    private DossierService dossierService;
    @Autowired
    private LogService logService;
    @Autowired
    private UtilsService utilsService;
    @Autowired
    private ParameterService parameterService;

    @Value("${generated.npkd.certificates.classes}")
    private String certificatesPackage;
    @Value("${generated.sif.auth.services.classes}")
    private String sifAuthServicesPackage;
    @Value("${generated.sif.wfe-and-survey.classes}")
    private String eesNsPackage;
    @Value("${generated.sif.async.classes}")
    private String sifAsyncPackage;

    @Value("${fake.clients.returns}")
    private Boolean fakeClientsReturns;
    @Value("${print.soap.messages}")
    private Boolean printSoapMessages;
    @Value("${sif.https.certificates.keystore.password}")
    private String sifHttpsCertificatesKeystorePassword;
    @Value("${sif.auth.user}")
    private String sifAuthenticationUser;
    @Value("${sif.auth.password}")
    private String sifAuthenticationPassword;

    public SoapClient createSoapClient(ParametersEnum uriParameter, Object specificHeader) {
        this.setDefaultUri(parameterService.getParameterById(uriParameter.getParametersEnumValue()).getValue());

        Jaxb2Marshaller jaxb2Marshaller = new Jaxb2Marshaller();
        jaxb2Marshaller.setPackagesToScan(certificatesPackage, sifAuthServicesPackage, eesNsPackage, sifAsyncPackage);
        this.setMarshaller(jaxb2Marshaller);
        this.setUnmarshaller(jaxb2Marshaller);

        String callingMethodName = Thread.currentThread().getStackTrace()[2].getMethodName();

        HttpsUrlConnectionMessageSender httpsSender = null;
        try {
            httpsSender = this.prepareSecureChannel();
            log.info("HTTPS ready for the Soap Client " + callingMethodName);
        } catch (KeyStoreException | IOException | CertificateException | NoSuchAlgorithmException |
                 UnrecoverableKeyException | KeyManagementException e) {
            log.error("Error during secure channel preparation of the Soap Client " + callingMethodName);
            throw new RuntimeException(e);
        }
        this.setMessageSender(httpsSender);

        SoapHeaderInterceptor interceptor = new SoapHeaderInterceptor();
        this.soapHeaderInterceptor = interceptor;
        this.getWebServiceTemplate().setInterceptors(new ClientInterceptor[]{interceptor});

        return this;
    }

    private HttpsUrlConnectionMessageSender prepareSecureChannel() throws KeyStoreException, IOException, CertificateException, NoSuchAlgorithmException, UnrecoverableKeyException, KeyManagementException {
        KeyStore ks = KeyStore.getInstance(KeyStore.getDefaultType());
        // KeyStore di esempio creato tramite: "keytool -genkeypair -alias myalias -keyalg RSA -keysize 2048 -validity 3650 -keystore mykeystore.jks -storepass password -keypass password"
        //Prod: Crea un nuovo keystore e importa il certificato root: keytool -import -alias CARootCen -file CARootCen.cer -keystore mykeystore.jks
        //Prod: Importa il certificato intermedio nel keystore esistente: keytool -import -alias IntermedieCACen -file IntermedieCACen.cer -keystore mykeystore.jks
        ClassPathResource resource = new ClassPathResource("mykeystore.jks");
        InputStream fis = resource.getInputStream();
        try {
            ks.load(fis, sifHttpsCertificatesKeystorePassword.toCharArray());
        } catch (IOException e) {
            System.out.println(e.getMessage());
            log.error("Error during keystore loading");
            throw new RuntimeException(e);
        }

        KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm());
        keyManagerFactory.init(ks, sifHttpsCertificatesKeystorePassword.toCharArray());

        TrustManagerFactory trustManagerFactory = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
        trustManagerFactory.init(ks);

        SSLContext sslContext = SSLContext.getInstance("TLS");
        sslContext.init(keyManagerFactory.getKeyManagers(), trustManagerFactory.getTrustManagers(), null);

        HttpsUrlConnectionMessageSender httpsSender = new HttpsUrlConnectionMessageSender();
        SSLSocketFactory socketFactory = sslContext.getSocketFactory();
        httpsSender.setSslSocketFactory(socketFactory);

        return httpsSender;
    }
}

*/
