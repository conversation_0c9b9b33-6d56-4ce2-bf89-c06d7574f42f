package com.reco.ees.gateway.soap_clients;

import com.reco.ees.gateway.enums.LogScopeEnum;
import com.reco.ees.gateway.enums.ParametersEnum;
import com.reco.ees.gateway.generated.sif.iae.DocumentiSDI;
import com.reco.ees.gateway.generated.sif.iae.DocumentiSDIResponse;
import com.reco.ees.gateway.generated.sif.iae.Lu<PERSON>hiSDI;
import com.reco.ees.gateway.generated.sif.iae.LuoghiSDIResponse;
import com.reco.ees.gateway.service.LogService;
import com.reco.ees.gateway.service.ParameterService;
import com.reco.ees.gateway.util.SoapUtils;
import com.reco.ees.gateway.util.UtilsService;
import jakarta.xml.soap.MessageFactory;
import jakarta.xml.soap.SOAPConstants;
import jakarta.xml.soap.SOAPException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.oxm.jaxb.Jaxb2Marshaller;
import org.springframework.stereotype.Component;
import org.springframework.ws.client.core.support.WebServiceGatewaySupport;
import org.springframework.ws.client.support.interceptor.ClientInterceptor;
import org.springframework.ws.soap.saaj.SaajSoapMessageFactory;
import org.springframework.ws.transport.http.HttpsUrlConnectionMessageSender;

import java.io.IOException;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.UnrecoverableKeyException;
import java.security.cert.CertificateException;

@Slf4j
@Component
public class SoapClientSifSupport extends WebServiceGatewaySupport {
    private final SoapUtils soapUtils;
    private final SoapInterceptorSifSupport soapInterceptorSifSupport;
    private final SoapClientFake soapClientFake;
    private final LogService logService;
    private final ParameterService parameterService;
    private final UtilsService utilsService;

    @Value("${generated.sif.support.soap.version:1.2}")
    private String sifSupportSoapVersion;
    @Value("${generated.sif.support.classes:com.reco.ees.gateway.generated.sif.iae}")
    private String sifSupportPackage;
    @Value("${fake.clients.returns:false}")
    private Boolean fakeClientsReturns;

    public SoapClientSifSupport(SoapUtils soapUtils, SoapInterceptorSifSupport soapInterceptorSifSupport, SoapClientFake soapClientFake, LogService logService, ParameterService parameterService, UtilsService utilsService) {
        this.soapUtils = soapUtils;
        this.soapInterceptorSifSupport = soapInterceptorSifSupport;
        this.soapClientFake = soapClientFake;
        this.logService = logService;
        this.parameterService = parameterService;
        this.utilsService = utilsService;
    }

    public SoapClientSifSupport createSifSupportSoapClient() {
        this.setDefaultUri(parameterService.getParameterById(ParametersEnum.SDI_LUOGHI_DOCUMENTI_URI.getParametersEnumValue()).getValue());
        Jaxb2Marshaller jaxb2Marshaller = new Jaxb2Marshaller();
        jaxb2Marshaller.setPackagesToScan(sifSupportPackage);
        try {
            jaxb2Marshaller.afterPropertiesSet();
        } catch (Exception e) {
            log.error("Error during marshaller creation of the SifSupport Soap Client: {}", e.getMessage());
        }
        this.setMarshaller(jaxb2Marshaller);
        this.setUnmarshaller(jaxb2Marshaller);

        HttpsUrlConnectionMessageSender httpsSender = null;
        try {
            httpsSender = soapUtils.prepareSecureChannel();
            log.info("HTTPS ready for the SifSupport Soap Client " + utilsService.getCurrentAndCallerMethodInfo());
        } catch (KeyStoreException | IOException | CertificateException | NoSuchAlgorithmException |
                 UnrecoverableKeyException | KeyManagementException e) {
            log.error("Error during secure channel preparation of the SifSupport Soap Client " + utilsService.getCurrentAndCallerMethodInfo());
            throw new RuntimeException(e);
        }
        this.setMessageSender(httpsSender);
        if(sifSupportSoapVersion.equals("1.1")) {
            try {
                this.getWebServiceTemplate().setMessageFactory(new SaajSoapMessageFactory(MessageFactory.newInstance(SOAPConstants.SOAP_1_1_PROTOCOL)));
            } catch (SOAPException e) {
                throw new RuntimeException(e);
            }
        } else if(sifSupportSoapVersion.equals("1.2")) {
            try {
                this.getWebServiceTemplate().setMessageFactory(new SaajSoapMessageFactory(MessageFactory.newInstance(SOAPConstants.SOAP_1_2_PROTOCOL)));
            } catch (SOAPException e) {
                throw new RuntimeException(e);
            }
        }
        this.getWebServiceTemplate().setInterceptors(new ClientInterceptor[]{soapInterceptorSifSupport});

        return this;
    }

    public DocumentiSDIResponse performGetDocumentiSDI(DocumentiSDI request) {
        return getDocumentiSDI(request);
    }

    //questo può dare errore solo causa DNS o connessione (nel caso si usasse IP invece), quindi I/O Error
    public DocumentiSDIResponse getDocumentiSDI(DocumentiSDI request) {
        if(fakeClientsReturns) return soapClientFake.fakeGetDocumentiSDIResponse();

        SoapClientSifSupport soapClient = createSifSupportSoapClient();

        String xmlRequest = SoapUtils.marshalToXml(request, DocumentiSDI.class, DocumentiSDIResponse.class);
        String transaction = "getDocumentiSDI for actor " + request.getRicDocumenti().getAttore();
        String logId = logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction, xmlRequest, null);
        try {
            DocumentiSDIResponse response = (DocumentiSDIResponse) soapClient.getWebServiceTemplate().marshalSendAndReceive(request);
            String responseCode = response.getDocumentiSDIResult() != null && response.getDocumentiSDIResult().getEsito() != null && response.getDocumentiSDIResult().getEsito().getCodEsito() != null && !response.getDocumentiSDIResult().getEsito().getCodEsito().equalsIgnoreCase("0") ? response.getDocumentiSDIResult().getEsito().getDescEsito() : null;
            String responseXml = SoapUtils.marshalToXml(response, DocumentiSDI.class, DocumentiSDIResponse.class);
            logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction, soapUtils.cutResponseXml(responseXml), responseCode, logId);
            return response;
        } catch (Exception e) {
            log.error("Error during getDocumentiSDI marshalSendAndReceive: {}", e.getMessage());
            logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction, e.getMessage(), null, logId);
            return null;
        }
    }

    public LuoghiSDIResponse performGetLuoghiSDI(LuoghiSDI request) {
        return getLuoghiSDI(request);
    }

    //questo può dare errore solo causa DNS o connessione (nel caso si usasse IP invece), quindi I/O Error
    public LuoghiSDIResponse getLuoghiSDI(LuoghiSDI request) {
        if(fakeClientsReturns) return soapClientFake.fakeGetLuoghiSDIResponse();

        SoapClientSifSupport soapClient = createSifSupportSoapClient();

        String xmlRequest = SoapUtils.marshalToXml(request, LuoghiSDI.class, LuoghiSDIResponse.class);
        String transaction = "getDocumentiSDI for actor " + request.getRicLuoghi().getAttore();
        String logId = logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction, xmlRequest, null);
        try {
            LuoghiSDIResponse response = (LuoghiSDIResponse) soapClient.getWebServiceTemplate().marshalSendAndReceive(request);
            String responseCode = response.getLuoghiSDIResult() != null && response.getLuoghiSDIResult().getEsito() != null && response.getLuoghiSDIResult().getEsito().getCodEsito() != null && !response.getLuoghiSDIResult().getEsito().getCodEsito().equalsIgnoreCase("0") ? response.getLuoghiSDIResult().getEsito().getDescEsito() : null;
            String responseXml = SoapUtils.marshalToXml(response, LuoghiSDI.class, LuoghiSDIResponse.class);
            logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction, soapUtils.cutResponseXml(responseXml), responseCode, logId);
            return response;
        } catch (Exception e) {
            log.error("Error during getLuoghiSDI marshalSendAndReceive: {}", e.getMessage());
            logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction, e.getMessage(), null, logId);
            return null;
        }
    }
}

