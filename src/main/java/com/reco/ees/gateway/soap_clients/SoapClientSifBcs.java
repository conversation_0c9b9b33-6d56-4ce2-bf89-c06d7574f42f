package com.reco.ees.gateway.soap_clients;

import com.reco.ees.gateway.enums.LogScopeEnum;
import com.reco.ees.gateway.enums.ParametersEnum;
import com.reco.ees.gateway.generated.sif.iae.SIFServizioBCS;
import com.reco.ees.gateway.generated.sif.iae.SIFServizioBCSResponse;
import com.reco.ees.gateway.service.LogService;
import com.reco.ees.gateway.service.ParameterService;
import com.reco.ees.gateway.util.SoapUtils;
import com.reco.ees.gateway.util.UtilsService;
import jakarta.xml.soap.MessageFactory;
import jakarta.xml.soap.SOAPConstants;
import jakarta.xml.soap.SOAPException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.oxm.jaxb.Jaxb2Marshaller;
import org.springframework.stereotype.Component;
import org.springframework.ws.client.core.support.WebServiceGatewaySupport;
import org.springframework.ws.client.support.interceptor.ClientInterceptor;
import org.springframework.ws.soap.saaj.SaajSoapMessageFactory;
import org.springframework.ws.transport.http.HttpsUrlConnectionMessageSender;

import java.io.IOException;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.UnrecoverableKeyException;
import java.security.cert.CertificateException;

@Slf4j
@Component
public class SoapClientSifBcs extends WebServiceGatewaySupport {
    private final SoapUtils soapUtils;
    private final SoapInterceptorSifBcs soapInterceptorSifBcs;
    private final SoapClientFake soapClientFake;
    private final LogService logService;
    private final ParameterService parameterService;
    private final UtilsService utilsService;

    @Value("${generated.sif.bcs.soap.version:1.2}")
    private String bcsSoapVersion;
    @Value("${generated.sif.bcs.classes:com.reco.ees.gateway.generated.sif.iae}")
    private String bcsPackage;
    @Value("${fake.clients.returns:false}")
    private Boolean fakeClientsReturns;

    public SoapClientSifBcs(SoapUtils soapUtils, SoapInterceptorSifBcs soapInterceptorSifBcs, SoapClientFake soapClientFake, LogService logService, ParameterService parameterService, UtilsService utilsService) {
        this.soapUtils = soapUtils;
        this.soapInterceptorSifBcs = soapInterceptorSifBcs;
        this.soapClientFake = soapClientFake;
        this.logService = logService;
        this.parameterService = parameterService;
        this.utilsService = utilsService;
    }

    public SoapClientSifBcs createSifBcsSoapClient() {
        this.setDefaultUri(parameterService.getParameterById(ParametersEnum.SDI_BCS_URI.getParametersEnumValue()).getValue());
        Jaxb2Marshaller jaxb2Marshaller = new Jaxb2Marshaller();
        jaxb2Marshaller.setPackagesToScan(bcsPackage);
        try {
            jaxb2Marshaller.afterPropertiesSet();
        } catch (Exception e) {
            log.error("Error during marshaller creation of the SifBcs Soap Client: {}", e.getMessage());
        }
        this.setMarshaller(jaxb2Marshaller);
        this.setUnmarshaller(jaxb2Marshaller);

        HttpsUrlConnectionMessageSender httpsSender = null;
        try {
            httpsSender = soapUtils.prepareSecureChannel();
            log.info("HTTPS ready for the SifBcs Soap Client " + utilsService.getCurrentAndCallerMethodInfo());
        } catch (KeyStoreException | IOException | CertificateException | NoSuchAlgorithmException |
                 UnrecoverableKeyException | KeyManagementException e) {
            log.error("Error during secure channel preparation of the SifBcs Soap Client " + utilsService.getCurrentAndCallerMethodInfo());
            throw new RuntimeException(e);
        }
        this.setMessageSender(httpsSender);
        if(bcsSoapVersion.equals("1.1")) {
            try {
                this.getWebServiceTemplate().setMessageFactory(new SaajSoapMessageFactory(MessageFactory.newInstance(SOAPConstants.SOAP_1_1_PROTOCOL)));
            } catch (SOAPException e) {
                throw new RuntimeException(e);
            }
        } else if(bcsSoapVersion.equals("1.2")) {
            try {
                this.getWebServiceTemplate().setMessageFactory(new SaajSoapMessageFactory(MessageFactory.newInstance(SOAPConstants.SOAP_1_2_PROTOCOL)));
            } catch (SOAPException e) {
                throw new RuntimeException(e);
            }
        }
        this.getWebServiceTemplate().setInterceptors(new ClientInterceptor[]{soapInterceptorSifBcs});

        return this;
    }

    public SIFServizioBCSResponse performBCS(SIFServizioBCS request) {
        return getBcsResponse(request);
    }

    //questo può dare errore solo causa DNS o connessione (nel caso si usasse IP invece), quindi I/O Error
    public SIFServizioBCSResponse getBcsResponse(SIFServizioBCS request) {
        if(fakeClientsReturns) return soapClientFake.fakeGetBcsResponse();

        SoapClientSifBcs soapClient = createSifBcsSoapClient();

        String xmlRequest = SoapUtils.marshalToXml(request, SIFServizioBCS.class, SIFServizioBCSResponse.class);
        String transaction = "getBcsResponse for transactionId " + request.getRic().getHeader().getIdTransazione();
        String logId = logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction, xmlRequest, null);
        try {
            SIFServizioBCSResponse response = (SIFServizioBCSResponse) soapClient.getWebServiceTemplate().marshalSendAndReceive(request);
            String responseCode = response.getSIFServizioBCSResult() != null && response.getSIFServizioBCSResult().getEsito() != 0 ? response.getSIFServizioBCSResult().getDescrizione() : null;
            String responseXml = SoapUtils.marshalToXml(response, SIFServizioBCS.class, SIFServizioBCSResponse.class);
            logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction, soapUtils.cutResponseXml(responseXml), responseCode, logId);
            return response;
        } catch (Exception e) {
            log.error("Error during getBcsResponse marshalSendAndReceive: {}", e.getMessage());
            logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction, e.getMessage(), null, logId);
            return null;
        }
    }
}

