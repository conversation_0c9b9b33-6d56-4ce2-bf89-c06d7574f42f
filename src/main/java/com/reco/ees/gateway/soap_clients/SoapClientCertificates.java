package com.reco.ees.gateway.soap_clients;

import com.reco.ees.gateway.enums.LogScopeEnum;
import com.reco.ees.gateway.enums.ParametersEnum;
import com.reco.ees.gateway.generated.npkd.certificates.*;
import com.reco.ees.gateway.service.LogService;
import com.reco.ees.gateway.service.ParameterService;
import com.reco.ees.gateway.util.SoapUtils;
import com.reco.ees.gateway.util.UtilsService;
import jakarta.xml.soap.MessageFactory;
import jakarta.xml.soap.SOAPConstants;
import jakarta.xml.soap.SOAPException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.oxm.jaxb.Jaxb2Marshaller;
import org.springframework.stereotype.Component;
import org.springframework.ws.client.core.support.WebServiceGatewaySupport;
import org.springframework.ws.client.support.interceptor.ClientInterceptor;
import org.springframework.ws.soap.saaj.SaajSoapMessageFactory;
import org.springframework.ws.transport.http.HttpsUrlConnectionMessageSender;

import java.io.IOException;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.UnrecoverableKeyException;
import java.security.cert.CertificateException;

@Slf4j
@Component
public class SoapClientCertificates extends WebServiceGatewaySupport {
    private final SoapUtils soapUtils;
    private final SoapInterceptorCertificates soapInterceptorCertificates;
    private final SoapClientFake soapClientFake;
    private final LogService logService;
    private final ParameterService parameterService;
    private final UtilsService utilsService;

    @Value("${generated.npkd.certificates.soap.version:1.2}")
    private String certificatesSoapVersion;
    @Value("${generated.npkd.certificates.classes:com.reco.ees.gateway.generated.npkd.certificates}")
    private String certificatesPackage;
    @Value("${fake.clients.returns:false}")
    private Boolean fakeClientsReturns;

    public SoapClientCertificates(SoapUtils soapUtils, SoapInterceptorCertificates soapInterceptorCertificates, SoapClientFake soapClientFake, LogService logService, ParameterService parameterService, UtilsService utilsService) {
        this.soapUtils = soapUtils;
        this.soapInterceptorCertificates = soapInterceptorCertificates;
        this.soapClientFake = soapClientFake;
        this.logService = logService;
        this.parameterService = parameterService;
        this.utilsService = utilsService;
    }

    public SoapClientCertificates createCertificatesSoapClient() {
        this.setDefaultUri(parameterService.getParameterById(ParametersEnum.CERTIFICATE_DOWNLOAD_URI.getParametersEnumValue()).getValue());
        Jaxb2Marshaller jaxb2Marshaller = new Jaxb2Marshaller();
        jaxb2Marshaller.setPackagesToScan(certificatesPackage);
        try {
            jaxb2Marshaller.afterPropertiesSet();
        } catch (Exception e) {
            log.error("Error during marshaller creation of the Certificates Soap Client: {}", e.getMessage());
        }
        this.setMarshaller(jaxb2Marshaller);
        this.setUnmarshaller(jaxb2Marshaller);

        HttpsUrlConnectionMessageSender httpsSender = null;
        try {
            httpsSender = soapUtils.prepareSecureChannel();
            log.info("HTTPS ready for the Certificates Soap Client " + utilsService.getCurrentAndCallerMethodInfo());
        } catch (KeyStoreException | IOException | CertificateException | NoSuchAlgorithmException |
                 UnrecoverableKeyException | KeyManagementException e) {
            log.error("Error during secure channel preparation of the Certificates Soap Client " + utilsService.getCurrentAndCallerMethodInfo());
            throw new RuntimeException(e);
        }
        this.setMessageSender(httpsSender);
        if(certificatesSoapVersion.equals("1.1")) {
            try {
                this.getWebServiceTemplate().setMessageFactory(new SaajSoapMessageFactory(MessageFactory.newInstance(SOAPConstants.SOAP_1_1_PROTOCOL)));
            } catch (SOAPException e) {
                throw new RuntimeException(e);
            }
        } else if(certificatesSoapVersion.equals("1.2")) {
            try {
                this.getWebServiceTemplate().setMessageFactory(new SaajSoapMessageFactory(MessageFactory.newInstance(SOAPConstants.SOAP_1_2_PROTOCOL)));
            } catch (SOAPException e) {
                throw new RuntimeException(e);
            }
        }
        this.getWebServiceTemplate().setInterceptors(new ClientInterceptor[]{soapInterceptorCertificates});

        return this;
    }

    //questo può dare errore solo causa DNS o connessione (nel caso si usasse IP invece), quindi I/O Error
    public GetDocSignerAndRevocationListResponse getDocSignerAndRevocationListResponse() {
        if(fakeClientsReturns) return soapClientFake.fakeGetDocSignerAndRevocationListResponse();

        SoapClientCertificates soapClient = createCertificatesSoapClient();
        GetDocSignerAndRevocationList request = new GetDocSignerAndRevocationList();
        String attore = parameterService.getParameterById(ParametersEnum.CERTIFICATE_DOWNLOAD_ACTOR.getParametersEnumValue()).getValue();
        if(attore != null) request.setAttore(attore); else log.error("getDocSignerAndRevocationListResponse() request: attore non impostato");
        //request.setBlocco();

        String xmlRequest = SoapUtils.marshalToXml(request, GetDocSignerAndRevocationList.class, GetDocSignerAndRevocationListResponse.class);
        String transaction = "GetDocSignerAndRevocationList for actor " + attore;
        String logId = logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction, xmlRequest, null);
        try {
            GetDocSignerAndRevocationListResponse response = (GetDocSignerAndRevocationListResponse) soapClient.getWebServiceTemplate().marshalSendAndReceive(request);
            String responseCode = response.getGetDocSignerAndRevocationListResult() != null && response.getGetDocSignerAndRevocationListResult().getCodError() != null ? response.getGetDocSignerAndRevocationListResult().getCodError() : null;
            String responseXml = SoapUtils.marshalToXml(response, GetDocSignerAndRevocationList.class, GetDocSignerAndRevocationListResponse.class);
            logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction, soapUtils.cutResponseXml(responseXml), responseCode, logId);
            return response;
        } catch (Exception e) {
            log.error("Error during GetDocSignerAndRevocationList marshalSendAndReceive: {}", e.getMessage());
            logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction, e.getMessage(), null, logId);
            return null;
        }
    }

    //questo può dare errore solo causa DNS o connessione (nel caso si usasse IP invece), quindi I/O Error
    public GetMasterListCSCAResponse getMasterListCSCAResponse() {
        if(fakeClientsReturns) return soapClientFake.fakeGetMasterListCSCAResponse();

        SoapClientCertificates soapClient = createCertificatesSoapClient();
        GetMasterListCSCA request = new GetMasterListCSCA();
        String attore = parameterService.getParameterById(ParametersEnum.CERTIFICATE_DOWNLOAD_ACTOR.getParametersEnumValue()).getValue();
        if(attore != null) request.setAttore(attore); else log.error("getMasterListCSCAResponse() request: attore non impostato");

        String xmlRequest = SoapUtils.marshalToXml(request, GetMasterListCSCA.class, GetMasterListCSCAResponse.class);
        String transaction = "GetMasterListCSCA for actor " + attore;
        String logId = logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction, xmlRequest, null);
        try {
            GetMasterListCSCAResponse response = (GetMasterListCSCAResponse) soapClient.getWebServiceTemplate().marshalSendAndReceive(request);
            String responseCode = response.getGetMasterListCSCAResult() != null && response.getGetMasterListCSCAResult().getCodError() != null ? response.getGetMasterListCSCAResult().getCodError() : null;
            String responseXml = SoapUtils.marshalToXml(response, GetMasterListCSCA.class, GetMasterListCSCAResponse.class);
            logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction, soapUtils.cutResponseXml(responseXml), responseCode, logId);
            return response;
        } catch (Exception e) {
            log.error("Error during GetMasterListCSCAResponse marshalSendAndReceive: {}", e.getMessage());
            logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction, e.getMessage(), null, logId);
            return null;
        }
    }

    //questo può dare errore solo causa DNS o connessione (nel caso si usasse IP invece), quindi I/O Error
    public GetMasterListaCVCAResponse getMasterListCVCAResponse() {
        if(fakeClientsReturns) return soapClientFake.fakeGetMasterListCVCAResponse();

        SoapClientCertificates soapClient = createCertificatesSoapClient();
        GetMasterListaCVCA request = new GetMasterListaCVCA();
        String attore = parameterService.getParameterById(ParametersEnum.CERTIFICATE_DOWNLOAD_ACTOR.getParametersEnumValue()).getValue();
        if(attore != null) request.setAttore(attore); else log.error("getMasterListCVCAResponse() request: attore non impostato");

        String xmlRequest = SoapUtils.marshalToXml(request, GetMasterListaCVCA.class, GetMasterListaCVCAResponse.class);
        String transaction = "GetMasterListCVCA for actor " + attore;
        String logId = logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction, xmlRequest, null);
        try {
            GetMasterListaCVCAResponse response = (GetMasterListaCVCAResponse) soapClient.getWebServiceTemplate().marshalSendAndReceive(request);
            String responseCode = response.getGetMasterListaCVCAResult() != null && response.getGetMasterListaCVCAResult().getCodError() != null ? response.getGetMasterListaCVCAResult().getCodError() : null;
            String responseXml = SoapUtils.marshalToXml(response, GetMasterListaCVCA.class, GetMasterListaCVCAResponse.class);
            logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction, soapUtils.cutResponseXml(responseXml), responseCode, logId);
            return response;
        } catch (Exception e) {
            log.error("Error during GetMasterListCVCAResponse marshalSendAndReceive: {}", e.getMessage());
            logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction, e.getMessage(), null, logId);
            return null;
        }
    }

    //questo può dare errore solo causa DNS o connessione (nel caso si usasse IP invece), quindi I/O Error
    public GetISCertResponse getISCertResponse() {
        if(fakeClientsReturns) return soapClientFake.fakeGetISCertResponse();

        SoapClientCertificates soapClient = createCertificatesSoapClient();
        GetISCert request = new GetISCert();
        String attore = parameterService.getParameterById(ParametersEnum.CERTIFICATE_DOWNLOAD_ACTOR.getParametersEnumValue()).getValue();
        if(attore != null) request.setAttore(attore); else log.error("getISCertResponse() request: attore non impostato");

        String xmlRequest = SoapUtils.marshalToXml(request, GetISCert.class, GetISCertResponse.class);
        String transaction = "GetISCert for actor " + attore;
        String logId = logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction, xmlRequest, null);
        try {
            GetISCertResponse response = (GetISCertResponse) soapClient.getWebServiceTemplate().marshalSendAndReceive(request);
            String responseCode = response.getGetISCertResult() != null && response.getGetISCertResult().getCodError() != null ? response.getGetISCertResult().getCodError() : null;
            String responseXml = SoapUtils.marshalToXml(response, GetISCert.class, GetISCertResponse.class);
            logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction, soapUtils.cutResponseXml(responseXml), responseCode, logId);
            return response;
        } catch (Exception e) {
            log.error("Error during GetISCertResponse marshalSendAndReceive: {}", e.getMessage());
            logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction, e.getMessage(), null, logId);
            return null;
        }
    }

    //questo può dare errore solo causa DNS o connessione (nel caso si usasse IP invece), quindi I/O Error
    public GetDVAndRevocationListResponse getDVAndRevocationListResponse() {
        if(fakeClientsReturns) return soapClientFake.fakeGetDVAndRevocationListResponse();

        SoapClientCertificates soapClient = createCertificatesSoapClient();
        GetDVAndRevocationList request = new GetDVAndRevocationList();
        String attore = parameterService.getParameterById(ParametersEnum.CERTIFICATE_DOWNLOAD_ACTOR.getParametersEnumValue()).getValue();
        if(attore != null) request.setAttore(attore); else log.error("getDVAndRevocationListResponse() request: attore non impostato");

        String xmlRequest = SoapUtils.marshalToXml(request, GetDVAndRevocationList.class, GetDVAndRevocationListResponse.class);
        String transaction = "GetDVAndRevocationList for actor " + attore;
        String logId = logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction, xmlRequest, null);
        try {
            GetDVAndRevocationListResponse response = (GetDVAndRevocationListResponse) soapClient.getWebServiceTemplate().marshalSendAndReceive(request);
            String responseCode = response.getGetDVAndRevocationListResult() != null && response.getGetDVAndRevocationListResult().getCodError() != null ? response.getGetDVAndRevocationListResult().getCodError() : null;
            String responseXml = SoapUtils.marshalToXml(response, GetDVAndRevocationList.class, GetDVAndRevocationListResponse.class);
            logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction, soapUtils.cutResponseXml(responseXml), responseCode, logId);
            return response;
        } catch (Exception e) {
            log.error("Error during GetDVAndRevocationListResponse marshalSendAndReceive: {}", e.getMessage());
            logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction, e.getMessage(), null, logId);
            return null;
        }
    }
}

