package com.reco.ees.gateway.soap_clients;

import com.reco.ees.gateway.enums.LogScopeEnum;
import com.reco.ees.gateway.enums.ParametersEnum;
import com.reco.ees.gateway.enums.SifParametersEnum;
import com.reco.ees.gateway.generated.sif.services.GetLoginAuthenticationClient;
import com.reco.ees.gateway.generated.sif.services.GetLoginAuthenticationClientResponse;
import com.reco.ees.gateway.repository.AttributeEncryptor;
import com.reco.ees.gateway.repository.SifParametersRepository;
import com.reco.ees.gateway.repository.model.Kiosk;
import com.reco.ees.gateway.service.LogService;
import com.reco.ees.gateway.service.ParameterService;
import com.reco.ees.gateway.util.SoapUtils;
import com.reco.ees.gateway.util.UtilsService;
import jakarta.xml.soap.MessageFactory;
import jakarta.xml.soap.SOAPConstants;
import jakarta.xml.soap.SOAPException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.oxm.jaxb.Jaxb2Marshaller;
import org.springframework.stereotype.Component;
import org.springframework.ws.client.core.support.WebServiceGatewaySupport;
import org.springframework.ws.client.support.interceptor.ClientInterceptor;
import org.springframework.ws.soap.saaj.SaajSoapMessageFactory;
import org.springframework.ws.transport.http.HttpsUrlConnectionMessageSender;

import java.io.IOException;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.UnrecoverableKeyException;
import java.security.cert.CertificateException;
import java.util.List;

@Slf4j
@Component
public class SoapClientSifAuth extends WebServiceGatewaySupport {
    private final AttributeEncryptor attributeEncryptor;
    private final LogService logService;
    private final SoapUtils soapUtils;
    final SoapInterceptorSifAuth soapInterceptorSifAuth;
    private final SoapClientFake soapClientFake;
    private final ParameterService parameterService;
    private final SifParametersRepository sifParametersRepository;
    private final UtilsService utilsService;

    @Value("${generated.sif.auth.services.soap.version:1.2}")
    private String sifAuthSoapVersion;
    @Value("${generated.sif.auth.services.classes:com.reco.ees.gateway.generated.sif.services}")
    private String sifAuthServicesPackage;
    @Value("${fake.clients.returns:false}")
    private Boolean fakeClientsReturns;

    public SoapClientSifAuth(AttributeEncryptor attributeEncryptor, LogService logService, SoapUtils soapUtils, SoapInterceptorSifAuth soapInterceptorSifAuth, SoapClientFake soapClientFake, ParameterService parameterService, SifParametersRepository sifParametersRepository, UtilsService utilsService) {
        this.attributeEncryptor = attributeEncryptor;
        this.logService = logService;
        this.soapUtils = soapUtils;
        this.soapInterceptorSifAuth = soapInterceptorSifAuth;
        this.soapClientFake = soapClientFake;
        this.parameterService = parameterService;
        this.sifParametersRepository = sifParametersRepository;
        this.utilsService = utilsService;
    }

    public SoapClientSifAuth createSoapClientSifAuth() {
        this.setDefaultUri(parameterService.getParameterById(ParametersEnum.SIF_AUTHENTICATION_URI.getParametersEnumValue()).getValue());
        Jaxb2Marshaller jaxb2Marshaller = new Jaxb2Marshaller();
        jaxb2Marshaller.setPackagesToScan(sifAuthServicesPackage);
        try {
            jaxb2Marshaller.afterPropertiesSet();
        } catch (Exception e) {
            log.error("Error during marshaller creation of the SifAuth Soap Client: {}", e.getMessage());
        }
        this.setMarshaller(jaxb2Marshaller);
        this.setUnmarshaller(jaxb2Marshaller);

        HttpsUrlConnectionMessageSender httpsSender = null;
        try {
            httpsSender = soapUtils.prepareSecureChannel();
            log.info("HTTPS ready for the Sif Authentication Soap Client " + utilsService.getCurrentAndCallerMethodInfo());
        } catch (KeyStoreException | IOException | CertificateException | NoSuchAlgorithmException |
                 UnrecoverableKeyException | KeyManagementException e) {
            log.error("Error during secure channel preparation of the Sif Authentication Soap Client " + utilsService.getCurrentAndCallerMethodInfo());
            throw new RuntimeException(e);
        }
        this.setMessageSender(httpsSender);
        if(sifAuthSoapVersion.equals("1.1")) {
            try {
                this.getWebServiceTemplate().setMessageFactory(new SaajSoapMessageFactory(MessageFactory.newInstance(SOAPConstants.SOAP_1_1_PROTOCOL)));
            } catch (SOAPException e) {
                throw new RuntimeException(e);
            }
        } else if(sifAuthSoapVersion.equals("1.2")) {
            try {
                this.getWebServiceTemplate().setMessageFactory(new SaajSoapMessageFactory(MessageFactory.newInstance(SOAPConstants.SOAP_1_2_PROTOCOL)));
            } catch (SOAPException e) {
                throw new RuntimeException(e);
            }
        }
        this.getWebServiceTemplate().setInterceptors(new ClientInterceptor[]{soapInterceptorSifAuth});

        return this;
    }

    //questo può dare errore solo causa DNS o connessione (nel caso si usasse IP invece), quindi I/O Error
    public GetLoginAuthenticationClientResponse getLoginAuthenticationClientResponse(List<Kiosk> kiosks, boolean isEgate) {
        if(fakeClientsReturns) return soapClientFake.fakeGetLoginAuthenticationClientResponse(kiosks);

        SoapClientSifAuth soapClient = createSoapClientSifAuth();
        GetLoginAuthenticationClient request = new GetLoginAuthenticationClient();
        request.setAccountName(isEgate ? parameterService.getParameterById(ParametersEnum.SIF_AUTHENTICATION_USER_EGATE.getParametersEnumValue()).getValue() : parameterService.getParameterById(ParametersEnum.SIF_AUTHENTICATION_USER_KIOSK.getParametersEnumValue()).getValue());
        request.setPassword(attributeEncryptor.convertToEntityAttribute(isEgate ? parameterService.getParameterById(ParametersEnum.SIF_AUTHENTICATION_PASSWORD_EGATE.getParametersEnumValue()).getValue() : parameterService.getParameterById(ParametersEnum.SIF_AUTHENTICATION_PASSWORD_KIOSK.getParametersEnumValue()).getValue())); //utilsService.generatePasswordMD5(sifAuthenticationPassword)
        kiosks.forEach(kiosk -> request.getIdMachine().add(kiosk.getIdMachine()));
        request.setApplication(sifParametersRepository.findByType(SifParametersEnum.SYSTEM_ID.toString()));

        String xmlRequest = SoapUtils.marshalToXml(request, GetLoginAuthenticationClient.class, GetLoginAuthenticationClientResponse.class);
        String transaction = "GetLoginAuthenticationClient for clients " + request.getIdMachine();
        String logId = logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction, xmlRequest, null);
        try {
            GetLoginAuthenticationClientResponse response = (GetLoginAuthenticationClientResponse) soapClient.getWebServiceTemplate().marshalSendAndReceive(request);
            String responseCode = String.valueOf(response.getGetLoginAuthenticationClientResult() != null ? response.getGetLoginAuthenticationClientResult().isIsAuthenticated() : null);
            String responseXml = SoapUtils.marshalToXml(response, GetLoginAuthenticationClient.class, GetLoginAuthenticationClientResponse.class);
            logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction, soapUtils.cutResponseXml(responseXml), responseCode, logId);
            return response;
        } catch (Exception e) {
            log.error("Error during GetLoginAuthenticationClientResponse marshalSendAndReceive: {}", e.getMessage());
            logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction, e.getMessage(), null, logId);
            return null;
        }
    }
}
