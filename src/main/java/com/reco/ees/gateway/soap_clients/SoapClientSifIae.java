package com.reco.ees.gateway.soap_clients;

import com.reco.ees.gateway.enums.LogScopeEnum;
import com.reco.ees.gateway.enums.ParametersEnum;
import com.reco.ees.gateway.generated.sif.iae.*;
import com.reco.ees.gateway.service.LogService;
import com.reco.ees.gateway.service.ParameterService;
import com.reco.ees.gateway.util.SoapUtils;
import com.reco.ees.gateway.util.UtilsService;
import jakarta.xml.soap.MessageFactory;
import jakarta.xml.soap.SOAPConstants;
import jakarta.xml.soap.SOAPException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.oxm.jaxb.Jaxb2Marshaller;
import org.springframework.stereotype.Component;
import org.springframework.ws.client.core.support.WebServiceGatewaySupport;
import org.springframework.ws.client.support.interceptor.ClientInterceptor;
import org.springframework.ws.soap.saaj.SaajSoapMessageFactory;
import org.springframework.ws.transport.http.HttpsUrlConnectionMessageSender;

import java.io.IOException;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.UnrecoverableKeyException;
import java.security.cert.CertificateException;

@Slf4j
@Component
public class SoapClientSifIae extends WebServiceGatewaySupport {
    private final SoapUtils soapUtils;
    private final SoapInterceptorSifIae soapInterceptorSifIae;
    private final SoapClientFake soapClientFake;
    private final LogService logService;
    private final ParameterService parameterService;
    private final UtilsService utilsService;

    @Value("${generated.sif.iae.soap.version:1.2}")
    private String iaeSoapVersion;
    @Value("${generated.sif.iae.classes:com.reco.ees.gateway.generated.sif.iae}")
    private String iaePackage;
    @Value("${fake.clients.returns:false}")
    private Boolean fakeClientsReturns;

    public SoapClientSifIae(SoapUtils soapUtils, SoapInterceptorSifIae soapInterceptorSifIae, SoapClientFake soapClientFake, LogService logService, ParameterService parameterService, UtilsService utilsService) {
        this.soapUtils = soapUtils;
        this.soapInterceptorSifIae = soapInterceptorSifIae;
        this.soapClientFake = soapClientFake;
        this.logService = logService;
        this.parameterService = parameterService;
        this.utilsService = utilsService;
    }

    public SoapClientSifIae createSifIaeSoapClient(ParametersEnum parametersEnum) {
        this.setDefaultUri(parameterService.getParameterById(parametersEnum.getParametersEnumValue()).getValue());
        Jaxb2Marshaller jaxb2Marshaller = new Jaxb2Marshaller();
        jaxb2Marshaller.setPackagesToScan(iaePackage);
        try {
            jaxb2Marshaller.afterPropertiesSet();
        } catch (Exception e) {
            log.error("Error during marshaller creation of the SifIae Soap Client: {}", e.getMessage());
        }
        this.setMarshaller(jaxb2Marshaller);
        this.setUnmarshaller(jaxb2Marshaller);

        HttpsUrlConnectionMessageSender httpsSender = null;
        try {
            httpsSender = soapUtils.prepareSecureChannel();
            log.info("HTTPS ready for the SifIae Soap Client " + utilsService.getCurrentAndCallerMethodInfo());
        } catch (KeyStoreException | IOException | CertificateException | NoSuchAlgorithmException |
                 UnrecoverableKeyException | KeyManagementException e) {
            log.error("Error during secure channel preparation of the SifIae Soap Client " + utilsService.getCurrentAndCallerMethodInfo());
            throw new RuntimeException(e);
        }
        this.setMessageSender(httpsSender);
        if(iaeSoapVersion.equals("1.1")) {
            try {
                this.getWebServiceTemplate().setMessageFactory(new SaajSoapMessageFactory(MessageFactory.newInstance(SOAPConstants.SOAP_1_1_PROTOCOL)));
            } catch (SOAPException e) {
                throw new RuntimeException(e);
            }
        } else if(iaeSoapVersion.equals("1.2")) {
            try {
                this.getWebServiceTemplate().setMessageFactory(new SaajSoapMessageFactory(MessageFactory.newInstance(SOAPConstants.SOAP_1_2_PROTOCOL)));
            } catch (SOAPException e) {
                throw new RuntimeException(e);
            }
        }
        this.getWebServiceTemplate().setInterceptors(new ClientInterceptor[]{soapInterceptorSifIae});

        return this;
    }

    public SIFServizioS016Response performS016(SIFServizioS016 request) {
        return getS016Response(request);
    }

    //questo può dare errore solo causa DNS o connessione (nel caso si usasse IP invece), quindi I/O Error
    public SIFServizioS016Response getS016Response(SIFServizioS016 request) {
        if(fakeClientsReturns) return soapClientFake.fakeGetS016Response();

        SoapClientSifIae soapClient = createSifIaeSoapClient(ParametersEnum.SDI_S016_URI);

        String xmlRequest = SoapUtils.marshalToXml(request, SIFServizioS016.class, SIFServizioS016Response.class);
        String transaction = "getS016Response for user " + request.getRic().getUserName();
        String logId = logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction, xmlRequest, null);
        try {
            SIFServizioS016Response response = (SIFServizioS016Response) soapClient.getWebServiceTemplate().marshalSendAndReceive(request);
            String responseCode = response.getSIFServizioS016Result() != null && response.getSIFServizioS016Result().getEsito() != 0 ? response.getSIFServizioS016Result().getMessaggi().toString() : null;
            String responseXml = SoapUtils.marshalToXml(response, SIFServizioS016.class, SIFServizioS016Response.class);
            logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction, soapUtils.cutResponseXml(responseXml), responseCode, logId);
            return response;
        } catch (Exception e) {
            log.error("Error during getS016Response marshalSendAndReceive: {}", e.getMessage());
            logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction, e.getMessage(), null, logId);
            return null;
        }
    }

    public SIFServizioS004Response performS004(SIFServizioS004 request) {
        return getS004Response(request);
    }

    //questo può dare errore solo causa DNS o connessione (nel caso si usasse IP invece), quindi I/O Error
    public SIFServizioS004Response getS004Response(SIFServizioS004 request) {
        if(fakeClientsReturns) return soapClientFake.fakeGetS004Response();

        SoapClientSifIae soapClient = createSifIaeSoapClient(ParametersEnum.SDI_S004_URI);

        String xmlRequest = SoapUtils.marshalToXml(request, SIFServizioS004.class, SIFServizioS004Response.class);
        String transaction = "getS004Response for user " + request.getRic().getUserName();
        String logId = logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction, xmlRequest, null);
        try {
            SIFServizioS004Response response = (SIFServizioS004Response) soapClient.getWebServiceTemplate().marshalSendAndReceive(request);
            String responseCode = response.getSIFServizioS004Result() != null && response.getSIFServizioS004Result().getEsito() != 0 ? response.getSIFServizioS004Result().getMessaggi().toString() : null;
            String responseXml = SoapUtils.marshalToXml(response, SIFServizioS004.class, SIFServizioS004Response.class);
            logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction, soapUtils.cutResponseXml(responseXml), responseCode, logId);
            return response;
        } catch (Exception e) {
            log.error("Error during getS004Response marshalSendAndReceive: {}", e.getMessage());
            logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction, e.getMessage(), null, logId);
            return null;
        }
    }

    public SIFServizioS000Response performS000(SIFServizioS000 request) {
        return getS000Response(request);
    }

    //questo può dare errore solo causa DNS o connessione (nel caso si usasse IP invece), quindi I/O Error
    public SIFServizioS000Response getS000Response(SIFServizioS000 request) {
        if(fakeClientsReturns) return soapClientFake.fakeGetS000Response();

        SoapClientSifIae soapClient = createSifIaeSoapClient(ParametersEnum.SDI_S000_URI);

        String xmlRequest = SoapUtils.marshalToXml(request, SIFServizioS000.class, SIFServizioS000Response.class);
        String transaction = "GetS000Response for user with id " + request.getRic().getUserId();
        String logId = logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction, xmlRequest, null);
        try {
            SIFServizioS000Response response = (SIFServizioS000Response) soapClient.getWebServiceTemplate().marshalSendAndReceive(request);
            String responseCode = response.getSIFServizioS000Result() != null && response.getSIFServizioS000Result().getEsito() != 0 ? String.valueOf(response.getSIFServizioS000Result().getCodMsg()) : null;
            String responseXml = SoapUtils.marshalToXml(response, SIFServizioS000.class, SIFServizioS000Response.class);
            logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction, soapUtils.cutResponseXml(responseXml), responseCode, logId);
            return response;
        } catch (Exception e) {
            log.error("Error during getS000Response marshalSendAndReceive: {}", e.getMessage());
            logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction, e.getMessage(), null, logId);
            return null;
        }
    }
}

