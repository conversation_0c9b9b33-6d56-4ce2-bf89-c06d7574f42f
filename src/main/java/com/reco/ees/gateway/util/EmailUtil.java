package com.reco.ees.gateway.util;

import com.reco.ees.gateway.enums.EmailTypeEnum;
import com.reco.ees.gateway.enums.ParametersEnum;
import com.reco.ees.gateway.repository.model.EmailBean;
import com.reco.ees.gateway.repository.model.Parameter;
import com.reco.ees.gateway.service.ParameterService;
import jakarta.mail.Message;
import jakarta.mail.internet.InternetAddress;
import jakarta.mail.internet.MimeMessage;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.MailException;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;

import java.util.Properties;

@Slf4j
@AllArgsConstructor
@Component
public class EmailUtil {

    private final ParameterService parameterService;

    @Value("${email.airport.name}")
    private String airportName;

    private boolean sendMessageWithAttachment(EmailBean emailBean) {
        try {
            log.debug("EmailUtil - sendMessageWithAttachment() -  PARAMS {}", emailBean);
            JavaMailSenderImpl mailSender = new JavaMailSenderImpl();
            mailSender.setHost(emailBean.getHost());
            mailSender.setPort(emailBean.getPort());
            Properties props = mailSender.getJavaMailProperties();
            props.put("mail.transport.protocol", "smtp");
            MimeMessage message = mailSender.createMimeMessage();
            if(emailBean.getAuthentication()){
                props.put("mail.smtp.auth", "true");
                mailSender.setUsername(emailBean.getUser_authentication());
                mailSender.setPassword(emailBean.getPassword_authentication());
                message.setFrom(emailBean.getFrom());
            }else{
                props.put("mail.smtp.auth", "false");
                message.setFrom(emailBean.getFrom());
            }

            props.put("mail.debug", "true");
            props.put("mail.smtp.starttls.enable", "true");

            message.setSubject(emailBean.getSubject());
            message.setRecipients(Message.RecipientType.TO, InternetAddress.parse(StringUtils.deleteWhitespace(emailBean.getTo()), false));
            if(emailBean.getCc() != null && !emailBean.getCc().isEmpty()) message.setRecipients(Message.RecipientType.CC,  InternetAddress.parse(StringUtils.deleteWhitespace(emailBean.getCc()), false));

            if(emailBean.getType() != null){
                switch (emailBean.getType()){
                    /*case FSConstant.EMAIL_NODE_ERROR_LOG_TYPE:
                        MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");
                        helper.setText(emailBean.getText());

                        if(emailBean.getPathToAttachment() != null && !emailBean.getPathToAttachment().isEmpty()){
                            for(EmailAttachmentBean emailAttachmentBean: emailBean.getPathToAttachment()){
                                ByteArrayResource csvFile = emailAttachmentBean.getFile();
                                helper.addAttachment(emailAttachmentBean.getName(), csvFile);
                            }
                        }
                        try {
                            mailSender.send(message);
                            statusSend =  FSConstant.STATUS_OK;
                        }catch (MailException me){
                            log.error("EmailUtil - sendMessageWithAttachment() - Error: {}", me.getMessage());
                            statusSend = FSConstant.STATUS_EMAIL_NOT_VALID;
                        }
                        break;*/
                    case EmailTypeEnum.SIF_CERTIFICATES_EMAIL:
                    case EmailTypeEnum.SIF_PASSWORD_EXPIRATION:
                        try {
                            MimeMessageHelper helper2 = new MimeMessageHelper(message, true, "UTF-8");
                            helper2.setText(emailBean.getText());
                            mailSender.send(message);
                            return true;
                        }catch (MailException me){
                            log.error("EmailUtil - sendMessageWithAttachment() - Error: {}", me.getMessage());
                            return false;
                        }
                    default:
                        return true;
                }
            }else
                return false;
        } catch (Exception e) {
            log.error("EmailUtil - sendMessageWithAttachment() - Error: {}", e.getMessage());
            return false;
        }
    }

    private EmailBean configureEmailObject(){
        try {
            log.debug("EmailUtil - configureEmailObject()");
            Parameter senderParameter = parameterService.getParameterById(ParametersEnum.EMAIL_SENDER.getParametersEnumValue());
            Parameter hostParameter = parameterService.getParameterById(ParametersEnum.EMAIL_HOST.getParametersEnumValue());
            Parameter portParameter = parameterService.getParameterById(ParametersEnum.EMAIL_PORT.getParametersEnumValue());
            Parameter smtpAuthParameter = parameterService.getParameterById(ParametersEnum.EMAIL_SMTP_AUTHENTICATION.getParametersEnumValue());

            EmailBean emailBean = new EmailBean();
            emailBean.setFrom(senderParameter.getValue());
            emailBean.setHost(hostParameter.getValue());
            emailBean.setPort(Integer.parseInt(portParameter.getValue()));
            emailBean.setAuthentication(Boolean.parseBoolean(smtpAuthParameter.getValue()));
            if(emailBean.getAuthentication()){
                Parameter usernameParameter = parameterService.getParameterById(ParametersEnum.EMAIL_USERNAME.getParametersEnumValue());
                Parameter passwordParameter = parameterService.getParameterById(ParametersEnum.EMAIL_PASSWORD.getParametersEnumValue());
                emailBean.setUser_authentication(usernameParameter.getValue());
                emailBean.setPassword_authentication(passwordParameter.getValue());
            }
            return emailBean;
        }catch (Exception e){
            log.error("EmailUtil - configureEmailObject() - Error: {}", e.getMessage());
        }
        return null;
    }

    public boolean sendEmail(EmailTypeEnum type, String subject, String text) {
        EmailBean emailBean = configureEmailObject();
        subject = subject.replace("XXX", airportName);
        emailBean.setSubject(subject);
        emailBean.setText(text);
        emailBean.setType(type);
        Parameter receivers = parameterService.getParameterById(ParametersEnum.EMAIL_RECIPIENTS.getParametersEnumValue());
        Parameter additionalRecipients = parameterService.getParameterById(ParametersEnum.EMAIL_ADDITIONAL_RECIPIENTS.getParametersEnumValue());

        boolean result = false;

        if(receivers!=null && receivers.getValue() != null && !receivers.getValue().isBlank()) {
            emailBean.setTo(receivers.getValue());
            if(additionalRecipients!=null && additionalRecipients.getValue() != null && !additionalRecipients.getValue().isBlank())
                emailBean.setCc(additionalRecipients.getValue());
            result = sendMessageWithAttachment(emailBean);
        }

        if(!result) log.error("Error occurred on sending email of type {}", type.getValue());

        return result;
    }
}
