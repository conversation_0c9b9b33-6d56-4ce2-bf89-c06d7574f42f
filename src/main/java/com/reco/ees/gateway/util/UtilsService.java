package com.reco.ees.gateway.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.reco.ees.gateway.ShutdownManager;
import com.reco.ees.gateway.kafka.producer.KafkaProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Base64;

@Slf4j
@Service
public class UtilsService {
    @Value("${all.done.and.sleep.time.ms:1000}")
    private int SLEEP_TIME_MS;
    //@Value("${key.encryption}")
    private static String keyEncryption;
    //@Value("${iv.encryption}")
    private static String ivEncryption;
    @Value("${is.encryption.active:false}")
    private Boolean isEncryptionActive;

    public static void setKeys(String cryptKey, String ivKey) {
        keyEncryption = cryptKey;
        ivEncryption = ivKey;
    }

    public void allDoneAndSleep(String jobName) {
        ShutdownManager.decrementActiveJobs(jobName);
        try {
            Thread.sleep(SLEEP_TIME_MS);
        } catch (InterruptedException e) {
            log.error("allDoneAndSleep() - Exception {}", e.getMessage());
        }
    }

    public String decodeBase64(String fileName, byte[] LDIFbytes, boolean saveFile) {
        if(LDIFbytes == null || LDIFbytes.length == 0) {
            log.error("decodeBase64() LDIFbytes is null for file {}", fileName);
            return null;
        }
        byte[] decoded;
        try {
            decoded = Base64.getDecoder().decode(LDIFbytes);
        } catch (IllegalArgumentException e) {
            log.error("decodeBase64() LDIF decoding error for file {} - Exception {}", fileName, e.getMessage());
            return null;
        }
        String result = new String(decoded);
        if (saveFile) {
            Path path = Paths.get(fileName);
            try {
                Files.write(path, result.getBytes());
            } catch (IOException e) {
                log.error("decodeBase64() can't write LDIF file {} - Exception {}", fileName, e.getMessage());
            }
        }
        return result;
    }

    /*@JsonBackReference
    @JsonManagedReference
    @JsonIdentityInfo(generator = ObjectIdGenerators.PropertyGenerator.class, property = "id")*/
    public String serializeMessage(Object object) throws JsonProcessingException {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.enable(SerializationFeature.INDENT_OUTPUT);
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        return objectMapper.writeValueAsString(object);
    }

    public String decryptContent(String contentToDecrypt){
        if(!isEncryptionActive) return contentToDecrypt;
        if(contentToDecrypt == null || contentToDecrypt.isEmpty()) {
            log.error("decryptContent() - contentToDecrypt is null or empty");
            return null;
        }

        try {
            SecretKeySpec secretKey=new SecretKeySpec(keyEncryption.getBytes(),"AES");
            IvParameterSpec ivParameterSpec=new IvParameterSpec(ivEncryption.getBytes());
            Cipher cipher=Cipher.getInstance("AES/CBC/PKCS5Padding");
            cipher.init(Cipher.DECRYPT_MODE,secretKey,ivParameterSpec);
            String decryptedValue = new String(cipher.doFinal(Base64.getDecoder().decode(contentToDecrypt)));
            log.debug("Decrypted string: {}", decryptedValue);
            return decryptedValue;
        } catch (Exception e) {
            log.error("decryptContent() - Exception {}", e.getMessage());
            StringWriter errors = new StringWriter();
            e.printStackTrace(new PrintWriter(errors));
            log.debug(errors.toString());
        }
        return null;
    }

    public static String decryptContentWithIV(byte[] contentToDecrypt){
        try {
            SecretKeySpec secretKey=new SecretKeySpec(keyEncryption.getBytes(),"AES");
            IvParameterSpec ivParameterSpec=new IvParameterSpec(ivEncryption.getBytes());
            Cipher cipher=Cipher.getInstance("AES/CBC/PKCS5Padding");
            cipher.init(Cipher.DECRYPT_MODE,secretKey,ivParameterSpec);
            String decryptedValue = new String(cipher.doFinal(org.springframework.security.crypto.codec.Base64.decode(contentToDecrypt)));
            log.debug("Decrypted string: {}", decryptedValue);
            return decryptedValue;
        } catch (IllegalBlockSizeException ibse) {
            return new String(contentToDecrypt);
        } catch (Exception e) {
            log.error("decryptContentWithIV() - Exception "+e.getMessage());
            StringWriter errors = new StringWriter();
            e.printStackTrace(new PrintWriter(errors));
            log.debug(errors.toString());
        }
        return null;
    }

    public String generatePasswordMD5(String password) {
        String md5Digest = DigestUtils.md5DigestAsHex(password.getBytes());
        // SIF richiede stringa base64 dell'hash md5
        return Base64.getEncoder().encodeToString(md5Digest.getBytes());
        /*try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            md.update(password.getBytes());
            byte[] digest = md.digest();
            StringBuilder sb = new StringBuilder();
            for (byte b : digest) {
                sb.append(String.format("%02x", b & 0xff));
            }
            return sb.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }*/
    }

    /*public String getCurrentAndCallerMethodInfo() {
        StackTraceElement[] stackTraceElements = Thread.currentThread().getStackTrace();
        if (stackTraceElements.length > 3) { // Caso normale con stack trace completo
            // Elemento 2: metodo corrente (dove viene chiamato getCallerMethodName)
            StackTraceElement current = stackTraceElements[2];
            String currentMethod = current.getMethodName();
            // Elemento 3: metodo chiamante (che ha chiamato il metodo corrente)
            StackTraceElement caller = stackTraceElements[3];
            String callerClass = caller.getClassName().substring(caller.getClassName().lastIndexOf('.') + 1);
            String callerMethod = caller.getMethodName();
            return currentMethod + "() (called by " + callerClass + "." + callerMethod + "())";
        } else if (stackTraceElements.length > 2) { // Fallback con informazioni parziali
            StackTraceElement current = stackTraceElements[2]; // Abbiamo almeno il metodo corrente
            String currentMethod = current.getMethodName();
            return currentMethod + "() (caller unknown)";
        }
        return this.getClass().getSimpleName() + " (stack trace insufficient)"; // Fallback estremo - raro ma teoricamente possibile
    }*/
    /**
     * Ottiene informazioni sul metodo corrente e sul suo chiamante.
     * @return Una stringa contenente le informazioni del metodo corrente e del chiamante.
     */
    /*public String getCurrentAndCallerMethodInfo() {
        StackTraceElement[] stackTraceElements = Thread.currentThread().getStackTrace();

        // stackTraceElements[0] è getStackTrace
        // stackTraceElements[1] è getCurrentAndCallerMethodInfo (questo metodo)
        // stackTraceElements[2] è il metodo che ha chiamato getCurrentAndCallerMethodInfo
        // stackTraceElements[3] è il chiamante di quel metodo (se disponibile)

        if (stackTraceElements.length < 3) {
            return "Metodo sconosciuto";
        }

        StringBuilder sb = new StringBuilder(100);
        sb.append("Metodo: ");
        sb.append(stackTraceElements[2].getClassName()).append('.').append(stackTraceElements[2].getMethodName());

        if (stackTraceElements.length >= 4) {
            sb.append(", chiamato da: ");
            sb.append(stackTraceElements[3].getClassName()).append('.').append(stackTraceElements[3].getMethodName());
        }

        return sb.toString();
    }*/
}
