//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package com.reco.ees.gateway.generated.sif.iae;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per Scomparso complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="Scomparso">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="codiceUfficioSegnalante" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="descrizioneUfficioSegnalante" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="protocolloSDI" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="descrizioneRuoloSoggetto" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="sesso" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         <element name="paternita" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         <element name="cognomeConiugata" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         <element name="cittadinanza" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         <element name="codCui" minOccurs="0">
 *           <simpleType>
 *             <restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               <pattern value="7"/>
 *             </restriction>
 *           </simpleType>
 *         </element>
 *         <element name="codFiscProvv" minOccurs="0">
 *           <simpleType>
 *             <restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               <pattern value="[0-9]{11}"/>
 *             </restriction>
 *           </simpleType>
 *         </element>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "Scomparso", namespace = "http://interrogazione.iae.sdi.ibm.it", propOrder = {
    "codiceUfficioSegnalante",
    "descrizioneUfficioSegnalante",
    "protocolloSDI",
    "descrizioneRuoloSoggetto",
    "sesso",
    "paternita",
    "cognomeConiugata",
    "cittadinanza",
    "codCui",
    "codFiscProvv"
})
public class Scomparso {

    protected String codiceUfficioSegnalante;
    protected String descrizioneUfficioSegnalante;
    protected String protocolloSDI;
    protected String descrizioneRuoloSoggetto;
    @XmlElement(required = true, nillable = true)
    protected String sesso;
    @XmlElement(required = true, nillable = true)
    protected String paternita;
    @XmlElement(required = true, nillable = true)
    protected String cognomeConiugata;
    @XmlElement(required = true, nillable = true)
    protected String cittadinanza;
    protected String codCui;
    protected String codFiscProvv;

    /**
     * Recupera il valore della proprietà codiceUfficioSegnalante.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCodiceUfficioSegnalante() {
        return codiceUfficioSegnalante;
    }

    /**
     * Imposta il valore della proprietà codiceUfficioSegnalante.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCodiceUfficioSegnalante(String value) {
        this.codiceUfficioSegnalante = value;
    }

    /**
     * Recupera il valore della proprietà descrizioneUfficioSegnalante.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDescrizioneUfficioSegnalante() {
        return descrizioneUfficioSegnalante;
    }

    /**
     * Imposta il valore della proprietà descrizioneUfficioSegnalante.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDescrizioneUfficioSegnalante(String value) {
        this.descrizioneUfficioSegnalante = value;
    }

    /**
     * Recupera il valore della proprietà protocolloSDI.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getProtocolloSDI() {
        return protocolloSDI;
    }

    /**
     * Imposta il valore della proprietà protocolloSDI.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setProtocolloSDI(String value) {
        this.protocolloSDI = value;
    }

    /**
     * Recupera il valore della proprietà descrizioneRuoloSoggetto.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDescrizioneRuoloSoggetto() {
        return descrizioneRuoloSoggetto;
    }

    /**
     * Imposta il valore della proprietà descrizioneRuoloSoggetto.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDescrizioneRuoloSoggetto(String value) {
        this.descrizioneRuoloSoggetto = value;
    }

    /**
     * Recupera il valore della proprietà sesso.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSesso() {
        return sesso;
    }

    /**
     * Imposta il valore della proprietà sesso.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSesso(String value) {
        this.sesso = value;
    }

    /**
     * Recupera il valore della proprietà paternita.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPaternita() {
        return paternita;
    }

    /**
     * Imposta il valore della proprietà paternita.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPaternita(String value) {
        this.paternita = value;
    }

    /**
     * Recupera il valore della proprietà cognomeConiugata.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCognomeConiugata() {
        return cognomeConiugata;
    }

    /**
     * Imposta il valore della proprietà cognomeConiugata.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCognomeConiugata(String value) {
        this.cognomeConiugata = value;
    }

    /**
     * Recupera il valore della proprietà cittadinanza.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCittadinanza() {
        return cittadinanza;
    }

    /**
     * Imposta il valore della proprietà cittadinanza.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCittadinanza(String value) {
        this.cittadinanza = value;
    }

    /**
     * Recupera il valore della proprietà codCui.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCodCui() {
        return codCui;
    }

    /**
     * Imposta il valore della proprietà codCui.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCodCui(String value) {
        this.codCui = value;
    }

    /**
     * Recupera il valore della proprietà codFiscProvv.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCodFiscProvv() {
        return codFiscProvv;
    }

    /**
     * Imposta il valore della proprietà codFiscProvv.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCodFiscProvv(String value) {
        this.codFiscProvv = value;
    }

}
