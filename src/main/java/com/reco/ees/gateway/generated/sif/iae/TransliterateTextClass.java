//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package com.reco.ees.gateway.generated.sif.iae;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per TransliterateTextClass complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="TransliterateTextClass">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="Source" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="TransliterateText" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         <element name="Transliteration" type="{http://tempuri.org/}ArrayOfString" minOccurs="0"/>
 *         <element name="TransliterationNumber" type="{http://www.w3.org/2001/XMLSchema}int"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "TransliterateTextClass", propOrder = {
    "source",
    "transliterateText",
    "transliteration",
    "transliterationNumber"
})
public class TransliterateTextClass {

    @XmlElement(name = "Source")
    protected String source;
    @XmlElement(name = "TransliterateText")
    protected boolean transliterateText;
    @XmlElement(name = "Transliteration")
    protected ArrayOfString transliteration;
    @XmlElement(name = "TransliterationNumber")
    protected int transliterationNumber;

    /**
     * Recupera il valore della proprietà source.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSource() {
        return source;
    }

    /**
     * Imposta il valore della proprietà source.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSource(String value) {
        this.source = value;
    }

    /**
     * Recupera il valore della proprietà transliterateText.
     * 
     */
    public boolean isTransliterateText() {
        return transliterateText;
    }

    /**
     * Imposta il valore della proprietà transliterateText.
     * 
     */
    public void setTransliterateText(boolean value) {
        this.transliterateText = value;
    }

    /**
     * Recupera il valore della proprietà transliteration.
     * 
     * @return
     *     possible object is
     *     {@link ArrayOfString }
     *     
     */
    public ArrayOfString getTransliteration() {
        return transliteration;
    }

    /**
     * Imposta il valore della proprietà transliteration.
     * 
     * @param value
     *     allowed object is
     *     {@link ArrayOfString }
     *     
     */
    public void setTransliteration(ArrayOfString value) {
        this.transliteration = value;
    }

    /**
     * Recupera il valore della proprietà transliterationNumber.
     * 
     */
    public int getTransliterationNumber() {
        return transliterationNumber;
    }

    /**
     * Imposta il valore della proprietà transliterationNumber.
     * 
     */
    public void setTransliterationNumber(int value) {
        this.transliterationNumber = value;
    }

}
