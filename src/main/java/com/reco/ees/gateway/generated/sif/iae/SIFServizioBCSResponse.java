//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package com.reco.ees.gateway.generated.sif.iae;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per anonymous complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType>
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="SIFServizioBCSResult" type="{http://schemas.sif.bcs.sita.it/}BCSAlertResponse" minOccurs="0"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "sifServizioBCSResult"
})
@XmlRootElement(name = "SIFServizioBCSResponse")
public class SIFServizioBCSResponse {

    @XmlElement(name = "SIFServizioBCSResult")
    protected BCSAlertResponse sifServizioBCSResult;

    /**
     * Recupera il valore della proprietà sifServizioBCSResult.
     * 
     * @return
     *     possible object is
     *     {@link BCSAlertResponse }
     *     
     */
    public BCSAlertResponse getSIFServizioBCSResult() {
        return sifServizioBCSResult;
    }

    /**
     * Imposta il valore della proprietà sifServizioBCSResult.
     * 
     * @param value
     *     allowed object is
     *     {@link BCSAlertResponse }
     *     
     */
    public void setSIFServizioBCSResult(BCSAlertResponse value) {
        this.sifServizioBCSResult = value;
    }

}
