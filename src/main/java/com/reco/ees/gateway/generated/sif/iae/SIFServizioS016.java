//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package com.reco.ees.gateway.generated.sif.iae;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per anonymous complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType>
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="ric" type="{http://tempuri.org/}IAERequestS016" minOccurs="0"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "ric"
})
@XmlRootElement(name = "SIFServizioS016")
public class SIFServizioS016 {

    protected IAERequestS016 ric;

    /**
     * Recupera il valore della proprietà ric.
     * 
     * @return
     *     possible object is
     *     {@link IAERequestS016 }
     *     
     */
    public IAERequestS016 getRic() {
        return ric;
    }

    /**
     * Imposta il valore della proprietà ric.
     * 
     * @param value
     *     allowed object is
     *     {@link IAERequestS016 }
     *     
     */
    public void setRic(IAERequestS016 value) {
        this.ric = value;
    }

}
