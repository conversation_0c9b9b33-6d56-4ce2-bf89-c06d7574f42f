//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package com.reco.ees.gateway.generated.sif.iae;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per anonymous complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType>
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="SendIVISCheckRequest3Result" type="{http://tempuri.org/}IVISCheckResponse3" minOccurs="0"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "sendIVISCheckRequest3Result"
})
@XmlRootElement(name = "SendIVISCheckRequest3Response")
public class SendIVISCheckRequest3Response {

    @XmlElement(name = "SendIVISCheckRequest3Result")
    protected IVISCheckResponse3 sendIVISCheckRequest3Result;

    /**
     * Recupera il valore della proprietà sendIVISCheckRequest3Result.
     * 
     * @return
     *     possible object is
     *     {@link IVISCheckResponse3 }
     *     
     */
    public IVISCheckResponse3 getSendIVISCheckRequest3Result() {
        return sendIVISCheckRequest3Result;
    }

    /**
     * Imposta il valore della proprietà sendIVISCheckRequest3Result.
     * 
     * @param value
     *     allowed object is
     *     {@link IVISCheckResponse3 }
     *     
     */
    public void setSendIVISCheckRequest3Result(IVISCheckResponse3 value) {
        this.sendIVISCheckRequest3Result = value;
    }

}
