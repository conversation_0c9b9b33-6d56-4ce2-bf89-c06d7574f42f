//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package com.reco.ees.gateway.generated.sif.services;

import javax.xml.datatype.XMLGregorianCalendar;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per anonymous complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType>
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="fromDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         <element name="toDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         <element name="fromRow" type="{http://www.w3.org/2001/XMLSchema}int"/>
 *         <element name="toRow" type="{http://www.w3.org/2001/XMLSchema}int"/>
 *         <element name="idOffice" type="{http://www.w3.org/2001/XMLSchema}int"/>
 *         <element name="idType" type="{http://www.w3.org/2001/XMLSchema}int"/>
 *         <element name="idSeverity" type="{http://www.w3.org/2001/XMLSchema}int"/>
 *         <element name="idMachine" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="idDomain" type="{http://microsoft.com/wsdl/types/}guid"/>
 *         <element name="userName" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="application" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "fromDate",
    "toDate",
    "fromRow",
    "toRow",
    "idOffice",
    "idType",
    "idSeverity",
    "idMachine",
    "idDomain",
    "userName",
    "application"
})
@XmlRootElement(name = "ViewLog")
public class ViewLog {

    @XmlElement(required = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar fromDate;
    @XmlElement(required = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar toDate;
    protected int fromRow;
    protected int toRow;
    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer idOffice;
    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer idType;
    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer idSeverity;
    protected String idMachine;
    @XmlElement(required = true, nillable = true)
    protected String idDomain;
    protected String userName;
    protected String application;

    /**
     * Recupera il valore della proprietà fromDate.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getFromDate() {
        return fromDate;
    }

    /**
     * Imposta il valore della proprietà fromDate.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setFromDate(XMLGregorianCalendar value) {
        this.fromDate = value;
    }

    /**
     * Recupera il valore della proprietà toDate.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getToDate() {
        return toDate;
    }

    /**
     * Imposta il valore della proprietà toDate.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setToDate(XMLGregorianCalendar value) {
        this.toDate = value;
    }

    /**
     * Recupera il valore della proprietà fromRow.
     * 
     */
    public int getFromRow() {
        return fromRow;
    }

    /**
     * Imposta il valore della proprietà fromRow.
     * 
     */
    public void setFromRow(int value) {
        this.fromRow = value;
    }

    /**
     * Recupera il valore della proprietà toRow.
     * 
     */
    public int getToRow() {
        return toRow;
    }

    /**
     * Imposta il valore della proprietà toRow.
     * 
     */
    public void setToRow(int value) {
        this.toRow = value;
    }

    /**
     * Recupera il valore della proprietà idOffice.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getIdOffice() {
        return idOffice;
    }

    /**
     * Imposta il valore della proprietà idOffice.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setIdOffice(Integer value) {
        this.idOffice = value;
    }

    /**
     * Recupera il valore della proprietà idType.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getIdType() {
        return idType;
    }

    /**
     * Imposta il valore della proprietà idType.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setIdType(Integer value) {
        this.idType = value;
    }

    /**
     * Recupera il valore della proprietà idSeverity.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getIdSeverity() {
        return idSeverity;
    }

    /**
     * Imposta il valore della proprietà idSeverity.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setIdSeverity(Integer value) {
        this.idSeverity = value;
    }

    /**
     * Recupera il valore della proprietà idMachine.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIdMachine() {
        return idMachine;
    }

    /**
     * Imposta il valore della proprietà idMachine.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIdMachine(String value) {
        this.idMachine = value;
    }

    /**
     * Recupera il valore della proprietà idDomain.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIdDomain() {
        return idDomain;
    }

    /**
     * Imposta il valore della proprietà idDomain.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIdDomain(String value) {
        this.idDomain = value;
    }

    /**
     * Recupera il valore della proprietà userName.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUserName() {
        return userName;
    }

    /**
     * Imposta il valore della proprietà userName.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUserName(String value) {
        this.userName = value;
    }

    /**
     * Recupera il valore della proprietà application.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getApplication() {
        return application;
    }

    /**
     * Imposta il valore della proprietà application.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setApplication(String value) {
        this.application = value;
    }

}
