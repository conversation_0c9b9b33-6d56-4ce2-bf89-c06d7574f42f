//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package com.reco.ees.gateway.generated.sif.iae;

import java.util.ArrayList;
import java.util.List;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per ArrayOfIAEResponseMessagge complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="ArrayOfIAEResponseMessagge">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="IAEResponseMessagge" type="{http://tempuri.org/}IAEResponseMessagge" maxOccurs="unbounded" minOccurs="0"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ArrayOfIAEResponseMessagge", propOrder = {
    "iaeResponseMessagge"
})
public class ArrayOfIAEResponseMessagge {

    @XmlElement(name = "IAEResponseMessagge", nillable = true)
    protected List<IAEResponseMessagge> iaeResponseMessagge;

    /**
     * Gets the value of the iaeResponseMessagge property.
     * 
     * <p>This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the iaeResponseMessagge property.</p>
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * </p>
     * <pre>
     * getIAEResponseMessagge().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link IAEResponseMessagge }
     * </p>
     * 
     * 
     * @return
     *     The value of the iaeResponseMessagge property.
     */
    public List<IAEResponseMessagge> getIAEResponseMessagge() {
        if (iaeResponseMessagge == null) {
            iaeResponseMessagge = new ArrayList<>();
        }
        return this.iaeResponseMessagge;
    }

}
