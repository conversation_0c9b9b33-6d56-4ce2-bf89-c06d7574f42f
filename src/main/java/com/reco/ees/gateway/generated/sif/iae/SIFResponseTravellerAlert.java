//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package com.reco.ees.gateway.generated.sif.iae;

import javax.xml.datatype.XMLGregorianCalendar;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per SIFResponseTravellerAlert complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="SIFResponseTravellerAlert">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="FlightCode" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="ArrAirportCode" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="ArrSchDatetime" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="TravellerName" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="BirthDate" type="{http://www.w3.org/2001/XMLSchema}date"/>
 *         <element name="Nationality" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="DocType" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="DocNumber" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="Notes" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SIFResponseTravellerAlert", propOrder = {
    "flightCode",
    "arrAirportCode",
    "arrSchDatetime",
    "travellerName",
    "birthDate",
    "nationality",
    "docType",
    "docNumber",
    "notes"
})
public class SIFResponseTravellerAlert {

    @XmlElement(name = "FlightCode")
    protected String flightCode;
    @XmlElement(name = "ArrAirportCode")
    protected String arrAirportCode;
    @XmlElement(name = "ArrSchDatetime")
    protected String arrSchDatetime;
    @XmlElement(name = "TravellerName")
    protected String travellerName;
    @XmlElement(name = "BirthDate", required = true)
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar birthDate;
    @XmlElement(name = "Nationality")
    protected String nationality;
    @XmlElement(name = "DocType")
    protected String docType;
    @XmlElement(name = "DocNumber")
    protected String docNumber;
    @XmlElement(name = "Notes")
    protected String notes;

    /**
     * Recupera il valore della proprietà flightCode.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFlightCode() {
        return flightCode;
    }

    /**
     * Imposta il valore della proprietà flightCode.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFlightCode(String value) {
        this.flightCode = value;
    }

    /**
     * Recupera il valore della proprietà arrAirportCode.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getArrAirportCode() {
        return arrAirportCode;
    }

    /**
     * Imposta il valore della proprietà arrAirportCode.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setArrAirportCode(String value) {
        this.arrAirportCode = value;
    }

    /**
     * Recupera il valore della proprietà arrSchDatetime.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getArrSchDatetime() {
        return arrSchDatetime;
    }

    /**
     * Imposta il valore della proprietà arrSchDatetime.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setArrSchDatetime(String value) {
        this.arrSchDatetime = value;
    }

    /**
     * Recupera il valore della proprietà travellerName.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTravellerName() {
        return travellerName;
    }

    /**
     * Imposta il valore della proprietà travellerName.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTravellerName(String value) {
        this.travellerName = value;
    }

    /**
     * Recupera il valore della proprietà birthDate.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getBirthDate() {
        return birthDate;
    }

    /**
     * Imposta il valore della proprietà birthDate.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setBirthDate(XMLGregorianCalendar value) {
        this.birthDate = value;
    }

    /**
     * Recupera il valore della proprietà nationality.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNationality() {
        return nationality;
    }

    /**
     * Imposta il valore della proprietà nationality.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNationality(String value) {
        this.nationality = value;
    }

    /**
     * Recupera il valore della proprietà docType.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDocType() {
        return docType;
    }

    /**
     * Imposta il valore della proprietà docType.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDocType(String value) {
        this.docType = value;
    }

    /**
     * Recupera il valore della proprietà docNumber.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDocNumber() {
        return docNumber;
    }

    /**
     * Imposta il valore della proprietà docNumber.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDocNumber(String value) {
        this.docNumber = value;
    }

    /**
     * Recupera il valore della proprietà notes.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNotes() {
        return notes;
    }

    /**
     * Imposta il valore della proprietà notes.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNotes(String value) {
        this.notes = value;
    }

}
