//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package com.reco.ees.gateway.generated.sif.iae;

import java.util.ArrayList;
import java.util.List;
import javax.xml.datatype.XMLGregorianCalendar;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per S004 complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="S004">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="cognome" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="nome" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="codiceFiscale" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         <element name="descrizioneLuogoNas" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="siglaProvinciaNas" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="dataNas" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         <element name="gradoCertificazioneDataNas" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="gradoPericolositaSoggetto" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         <element name="descrizioneAzioneEstesa" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="descrizioneAzioneSoggetto" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="numeroSegnalazioniSH" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         <element name="idSchengen" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         <element name="azioneSh" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         <element name="informativa" type="{http://interrogazione.iae.sdi.ibm.it}InformativaReato" maxOccurs="unbounded" minOccurs="0"/>
 *         <element name="permessoSoggiorno" type="{http://interrogazione.iae.sdi.ibm.it}PermessoSoggiorno" maxOccurs="unbounded" minOccurs="0"/>
 *         <element name="anagraficaCollegata" type="{http://interrogazione.iae.sdi.ibm.it}AnagraficaCollegata" maxOccurs="unbounded" minOccurs="0"/>
 *         <element name="scomparso" type="{http://interrogazione.iae.sdi.ibm.it}Scomparso" maxOccurs="unbounded" minOccurs="0"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "S004", namespace = "http://interrogazione.iae.sdi.ibm.it", propOrder = {
    "cognome",
    "nome",
    "codiceFiscale",
    "descrizioneLuogoNas",
    "siglaProvinciaNas",
    "dataNas",
    "gradoCertificazioneDataNas",
    "gradoPericolositaSoggetto",
    "descrizioneAzioneEstesa",
    "descrizioneAzioneSoggetto",
    "numeroSegnalazioniSH",
    "idSchengen",
    "azioneSh",
    "informativa",
    "permessoSoggiorno",
    "anagraficaCollegata",
    "scomparso"
})
public class S004 {

    protected String cognome;
    protected String nome;
    @XmlElement(required = true, nillable = true)
    protected String codiceFiscale;
    protected String descrizioneLuogoNas;
    protected String siglaProvinciaNas;
    @XmlElement(required = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar dataNas;
    protected String gradoCertificazioneDataNas;
    @XmlElement(required = true, nillable = true)
    protected String gradoPericolositaSoggetto;
    protected String descrizioneAzioneEstesa;
    protected String descrizioneAzioneSoggetto;
    @XmlElement(required = true, nillable = true)
    protected String numeroSegnalazioniSH;
    @XmlElement(required = true, nillable = true)
    protected String idSchengen;
    @XmlElement(required = true, nillable = true)
    protected String azioneSh;
    @XmlElement(nillable = true)
    protected List<InformativaReato> informativa;
    @XmlElement(nillable = true)
    protected List<PermessoSoggiorno> permessoSoggiorno;
    @XmlElement(nillable = true)
    protected List<AnagraficaCollegata> anagraficaCollegata;
    @XmlElement(nillable = true)
    protected List<Scomparso> scomparso;

    /**
     * Recupera il valore della proprietà cognome.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCognome() {
        return cognome;
    }

    /**
     * Imposta il valore della proprietà cognome.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCognome(String value) {
        this.cognome = value;
    }

    /**
     * Recupera il valore della proprietà nome.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNome() {
        return nome;
    }

    /**
     * Imposta il valore della proprietà nome.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNome(String value) {
        this.nome = value;
    }

    /**
     * Recupera il valore della proprietà codiceFiscale.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCodiceFiscale() {
        return codiceFiscale;
    }

    /**
     * Imposta il valore della proprietà codiceFiscale.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCodiceFiscale(String value) {
        this.codiceFiscale = value;
    }

    /**
     * Recupera il valore della proprietà descrizioneLuogoNas.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDescrizioneLuogoNas() {
        return descrizioneLuogoNas;
    }

    /**
     * Imposta il valore della proprietà descrizioneLuogoNas.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDescrizioneLuogoNas(String value) {
        this.descrizioneLuogoNas = value;
    }

    /**
     * Recupera il valore della proprietà siglaProvinciaNas.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSiglaProvinciaNas() {
        return siglaProvinciaNas;
    }

    /**
     * Imposta il valore della proprietà siglaProvinciaNas.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSiglaProvinciaNas(String value) {
        this.siglaProvinciaNas = value;
    }

    /**
     * Recupera il valore della proprietà dataNas.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getDataNas() {
        return dataNas;
    }

    /**
     * Imposta il valore della proprietà dataNas.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setDataNas(XMLGregorianCalendar value) {
        this.dataNas = value;
    }

    /**
     * Recupera il valore della proprietà gradoCertificazioneDataNas.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getGradoCertificazioneDataNas() {
        return gradoCertificazioneDataNas;
    }

    /**
     * Imposta il valore della proprietà gradoCertificazioneDataNas.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setGradoCertificazioneDataNas(String value) {
        this.gradoCertificazioneDataNas = value;
    }

    /**
     * Recupera il valore della proprietà gradoPericolositaSoggetto.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getGradoPericolositaSoggetto() {
        return gradoPericolositaSoggetto;
    }

    /**
     * Imposta il valore della proprietà gradoPericolositaSoggetto.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setGradoPericolositaSoggetto(String value) {
        this.gradoPericolositaSoggetto = value;
    }

    /**
     * Recupera il valore della proprietà descrizioneAzioneEstesa.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDescrizioneAzioneEstesa() {
        return descrizioneAzioneEstesa;
    }

    /**
     * Imposta il valore della proprietà descrizioneAzioneEstesa.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDescrizioneAzioneEstesa(String value) {
        this.descrizioneAzioneEstesa = value;
    }

    /**
     * Recupera il valore della proprietà descrizioneAzioneSoggetto.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDescrizioneAzioneSoggetto() {
        return descrizioneAzioneSoggetto;
    }

    /**
     * Imposta il valore della proprietà descrizioneAzioneSoggetto.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDescrizioneAzioneSoggetto(String value) {
        this.descrizioneAzioneSoggetto = value;
    }

    /**
     * Recupera il valore della proprietà numeroSegnalazioniSH.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNumeroSegnalazioniSH() {
        return numeroSegnalazioniSH;
    }

    /**
     * Imposta il valore della proprietà numeroSegnalazioniSH.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNumeroSegnalazioniSH(String value) {
        this.numeroSegnalazioniSH = value;
    }

    /**
     * Recupera il valore della proprietà idSchengen.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIdSchengen() {
        return idSchengen;
    }

    /**
     * Imposta il valore della proprietà idSchengen.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIdSchengen(String value) {
        this.idSchengen = value;
    }

    /**
     * Recupera il valore della proprietà azioneSh.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAzioneSh() {
        return azioneSh;
    }

    /**
     * Imposta il valore della proprietà azioneSh.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAzioneSh(String value) {
        this.azioneSh = value;
    }

    /**
     * Gets the value of the informativa property.
     * 
     * <p>This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the informativa property.</p>
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * </p>
     * <pre>
     * getInformativa().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link InformativaReato }
     * </p>
     * 
     * 
     * @return
     *     The value of the informativa property.
     */
    public List<InformativaReato> getInformativa() {
        if (informativa == null) {
            informativa = new ArrayList<>();
        }
        return this.informativa;
    }

    /**
     * Gets the value of the permessoSoggiorno property.
     * 
     * <p>This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the permessoSoggiorno property.</p>
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * </p>
     * <pre>
     * getPermessoSoggiorno().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link PermessoSoggiorno }
     * </p>
     * 
     * 
     * @return
     *     The value of the permessoSoggiorno property.
     */
    public List<PermessoSoggiorno> getPermessoSoggiorno() {
        if (permessoSoggiorno == null) {
            permessoSoggiorno = new ArrayList<>();
        }
        return this.permessoSoggiorno;
    }

    /**
     * Gets the value of the anagraficaCollegata property.
     * 
     * <p>This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the anagraficaCollegata property.</p>
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * </p>
     * <pre>
     * getAnagraficaCollegata().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link AnagraficaCollegata }
     * </p>
     * 
     * 
     * @return
     *     The value of the anagraficaCollegata property.
     */
    public List<AnagraficaCollegata> getAnagraficaCollegata() {
        if (anagraficaCollegata == null) {
            anagraficaCollegata = new ArrayList<>();
        }
        return this.anagraficaCollegata;
    }

    /**
     * Gets the value of the scomparso property.
     * 
     * <p>This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the scomparso property.</p>
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * </p>
     * <pre>
     * getScomparso().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link Scomparso }
     * </p>
     * 
     * 
     * @return
     *     The value of the scomparso property.
     */
    public List<Scomparso> getScomparso() {
        if (scomparso == null) {
            scomparso = new ArrayList<>();
        }
        return this.scomparso;
    }

}
