//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package com.reco.ees.gateway.generated.sif.iae;

import javax.xml.datatype.XMLGregorianCalendar;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlSeeAlso;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per IVISCheckResponse complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="IVISCheckResponse">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="IsBiometric" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         <element name="MessageId" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="Date" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         <element name="Result" type="{http://tempuri.org/}ResultClass" minOccurs="0"/>
 *         <element name="Check" type="{http://tempuri.org/}CheckClass" minOccurs="0"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "IVISCheckResponse", propOrder = {
    "isBiometric",
    "messageId",
    "date",
    "result",
    "check"
})
@XmlSeeAlso({
    IVISCheckResponse3 .class
})
public class IVISCheckResponse {

    @XmlElement(name = "IsBiometric")
    protected boolean isBiometric;
    @XmlElement(name = "MessageId")
    protected String messageId;
    @XmlElement(name = "Date", required = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar date;
    @XmlElement(name = "Result")
    protected ResultClass result;
    @XmlElement(name = "Check")
    protected CheckClass check;

    /**
     * Recupera il valore della proprietà isBiometric.
     * 
     */
    public boolean isIsBiometric() {
        return isBiometric;
    }

    /**
     * Imposta il valore della proprietà isBiometric.
     * 
     */
    public void setIsBiometric(boolean value) {
        this.isBiometric = value;
    }

    /**
     * Recupera il valore della proprietà messageId.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMessageId() {
        return messageId;
    }

    /**
     * Imposta il valore della proprietà messageId.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMessageId(String value) {
        this.messageId = value;
    }

    /**
     * Recupera il valore della proprietà date.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getDate() {
        return date;
    }

    /**
     * Imposta il valore della proprietà date.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setDate(XMLGregorianCalendar value) {
        this.date = value;
    }

    /**
     * Recupera il valore della proprietà result.
     * 
     * @return
     *     possible object is
     *     {@link ResultClass }
     *     
     */
    public ResultClass getResult() {
        return result;
    }

    /**
     * Imposta il valore della proprietà result.
     * 
     * @param value
     *     allowed object is
     *     {@link ResultClass }
     *     
     */
    public void setResult(ResultClass value) {
        this.result = value;
    }

    /**
     * Recupera il valore della proprietà check.
     * 
     * @return
     *     possible object is
     *     {@link CheckClass }
     *     
     */
    public CheckClass getCheck() {
        return check;
    }

    /**
     * Imposta il valore della proprietà check.
     * 
     * @param value
     *     allowed object is
     *     {@link CheckClass }
     *     
     */
    public void setCheck(CheckClass value) {
        this.check = value;
    }

}
