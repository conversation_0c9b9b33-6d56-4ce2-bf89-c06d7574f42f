//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package com.reco.ees.gateway.generated.sif.iae;

import java.util.ArrayList;
import java.util.List;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per SIFResponse complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="SIFResponse">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="ServiceStatus" type="{http://www.w3.org/2001/XMLSchema}byte"/>
 *         <element name="TravellerAlert" type="{http://tempuri.org/}SIFResponseTravellerAlert" maxOccurs="unbounded" minOccurs="0"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SIFResponse", propOrder = {
    "serviceStatus",
    "travellerAlert"
})
public class SIFResponse {

    @XmlElement(name = "ServiceStatus")
    protected byte serviceStatus;
    @XmlElement(name = "TravellerAlert")
    protected List<SIFResponseTravellerAlert> travellerAlert;

    /**
     * Recupera il valore della proprietà serviceStatus.
     * 
     */
    public byte getServiceStatus() {
        return serviceStatus;
    }

    /**
     * Imposta il valore della proprietà serviceStatus.
     * 
     */
    public void setServiceStatus(byte value) {
        this.serviceStatus = value;
    }

    /**
     * Gets the value of the travellerAlert property.
     * 
     * <p>This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the travellerAlert property.</p>
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * </p>
     * <pre>
     * getTravellerAlert().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link SIFResponseTravellerAlert }
     * </p>
     * 
     * 
     * @return
     *     The value of the travellerAlert property.
     */
    public List<SIFResponseTravellerAlert> getTravellerAlert() {
        if (travellerAlert == null) {
            travellerAlert = new ArrayList<>();
        }
        return this.travellerAlert;
    }

}
