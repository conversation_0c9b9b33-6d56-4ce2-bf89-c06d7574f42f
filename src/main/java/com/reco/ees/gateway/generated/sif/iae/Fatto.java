//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package com.reco.ees.gateway.generated.sif.iae;

import javax.xml.datatype.XMLGregorianCalendar;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per Fatto complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="Fatto">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="tipoComunicazione" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="titoloFatto" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="protocolloFatto" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="dataFatto" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         <element name="codiceUfficioSegnalante" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="descrizioneUfficioSegnalante" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="descrizioneLuogoUfficioSegnalante" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="siglaProvinciaUfficioSegnalante" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="soggettoPF" type="{http://interrogazione.iae.sdi.ibm.it}PersonaFisica"/>
 *         <element name="soggettoPG" type="{http://interrogazione.iae.sdi.ibm.it}PersonaGiuridica"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "Fatto", namespace = "http://interrogazione.iae.sdi.ibm.it", propOrder = {
    "tipoComunicazione",
    "titoloFatto",
    "protocolloFatto",
    "dataFatto",
    "codiceUfficioSegnalante",
    "descrizioneUfficioSegnalante",
    "descrizioneLuogoUfficioSegnalante",
    "siglaProvinciaUfficioSegnalante",
    "soggettoPF",
    "soggettoPG"
})
public class Fatto {

    protected String tipoComunicazione;
    protected String titoloFatto;
    protected String protocolloFatto;
    @XmlElement(required = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar dataFatto;
    protected String codiceUfficioSegnalante;
    protected String descrizioneUfficioSegnalante;
    protected String descrizioneLuogoUfficioSegnalante;
    protected String siglaProvinciaUfficioSegnalante;
    @XmlElement(required = true, nillable = true)
    protected PersonaFisica soggettoPF;
    @XmlElement(required = true, nillable = true)
    protected PersonaGiuridica soggettoPG;

    /**
     * Recupera il valore della proprietà tipoComunicazione.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTipoComunicazione() {
        return tipoComunicazione;
    }

    /**
     * Imposta il valore della proprietà tipoComunicazione.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTipoComunicazione(String value) {
        this.tipoComunicazione = value;
    }

    /**
     * Recupera il valore della proprietà titoloFatto.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTitoloFatto() {
        return titoloFatto;
    }

    /**
     * Imposta il valore della proprietà titoloFatto.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTitoloFatto(String value) {
        this.titoloFatto = value;
    }

    /**
     * Recupera il valore della proprietà protocolloFatto.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getProtocolloFatto() {
        return protocolloFatto;
    }

    /**
     * Imposta il valore della proprietà protocolloFatto.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setProtocolloFatto(String value) {
        this.protocolloFatto = value;
    }

    /**
     * Recupera il valore della proprietà dataFatto.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getDataFatto() {
        return dataFatto;
    }

    /**
     * Imposta il valore della proprietà dataFatto.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setDataFatto(XMLGregorianCalendar value) {
        this.dataFatto = value;
    }

    /**
     * Recupera il valore della proprietà codiceUfficioSegnalante.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCodiceUfficioSegnalante() {
        return codiceUfficioSegnalante;
    }

    /**
     * Imposta il valore della proprietà codiceUfficioSegnalante.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCodiceUfficioSegnalante(String value) {
        this.codiceUfficioSegnalante = value;
    }

    /**
     * Recupera il valore della proprietà descrizioneUfficioSegnalante.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDescrizioneUfficioSegnalante() {
        return descrizioneUfficioSegnalante;
    }

    /**
     * Imposta il valore della proprietà descrizioneUfficioSegnalante.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDescrizioneUfficioSegnalante(String value) {
        this.descrizioneUfficioSegnalante = value;
    }

    /**
     * Recupera il valore della proprietà descrizioneLuogoUfficioSegnalante.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDescrizioneLuogoUfficioSegnalante() {
        return descrizioneLuogoUfficioSegnalante;
    }

    /**
     * Imposta il valore della proprietà descrizioneLuogoUfficioSegnalante.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDescrizioneLuogoUfficioSegnalante(String value) {
        this.descrizioneLuogoUfficioSegnalante = value;
    }

    /**
     * Recupera il valore della proprietà siglaProvinciaUfficioSegnalante.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSiglaProvinciaUfficioSegnalante() {
        return siglaProvinciaUfficioSegnalante;
    }

    /**
     * Imposta il valore della proprietà siglaProvinciaUfficioSegnalante.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSiglaProvinciaUfficioSegnalante(String value) {
        this.siglaProvinciaUfficioSegnalante = value;
    }

    /**
     * Recupera il valore della proprietà soggettoPF.
     * 
     * @return
     *     possible object is
     *     {@link PersonaFisica }
     *     
     */
    public PersonaFisica getSoggettoPF() {
        return soggettoPF;
    }

    /**
     * Imposta il valore della proprietà soggettoPF.
     * 
     * @param value
     *     allowed object is
     *     {@link PersonaFisica }
     *     
     */
    public void setSoggettoPF(PersonaFisica value) {
        this.soggettoPF = value;
    }

    /**
     * Recupera il valore della proprietà soggettoPG.
     * 
     * @return
     *     possible object is
     *     {@link PersonaGiuridica }
     *     
     */
    public PersonaGiuridica getSoggettoPG() {
        return soggettoPG;
    }

    /**
     * Imposta il valore della proprietà soggettoPG.
     * 
     * @param value
     *     allowed object is
     *     {@link PersonaGiuridica }
     *     
     */
    public void setSoggettoPG(PersonaGiuridica value) {
        this.soggettoPG = value;
    }

}
