//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package com.reco.ees.gateway.generated.sif.services;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per anonymous complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType>
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="LoadDocumentLayoutDatabaseResult" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "loadDocumentLayoutDatabaseResult"
})
@XmlRootElement(name = "LoadDocumentLayoutDatabaseResponse")
public class LoadDocumentLayoutDatabaseResponse {

    @XmlElement(name = "LoadDocumentLayoutDatabaseResult")
    protected boolean loadDocumentLayoutDatabaseResult;

    /**
     * Recupera il valore della proprietà loadDocumentLayoutDatabaseResult.
     * 
     */
    public boolean isLoadDocumentLayoutDatabaseResult() {
        return loadDocumentLayoutDatabaseResult;
    }

    /**
     * Imposta il valore della proprietà loadDocumentLayoutDatabaseResult.
     * 
     */
    public void setLoadDocumentLayoutDatabaseResult(boolean value) {
        this.loadDocumentLayoutDatabaseResult = value;
    }

}
