//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package com.reco.ees.gateway.generated.sif.services;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per anonymous complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType>
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="dsUserName" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="dsApplication" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="idType" type="{http://www.w3.org/2001/XMLSchema}int"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "dsUserName",
    "dsApplication",
    "idType"
})
@XmlRootElement(name = "GetAdministeredOfficesByType")
public class GetAdministeredOfficesByType {

    protected String dsUserName;
    protected String dsApplication;
    protected int idType;

    /**
     * Recupera il valore della proprietà dsUserName.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDsUserName() {
        return dsUserName;
    }

    /**
     * Imposta il valore della proprietà dsUserName.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDsUserName(String value) {
        this.dsUserName = value;
    }

    /**
     * Recupera il valore della proprietà dsApplication.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDsApplication() {
        return dsApplication;
    }

    /**
     * Imposta il valore della proprietà dsApplication.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDsApplication(String value) {
        this.dsApplication = value;
    }

    /**
     * Recupera il valore della proprietà idType.
     * 
     */
    public int getIdType() {
        return idType;
    }

    /**
     * Imposta il valore della proprietà idType.
     * 
     */
    public void setIdType(int value) {
        this.idType = value;
    }

}
