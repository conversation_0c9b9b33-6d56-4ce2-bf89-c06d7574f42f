//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package com.reco.ees.gateway.generated.sif.services;

import javax.xml.datatype.XMLGregorianCalendar;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlSeeAlso;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per AuthorizationIdentityBaseClient complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="AuthorizationIdentityBaseClient">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="Status" type="{http://tempuri.org/}ApplicationIdentityStatus"/>
 *         <element name="DateOfExpire" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         <element name="PasswordExpirationDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         <element name="IsAuthenticated" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         <element name="Message" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="UserName" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="MaxInvalidPasswordAttempts" type="{http://www.w3.org/2001/XMLSchema}int"/>
 *         <element name="LastLoginDateTime" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "AuthorizationIdentityBaseClient", propOrder = {
    "status",
    "dateOfExpire",
    "passwordExpirationDate",
    "isAuthenticated",
    "message",
    "userName",
    "maxInvalidPasswordAttempts",
    "lastLoginDateTime"
})
@XmlSeeAlso({
    ServiceIdentityClient.class
})
public abstract class AuthorizationIdentityBaseClient {

    @XmlElement(name = "Status", required = true)
    @XmlSchemaType(name = "string")
    protected ApplicationIdentityStatus status;
    @XmlElement(name = "DateOfExpire", required = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar dateOfExpire;
    @XmlElement(name = "PasswordExpirationDate", required = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar passwordExpirationDate;
    @XmlElement(name = "IsAuthenticated")
    protected boolean isAuthenticated;
    @XmlElement(name = "Message")
    protected String message;
    @XmlElement(name = "UserName")
    protected String userName;
    @XmlElement(name = "MaxInvalidPasswordAttempts")
    protected int maxInvalidPasswordAttempts;
    @XmlElement(name = "LastLoginDateTime", required = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar lastLoginDateTime;

    /**
     * Recupera il valore della proprietà status.
     * 
     * @return
     *     possible object is
     *     {@link ApplicationIdentityStatus }
     *     
     */
    public ApplicationIdentityStatus getStatus() {
        return status;
    }

    /**
     * Imposta il valore della proprietà status.
     * 
     * @param value
     *     allowed object is
     *     {@link ApplicationIdentityStatus }
     *     
     */
    public void setStatus(ApplicationIdentityStatus value) {
        this.status = value;
    }

    /**
     * Recupera il valore della proprietà dateOfExpire.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getDateOfExpire() {
        return dateOfExpire;
    }

    /**
     * Imposta il valore della proprietà dateOfExpire.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setDateOfExpire(XMLGregorianCalendar value) {
        this.dateOfExpire = value;
    }

    /**
     * Recupera il valore della proprietà passwordExpirationDate.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getPasswordExpirationDate() {
        return passwordExpirationDate;
    }

    /**
     * Imposta il valore della proprietà passwordExpirationDate.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setPasswordExpirationDate(XMLGregorianCalendar value) {
        this.passwordExpirationDate = value;
    }

    /**
     * Recupera il valore della proprietà isAuthenticated.
     * 
     */
    public boolean isIsAuthenticated() {
        return isAuthenticated;
    }

    /**
     * Imposta il valore della proprietà isAuthenticated.
     * 
     */
    public void setIsAuthenticated(boolean value) {
        this.isAuthenticated = value;
    }

    /**
     * Recupera il valore della proprietà message.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMessage() {
        return message;
    }

    /**
     * Imposta il valore della proprietà message.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMessage(String value) {
        this.message = value;
    }

    /**
     * Recupera il valore della proprietà userName.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUserName() {
        return userName;
    }

    /**
     * Imposta il valore della proprietà userName.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUserName(String value) {
        this.userName = value;
    }

    /**
     * Recupera il valore della proprietà maxInvalidPasswordAttempts.
     * 
     */
    public int getMaxInvalidPasswordAttempts() {
        return maxInvalidPasswordAttempts;
    }

    /**
     * Imposta il valore della proprietà maxInvalidPasswordAttempts.
     * 
     */
    public void setMaxInvalidPasswordAttempts(int value) {
        this.maxInvalidPasswordAttempts = value;
    }

    /**
     * Recupera il valore della proprietà lastLoginDateTime.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getLastLoginDateTime() {
        return lastLoginDateTime;
    }

    /**
     * Imposta il valore della proprietà lastLoginDateTime.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setLastLoginDateTime(XMLGregorianCalendar value) {
        this.lastLoginDateTime = value;
    }

}
