//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package com.reco.ees.gateway.generated.sif.services;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per SecurityType complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="SecurityType">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="idMachine" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         <element name="SecurityToken" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SecurityType", propOrder = {
    "idMachine",
    "securityToken"
})
public class SecurityType {

    @XmlElement(required = true)
    protected String idMachine;
    @XmlElement(name = "SecurityToken", required = true)
    protected String securityToken;

    /**
     * Recupera il valore della proprietà idMachine.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIdMachine() {
        return idMachine;
    }

    /**
     * Imposta il valore della proprietà idMachine.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIdMachine(String value) {
        this.idMachine = value;
    }

    /**
     * Recupera il valore della proprietà securityToken.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSecurityToken() {
        return securityToken;
    }

    /**
     * Imposta il valore della proprietà securityToken.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSecurityToken(String value) {
        this.securityToken = value;
    }

}
