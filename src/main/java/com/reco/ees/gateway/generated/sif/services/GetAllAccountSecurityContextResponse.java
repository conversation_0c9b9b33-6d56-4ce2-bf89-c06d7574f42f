//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package com.reco.ees.gateway.generated.sif.services;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlAnyElement;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per anonymous complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType>
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="GetAllAccountSecurityContextResult" minOccurs="0">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <any/>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "getAllAccountSecurityContextResult"
})
@XmlRootElement(name = "GetAllAccountSecurityContextResponse")
public class GetAllAccountSecurityContextResponse {

    @XmlElement(name = "GetAllAccountSecurityContextResult")
    protected GetAllAccountSecurityContextResponse.GetAllAccountSecurityContextResult getAllAccountSecurityContextResult;

    /**
     * Recupera il valore della proprietà getAllAccountSecurityContextResult.
     * 
     * @return
     *     possible object is
     *     {@link GetAllAccountSecurityContextResponse.GetAllAccountSecurityContextResult }
     *     
     */
    public GetAllAccountSecurityContextResponse.GetAllAccountSecurityContextResult getGetAllAccountSecurityContextResult() {
        return getAllAccountSecurityContextResult;
    }

    /**
     * Imposta il valore della proprietà getAllAccountSecurityContextResult.
     * 
     * @param value
     *     allowed object is
     *     {@link GetAllAccountSecurityContextResponse.GetAllAccountSecurityContextResult }
     *     
     */
    public void setGetAllAccountSecurityContextResult(GetAllAccountSecurityContextResponse.GetAllAccountSecurityContextResult value) {
        this.getAllAccountSecurityContextResult = value;
    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <any/>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "any"
    })
    public static class GetAllAccountSecurityContextResult {

        @XmlAnyElement(lax = true)
        protected Object any;

        /**
         * Recupera il valore della proprietà any.
         * 
         * @return
         *     possible object is
         *     {@link Object }
         *     
         */
        public Object getAny() {
            return any;
        }

        /**
         * Imposta il valore della proprietà any.
         * 
         * @param value
         *     allowed object is
         *     {@link Object }
         *     
         */
        public void setAny(Object value) {
            this.any = value;
        }

    }

}
