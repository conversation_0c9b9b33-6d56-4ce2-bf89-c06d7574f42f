//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package com.reco.ees.gateway.generated.sif.services;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSeeAlso;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per ServiceIdentity complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="ServiceIdentity">
 *   <complexContent>
 *     <extension base="{http://tempuri.org/}AuthorizationIdentityBase">
 *       <sequence>
 *         <element name="AuthorizedServices" type="{http://tempuri.org/}ArrayOfApplicationService" minOccurs="0"/>
 *       </sequence>
 *     </extension>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ServiceIdentity", propOrder = {
    "authorizedServices"
})
@XmlSeeAlso({
    SIFIdentity.class
})
public abstract class ServiceIdentity
    extends AuthorizationIdentityBase
{

    @XmlElement(name = "AuthorizedServices")
    protected ArrayOfApplicationService authorizedServices;

    /**
     * Recupera il valore della proprietà authorizedServices.
     * 
     * @return
     *     possible object is
     *     {@link ArrayOfApplicationService }
     *     
     */
    public ArrayOfApplicationService getAuthorizedServices() {
        return authorizedServices;
    }

    /**
     * Imposta il valore della proprietà authorizedServices.
     * 
     * @param value
     *     allowed object is
     *     {@link ArrayOfApplicationService }
     *     
     */
    public void setAuthorizedServices(ArrayOfApplicationService value) {
        this.authorizedServices = value;
    }

}
