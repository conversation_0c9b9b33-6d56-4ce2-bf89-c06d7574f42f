//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package com.reco.ees.gateway.generated.sif.services;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per anonymous complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType>
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="idKnowledge" type="{http://microsoft.com/wsdl/types/}guid"/>
 *         <element name="idAttaches" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="newWidth" type="{http://www.w3.org/2001/XMLSchema}int"/>
 *         <element name="maxHeight" type="{http://www.w3.org/2001/XMLSchema}int"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "idKnowledge",
    "idAttaches",
    "newWidth",
    "maxHeight"
})
@XmlRootElement(name = "GetCertifiedAlertImages")
public class GetCertifiedAlertImages {

    @XmlElement(required = true)
    protected String idKnowledge;
    protected String idAttaches;
    protected int newWidth;
    protected int maxHeight;

    /**
     * Recupera il valore della proprietà idKnowledge.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIdKnowledge() {
        return idKnowledge;
    }

    /**
     * Imposta il valore della proprietà idKnowledge.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIdKnowledge(String value) {
        this.idKnowledge = value;
    }

    /**
     * Recupera il valore della proprietà idAttaches.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIdAttaches() {
        return idAttaches;
    }

    /**
     * Imposta il valore della proprietà idAttaches.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIdAttaches(String value) {
        this.idAttaches = value;
    }

    /**
     * Recupera il valore della proprietà newWidth.
     * 
     */
    public int getNewWidth() {
        return newWidth;
    }

    /**
     * Imposta il valore della proprietà newWidth.
     * 
     */
    public void setNewWidth(int value) {
        this.newWidth = value;
    }

    /**
     * Recupera il valore della proprietà maxHeight.
     * 
     */
    public int getMaxHeight() {
        return maxHeight;
    }

    /**
     * Imposta il valore della proprietà maxHeight.
     * 
     */
    public void setMaxHeight(int value) {
        this.maxHeight = value;
    }

}
