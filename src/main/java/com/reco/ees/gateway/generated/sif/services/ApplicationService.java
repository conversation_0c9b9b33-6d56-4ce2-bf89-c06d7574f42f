//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package com.reco.ees.gateway.generated.sif.services;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per ApplicationService complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="ApplicationService">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="Address" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="ServiceType" type="{http://tempuri.org/}ApplicationServiceTypes"/>
 *         <element name="Namespace" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="Class" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="Assembly" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="Name" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="Method" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="ConnectedMode" type="{http://tempuri.org/}ApplicationServiceModes"/>
 *         <element name="ExecutingIdentity" type="{http://tempuri.org/}ServiceIdentity" minOccurs="0"/>
 *         <element name="ServiceExecutionRules" type="{http://tempuri.org/}ArrayOfApplicationServiceRule" minOccurs="0"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ApplicationService", propOrder = {
    "address",
    "serviceType",
    "namespace",
    "clazz",
    "assembly",
    "name",
    "method",
    "connectedMode",
    "executingIdentity",
    "serviceExecutionRules"
})
public class ApplicationService {

    @XmlElement(name = "Address")
    protected String address;
    @XmlElement(name = "ServiceType", required = true)
    @XmlSchemaType(name = "string")
    protected ApplicationServiceTypes serviceType;
    @XmlElement(name = "Namespace")
    protected String namespace;
    @XmlElement(name = "Class")
    protected String clazz;
    @XmlElement(name = "Assembly")
    protected String assembly;
    @XmlElement(name = "Name")
    protected String name;
    @XmlElement(name = "Method")
    protected String method;
    @XmlElement(name = "ConnectedMode", required = true)
    @XmlSchemaType(name = "string")
    protected ApplicationServiceModes connectedMode;
    @XmlElement(name = "ExecutingIdentity")
    protected ServiceIdentity executingIdentity;
    @XmlElement(name = "ServiceExecutionRules")
    protected ArrayOfApplicationServiceRule serviceExecutionRules;

    /**
     * Recupera il valore della proprietà address.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAddress() {
        return address;
    }

    /**
     * Imposta il valore della proprietà address.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAddress(String value) {
        this.address = value;
    }

    /**
     * Recupera il valore della proprietà serviceType.
     * 
     * @return
     *     possible object is
     *     {@link ApplicationServiceTypes }
     *     
     */
    public ApplicationServiceTypes getServiceType() {
        return serviceType;
    }

    /**
     * Imposta il valore della proprietà serviceType.
     * 
     * @param value
     *     allowed object is
     *     {@link ApplicationServiceTypes }
     *     
     */
    public void setServiceType(ApplicationServiceTypes value) {
        this.serviceType = value;
    }

    /**
     * Recupera il valore della proprietà namespace.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNamespace() {
        return namespace;
    }

    /**
     * Imposta il valore della proprietà namespace.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNamespace(String value) {
        this.namespace = value;
    }

    /**
     * Recupera il valore della proprietà clazz.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getClazz() {
        return clazz;
    }

    /**
     * Imposta il valore della proprietà clazz.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setClazz(String value) {
        this.clazz = value;
    }

    /**
     * Recupera il valore della proprietà assembly.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAssembly() {
        return assembly;
    }

    /**
     * Imposta il valore della proprietà assembly.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAssembly(String value) {
        this.assembly = value;
    }

    /**
     * Recupera il valore della proprietà name.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getName() {
        return name;
    }

    /**
     * Imposta il valore della proprietà name.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setName(String value) {
        this.name = value;
    }

    /**
     * Recupera il valore della proprietà method.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMethod() {
        return method;
    }

    /**
     * Imposta il valore della proprietà method.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMethod(String value) {
        this.method = value;
    }

    /**
     * Recupera il valore della proprietà connectedMode.
     * 
     * @return
     *     possible object is
     *     {@link ApplicationServiceModes }
     *     
     */
    public ApplicationServiceModes getConnectedMode() {
        return connectedMode;
    }

    /**
     * Imposta il valore della proprietà connectedMode.
     * 
     * @param value
     *     allowed object is
     *     {@link ApplicationServiceModes }
     *     
     */
    public void setConnectedMode(ApplicationServiceModes value) {
        this.connectedMode = value;
    }

    /**
     * Recupera il valore della proprietà executingIdentity.
     * 
     * @return
     *     possible object is
     *     {@link ServiceIdentity }
     *     
     */
    public ServiceIdentity getExecutingIdentity() {
        return executingIdentity;
    }

    /**
     * Imposta il valore della proprietà executingIdentity.
     * 
     * @param value
     *     allowed object is
     *     {@link ServiceIdentity }
     *     
     */
    public void setExecutingIdentity(ServiceIdentity value) {
        this.executingIdentity = value;
    }

    /**
     * Recupera il valore della proprietà serviceExecutionRules.
     * 
     * @return
     *     possible object is
     *     {@link ArrayOfApplicationServiceRule }
     *     
     */
    public ArrayOfApplicationServiceRule getServiceExecutionRules() {
        return serviceExecutionRules;
    }

    /**
     * Imposta il valore della proprietà serviceExecutionRules.
     * 
     * @param value
     *     allowed object is
     *     {@link ArrayOfApplicationServiceRule }
     *     
     */
    public void setServiceExecutionRules(ArrayOfApplicationServiceRule value) {
        this.serviceExecutionRules = value;
    }

}
