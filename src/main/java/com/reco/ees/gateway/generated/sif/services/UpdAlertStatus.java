//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package com.reco.ees.gateway.generated.sif.services;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per anonymous complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType>
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="idKnowledge" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="idState" type="{http://www.w3.org/2001/XMLSchema}int"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "idKnowledge",
    "idState"
})
@XmlRootElement(name = "UpdAlertStatus")
public class UpdAlertStatus {

    protected String idKnowledge;
    protected int idState;

    /**
     * Recupera il valore della proprietà idKnowledge.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIdKnowledge() {
        return idKnowledge;
    }

    /**
     * Imposta il valore della proprietà idKnowledge.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIdKnowledge(String value) {
        this.idKnowledge = value;
    }

    /**
     * Recupera il valore della proprietà idState.
     * 
     */
    public int getIdState() {
        return idState;
    }

    /**
     * Imposta il valore della proprietà idState.
     * 
     */
    public void setIdState(int value) {
        this.idState = value;
    }

}
