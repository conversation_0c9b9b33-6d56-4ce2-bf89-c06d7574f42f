//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package com.reco.ees.gateway.generated.sif.services;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per anonymous complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType>
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="GetMachineInfoAppResult">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="idMachine" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *                   <element name="dsName" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *                   <element name="dsIPAddress" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *                   <element name="dsDescription" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *                   <element name="dsSystem" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *                   <element name="dsType" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *                   <element name="dsDomain" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *                   <element name="idDomain" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *                   <element name="idOffice" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *                   <element name="dsOffice" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *                   <element name="dsOfficeInternal" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *                   <element name="dsApplicationName" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *                   <element name="esito" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *                   <element name="errorMessage" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "getMachineInfoAppResult"
})
@XmlRootElement(name = "GetMachineInfoAppResponse")
public class GetMachineInfoAppResponse {

    @XmlElement(name = "GetMachineInfoAppResult", required = true)
    protected GetMachineInfoAppResponse.GetMachineInfoAppResult getMachineInfoAppResult;

    /**
     * Recupera il valore della proprietà getMachineInfoAppResult.
     * 
     * @return
     *     possible object is
     *     {@link GetMachineInfoAppResponse.GetMachineInfoAppResult }
     *     
     */
    public GetMachineInfoAppResponse.GetMachineInfoAppResult getGetMachineInfoAppResult() {
        return getMachineInfoAppResult;
    }

    /**
     * Imposta il valore della proprietà getMachineInfoAppResult.
     * 
     * @param value
     *     allowed object is
     *     {@link GetMachineInfoAppResponse.GetMachineInfoAppResult }
     *     
     */
    public void setGetMachineInfoAppResult(GetMachineInfoAppResponse.GetMachineInfoAppResult value) {
        this.getMachineInfoAppResult = value;
    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="idMachine" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
     *         <element name="dsName" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
     *         <element name="dsIPAddress" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
     *         <element name="dsDescription" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
     *         <element name="dsSystem" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
     *         <element name="dsType" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
     *         <element name="dsDomain" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
     *         <element name="idDomain" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
     *         <element name="idOffice" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
     *         <element name="dsOffice" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
     *         <element name="dsOfficeInternal" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
     *         <element name="dsApplicationName" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
     *         <element name="esito" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
     *         <element name="errorMessage" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "idMachine",
        "dsName",
        "dsIPAddress",
        "dsDescription",
        "dsSystem",
        "dsType",
        "dsDomain",
        "idDomain",
        "idOffice",
        "dsOffice",
        "dsOfficeInternal",
        "dsApplicationName",
        "esito",
        "errorMessage"
    })
    public static class GetMachineInfoAppResult {

        protected String idMachine;
        protected String dsName;
        protected String dsIPAddress;
        protected String dsDescription;
        protected String dsSystem;
        protected String dsType;
        protected String dsDomain;
        protected String idDomain;
        protected String idOffice;
        protected String dsOffice;
        protected String dsOfficeInternal;
        protected String dsApplicationName;
        protected Integer esito;
        protected String errorMessage;

        /**
         * Recupera il valore della proprietà idMachine.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getIdMachine() {
            return idMachine;
        }

        /**
         * Imposta il valore della proprietà idMachine.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setIdMachine(String value) {
            this.idMachine = value;
        }

        /**
         * Recupera il valore della proprietà dsName.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getDsName() {
            return dsName;
        }

        /**
         * Imposta il valore della proprietà dsName.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setDsName(String value) {
            this.dsName = value;
        }

        /**
         * Recupera il valore della proprietà dsIPAddress.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getDsIPAddress() {
            return dsIPAddress;
        }

        /**
         * Imposta il valore della proprietà dsIPAddress.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setDsIPAddress(String value) {
            this.dsIPAddress = value;
        }

        /**
         * Recupera il valore della proprietà dsDescription.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getDsDescription() {
            return dsDescription;
        }

        /**
         * Imposta il valore della proprietà dsDescription.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setDsDescription(String value) {
            this.dsDescription = value;
        }

        /**
         * Recupera il valore della proprietà dsSystem.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getDsSystem() {
            return dsSystem;
        }

        /**
         * Imposta il valore della proprietà dsSystem.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setDsSystem(String value) {
            this.dsSystem = value;
        }

        /**
         * Recupera il valore della proprietà dsType.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getDsType() {
            return dsType;
        }

        /**
         * Imposta il valore della proprietà dsType.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setDsType(String value) {
            this.dsType = value;
        }

        /**
         * Recupera il valore della proprietà dsDomain.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getDsDomain() {
            return dsDomain;
        }

        /**
         * Imposta il valore della proprietà dsDomain.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setDsDomain(String value) {
            this.dsDomain = value;
        }

        /**
         * Recupera il valore della proprietà idDomain.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getIdDomain() {
            return idDomain;
        }

        /**
         * Imposta il valore della proprietà idDomain.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setIdDomain(String value) {
            this.idDomain = value;
        }

        /**
         * Recupera il valore della proprietà idOffice.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getIdOffice() {
            return idOffice;
        }

        /**
         * Imposta il valore della proprietà idOffice.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setIdOffice(String value) {
            this.idOffice = value;
        }

        /**
         * Recupera il valore della proprietà dsOffice.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getDsOffice() {
            return dsOffice;
        }

        /**
         * Imposta il valore della proprietà dsOffice.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setDsOffice(String value) {
            this.dsOffice = value;
        }

        /**
         * Recupera il valore della proprietà dsOfficeInternal.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getDsOfficeInternal() {
            return dsOfficeInternal;
        }

        /**
         * Imposta il valore della proprietà dsOfficeInternal.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setDsOfficeInternal(String value) {
            this.dsOfficeInternal = value;
        }

        /**
         * Recupera il valore della proprietà dsApplicationName.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getDsApplicationName() {
            return dsApplicationName;
        }

        /**
         * Imposta il valore della proprietà dsApplicationName.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setDsApplicationName(String value) {
            this.dsApplicationName = value;
        }

        /**
         * Recupera il valore della proprietà esito.
         * 
         * @return
         *     possible object is
         *     {@link Integer }
         *     
         */
        public Integer getEsito() {
            return esito;
        }

        /**
         * Imposta il valore della proprietà esito.
         * 
         * @param value
         *     allowed object is
         *     {@link Integer }
         *     
         */
        public void setEsito(Integer value) {
            this.esito = value;
        }

        /**
         * Recupera il valore della proprietà errorMessage.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getErrorMessage() {
            return errorMessage;
        }

        /**
         * Imposta il valore della proprietà errorMessage.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setErrorMessage(String value) {
            this.errorMessage = value;
        }

    }

}
