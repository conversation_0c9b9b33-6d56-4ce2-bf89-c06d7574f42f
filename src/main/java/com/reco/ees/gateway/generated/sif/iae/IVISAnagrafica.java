//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package com.reco.ees.gateway.generated.sif.iae;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per IVISAnagrafica complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="IVISAnagrafica">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="nominativo" type="{http://www.interno.it/schengen/vis/webservice/common}NominativoType" minOccurs="0"/>
 *         <element name="data-di-nascita" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="sesso" type="{http://www.interno.it/schengen/vis/webservice/common}SessoType"/>
 *         <element name="nazionalita" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="nazione-di-nascita" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="luogo-di-nascita" type="{http://www.interno.it/schengen/vis/webservice/common}IVISTestoTraslitterato" minOccurs="0"/>
 *         <element name="padre" type="{http://www.interno.it/schengen/vis/webservice/common}NominativoType" minOccurs="0"/>
 *         <element name="madre" type="{http://www.interno.it/schengen/vis/webservice/common}NominativoType" minOccurs="0"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "IVISAnagrafica", namespace = "http://www.interno.it/schengen/vis/webservice/common", propOrder = {
    "nominativo",
    "dataDiNascita",
    "sesso",
    "nazionalita",
    "nazioneDiNascita",
    "luogoDiNascita",
    "padre",
    "madre"
})
public class IVISAnagrafica {

    protected NominativoType nominativo;
    @XmlElement(name = "data-di-nascita")
    protected String dataDiNascita;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected SessoType sesso;
    protected String nazionalita;
    @XmlElement(name = "nazione-di-nascita")
    protected String nazioneDiNascita;
    @XmlElement(name = "luogo-di-nascita")
    protected IVISTestoTraslitterato luogoDiNascita;
    protected NominativoType padre;
    protected NominativoType madre;

    /**
     * Recupera il valore della proprietà nominativo.
     * 
     * @return
     *     possible object is
     *     {@link NominativoType }
     *     
     */
    public NominativoType getNominativo() {
        return nominativo;
    }

    /**
     * Imposta il valore della proprietà nominativo.
     * 
     * @param value
     *     allowed object is
     *     {@link NominativoType }
     *     
     */
    public void setNominativo(NominativoType value) {
        this.nominativo = value;
    }

    /**
     * Recupera il valore della proprietà dataDiNascita.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDataDiNascita() {
        return dataDiNascita;
    }

    /**
     * Imposta il valore della proprietà dataDiNascita.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDataDiNascita(String value) {
        this.dataDiNascita = value;
    }

    /**
     * Recupera il valore della proprietà sesso.
     * 
     * @return
     *     possible object is
     *     {@link SessoType }
     *     
     */
    public SessoType getSesso() {
        return sesso;
    }

    /**
     * Imposta il valore della proprietà sesso.
     * 
     * @param value
     *     allowed object is
     *     {@link SessoType }
     *     
     */
    public void setSesso(SessoType value) {
        this.sesso = value;
    }

    /**
     * Recupera il valore della proprietà nazionalita.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNazionalita() {
        return nazionalita;
    }

    /**
     * Imposta il valore della proprietà nazionalita.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNazionalita(String value) {
        this.nazionalita = value;
    }

    /**
     * Recupera il valore della proprietà nazioneDiNascita.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNazioneDiNascita() {
        return nazioneDiNascita;
    }

    /**
     * Imposta il valore della proprietà nazioneDiNascita.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNazioneDiNascita(String value) {
        this.nazioneDiNascita = value;
    }

    /**
     * Recupera il valore della proprietà luogoDiNascita.
     * 
     * @return
     *     possible object is
     *     {@link IVISTestoTraslitterato }
     *     
     */
    public IVISTestoTraslitterato getLuogoDiNascita() {
        return luogoDiNascita;
    }

    /**
     * Imposta il valore della proprietà luogoDiNascita.
     * 
     * @param value
     *     allowed object is
     *     {@link IVISTestoTraslitterato }
     *     
     */
    public void setLuogoDiNascita(IVISTestoTraslitterato value) {
        this.luogoDiNascita = value;
    }

    /**
     * Recupera il valore della proprietà padre.
     * 
     * @return
     *     possible object is
     *     {@link NominativoType }
     *     
     */
    public NominativoType getPadre() {
        return padre;
    }

    /**
     * Imposta il valore della proprietà padre.
     * 
     * @param value
     *     allowed object is
     *     {@link NominativoType }
     *     
     */
    public void setPadre(NominativoType value) {
        this.padre = value;
    }

    /**
     * Recupera il valore della proprietà madre.
     * 
     * @return
     *     possible object is
     *     {@link NominativoType }
     *     
     */
    public NominativoType getMadre() {
        return madre;
    }

    /**
     * Imposta il valore della proprietà madre.
     * 
     * @param value
     *     allowed object is
     *     {@link NominativoType }
     *     
     */
    public void setMadre(NominativoType value) {
        this.madre = value;
    }

}
