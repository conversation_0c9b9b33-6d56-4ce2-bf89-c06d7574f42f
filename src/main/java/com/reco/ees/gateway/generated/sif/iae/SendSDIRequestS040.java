//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package com.reco.ees.gateway.generated.sif.iae;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per anonymous complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType>
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="flagsedicente" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         <element name="cognome" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="nome" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="dataNascita" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="indicatoreDataNascita" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="luogoNascita" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="provinciaNascita" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="codiceLuogoNascita" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="codIso3LuogoNascita" minOccurs="0">
 *           <simpleType>
 *             <restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               <maxLength value="3"/>
 *             </restriction>
 *           </simpleType>
 *         </element>
 *         <element name="tipoDocumento" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="numeroDocumento" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="luogoRilascioDocumento" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="provinciaRilascioDocumento" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="codiceLuogoRilascioDocumento" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="codIso3LuogoRilascioDocumento" minOccurs="0">
 *           <simpleType>
 *             <restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               <maxLength value="3"/>
 *             </restriction>
 *           </simpleType>
 *         </element>
 *         <element name="username" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="password" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="note" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="myService" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="desFinalita" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="pattuglia" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="tipoLuogoControllo" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="descrizioneLuogoControllo" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="codIso3LuogoControllo" minOccurs="0">
 *           <simpleType>
 *             <restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               <maxLength value="3"/>
 *             </restriction>
 *           </simpleType>
 *         </element>
 *         <element name="siglaProvinciaControllo" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="indirizzo" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="targa" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="tipotarga" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="telaio" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="nazionetarga" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="codIso3NazioneTarga" minOccurs="0">
 *           <simpleType>
 *             <restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               <maxLength value="3"/>
 *             </restriction>
 *           </simpleType>
 *         </element>
 *         <element name="targarip" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="targaRim" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="nazioneTargaRim" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="codIso3NazioneTargaRim" minOccurs="0">
 *           <simpleType>
 *             <restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               <maxLength value="3"/>
 *             </restriction>
 *           </simpleType>
 *         </element>
 *         <element name="telaioRim" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="datacontrollo" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="oracontrollo" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="dataControllo" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="oraControllo" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="idMachine" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="sintesi" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
 *         <element name="idTransaction" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "flagsedicente",
    "cognome",
    "nome",
    "dataNascita",
    "indicatoreDataNascita",
    "luogoNascita",
    "provinciaNascita",
    "codiceLuogoNascita",
    "codIso3LuogoNascita",
    "tipoDocumento",
    "numeroDocumento",
    "luogoRilascioDocumento",
    "provinciaRilascioDocumento",
    "codiceLuogoRilascioDocumento",
    "codIso3LuogoRilascioDocumento",
    "username",
    "password",
    "note",
    "myService",
    "desFinalita",
    "pattuglia",
    "tipoLuogoControllo",
    "descrizioneLuogoControllo",
    "codIso3LuogoControllo",
    "siglaProvinciaControllo",
    "indirizzo",
    "targa",
    "tipotarga",
    "telaio",
    "nazionetarga",
    "codIso3NazioneTarga",
    "targarip",
    "targaRim",
    "nazioneTargaRim",
    "codIso3NazioneTargaRim",
    "telaioRim",
    "datacontrollo",
    "oracontrollo",
    "dataControllo",
    "oraControllo",
    "idMachine",
    "sintesi",
    "idTransaction"
})
@XmlRootElement(name = "SendSDIRequestS040")
public class SendSDIRequestS040 {

    protected boolean flagsedicente;
    protected String cognome;
    protected String nome;
    protected String dataNascita;
    protected String indicatoreDataNascita;
    protected String luogoNascita;
    protected String provinciaNascita;
    protected String codiceLuogoNascita;
    protected String codIso3LuogoNascita;
    protected String tipoDocumento;
    protected String numeroDocumento;
    protected String luogoRilascioDocumento;
    protected String provinciaRilascioDocumento;
    protected String codiceLuogoRilascioDocumento;
    protected String codIso3LuogoRilascioDocumento;
    protected String username;
    protected String password;
    protected String note;
    protected String myService;
    protected String desFinalita;
    protected String pattuglia;
    protected String tipoLuogoControllo;
    protected String descrizioneLuogoControllo;
    protected String codIso3LuogoControllo;
    protected String siglaProvinciaControllo;
    protected String indirizzo;
    protected String targa;
    protected String tipotarga;
    protected String telaio;
    protected String nazionetarga;
    protected String codIso3NazioneTarga;
    protected String targarip;
    protected String targaRim;
    protected String nazioneTargaRim;
    protected String codIso3NazioneTargaRim;
    protected String telaioRim;
    protected String datacontrollo;
    protected String oracontrollo;
    protected String dataControllo;
    protected String oraControllo;
    protected String idMachine;
    protected Boolean sintesi;
    protected String idTransaction;

    /**
     * Recupera il valore della proprietà flagsedicente.
     * 
     */
    public boolean isFlagsedicente() {
        return flagsedicente;
    }

    /**
     * Imposta il valore della proprietà flagsedicente.
     * 
     */
    public void setFlagsedicente(boolean value) {
        this.flagsedicente = value;
    }

    /**
     * Recupera il valore della proprietà cognome.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCognome() {
        return cognome;
    }

    /**
     * Imposta il valore della proprietà cognome.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCognome(String value) {
        this.cognome = value;
    }

    /**
     * Recupera il valore della proprietà nome.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNome() {
        return nome;
    }

    /**
     * Imposta il valore della proprietà nome.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNome(String value) {
        this.nome = value;
    }

    /**
     * Recupera il valore della proprietà dataNascita.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDataNascita() {
        return dataNascita;
    }

    /**
     * Imposta il valore della proprietà dataNascita.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDataNascita(String value) {
        this.dataNascita = value;
    }

    /**
     * Recupera il valore della proprietà indicatoreDataNascita.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIndicatoreDataNascita() {
        return indicatoreDataNascita;
    }

    /**
     * Imposta il valore della proprietà indicatoreDataNascita.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIndicatoreDataNascita(String value) {
        this.indicatoreDataNascita = value;
    }

    /**
     * Recupera il valore della proprietà luogoNascita.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLuogoNascita() {
        return luogoNascita;
    }

    /**
     * Imposta il valore della proprietà luogoNascita.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLuogoNascita(String value) {
        this.luogoNascita = value;
    }

    /**
     * Recupera il valore della proprietà provinciaNascita.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getProvinciaNascita() {
        return provinciaNascita;
    }

    /**
     * Imposta il valore della proprietà provinciaNascita.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setProvinciaNascita(String value) {
        this.provinciaNascita = value;
    }

    /**
     * Recupera il valore della proprietà codiceLuogoNascita.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCodiceLuogoNascita() {
        return codiceLuogoNascita;
    }

    /**
     * Imposta il valore della proprietà codiceLuogoNascita.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCodiceLuogoNascita(String value) {
        this.codiceLuogoNascita = value;
    }

    /**
     * Recupera il valore della proprietà codIso3LuogoNascita.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCodIso3LuogoNascita() {
        return codIso3LuogoNascita;
    }

    /**
     * Imposta il valore della proprietà codIso3LuogoNascita.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCodIso3LuogoNascita(String value) {
        this.codIso3LuogoNascita = value;
    }

    /**
     * Recupera il valore della proprietà tipoDocumento.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTipoDocumento() {
        return tipoDocumento;
    }

    /**
     * Imposta il valore della proprietà tipoDocumento.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTipoDocumento(String value) {
        this.tipoDocumento = value;
    }

    /**
     * Recupera il valore della proprietà numeroDocumento.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNumeroDocumento() {
        return numeroDocumento;
    }

    /**
     * Imposta il valore della proprietà numeroDocumento.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNumeroDocumento(String value) {
        this.numeroDocumento = value;
    }

    /**
     * Recupera il valore della proprietà luogoRilascioDocumento.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLuogoRilascioDocumento() {
        return luogoRilascioDocumento;
    }

    /**
     * Imposta il valore della proprietà luogoRilascioDocumento.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLuogoRilascioDocumento(String value) {
        this.luogoRilascioDocumento = value;
    }

    /**
     * Recupera il valore della proprietà provinciaRilascioDocumento.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getProvinciaRilascioDocumento() {
        return provinciaRilascioDocumento;
    }

    /**
     * Imposta il valore della proprietà provinciaRilascioDocumento.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setProvinciaRilascioDocumento(String value) {
        this.provinciaRilascioDocumento = value;
    }

    /**
     * Recupera il valore della proprietà codiceLuogoRilascioDocumento.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCodiceLuogoRilascioDocumento() {
        return codiceLuogoRilascioDocumento;
    }

    /**
     * Imposta il valore della proprietà codiceLuogoRilascioDocumento.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCodiceLuogoRilascioDocumento(String value) {
        this.codiceLuogoRilascioDocumento = value;
    }

    /**
     * Recupera il valore della proprietà codIso3LuogoRilascioDocumento.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCodIso3LuogoRilascioDocumento() {
        return codIso3LuogoRilascioDocumento;
    }

    /**
     * Imposta il valore della proprietà codIso3LuogoRilascioDocumento.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCodIso3LuogoRilascioDocumento(String value) {
        this.codIso3LuogoRilascioDocumento = value;
    }

    /**
     * Recupera il valore della proprietà username.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUsername() {
        return username;
    }

    /**
     * Imposta il valore della proprietà username.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUsername(String value) {
        this.username = value;
    }

    /**
     * Recupera il valore della proprietà password.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPassword() {
        return password;
    }

    /**
     * Imposta il valore della proprietà password.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPassword(String value) {
        this.password = value;
    }

    /**
     * Recupera il valore della proprietà note.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNote() {
        return note;
    }

    /**
     * Imposta il valore della proprietà note.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNote(String value) {
        this.note = value;
    }

    /**
     * Recupera il valore della proprietà myService.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMyService() {
        return myService;
    }

    /**
     * Imposta il valore della proprietà myService.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMyService(String value) {
        this.myService = value;
    }

    /**
     * Recupera il valore della proprietà desFinalita.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDesFinalita() {
        return desFinalita;
    }

    /**
     * Imposta il valore della proprietà desFinalita.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDesFinalita(String value) {
        this.desFinalita = value;
    }

    /**
     * Recupera il valore della proprietà pattuglia.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPattuglia() {
        return pattuglia;
    }

    /**
     * Imposta il valore della proprietà pattuglia.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPattuglia(String value) {
        this.pattuglia = value;
    }

    /**
     * Recupera il valore della proprietà tipoLuogoControllo.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTipoLuogoControllo() {
        return tipoLuogoControllo;
    }

    /**
     * Imposta il valore della proprietà tipoLuogoControllo.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTipoLuogoControllo(String value) {
        this.tipoLuogoControllo = value;
    }

    /**
     * Recupera il valore della proprietà descrizioneLuogoControllo.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDescrizioneLuogoControllo() {
        return descrizioneLuogoControllo;
    }

    /**
     * Imposta il valore della proprietà descrizioneLuogoControllo.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDescrizioneLuogoControllo(String value) {
        this.descrizioneLuogoControllo = value;
    }

    /**
     * Recupera il valore della proprietà codIso3LuogoControllo.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCodIso3LuogoControllo() {
        return codIso3LuogoControllo;
    }

    /**
     * Imposta il valore della proprietà codIso3LuogoControllo.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCodIso3LuogoControllo(String value) {
        this.codIso3LuogoControllo = value;
    }

    /**
     * Recupera il valore della proprietà siglaProvinciaControllo.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSiglaProvinciaControllo() {
        return siglaProvinciaControllo;
    }

    /**
     * Imposta il valore della proprietà siglaProvinciaControllo.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSiglaProvinciaControllo(String value) {
        this.siglaProvinciaControllo = value;
    }

    /**
     * Recupera il valore della proprietà indirizzo.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIndirizzo() {
        return indirizzo;
    }

    /**
     * Imposta il valore della proprietà indirizzo.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIndirizzo(String value) {
        this.indirizzo = value;
    }

    /**
     * Recupera il valore della proprietà targa.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTarga() {
        return targa;
    }

    /**
     * Imposta il valore della proprietà targa.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTarga(String value) {
        this.targa = value;
    }

    /**
     * Recupera il valore della proprietà tipotarga.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTipotarga() {
        return tipotarga;
    }

    /**
     * Imposta il valore della proprietà tipotarga.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTipotarga(String value) {
        this.tipotarga = value;
    }

    /**
     * Recupera il valore della proprietà telaio.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTelaio() {
        return telaio;
    }

    /**
     * Imposta il valore della proprietà telaio.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTelaio(String value) {
        this.telaio = value;
    }

    /**
     * Recupera il valore della proprietà nazionetarga.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNazionetarga() {
        return nazionetarga;
    }

    /**
     * Imposta il valore della proprietà nazionetarga.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNazionetarga(String value) {
        this.nazionetarga = value;
    }

    /**
     * Recupera il valore della proprietà codIso3NazioneTarga.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCodIso3NazioneTarga() {
        return codIso3NazioneTarga;
    }

    /**
     * Imposta il valore della proprietà codIso3NazioneTarga.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCodIso3NazioneTarga(String value) {
        this.codIso3NazioneTarga = value;
    }

    /**
     * Recupera il valore della proprietà targarip.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTargarip() {
        return targarip;
    }

    /**
     * Imposta il valore della proprietà targarip.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTargarip(String value) {
        this.targarip = value;
    }

    /**
     * Recupera il valore della proprietà targaRim.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTargaRim() {
        return targaRim;
    }

    /**
     * Imposta il valore della proprietà targaRim.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTargaRim(String value) {
        this.targaRim = value;
    }

    /**
     * Recupera il valore della proprietà nazioneTargaRim.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNazioneTargaRim() {
        return nazioneTargaRim;
    }

    /**
     * Imposta il valore della proprietà nazioneTargaRim.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNazioneTargaRim(String value) {
        this.nazioneTargaRim = value;
    }

    /**
     * Recupera il valore della proprietà codIso3NazioneTargaRim.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCodIso3NazioneTargaRim() {
        return codIso3NazioneTargaRim;
    }

    /**
     * Imposta il valore della proprietà codIso3NazioneTargaRim.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCodIso3NazioneTargaRim(String value) {
        this.codIso3NazioneTargaRim = value;
    }

    /**
     * Recupera il valore della proprietà telaioRim.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTelaioRim() {
        return telaioRim;
    }

    /**
     * Imposta il valore della proprietà telaioRim.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTelaioRim(String value) {
        this.telaioRim = value;
    }

    /**
     * Recupera il valore della proprietà datacontrollo.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDatacontrollo() {
        return datacontrollo;
    }

    /**
     * Imposta il valore della proprietà datacontrollo.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDatacontrollo(String value) {
        this.datacontrollo = value;
    }

    /**
     * Recupera il valore della proprietà oracontrollo.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getOracontrollo() {
        return oracontrollo;
    }

    /**
     * Imposta il valore della proprietà oracontrollo.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setOracontrollo(String value) {
        this.oracontrollo = value;
    }

    /**
     * Recupera il valore della proprietà dataControllo.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDataControllo() {
        return dataControllo;
    }

    /**
     * Imposta il valore della proprietà dataControllo.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDataControllo(String value) {
        this.dataControllo = value;
    }

    /**
     * Recupera il valore della proprietà oraControllo.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getOraControllo() {
        return oraControllo;
    }

    /**
     * Imposta il valore della proprietà oraControllo.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setOraControllo(String value) {
        this.oraControllo = value;
    }

    /**
     * Recupera il valore della proprietà idMachine.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIdMachine() {
        return idMachine;
    }

    /**
     * Imposta il valore della proprietà idMachine.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIdMachine(String value) {
        this.idMachine = value;
    }

    /**
     * Recupera il valore della proprietà sintesi.
     * 
     * @return
     *     possible object is
     *     {@link Boolean }
     *     
     */
    public Boolean isSintesi() {
        return sintesi;
    }

    /**
     * Imposta il valore della proprietà sintesi.
     * 
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *     
     */
    public void setSintesi(Boolean value) {
        this.sintesi = value;
    }

    /**
     * Recupera il valore della proprietà idTransaction.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIdTransaction() {
        return idTransaction;
    }

    /**
     * Imposta il valore della proprietà idTransaction.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIdTransaction(String value) {
        this.idTransaction = value;
    }

}
