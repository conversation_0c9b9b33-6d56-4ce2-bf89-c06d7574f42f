//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package com.reco.ees.gateway.generated.sif.services;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per anonymous complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType>
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="idAccount" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="application" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="idContext" type="{http://microsoft.com/wsdl/types/}guid"/>
 *         <element name="newRoles" type="{http://tempuri.org/}ArrayOfAuthorizationRole" minOccurs="0"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "idAccount",
    "application",
    "idContext",
    "newRoles"
})
@XmlRootElement(name = "UpdateAccountRoles")
public class UpdateAccountRoles {

    protected String idAccount;
    protected String application;
    @XmlElement(required = true)
    protected String idContext;
    protected ArrayOfAuthorizationRole newRoles;

    /**
     * Recupera il valore della proprietà idAccount.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIdAccount() {
        return idAccount;
    }

    /**
     * Imposta il valore della proprietà idAccount.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIdAccount(String value) {
        this.idAccount = value;
    }

    /**
     * Recupera il valore della proprietà application.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getApplication() {
        return application;
    }

    /**
     * Imposta il valore della proprietà application.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setApplication(String value) {
        this.application = value;
    }

    /**
     * Recupera il valore della proprietà idContext.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIdContext() {
        return idContext;
    }

    /**
     * Imposta il valore della proprietà idContext.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIdContext(String value) {
        this.idContext = value;
    }

    /**
     * Recupera il valore della proprietà newRoles.
     * 
     * @return
     *     possible object is
     *     {@link ArrayOfAuthorizationRole }
     *     
     */
    public ArrayOfAuthorizationRole getNewRoles() {
        return newRoles;
    }

    /**
     * Imposta il valore della proprietà newRoles.
     * 
     * @param value
     *     allowed object is
     *     {@link ArrayOfAuthorizationRole }
     *     
     */
    public void setNewRoles(ArrayOfAuthorizationRole value) {
        this.newRoles = value;
    }

}
