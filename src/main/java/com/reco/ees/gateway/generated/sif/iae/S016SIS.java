//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package com.reco.ees.gateway.generated.sif.iae;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per S016SIS complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="S016SIS">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="idSchengen" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="descrizioneAzioneSchengen" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="nDoc" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="desCategory" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="desNationality" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "S016SIS", namespace = "http://interrogazione.iae.sdi.ibm.it", propOrder = {
    "idSchengen",
    "descrizioneAzioneSchengen",
    "nDoc",
    "desCategory",
    "desNationality"
})
public class S016SIS {

    protected String idSchengen;
    protected String descrizioneAzioneSchengen;
    protected String nDoc;
    protected String desCategory;
    protected String desNationality;

    /**
     * Recupera il valore della proprietà idSchengen.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIdSchengen() {
        return idSchengen;
    }

    /**
     * Imposta il valore della proprietà idSchengen.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIdSchengen(String value) {
        this.idSchengen = value;
    }

    /**
     * Recupera il valore della proprietà descrizioneAzioneSchengen.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDescrizioneAzioneSchengen() {
        return descrizioneAzioneSchengen;
    }

    /**
     * Imposta il valore della proprietà descrizioneAzioneSchengen.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDescrizioneAzioneSchengen(String value) {
        this.descrizioneAzioneSchengen = value;
    }

    /**
     * Recupera il valore della proprietà nDoc.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNDoc() {
        return nDoc;
    }

    /**
     * Imposta il valore della proprietà nDoc.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNDoc(String value) {
        this.nDoc = value;
    }

    /**
     * Recupera il valore della proprietà desCategory.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDesCategory() {
        return desCategory;
    }

    /**
     * Imposta il valore della proprietà desCategory.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDesCategory(String value) {
        this.desCategory = value;
    }

    /**
     * Recupera il valore della proprietà desNationality.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDesNationality() {
        return desNationality;
    }

    /**
     * Imposta il valore della proprietà desNationality.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDesNationality(String value) {
        this.desNationality = value;
    }

}
