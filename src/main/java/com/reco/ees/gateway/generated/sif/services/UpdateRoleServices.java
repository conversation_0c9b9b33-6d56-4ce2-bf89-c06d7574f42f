//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package com.reco.ees.gateway.generated.sif.services;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per anonymous complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType>
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="dsRole" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="services" type="{http://tempuri.org/}ArrayOfAnyType" minOccurs="0"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "dsRole",
    "services"
})
@XmlRootElement(name = "UpdateRoleServices")
public class UpdateRoleServices {

    protected String dsRole;
    protected ArrayOfAnyType services;

    /**
     * Recupera il valore della proprietà dsRole.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDsRole() {
        return dsRole;
    }

    /**
     * Imposta il valore della proprietà dsRole.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDsRole(String value) {
        this.dsRole = value;
    }

    /**
     * Recupera il valore della proprietà services.
     * 
     * @return
     *     possible object is
     *     {@link ArrayOfAnyType }
     *     
     */
    public ArrayOfAnyType getServices() {
        return services;
    }

    /**
     * Imposta il valore della proprietà services.
     * 
     * @param value
     *     allowed object is
     *     {@link ArrayOfAnyType }
     *     
     */
    public void setServices(ArrayOfAnyType value) {
        this.services = value;
    }

}
