//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package com.reco.ees.gateway.generated.sif.services;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per FieldMapping complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="FieldMapping">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="SourceField" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="DestinationField" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "FieldMapping", propOrder = {
    "sourceField",
    "destinationField"
})
public class FieldMapping {

    @XmlElement(name = "SourceField")
    protected String sourceField;
    @XmlElement(name = "DestinationField")
    protected String destinationField;

    /**
     * Recupera il valore della proprietà sourceField.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSourceField() {
        return sourceField;
    }

    /**
     * Imposta il valore della proprietà sourceField.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSourceField(String value) {
        this.sourceField = value;
    }

    /**
     * Recupera il valore della proprietà destinationField.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDestinationField() {
        return destinationField;
    }

    /**
     * Imposta il valore della proprietà destinationField.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDestinationField(String value) {
        this.destinationField = value;
    }

}
