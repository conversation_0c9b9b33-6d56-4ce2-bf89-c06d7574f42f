//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package com.reco.ees.gateway.generated.npkd.certificates;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per anonymous complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType>
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="GetISCertResult" type="{http://npkd.cen.it/NPKD/}Esito" minOccurs="0"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "getISCertResult"
})
@XmlRootElement(name = "GetISCertResponse")
public class GetISCertResponse {

    @XmlElement(name = "GetISCertResult")
    protected Esito getISCertResult;

    /**
     * Recupera il valore della proprietà getISCertResult.
     * 
     * @return
     *     possible object is
     *     {@link Esito }
     *     
     */
    public Esito getGetISCertResult() {
        return getISCertResult;
    }

    /**
     * Imposta il valore della proprietà getISCertResult.
     * 
     * @param value
     *     allowed object is
     *     {@link Esito }
     *     
     */
    public void setGetISCertResult(Esito value) {
        this.getISCertResult = value;
    }

}
