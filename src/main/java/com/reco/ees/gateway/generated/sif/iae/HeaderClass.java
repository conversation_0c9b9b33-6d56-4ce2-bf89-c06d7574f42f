//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package com.reco.ees.gateway.generated.sif.iae;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per HeaderClass complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="HeaderClass">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="OrganizationUnit" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="User" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="System" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="IdMachine" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "HeaderClass", propOrder = {
    "organizationUnit",
    "user",
    "system",
    "idMachine"
})
public class HeaderClass {

    @XmlElement(name = "OrganizationUnit")
    protected String organizationUnit;
    @XmlElement(name = "User")
    protected String user;
    @XmlElement(name = "System")
    protected String system;
    @XmlElement(name = "IdMachine")
    protected String idMachine;

    /**
     * Recupera il valore della proprietà organizationUnit.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getOrganizationUnit() {
        return organizationUnit;
    }

    /**
     * Imposta il valore della proprietà organizationUnit.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setOrganizationUnit(String value) {
        this.organizationUnit = value;
    }

    /**
     * Recupera il valore della proprietà user.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUser() {
        return user;
    }

    /**
     * Imposta il valore della proprietà user.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUser(String value) {
        this.user = value;
    }

    /**
     * Recupera il valore della proprietà system.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSystem() {
        return system;
    }

    /**
     * Imposta il valore della proprietà system.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSystem(String value) {
        this.system = value;
    }

    /**
     * Recupera il valore della proprietà idMachine.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIdMachine() {
        return idMachine;
    }

    /**
     * Imposta il valore della proprietà idMachine.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIdMachine(String value) {
        this.idMachine = value;
    }

}
