//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package com.reco.ees.gateway.generated.sif.iae;

import javax.xml.datatype.XMLGregorianCalendar;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per anonymous complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType>
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="flagsedicente" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         <element name="cognome" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="nome" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="dataNascita" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         <element name="indicatoreDataNascita" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="luogoNascita" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="provinciaNascita" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="codiceLuogoNascita" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="tipoDocumento" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="numeroDocumento" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="luogoRilascioDocumento" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="provinciaRilascioDocumento" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="codiceLuogoRilascioDocumento" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="username" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="password" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="note" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="myService" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="desFinalita" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="pattuglia" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="tipoLuogoControllo" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="descrizioneLuogoControllo" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="siglaProvinciaControllo" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="indirizzo" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="targa" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="tipotarga" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="telaio" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="nazionetarga" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "flagsedicente",
    "cognome",
    "nome",
    "dataNascita",
    "indicatoreDataNascita",
    "luogoNascita",
    "provinciaNascita",
    "codiceLuogoNascita",
    "tipoDocumento",
    "numeroDocumento",
    "luogoRilascioDocumento",
    "provinciaRilascioDocumento",
    "codiceLuogoRilascioDocumento",
    "username",
    "password",
    "note",
    "myService",
    "desFinalita",
    "pattuglia",
    "tipoLuogoControllo",
    "descrizioneLuogoControllo",
    "siglaProvinciaControllo",
    "indirizzo",
    "targa",
    "tipotarga",
    "telaio",
    "nazionetarga"
})
@XmlRootElement(name = "SendSDIRequest")
public class SendSDIRequest {

    protected boolean flagsedicente;
    protected String cognome;
    protected String nome;
    @XmlElement(required = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar dataNascita;
    protected String indicatoreDataNascita;
    protected String luogoNascita;
    protected String provinciaNascita;
    protected String codiceLuogoNascita;
    protected String tipoDocumento;
    protected String numeroDocumento;
    protected String luogoRilascioDocumento;
    protected String provinciaRilascioDocumento;
    protected String codiceLuogoRilascioDocumento;
    protected String username;
    protected String password;
    protected String note;
    protected String myService;
    protected String desFinalita;
    protected String pattuglia;
    protected String tipoLuogoControllo;
    protected String descrizioneLuogoControllo;
    protected String siglaProvinciaControllo;
    protected String indirizzo;
    protected String targa;
    protected String tipotarga;
    protected String telaio;
    protected String nazionetarga;

    /**
     * Recupera il valore della proprietà flagsedicente.
     * 
     */
    public boolean isFlagsedicente() {
        return flagsedicente;
    }

    /**
     * Imposta il valore della proprietà flagsedicente.
     * 
     */
    public void setFlagsedicente(boolean value) {
        this.flagsedicente = value;
    }

    /**
     * Recupera il valore della proprietà cognome.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCognome() {
        return cognome;
    }

    /**
     * Imposta il valore della proprietà cognome.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCognome(String value) {
        this.cognome = value;
    }

    /**
     * Recupera il valore della proprietà nome.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNome() {
        return nome;
    }

    /**
     * Imposta il valore della proprietà nome.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNome(String value) {
        this.nome = value;
    }

    /**
     * Recupera il valore della proprietà dataNascita.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getDataNascita() {
        return dataNascita;
    }

    /**
     * Imposta il valore della proprietà dataNascita.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setDataNascita(XMLGregorianCalendar value) {
        this.dataNascita = value;
    }

    /**
     * Recupera il valore della proprietà indicatoreDataNascita.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIndicatoreDataNascita() {
        return indicatoreDataNascita;
    }

    /**
     * Imposta il valore della proprietà indicatoreDataNascita.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIndicatoreDataNascita(String value) {
        this.indicatoreDataNascita = value;
    }

    /**
     * Recupera il valore della proprietà luogoNascita.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLuogoNascita() {
        return luogoNascita;
    }

    /**
     * Imposta il valore della proprietà luogoNascita.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLuogoNascita(String value) {
        this.luogoNascita = value;
    }

    /**
     * Recupera il valore della proprietà provinciaNascita.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getProvinciaNascita() {
        return provinciaNascita;
    }

    /**
     * Imposta il valore della proprietà provinciaNascita.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setProvinciaNascita(String value) {
        this.provinciaNascita = value;
    }

    /**
     * Recupera il valore della proprietà codiceLuogoNascita.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCodiceLuogoNascita() {
        return codiceLuogoNascita;
    }

    /**
     * Imposta il valore della proprietà codiceLuogoNascita.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCodiceLuogoNascita(String value) {
        this.codiceLuogoNascita = value;
    }

    /**
     * Recupera il valore della proprietà tipoDocumento.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTipoDocumento() {
        return tipoDocumento;
    }

    /**
     * Imposta il valore della proprietà tipoDocumento.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTipoDocumento(String value) {
        this.tipoDocumento = value;
    }

    /**
     * Recupera il valore della proprietà numeroDocumento.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNumeroDocumento() {
        return numeroDocumento;
    }

    /**
     * Imposta il valore della proprietà numeroDocumento.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNumeroDocumento(String value) {
        this.numeroDocumento = value;
    }

    /**
     * Recupera il valore della proprietà luogoRilascioDocumento.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLuogoRilascioDocumento() {
        return luogoRilascioDocumento;
    }

    /**
     * Imposta il valore della proprietà luogoRilascioDocumento.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLuogoRilascioDocumento(String value) {
        this.luogoRilascioDocumento = value;
    }

    /**
     * Recupera il valore della proprietà provinciaRilascioDocumento.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getProvinciaRilascioDocumento() {
        return provinciaRilascioDocumento;
    }

    /**
     * Imposta il valore della proprietà provinciaRilascioDocumento.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setProvinciaRilascioDocumento(String value) {
        this.provinciaRilascioDocumento = value;
    }

    /**
     * Recupera il valore della proprietà codiceLuogoRilascioDocumento.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCodiceLuogoRilascioDocumento() {
        return codiceLuogoRilascioDocumento;
    }

    /**
     * Imposta il valore della proprietà codiceLuogoRilascioDocumento.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCodiceLuogoRilascioDocumento(String value) {
        this.codiceLuogoRilascioDocumento = value;
    }

    /**
     * Recupera il valore della proprietà username.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUsername() {
        return username;
    }

    /**
     * Imposta il valore della proprietà username.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUsername(String value) {
        this.username = value;
    }

    /**
     * Recupera il valore della proprietà password.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPassword() {
        return password;
    }

    /**
     * Imposta il valore della proprietà password.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPassword(String value) {
        this.password = value;
    }

    /**
     * Recupera il valore della proprietà note.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNote() {
        return note;
    }

    /**
     * Imposta il valore della proprietà note.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNote(String value) {
        this.note = value;
    }

    /**
     * Recupera il valore della proprietà myService.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMyService() {
        return myService;
    }

    /**
     * Imposta il valore della proprietà myService.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMyService(String value) {
        this.myService = value;
    }

    /**
     * Recupera il valore della proprietà desFinalita.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDesFinalita() {
        return desFinalita;
    }

    /**
     * Imposta il valore della proprietà desFinalita.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDesFinalita(String value) {
        this.desFinalita = value;
    }

    /**
     * Recupera il valore della proprietà pattuglia.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPattuglia() {
        return pattuglia;
    }

    /**
     * Imposta il valore della proprietà pattuglia.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPattuglia(String value) {
        this.pattuglia = value;
    }

    /**
     * Recupera il valore della proprietà tipoLuogoControllo.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTipoLuogoControllo() {
        return tipoLuogoControllo;
    }

    /**
     * Imposta il valore della proprietà tipoLuogoControllo.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTipoLuogoControllo(String value) {
        this.tipoLuogoControllo = value;
    }

    /**
     * Recupera il valore della proprietà descrizioneLuogoControllo.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDescrizioneLuogoControllo() {
        return descrizioneLuogoControllo;
    }

    /**
     * Imposta il valore della proprietà descrizioneLuogoControllo.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDescrizioneLuogoControllo(String value) {
        this.descrizioneLuogoControllo = value;
    }

    /**
     * Recupera il valore della proprietà siglaProvinciaControllo.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSiglaProvinciaControllo() {
        return siglaProvinciaControllo;
    }

    /**
     * Imposta il valore della proprietà siglaProvinciaControllo.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSiglaProvinciaControllo(String value) {
        this.siglaProvinciaControllo = value;
    }

    /**
     * Recupera il valore della proprietà indirizzo.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIndirizzo() {
        return indirizzo;
    }

    /**
     * Imposta il valore della proprietà indirizzo.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIndirizzo(String value) {
        this.indirizzo = value;
    }

    /**
     * Recupera il valore della proprietà targa.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTarga() {
        return targa;
    }

    /**
     * Imposta il valore della proprietà targa.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTarga(String value) {
        this.targa = value;
    }

    /**
     * Recupera il valore della proprietà tipotarga.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTipotarga() {
        return tipotarga;
    }

    /**
     * Imposta il valore della proprietà tipotarga.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTipotarga(String value) {
        this.tipotarga = value;
    }

    /**
     * Recupera il valore della proprietà telaio.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTelaio() {
        return telaio;
    }

    /**
     * Imposta il valore della proprietà telaio.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTelaio(String value) {
        this.telaio = value;
    }

    /**
     * Recupera il valore della proprietà nazionetarga.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNazionetarga() {
        return nazionetarga;
    }

    /**
     * Imposta il valore della proprietà nazionetarga.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNazionetarga(String value) {
        this.nazionetarga = value;
    }

}
