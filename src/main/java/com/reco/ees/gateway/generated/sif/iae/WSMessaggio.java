//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package com.reco.ees.gateway.generated.sif.iae;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per WSMessaggio complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="WSMessaggio">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="campoInErrore" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         <element name="codiceErrore" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         <element name="messaggio" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         <element name="gravita" type="{http://www.w3.org/2001/XMLSchema}int"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "WSMessaggio", namespace = "http://common.business.sdi.ibm.it", propOrder = {
    "campoInErrore",
    "codiceErrore",
    "messaggio",
    "gravita"
})
public class WSMessaggio {

    @XmlElement(required = true, nillable = true)
    protected String campoInErrore;
    @XmlElement(required = true, nillable = true)
    protected String codiceErrore;
    @XmlElement(required = true, nillable = true)
    protected String messaggio;
    protected int gravita;

    /**
     * Recupera il valore della proprietà campoInErrore.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCampoInErrore() {
        return campoInErrore;
    }

    /**
     * Imposta il valore della proprietà campoInErrore.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCampoInErrore(String value) {
        this.campoInErrore = value;
    }

    /**
     * Recupera il valore della proprietà codiceErrore.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCodiceErrore() {
        return codiceErrore;
    }

    /**
     * Imposta il valore della proprietà codiceErrore.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCodiceErrore(String value) {
        this.codiceErrore = value;
    }

    /**
     * Recupera il valore della proprietà messaggio.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMessaggio() {
        return messaggio;
    }

    /**
     * Imposta il valore della proprietà messaggio.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMessaggio(String value) {
        this.messaggio = value;
    }

    /**
     * Recupera il valore della proprietà gravita.
     * 
     */
    public int getGravita() {
        return gravita;
    }

    /**
     * Imposta il valore della proprietà gravita.
     * 
     */
    public void setGravita(int value) {
        this.gravita = value;
    }

}
