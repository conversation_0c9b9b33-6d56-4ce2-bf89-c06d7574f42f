//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package com.reco.ees.gateway.generated.sif.services;

import javax.xml.datatype.XMLGregorianCalendar;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlSeeAlso;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per AuthorizationIdentityBase complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="AuthorizationIdentityBase">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="Status" type="{http://tempuri.org/}ApplicationIdentityStatus"/>
 *         <element name="DateOfExpire" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         <element name="PasswordExpirationDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         <element name="InitialPassword" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="IsAuthenticated" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         <element name="Password" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="Roles" type="{http://tempuri.org/}ArrayOfAuthorizationRole" minOccurs="0"/>
 *         <element name="User" type="{http://tempuri.org/}User" minOccurs="0"/>
 *         <element name="UserName" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="ApplicationName" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         <element name="CanResetPassword" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         <element name="PasswordNewerExpire" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         <element name="MaxInvalidPasswordAttempts" type="{http://www.w3.org/2001/XMLSchema}int"/>
 *         <element name="CreationDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         <element name="IsLogged" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         <element name="LastActivityDateTime" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         <element name="LastLoginDateTime" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         <element name="LastPasswordChangedDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         <element name="IsExpired" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         <element name="IsLocked" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         <element name="PasswordChangeNeeded" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "AuthorizationIdentityBase", propOrder = {
    "status",
    "dateOfExpire",
    "passwordExpirationDate",
    "initialPassword",
    "isAuthenticated",
    "password",
    "roles",
    "user",
    "userName",
    "applicationName",
    "canResetPassword",
    "passwordNewerExpire",
    "maxInvalidPasswordAttempts",
    "creationDate",
    "isLogged",
    "lastActivityDateTime",
    "lastLoginDateTime",
    "lastPasswordChangedDate",
    "isExpired",
    "isLocked",
    "passwordChangeNeeded"
})
@XmlSeeAlso({
    ServiceIdentity.class
})
public abstract class AuthorizationIdentityBase {

    @XmlElement(name = "Status", required = true)
    @XmlSchemaType(name = "string")
    protected ApplicationIdentityStatus status;
    @XmlElement(name = "DateOfExpire", required = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar dateOfExpire;
    @XmlElement(name = "PasswordExpirationDate", required = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar passwordExpirationDate;
    @XmlElement(name = "InitialPassword")
    protected String initialPassword;
    @XmlElement(name = "IsAuthenticated")
    protected boolean isAuthenticated;
    @XmlElement(name = "Password")
    protected String password;
    @XmlElement(name = "Roles")
    protected ArrayOfAuthorizationRole roles;
    @XmlElement(name = "User")
    protected User user;
    @XmlElement(name = "UserName")
    protected String userName;
    @XmlElement(name = "ApplicationName")
    protected String applicationName;
    @XmlElement(name = "CanResetPassword")
    protected boolean canResetPassword;
    @XmlElement(name = "PasswordNewerExpire")
    protected boolean passwordNewerExpire;
    @XmlElement(name = "MaxInvalidPasswordAttempts")
    protected int maxInvalidPasswordAttempts;
    @XmlElement(name = "CreationDate", required = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar creationDate;
    @XmlElement(name = "IsLogged")
    protected boolean isLogged;
    @XmlElement(name = "LastActivityDateTime", required = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar lastActivityDateTime;
    @XmlElement(name = "LastLoginDateTime", required = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar lastLoginDateTime;
    @XmlElement(name = "LastPasswordChangedDate", required = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar lastPasswordChangedDate;
    @XmlElement(name = "IsExpired")
    protected boolean isExpired;
    @XmlElement(name = "IsLocked")
    protected boolean isLocked;
    @XmlElement(name = "PasswordChangeNeeded")
    protected boolean passwordChangeNeeded;

    /**
     * Recupera il valore della proprietà status.
     * 
     * @return
     *     possible object is
     *     {@link ApplicationIdentityStatus }
     *     
     */
    public ApplicationIdentityStatus getStatus() {
        return status;
    }

    /**
     * Imposta il valore della proprietà status.
     * 
     * @param value
     *     allowed object is
     *     {@link ApplicationIdentityStatus }
     *     
     */
    public void setStatus(ApplicationIdentityStatus value) {
        this.status = value;
    }

    /**
     * Recupera il valore della proprietà dateOfExpire.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getDateOfExpire() {
        return dateOfExpire;
    }

    /**
     * Imposta il valore della proprietà dateOfExpire.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setDateOfExpire(XMLGregorianCalendar value) {
        this.dateOfExpire = value;
    }

    /**
     * Recupera il valore della proprietà passwordExpirationDate.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getPasswordExpirationDate() {
        return passwordExpirationDate;
    }

    /**
     * Imposta il valore della proprietà passwordExpirationDate.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setPasswordExpirationDate(XMLGregorianCalendar value) {
        this.passwordExpirationDate = value;
    }

    /**
     * Recupera il valore della proprietà initialPassword.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getInitialPassword() {
        return initialPassword;
    }

    /**
     * Imposta il valore della proprietà initialPassword.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setInitialPassword(String value) {
        this.initialPassword = value;
    }

    /**
     * Recupera il valore della proprietà isAuthenticated.
     * 
     */
    public boolean isIsAuthenticated() {
        return isAuthenticated;
    }

    /**
     * Imposta il valore della proprietà isAuthenticated.
     * 
     */
    public void setIsAuthenticated(boolean value) {
        this.isAuthenticated = value;
    }

    /**
     * Recupera il valore della proprietà password.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPassword() {
        return password;
    }

    /**
     * Imposta il valore della proprietà password.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPassword(String value) {
        this.password = value;
    }

    /**
     * Recupera il valore della proprietà roles.
     * 
     * @return
     *     possible object is
     *     {@link ArrayOfAuthorizationRole }
     *     
     */
    public ArrayOfAuthorizationRole getRoles() {
        return roles;
    }

    /**
     * Imposta il valore della proprietà roles.
     * 
     * @param value
     *     allowed object is
     *     {@link ArrayOfAuthorizationRole }
     *     
     */
    public void setRoles(ArrayOfAuthorizationRole value) {
        this.roles = value;
    }

    /**
     * Recupera il valore della proprietà user.
     * 
     * @return
     *     possible object is
     *     {@link User }
     *     
     */
    public User getUser() {
        return user;
    }

    /**
     * Imposta il valore della proprietà user.
     * 
     * @param value
     *     allowed object is
     *     {@link User }
     *     
     */
    public void setUser(User value) {
        this.user = value;
    }

    /**
     * Recupera il valore della proprietà userName.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUserName() {
        return userName;
    }

    /**
     * Imposta il valore della proprietà userName.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUserName(String value) {
        this.userName = value;
    }

    /**
     * Recupera il valore della proprietà applicationName.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getApplicationName() {
        return applicationName;
    }

    /**
     * Imposta il valore della proprietà applicationName.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setApplicationName(String value) {
        this.applicationName = value;
    }

    /**
     * Recupera il valore della proprietà canResetPassword.
     * 
     */
    public boolean isCanResetPassword() {
        return canResetPassword;
    }

    /**
     * Imposta il valore della proprietà canResetPassword.
     * 
     */
    public void setCanResetPassword(boolean value) {
        this.canResetPassword = value;
    }

    /**
     * Recupera il valore della proprietà passwordNewerExpire.
     * 
     */
    public boolean isPasswordNewerExpire() {
        return passwordNewerExpire;
    }

    /**
     * Imposta il valore della proprietà passwordNewerExpire.
     * 
     */
    public void setPasswordNewerExpire(boolean value) {
        this.passwordNewerExpire = value;
    }

    /**
     * Recupera il valore della proprietà maxInvalidPasswordAttempts.
     * 
     */
    public int getMaxInvalidPasswordAttempts() {
        return maxInvalidPasswordAttempts;
    }

    /**
     * Imposta il valore della proprietà maxInvalidPasswordAttempts.
     * 
     */
    public void setMaxInvalidPasswordAttempts(int value) {
        this.maxInvalidPasswordAttempts = value;
    }

    /**
     * Recupera il valore della proprietà creationDate.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getCreationDate() {
        return creationDate;
    }

    /**
     * Imposta il valore della proprietà creationDate.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setCreationDate(XMLGregorianCalendar value) {
        this.creationDate = value;
    }

    /**
     * Recupera il valore della proprietà isLogged.
     * 
     */
    public boolean isIsLogged() {
        return isLogged;
    }

    /**
     * Imposta il valore della proprietà isLogged.
     * 
     */
    public void setIsLogged(boolean value) {
        this.isLogged = value;
    }

    /**
     * Recupera il valore della proprietà lastActivityDateTime.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getLastActivityDateTime() {
        return lastActivityDateTime;
    }

    /**
     * Imposta il valore della proprietà lastActivityDateTime.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setLastActivityDateTime(XMLGregorianCalendar value) {
        this.lastActivityDateTime = value;
    }

    /**
     * Recupera il valore della proprietà lastLoginDateTime.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getLastLoginDateTime() {
        return lastLoginDateTime;
    }

    /**
     * Imposta il valore della proprietà lastLoginDateTime.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setLastLoginDateTime(XMLGregorianCalendar value) {
        this.lastLoginDateTime = value;
    }

    /**
     * Recupera il valore della proprietà lastPasswordChangedDate.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getLastPasswordChangedDate() {
        return lastPasswordChangedDate;
    }

    /**
     * Imposta il valore della proprietà lastPasswordChangedDate.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setLastPasswordChangedDate(XMLGregorianCalendar value) {
        this.lastPasswordChangedDate = value;
    }

    /**
     * Recupera il valore della proprietà isExpired.
     * 
     */
    public boolean isIsExpired() {
        return isExpired;
    }

    /**
     * Imposta il valore della proprietà isExpired.
     * 
     */
    public void setIsExpired(boolean value) {
        this.isExpired = value;
    }

    /**
     * Recupera il valore della proprietà isLocked.
     * 
     */
    public boolean isIsLocked() {
        return isLocked;
    }

    /**
     * Imposta il valore della proprietà isLocked.
     * 
     */
    public void setIsLocked(boolean value) {
        this.isLocked = value;
    }

    /**
     * Recupera il valore della proprietà passwordChangeNeeded.
     * 
     */
    public boolean isPasswordChangeNeeded() {
        return passwordChangeNeeded;
    }

    /**
     * Imposta il valore della proprietà passwordChangeNeeded.
     * 
     */
    public void setPasswordChangeNeeded(boolean value) {
        this.passwordChangeNeeded = value;
    }

}
