//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package com.reco.ees.gateway.generated.npkd.certificates;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per Esito complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="Esito">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="ldif" type="{http://www.w3.org/2001/XMLSchema}base64Binary" minOccurs="0" form="unqualified"/>
 *         <element name="codError" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         <element name="descError" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "Esito", namespace = "http://npkd.cen.it/NPKD/", propOrder = {
    "ldif",
    "codError",
    "descError"
})
public class Esito {

    @XmlElement(namespace = "")
    protected byte[] ldif;
    @XmlElement(namespace = "")
    protected String codError;
    @XmlElement(namespace = "")
    protected String descError;

    /**
     * Recupera il valore della proprietà ldif.
     * 
     * @return
     *     possible object is
     *     byte[]
     */
    public byte[] getLdif() {
        return ldif;
    }

    /**
     * Imposta il valore della proprietà ldif.
     * 
     * @param value
     *     allowed object is
     *     byte[]
     */
    public void setLdif(byte[] value) {
        this.ldif = value;
    }

    /**
     * Recupera il valore della proprietà codError.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCodError() {
        return codError;
    }

    /**
     * Imposta il valore della proprietà codError.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCodError(String value) {
        this.codError = value;
    }

    /**
     * Recupera il valore della proprietà descError.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDescError() {
        return descError;
    }

    /**
     * Imposta il valore della proprietà descError.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDescError(String value) {
        this.descError = value;
    }

}
