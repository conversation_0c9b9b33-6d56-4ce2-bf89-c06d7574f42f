//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package com.reco.ees.gateway.generated.sif.iae;

import jakarta.xml.bind.annotation.XmlEnum;
import jakarta.xml.bind.annotation.XmlEnumValue;
import jakarta.xml.bind.annotation.XmlType;


/**
 * 
 * 
 * <p>Classe Java per StatusVistoType.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * <pre>{@code
 * <simpleType name="StatusVistoType">
 *   <restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *     <enumeration value="S1"/>
 *     <enumeration value="S2"/>
 *     <enumeration value="S3"/>
 *     <enumeration value="S4"/>
 *     <enumeration value="S5"/>
 *     <enumeration value="S6"/>
 *     <enumeration value="S7"/>
 *     <enumeration value="NV"/>
 *   </restriction>
 * </simpleType>
 * }</pre>
 * 
 */
@XmlType(name = "StatusVistoType")
@XmlEnum
public enum StatusVistoType {

    @XmlEnumValue("S1")
    S_1("S1"),
    @XmlEnumValue("S2")
    S_2("S2"),
    @XmlEnumValue("S3")
    S_3("S3"),
    @XmlEnumValue("S4")
    S_4("S4"),
    @XmlEnumValue("S5")
    S_5("S5"),
    @XmlEnumValue("S6")
    S_6("S6"),
    @XmlEnumValue("S7")
    S_7("S7"),
    NV("NV");
    private final String value;

    StatusVistoType(String v) {
        value = v;
    }

    /**
     * Gets the value associated to the enum constant.
     * 
     * @return
     *     The value linked to the enum.
     */
    public String value() {
        return value;
    }

    /**
     * Gets the enum associated to the value passed as parameter.
     * 
     * @param v
     *     The value to get the enum from.
     * @return
     *     The enum which corresponds to the value, if it exists.
     * @throws IllegalArgumentException
     *     If no value matches in the enum declaration.
     */
    public static StatusVistoType fromValue(String v) {
        for (StatusVistoType c: StatusVistoType.values()) {
            if (c.value.equals(v)) {
                return c;
            }
        }
        throw new IllegalArgumentException(v);
    }

}
