//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package com.reco.ees.gateway.generated.sif.iae;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per anonymous complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType>
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="SendSDIRequestResult" type="{http://tempuri.org/}IAEResponse" minOccurs="0"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "sendSDIRequestResult"
})
@XmlRootElement(name = "SendSDIRequestResponse")
public class SendSDIRequestResponse {

    @XmlElement(name = "SendSDIRequestResult")
    protected IAEResponse sendSDIRequestResult;

    /**
     * Recupera il valore della proprietà sendSDIRequestResult.
     * 
     * @return
     *     possible object is
     *     {@link IAEResponse }
     *     
     */
    public IAEResponse getSendSDIRequestResult() {
        return sendSDIRequestResult;
    }

    /**
     * Imposta il valore della proprietà sendSDIRequestResult.
     * 
     * @param value
     *     allowed object is
     *     {@link IAEResponse }
     *     
     */
    public void setSendSDIRequestResult(IAEResponse value) {
        this.sendSDIRequestResult = value;
    }

}
