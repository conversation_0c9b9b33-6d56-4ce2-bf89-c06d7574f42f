//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package com.reco.ees.gateway.generated.sif.iae;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSeeAlso;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per WSRichiesta complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType name="WSRichiesta">
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="idApplicazioneChiamante" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "WSRichiesta", namespace = "http://common.business.sdi.ibm.it", propOrder = {
    "idApplicazioneChiamante"
})
@XmlSeeAlso({
    RichiestaS004 .class,
    RichiestaS016 .class
})
public class WSRichiesta {

    @XmlElement(required = true, nillable = true)
    protected String idApplicazioneChiamante;

    /**
     * Recupera il valore della proprietà idApplicazioneChiamante.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIdApplicazioneChiamante() {
        return idApplicazioneChiamante;
    }

    /**
     * Imposta il valore della proprietà idApplicazioneChiamante.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIdApplicazioneChiamante(String value) {
        this.idApplicazioneChiamante = value;
    }

}
