//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package com.reco.ees.gateway.generated.sif.iae;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java per anonymous complex type.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * 
 * <pre>{@code
 * <complexType>
 *   <complexContent>
 *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       <sequence>
 *         <element name="ricLuoghi" minOccurs="0">
 *           <complexType>
 *             <complexContent>
 *               <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 <sequence>
 *                   <element name="Attore" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *                 </sequence>
 *               </restriction>
 *             </complexContent>
 *           </complexType>
 *         </element>
 *       </sequence>
 *     </restriction>
 *   </complexContent>
 * </complexType>
 * }</pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "ricLuoghi"
})
@XmlRootElement(name = "LuoghiSDI", namespace = "http://sifiiegate.dcft.interno.it")
public class LuoghiSDI {

    @XmlElement(namespace = "http://sifiiegate.dcft.interno.it")
    protected LuoghiSDI.RicLuoghi ricLuoghi;

    /**
     * Recupera il valore della proprietà ricLuoghi.
     * 
     * @return
     *     possible object is
     *     {@link LuoghiSDI.RicLuoghi }
     *     
     */
    public LuoghiSDI.RicLuoghi getRicLuoghi() {
        return ricLuoghi;
    }

    /**
     * Imposta il valore della proprietà ricLuoghi.
     * 
     * @param value
     *     allowed object is
     *     {@link LuoghiSDI.RicLuoghi }
     *     
     */
    public void setRicLuoghi(LuoghiSDI.RicLuoghi value) {
        this.ricLuoghi = value;
    }


    /**
     * <p>Classe Java per anonymous complex type.</p>
     * 
     * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
     * 
     * <pre>{@code
     * <complexType>
     *   <complexContent>
     *     <restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       <sequence>
     *         <element name="Attore" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
     *       </sequence>
     *     </restriction>
     *   </complexContent>
     * </complexType>
     * }</pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "attore"
    })
    public static class RicLuoghi {

        @XmlElement(name = "Attore", namespace = "http://sifiiegate.dcft.interno.it")
        protected String attore;

        /**
         * Recupera il valore della proprietà attore.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getAttore() {
            return attore;
        }

        /**
         * Imposta il valore della proprietà attore.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setAttore(String value) {
            this.attore = value;
        }

    }

}
