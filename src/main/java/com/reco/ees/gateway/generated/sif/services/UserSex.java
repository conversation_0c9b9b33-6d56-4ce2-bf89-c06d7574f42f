//
// Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
// Vedere https://eclipse-ee4j.github.io/jaxb-ri 
// Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
//


package com.reco.ees.gateway.generated.sif.services;

import jakarta.xml.bind.annotation.XmlEnum;
import jakarta.xml.bind.annotation.XmlType;


/**
 * 
 * 
 * <p>Classe Java per UserSex.</p>
 * 
 * <p>Il seguente frammento di schema specifica il contenuto previsto contenuto in questa classe.</p>
 * <pre>{@code
 * <simpleType name="UserSex">
 *   <restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *     <enumeration value="M"/>
 *     <enumeration value="F"/>
 *   </restriction>
 * </simpleType>
 * }</pre>
 * 
 */
@XmlType(name = "UserSex")
@XmlEnum
public enum UserSex {

    M,
    F;

    public String value() {
        return name();
    }

    public static UserSex fromValue(String v) {
        return valueOf(v);
    }

}
