package com.reco.ees.gateway.mapper;

import com.reco.ees.gateway.enums.AbcEgateMessageDocumentTypeEnum;
import com.reco.ees.gateway.enums.AbcEgateMessageTypeEnum;
import com.reco.ees.gateway.kafka.model.*;
import com.reco.ees.gateway.model.NodeAbcProcess;
import com.reco.ees.gateway.repository.model.Dossier;
import com.reco.ees.gateway.repository.model.NfiqSubsection;
import com.reco.ees.gateway.repository.model.QuestionSubsection;
import com.reco.ees.gateway.util.UtilsService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
@AllArgsConstructor
public class DossierMapper {
    private final UtilsService utilsService;

    public Dossier convertRegistrationMessageToDossier(RegistrationMessage registrationMessage) {
        Dossier dossier = new Dossier();
        dossier.setCreationDate(new Date());
        dossier.setIdMessage(registrationMessage.getId());
        dossier.setNodeId(registrationMessage.getNodeId());
        dossier.setIdChiamante(registrationMessage.getIdChiamante());
        dossier.setTimestamp(registrationMessage.getTimestamp());

        dossier.setEgateHandled(Boolean.FALSE);

        DocumentSection document = registrationMessage.getDocument();
        if(document != null) {
            dossier.setSurname(document.getSurname());
            dossier.setName(document.getName());
            dossier.setGender(document.getGender());
            dossier.setBirthdate(document.getBirthdate());
            dossier.setNationality(document.getNationality());
            dossier.setDocumentNumber(document.getDocument_number());
            dossier.setCountryIssue(document.getCountry_issue());
            dossier.setExpiryDate(document.getExpiry_date());
            dossier.setIssueDate(document.getDate_issue());
            dossier.setDocumentType(AbcEgateMessageDocumentTypeEnum.PASSPORT.getAbcEgateMessageDocumentTypeEnumValue());
        }

        extractAdditionalInfo(registrationMessage, dossier);

        dossier.setDirectionEgate(registrationMessage.getDirectionEgate());

        return dossier;
    }

    public Dossier convertNodeAbcProcessToDossier(NodeAbcProcess nodeAbcProcess) {
        Dossier dossier = new Dossier();

        dossier.setEgateHandled(Boolean.TRUE);
        dossier.setHasToBeProcessed(nodeAbcProcess.isHas_to_be_processed());

        dossier.setNodeId(nodeAbcProcess.getNodeId());
        dossier.setIdChiamante(nodeAbcProcess.getIdChiamante());
        dossier.setTimestamp(nodeAbcProcess.getTimestamp());

        AbcEgateMessageContentDocument document = nodeAbcProcess.getDocument();
        if(document != null) {
            dossier.setSurname(document.getSurname());
            dossier.setName(document.getName());
            dossier.setGender(document.getGender());
            dossier.setBirthdate(document.getBirthdate());
            dossier.setNationality(document.getNationality());
            dossier.setDocumentNumber(document.getDocument_number());
            dossier.setCountryIssue(document.getCountry_issue());
            dossier.setDocumentType(document.getDocument_type());
            dossier.setIssueDate(document.getDate_issue());
            dossier.setExpiryDate(document.getExpiry_date());
        }

        AbcEgateMessageContentFace face = nodeAbcProcess.getFace();
        if(face != null) {
            dossier.setFaceImage(face.getImage()); //dovrebbe essere gia' decriptata ma forse sarebbe meglio avere il contrario: utilsService.decryptContent(face.getImage())
            dossier.setIsFaceTakenLive(face.is_taken_live());
            dossier.setFaceQuality(face.getQuality());
        }

        AbcEgateMessageContentFingerprint fingerprint = nodeAbcProcess.getFingerprint();
        if(fingerprint != null) {
            dossier.setFingerprintImage(fingerprint.getImage()); //dovrebbe essere gia' decriptata ma forse sarebbe meglio avere il contrario: utilsService.decryptContent(fingerprint.getImage())
            if(!fingerprint.getNfiq().isEmpty()) {
                List<NfiqSubsection> nfiqSubsection = new ArrayList<>();
                fingerprint.getNfiq().forEach(nfiq -> {
                    NfiqSubsection nfiqSubsection1 = new NfiqSubsection();
                    nfiqSubsection1.setFinger(nfiq.getFinger());
                    nfiqSubsection1.setNfiq(nfiq.getNfiq());
                    nfiqSubsection1.setDossier(dossier);
                    nfiqSubsection.add(nfiqSubsection1);
                });
                dossier.setNfiq(nfiqSubsection);
            }
        }

        dossier.setSurveyVersion(null);
        if(nodeAbcProcess.getSurvey() != null && nodeAbcProcess.getSurvey().getQuestions() != null && !nodeAbcProcess.getSurvey().getQuestions().isEmpty()) {
            List<QuestionSubsection> questionSubsectionList = new ArrayList<>();
            nodeAbcProcess.getSurvey().getQuestions().forEach(question -> {
                QuestionSubsection questionSubsection = new QuestionSubsection();
                questionSubsection.setQuestion(question.getQuestion());
                questionSubsection.setAnswer(question.getAnswer());
                questionSubsection.setId(null);
                questionSubsection.setDossier(dossier);
                questionSubsectionList.add(questionSubsection);
            });
            dossier.setSurvey(questionSubsectionList);
        } else dossier.setSurvey(null);

        return dossier;
    }

    public Dossier convertAbcEgateMessageToDossier(AbcEgateMessage abcEgateMessage) {
        Dossier dossier = new Dossier();

        dossier.setSurveyVersion(null);

        dossier.setIdMessage(abcEgateMessage.getId());
        dossier.setEgateHandled(Boolean.TRUE);
        dossier.setHasToBeProcessed(Boolean.parseBoolean(abcEgateMessage.getHas_to_be_processed()));
        //status da gestire opportunamente

        dossier.setNodeId(abcEgateMessage.getNodeId());
        dossier.setIdChiamante(abcEgateMessage.getIdChiamante());
        dossier.setTimestamp(abcEgateMessage.getTimestamp());
        dossier.setPassengerId(abcEgateMessage.getPassengerId());

        eventuallySetDossierDocument(abcEgateMessage, dossier);
        eventuallySetDossierFace(abcEgateMessage, dossier);
        eventuallySetDossierFingerprint(abcEgateMessage, dossier);

        /*dossier.setSurveyVersion(null);
        dossier.setSurvey(null);*/

        return dossier;
    }

    public void eventuallySetDossierDocument(AbcEgateMessage abcEgateMessage, Dossier dossier) {
        if(abcEgateMessage.getType().equalsIgnoreCase(AbcEgateMessageTypeEnum.DOCUMENT.getAbcEgateMessageTypeEnumValue())) {
            AbcEgateMessageContentDocument document = (AbcEgateMessageContentDocument) abcEgateMessage.getContent();
            if(document != null) {
                dossier.setSurname(document.getSurname());
                dossier.setName(document.getName());
                dossier.setGender(document.getGender());
                dossier.setBirthdate(document.getBirthdate());
                dossier.setNationality(document.getNationality());
                dossier.setDocumentNumber(document.getDocument_number());
                dossier.setCountryIssue(document.getCountry_issue());
                dossier.setExpiryDate(document.getExpiry_date());
                dossier.setDocumentType(document.getDocument_type());
            }
        }
    }

    public void eventuallySetDossierFace(AbcEgateMessage abcEgateMessage, Dossier dossier) {
        if(abcEgateMessage.getType().equalsIgnoreCase(AbcEgateMessageTypeEnum.FACE.getAbcEgateMessageTypeEnumValue())) {
            AbcEgateMessageContentFace face = (AbcEgateMessageContentFace) abcEgateMessage.getContent();
            if(face != null) {
                dossier.setFaceImage(face.getImage()); //dovrebbe essere gia' decriptata ma forse sarebbe meglio avere il contrario: utilsService.decryptContent(face.getImage())
                dossier.setIsFaceTakenLive(face.is_taken_live());
                dossier.setFaceQuality(face.getQuality());
            }
        }
    }

    public void eventuallySetDossierFingerprint(AbcEgateMessage abcEgateMessage, Dossier dossier) {
        if(abcEgateMessage.getType().equalsIgnoreCase(AbcEgateMessageTypeEnum.FINGERPRINT.getAbcEgateMessageTypeEnumValue())) {
            AbcEgateMessageContentFingerprint fingerprint = (AbcEgateMessageContentFingerprint) abcEgateMessage.getContent();
            if(fingerprint != null) {
                dossier.setFingerprintImage(fingerprint.getImage()); //dovrebbe essere gia' decriptata ma forse sarebbe meglio avere il contrario: utilsService.decryptContent(fingerprint.getImage())
                if(!fingerprint.getNfiq().isEmpty()) {
                    List<NfiqSubsection> nfiqSubsection = new ArrayList<>();
                    fingerprint.getNfiq().forEach(nfiq -> {
                        NfiqSubsection nfiqSubsection1 = new NfiqSubsection();
                        nfiqSubsection1.setFinger(nfiq.getFinger());
                        nfiqSubsection1.setNfiq(nfiq.getNfiq());
                        nfiqSubsection1.setDossier(dossier);
                        nfiqSubsection.add(nfiqSubsection1);
                    });
                    dossier.setNfiq(nfiqSubsection);
                }
                //dossier.setNotFingerEligibilityAge(fingerprint.);
            }
        }
    }

    public void extractAdditionalInfo(RegistrationMessage registrationMessage, Dossier dossier) {
        FaceSection face = registrationMessage.getFace();
        if(face != null) {
            dossier.setFaceImage(utilsService.decryptContent(face.getImage()));
            dossier.setIsFaceTakenLive(face.getIsTakenLive());
            dossier.setFaceQuality(face.getQuality());
        }

        FingerprintSection fingerprint = registrationMessage.getFingerprint();
        if(fingerprint != null) {
            dossier.setFingerprintImage(utilsService.decryptContent(fingerprint.getImage()));
            List<NfiqSubsection> nfiqSubsection = new ArrayList<>();
            fingerprint.getNfiq().forEach(nfiqSubSection -> {
                NfiqSubsection nfiqSubsection1 = new NfiqSubsection();
                nfiqSubsection1.setFinger(nfiqSubSection.getFinger());
                nfiqSubsection1.setNfiq(nfiqSubSection.getNfiq());
                nfiqSubsection1.setDossier(dossier);
                nfiqSubsection.add(nfiqSubsection1);
            });
            dossier.setNfiq(nfiqSubsection);
        }
        dossier.setNotFingerEligibilityAge(registrationMessage.getNotFingerEligibilityAge());

        SurveySection survey = registrationMessage.getSurvey();
        if(survey != null && survey.getSurvey() != null) {
            List<QuestionSubsection> questionSubsection = new ArrayList<>();
            survey.getSurvey().forEach(questionSubSection -> {
                QuestionSubsection questionSubsection1 = new QuestionSubsection();
                questionSubsection1.setQuestion(questionSubSection.getQuestion());
                questionSubsection1.setAnswer(questionSubSection.getAnswer());
                questionSubsection1.setDossier(dossier);
                questionSubsection.add(questionSubsection1);
            });
            dossier.setSurvey(questionSubsection);
            dossier.setSurveyVersion(registrationMessage.getSurvey().getVersion());
        }
    }
}
