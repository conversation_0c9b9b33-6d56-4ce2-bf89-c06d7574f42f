package com.reco.ees.gateway.repository.model;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Immutable;
import org.hibernate.annotations.UuidGenerator;

@Getter
@Entity(name = "SifGenericCode")
@Table(name = "sif_generic_code", indexes = {
        @Index(name = "idx_sgc_type_kiosk_value", columnList = "type, kioskValue"), // Per getSifValueByTypeAndKioskValue
        @Index(name = "idx_sgc_type", columnList = "type") // Per findAllByType e findByType
})
@Cache(usage = CacheConcurrencyStrategy.READ_ONLY, region = "com.reco.ees.gateway.repository.model.SifGenericCode")
@Immutable
@NoArgsConstructor(access = AccessLevel.PROTECTED, force = true)
@AllArgsConstructor
public class SifGenericCode { //SUPPOSTO IMMUTABILE A RUNTIME
    @Id
    @UuidGenerator
    private final String idGenericCode;

    private final String type;
    private final String kioskValue;
    private final String sifValue;
    private final String description;
}
    