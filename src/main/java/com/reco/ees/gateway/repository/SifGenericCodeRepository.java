package com.reco.ees.gateway.repository;

import com.reco.ees.gateway.config.HibernateCacheConfig;
import com.reco.ees.gateway.repository.model.SifGenericCode;
import jakarta.persistence.QueryHint;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.QueryHints;
import org.springframework.stereotype.Repository;

import java.util.List;

import static org.hibernate.jpa.HibernateHints.HINT_CACHEABLE;

@Repository
public interface SifGenericCodeRepository extends JpaRepository<SifGenericCode, String> {

    @QueryHints({
            @QueryHint(name = HINT_CACHEABLE, value = "true"),
            @QueryHint(name = "org.hibernate.cacheRegion", value = HibernateCacheConfig.SIF_GENERIC_CODE_QUERIES_REGION_NAME)
    })
    @Query("SELECT sgc.sifValue FROM SifGenericCode sgc where sgc.type = :type AND sgc.kioskValue = :kioskValue")
    String getSifValueByTypeAndKioskValue(String type, String kioskValue);

    @QueryHints({
            @QueryHint(name = HINT_CACHEABLE, value = "true"),
            @QueryHint(name = "org.hibernate.cacheRegion", value = HibernateCacheConfig.SIF_GENERIC_CODE_QUERIES_REGION_NAME)
    })
    List<SifGenericCode> findAllByType(String type);

    @QueryHints({
            @QueryHint(name = HINT_CACHEABLE, value = "true"),
            @QueryHint(name = "org.hibernate.cacheRegion", value = HibernateCacheConfig.SIF_GENERIC_CODE_QUERIES_REGION_NAME)
    })
    SifGenericCode findByType(String type);


    @Override
    @QueryHints({
            @QueryHint(name = HINT_CACHEABLE, value = "true"),
            @QueryHint(name = "org.hibernate.cacheRegion", value = HibernateCacheConfig.SIF_GENERIC_CODE_QUERIES_REGION_NAME)
    })
    long count();
}
        