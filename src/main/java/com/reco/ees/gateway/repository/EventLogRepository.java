package com.reco.ees.gateway.repository;

import com.reco.ees.gateway.repository.model.EventLog;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Date;

@Repository
public interface EventLogRepository extends JpaRepository<EventLog, String> {
    Page<EventLog> findAllByRequestdateBefore(Date deleteFromDate, Pageable pageable);

    //long deleteAllByEventdateBefore(Date date);
}
