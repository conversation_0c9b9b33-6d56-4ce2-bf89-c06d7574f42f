package com.reco.ees.gateway.repository.model;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Immutable;
import org.hibernate.annotations.UuidGenerator;

@Getter
// @Setter
@Entity(name = "SifCountryCode")
@Table(name = "sif_country_code", indexes = {
        @Index(name = "idx_scc_kiosk_value", columnList = "kioskValue")
})
//@Cacheable // JPA standard, può coesistere
@Cache(usage = CacheConcurrencyStrategy.READ_ONLY, region = "com.reco.ees.gateway.repository.model.SifCountryCode") // Specifica la regione qui
@NoArgsConstructor(access = AccessLevel.PROTECTED, force = true) // No-arg constructor protetto per JPA/Hibernate
@AllArgsConstructor // Costruttore con tutti gli argomenti per creare istanze immutabili
@Immutable
public class SifCountryCode { //SUPPOSTO IMMUTABILE A RUNTIME
    @Id
    @UuidGenerator
    private final String idCountryCode;

    private final String description;
    private final String kioskValue;
    private final String countrySifValue;
    private final String tcnTypeSifValue;
    private final String note;
}