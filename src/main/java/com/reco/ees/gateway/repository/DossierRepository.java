package com.reco.ees.gateway.repository;

import com.reco.ees.gateway.repository.model.Dossier;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

@Repository
public interface DossierRepository extends JpaRepository<Dossier, String> {
    Dossier findFirstByDocumentNumberAndCountryIssueOrderByCreationDateDesc(String documentNumber, String countryIssue);

    /*@Modifying
    @Query(value = "INSERT INTO dossier (id_message, application, status) values (:idMessage, :application, :status)", nativeQuery = true)
    void facesContention(String idMessage, String application, String status);

    @Modifying
    void deleteAllByApplicationAndSifTransactionIdNull(String application);*/

    /*List<Dossier> findAllByApplicationAndSifTransactionIdNotNullAndStatusInAndSifInteractionCountLessThanAndLastSifInteractionDateIsNotNullAndLastSifInteractionDateBefore(
            String application, List<String> statuses, int maxRetries, Date checkDate);*/

    @Transactional
    @Modifying
    long deleteAllByLastSifInteractionDateLessThan(Date deleteFromDate);

    @Modifying
    @Query("UPDATE dossier d SET d.status = :status WHERE d.idMessage = :id")
    void updateDossierStatus(String id, String status);

    //List<Dossier> findAllByStatusIn(List<String> dossierStatusEnumValues);

    @Transactional
    @Modifying
    @Query(value = "UPDATE dossier SET application = :newApplication WHERE application = :oldApplication AND status != 'SURVEY_SENT_OK'", nativeQuery = true) //test1 solo: UPDATE dossier SET application = :newApplication WHERE application = :oldApplication
    void updateDossierApplication(String oldApplication, String newApplication);

    List<Dossier> findAllByApplicationAndStatusIn(String applicationName, List<String> dossierStatusEnumValue);
    Page<Dossier> findAllByApplicationAndStatusIn(String applicationName, List<String> dossierStatusEnumValue, Pageable pageable);

    List<Dossier> findAllByApplicationAndStatusInAndSifInteractionCountLessThanAndLastSifInteractionDateBefore(String application, List<String> statuses, Integer maxRetries, Date date); //findAllByApplicationAndStatusInAndSifInteractionCountLessThanAndLastSifInteractionDateBeforeOrLastSifInteractionDateIsNull //da DossierResponseJob su chiamata di getDossiersToBeProcessedByDossierResponseJob(): forse viene prima fatto partire response come retrieveasync di mex per cui non ho neanche fatto la startbordercontrol (non ho transaction id generato da DossierSender): la named query FORSE non e' corretta in quanto la parentesizzazione attesa nell'ultima OR semplicemente non e' una parentesizzazione quindi scrivi query estesa in JPQL
    Page<Dossier> findAllByApplicationAndStatusInAndSifInteractionCountLessThanAndLastSifInteractionDateBefore(String application, List<String> statuses, Integer maxRetries, Date date, Pageable pageable); //findAllByApplicationAndStatusInAndSifInteractionCountLessThanAndLastSifInteractionDateBeforeOrLastSifInteractionDateIsNull //da DossierResponseJob su chiamata di getDossiersToBeProcessedByDossierResponseJob(): forse viene prima fatto partire response come retrieveasync di mex per cui non ho neanche fatto la startbordercontrol (non ho transaction id generato da DossierSender): la named query FORSE non e' corretta in quanto la parentesizzazione attesa nell'ultima OR semplicemente non e' una parentesizzazione quindi scrivi query estesa in JPQL

    List<Dossier> findAllByApplicationAndStatusInAndSifInteractionCountLessThan(String application, List<String> statuses, Integer maxRetries);
    Page<Dossier> findAllByApplicationAndStatusInAndSifInteractionCountLessThan(String application, List<String> statuses, Integer maxRetries, Pageable pageable);

    //Page<Dossier> findAllByLastSifInteractionDateBefore(Date deleteFromDate, Pageable pageable);

    @Query("SELECT d FROM dossier d WHERE d.lastSifInteractionDate < :deleteFromDate OR (d.lastSifInteractionDate IS NULL AND d.creationDate < :deleteFromDate)")
    Page<Dossier> findAllByLastSifInteractionDateBeforeOrLastSifInteractionDateIsNullAndCreationDateBefore(Date deleteFromDate, Pageable pageable);

    //@Query("SELECT d FROM dossier d WHERE d.nodeId = :nodeId AND d.documentNumber = :documentNumber AND d.countryIssue = :countryIssue ORDER BY d.creationDate DESC")
    Dossier findFirstByNodeIdAndDocumentNumberAndCountryIssueOrderByCreationDateDesc(String nodeId, String documentNumber, String countryIssue);

    Dossier findFirstByPassengerIdAndEgateHandledTrueOrderByCreationDateDesc(String passengerId);

    Dossier findFirstByNodeIdAndPassengerIdAndEgateHandledTrueOrderByCreationDateDesc(String nodeId, String passengerId);

    Dossier findFirstByDocumentNumberAndCountryIssueAndEgateHandledFalseOrEgateHandledIsNullOrderByCreationDateDesc(String documentNumber, String countryIssue);

    Dossier findFirstByDocumentNumberAndCountryIssueAndEgateHandledTrueOrderByCreationDateDesc(String documentNumber, String countryIssue);

    Dossier findByNodeId(String nodeId);
}
