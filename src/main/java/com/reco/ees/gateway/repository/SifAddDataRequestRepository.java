package com.reco.ees.gateway.repository;

import com.reco.ees.gateway.config.HibernateCacheConfig;
import com.reco.ees.gateway.repository.model.SifAddDataRequest;
import jakarta.persistence.QueryHint;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.QueryHints;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

import static org.hibernate.jpa.HibernateHints.HINT_CACHEABLE;

@Repository
public interface SifAddDataRequestRepository extends JpaRepository<SifAddDataRequest, String> {

    @Override
    @QueryHints({
            @QueryHint(name = HINT_CACHEABLE, value = "true"),
            @QueryHint(name = "org.hibernate.cacheRegion", value = HibernateCacheConfig.SIF_ADD_DATA_REQUEST_QUERIES_REGION_NAME)
    })
    List<SifAddDataRequest> findAll();

    @QueryHints({
            @QueryHint(name = HINT_CACHEABLE, value = "true"),
            @QueryHint(name = "org.hibernate.cacheRegion", value = HibernateCacheConfig.SIF_ADD_DATA_REQUEST_QUERIES_REGION_NAME)
    })
    SifAddDataRequest findByValue(String dataRequest);

    @QueryHints({
            @QueryHint(name = HINT_CACHEABLE, value = "true"),
            @QueryHint(name = "org.hibernate.cacheRegion", value = HibernateCacheConfig.SIF_ADD_DATA_REQUEST_QUERIES_REGION_NAME)
    })
    SifAddDataRequest findFirstByRequestForEgate(String requestForEgate);

    @Override
        // Rimosso @QueryHints da findById - si affida alla cache L2 dell'entità
    Optional<SifAddDataRequest> findById(String id);

    @Override
    @QueryHints({
            @QueryHint(name = HINT_CACHEABLE, value = "true"),
            @QueryHint(name = "org.hibernate.cacheRegion", value = HibernateCacheConfig.SIF_ADD_DATA_REQUEST_QUERIES_REGION_NAME)
    })
    List<SifAddDataRequest> findAllById(Iterable<String> ids);

    @Override
    @QueryHints({
            @QueryHint(name = HINT_CACHEABLE, value = "true"),
            @QueryHint(name = "org.hibernate.cacheRegion", value = HibernateCacheConfig.SIF_ADD_DATA_REQUEST_QUERIES_REGION_NAME)
    })
    long count();

    @Override
    @QueryHints({
            @QueryHint(name = HINT_CACHEABLE, value = "true"),
            @QueryHint(name = "org.hibernate.cacheRegion", value = HibernateCacheConfig.SIF_ADD_DATA_REQUEST_QUERIES_REGION_NAME)
    })
    boolean existsById(String id);
}
        