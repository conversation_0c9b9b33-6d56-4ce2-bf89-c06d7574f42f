package com.reco.ees.gateway.repository.model;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@Entity(name = "documenti_sdi")
@Table(name = "documenti_sdi", indexes = {
        @Index(name = "idx_doc_sdi_t_doc", columnList = "tDoc"),
        @Index(name = "idx_doc_sdi_d_ini_val_d_fin_val", columnList = "dIniVal, dFinVal")
})
public class DocumentiSDI {
    @Id
    private String id;
    private String tDoc;
    private String desTipoDoc;
    private Date dIniVal;
    private Date dFinVal;
    private String inLuoDoc;
    private String flgId;
    private Date dtLastUpd;
}
