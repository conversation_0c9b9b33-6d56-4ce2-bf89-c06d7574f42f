package com.reco.ees.gateway.repository;

import com.reco.ees.gateway.config.HibernateCacheConfig;
import com.reco.ees.gateway.repository.model.SifParameter;
import jakarta.persistence.QueryHint;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.QueryHints;
import org.springframework.stereotype.Repository;

import java.util.List;

import static org.hibernate.jpa.HibernateHints.HINT_CACHEABLE;


@Repository
public interface SifParametersRepository extends JpaRepository<SifParameter, String> {

    // @Cacheable("sifParameterByType") // Spring Cache - RIMOSSO
    @QueryHints({
            @QueryHint(name = HINT_CACHEABLE, value = "true"), // Abilita cache L2
            @QueryHint(name = "org.hibernate.cacheRegion", value = HibernateCacheConfig.SIF_PARAMETER_BY_TYPE_REGION_NAME) // Specifica la regione
    })
    @Query("SELECT sifp.value FROM SifParameter sifp WHERE sifp.type = :type")
    String findByType(String type);

    @Override
    @QueryHints({
            @QueryHint(name = HINT_CACHEABLE, value = "true"),
            @QueryHint(name = "org.hibernate.cacheRegion", value = HibernateCacheConfig.SIF_PARAMETERS_FIND_ALL_QUERIES_REGION_NAME)
    })
    List<SifParameter> findAll();
}
