package com.reco.ees.gateway.controller;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.reco.ees.gateway.enums.*;
import com.reco.ees.gateway.generated.sif.iae.*;
import com.reco.ees.gateway.mapper.DossierMapper;
import com.reco.ees.gateway.mapper.SurveyMapper;
import com.reco.ees.gateway.model.CustomSurvey;
import com.reco.ees.gateway.model.NodeAbcProcess;
import com.reco.ees.gateway.repository.KioskRepository;
import com.reco.ees.gateway.repository.SifParametersRepository;
import com.reco.ees.gateway.repository.model.Dossier;
import com.reco.ees.gateway.repository.model.Kiosk;
import com.reco.ees.gateway.service.DossierService;
import com.reco.ees.gateway.service.LogService;
import com.reco.ees.gateway.service.ParameterService;
import com.reco.ees.gateway.soap_clients.SoapClientSifBcs;
import com.reco.ees.gateway.soap_clients.SoapClientSifIae;
import com.reco.ees.gateway.soap_clients.SoapClientWfe;
import com.reco.ees.gateway.util.DateUtil;
import com.reco.ees.gateway.util.SoapUtils;
import eu.europa.schengen.ees.xsd.v1.SearchByPersonalDataRequestMessageType;
import eu.europa.schengen.ees.xsd.v1.SearchByPersonalDataResponseMessageType;
import eu.europa.schengen.ees_ns.xsd.v1.*;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.security.RolesAllowed;
import lombok.*;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.web.ServerProperties;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

//TODO  adatta tutto (qui e altrove) a nuove classi aggiornate generate con "MININT SIF Servizi Esposti 1.4.1"
@RestController
@RequestMapping("/api")
@RequiredArgsConstructor
public class PreFileController {
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final SoapClientSifBcs soapClientSifBcs;
    private final SoapClientSifIae soapClientSifIae;
    private final SoapClientWfe soapClientWfe;
    private final ExecutorService virtualThreadExecutor = Executors.newVirtualThreadPerTaskExecutor();
    private final SurveyMapper surveyMapper;
    private final DossierMapper dossierMapper;
    private final DossierService dossierService;
    private final LogService logService;
    private final SifParametersRepository sifParametersRepository;
    private final KioskRepository kioskRepository;
    private final ServerProperties serverProperties;
    public static final int subConnectionTimeoutSeconds = 10; //questo valore deve essere strettamente minore di server.tomcat.connection-timeout (property)
    private final ParameterService parameterService;
    public static String airportCode;
    private final boolean smartTimeoutClient = false;

    @PostConstruct
    public void init() {
        airportCode = parameterService.getParameterById(ParametersEnum.AIRPORT_CODE.getParametersEnumValue()).getValue();
    }

    @GetMapping("/checkPreFile")
    @RolesAllowed("EGATE")
    public ResponseEntity<CheckPreFileResponse> checkPreFile(@RequestParam String idClient, @RequestParam String document_number, @RequestParam String country_issue) {
        try { //TODO: idClient dovrebbe essere il SIF id ma devo solo loggarlo?!?
            if (idClient == null || document_number == null || country_issue == null) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(new CheckPreFileResponse("Invalid request parameters. Please provide idClient, document_number and country_issue."));
            }
            Future<ResponseEntity<CheckPreFileResponse>> future = virtualThreadExecutor.submit(() -> {
                CheckPreFileResponse checkPreFileResponse = new CheckPreFileResponse();
                Dossier retrievedDossier = dossierService.getDossierByPassportData(document_number, country_issue);

                String transaction = String.format("CheckPreFile by ABC e-Gate #%s with %s doc. #%s ", idClient, country_issue, document_number);

                //TODO: è giusto loggare anche numero documento e paese di rilascio?
                if(retrievedDossier == null) {
                    logService.saveEventLog(LogScopeEnum.INTERNAL.getValue(), transaction + "KO", "Passenger not found", null);
                    checkPreFileResponse.setStatus(CheckPreFileEnum.NOTFOUND.getCheckPreFileEnumValue());
                    return ResponseEntity.ok(checkPreFileResponse);
                } else if (Boolean.FALSE.equals(retrievedDossier.getDirectionEgate())) {
                    logService.saveEventLog(LogScopeEnum.INTERNAL.getValue(), transaction + "KO", "Passenger should go to passport control", null);
                    checkPreFileResponse.setStatus(CheckPreFileEnum.FORMANUAL.getCheckPreFileEnumValue());
                    return ResponseEntity.ok(checkPreFileResponse);
                }
                if(!retrievedDossier.getStatus().equalsIgnoreCase(DossierStatusEnum.EES_NEEDS_SURVEY.getDossierStatusEnumValue()) &&
                        !retrievedDossier.getStatus().equalsIgnoreCase(DossierStatusEnum.SURVEY_SENT_OK.getDossierStatusEnumValue()) &&
                        !retrievedDossier.getStatus().equalsIgnoreCase(DossierStatusEnum.SURVEY_SENT_KO.getDossierStatusEnumValue())) { //TODO: corretto? (prima HasSifProcessedDossier era impostato true quando DossierResponseJob finiva)!
                    logService.saveEventLog(LogScopeEnum.INTERNAL.getValue(), transaction + "KO", "Passenger does not have a successful SIF response", null);
                    checkPreFileResponse.setStatus(CheckPreFileEnum.MISSING4EGATE.getCheckPreFileEnumValue());
                    return ResponseEntity.ok(checkPreFileResponse);
                }
                logService.saveEventLog(LogScopeEnum.INTERNAL.getValue(), transaction + "OK", "Passenger can go to Egate", null);
                checkPreFileResponse.setStatus(CheckPreFileEnum.EXIST4EGATE.getCheckPreFileEnumValue());
                return ResponseEntity.ok(checkPreFileResponse);
            });

            return future.get();
        } catch (SecurityException ex) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(new CheckPreFileResponse("Unauthorized request. Please provide valid authentication credentials."));
        } catch (Exception ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CheckPreFileResponse {
        private String status;
    }

    @ConditionalOnProperty(name = "endpoint.endBorderControl.withTooMuchData.enabled", havingValue = "true")
    @PostMapping("/endBorderControl")
    @RolesAllowed("EGATE")
    public ResponseEntity<Boolean> endBorderControlWithTooMuchData(@RequestBody NodeAbcProcess nodeAbcProcess, @RequestParam SifEndBorderControlEnum endBorderControlOperation) {
        Dossier dossier = dossierMapper.convertNodeAbcProcessToDossier(nodeAbcProcess);
        EndBorderControlRequestMessageType endBorderControlRequestMessageType = dossierService.mapDossierToEndBorderControlRequest(dossier, endBorderControlOperation);
        HeaderRequestType headerRequestType = dossierService.buildHeaderRequestType(dossier);
        EndBorderControlResponseMessageType endBorderControlResponseMessageType = soapClientWfe.performEndBorderControl(endBorderControlRequestMessageType, headerRequestType);
        //dossierService.deleteSensitiveData(dossier);
        if(endBorderControlResponseMessageType != null && endBorderControlResponseMessageType.getResponse() != null) {
            if(endBorderControlResponseMessageType.getReturnCodes() == null || endBorderControlResponseMessageType.getReturnCodes().getErrorCodes() == null || endBorderControlResponseMessageType.getReturnCodes().getErrorCodes().getErrorCode() == null || endBorderControlResponseMessageType.getReturnCodes().getErrorCodes().getErrorCode().isEmpty()) {
                dossierService.updateDossierStatus(dossier.getIdMessage(), DossierStatusEnum.END_BORDER_CONTROL_OK.getDossierStatusEnumValue());
                dossier = dossierService.deleteSensitiveData(dossier);
                dossierService.save(dossier);
                return ResponseEntity.ok(Boolean.TRUE);
            } else {
                dossierService.updateDossierStatus(dossier.getIdMessage(), DossierStatusEnum.END_BORDER_CONTROL_KO.getDossierStatusEnumValue());
                return ResponseEntity.ok(Boolean.FALSE); //TODO  attenzione: ci possono essere dei warning codes inclusi //ResponseEntity.ok("EndBorderControl successful for dossier with id: " + dossier.getIdMessage());
            }
        } else return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
    }

    @ConditionalOnProperty(name = "endpoint.endBorderControl.withTooMuchData.enabled", havingValue = "false", matchIfMissing = true)
    @GetMapping("/endBorderControl")
    @RolesAllowed("EGATE")
    public ResponseEntity<Boolean> endBorderControl(@RequestParam String nodeId, @RequestParam String passengerId, @RequestParam SifEndBorderControlEnum endBorderControlOperation) {
        Dossier dossier =  dossierService.getEgateDossierByNodeIdAndPassengerId(nodeId, passengerId);
        if(dossier == null) return ResponseEntity.status(HttpStatus.NOT_FOUND).build(); //.body("Dossier not found for nodeId: " + nodeId);
        EndBorderControlRequestMessageType endBorderControlRequestMessageType = dossierService.mapDossierToEndBorderControlRequest(dossier, endBorderControlOperation);
        HeaderRequestType headerRequestType = dossierService.buildHeaderRequestType(dossier);
        EndBorderControlResponseMessageType endBorderControlResponseMessageType = soapClientWfe.performEndBorderControl(endBorderControlRequestMessageType, headerRequestType);
        //dossierService.deleteSensitiveData(dossier);
        if(endBorderControlResponseMessageType != null && endBorderControlResponseMessageType.getResponse() != null) {
            if(endBorderControlResponseMessageType.getReturnCodes() == null || endBorderControlResponseMessageType.getReturnCodes().getErrorCodes() == null || endBorderControlResponseMessageType.getReturnCodes().getErrorCodes().getErrorCode() == null || endBorderControlResponseMessageType.getReturnCodes().getErrorCodes().getErrorCode().isEmpty()) {
                dossierService.updateDossierStatus(dossier.getIdMessage(), DossierStatusEnum.END_BORDER_CONTROL_OK.getDossierStatusEnumValue());
                dossier = dossierService.deleteSensitiveData(dossier);
                dossierService.save(dossier);
                return ResponseEntity.ok(Boolean.TRUE);
            } else {
                dossierService.updateDossierStatus(dossier.getIdMessage(), DossierStatusEnum.END_BORDER_CONTROL_KO.getDossierStatusEnumValue());
                return ResponseEntity.ok(Boolean.FALSE); //TODO  attenzione: ci possono essere dei warning codes inclusi //ResponseEntity.ok("EndBorderControl successful for dossier with id: " + dossier.getIdMessage());
            }
        }
        else return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
    }

    @GetMapping("/getSurvey")
    @RolesAllowed("EGATE")
    public ResponseEntity<String> getSurvey(@RequestParam String nodeId, @RequestParam String passengerId) {
        Dossier dossier = dossierService.getEgateDossierByNodeIdAndPassengerId(nodeId, passengerId); //credo di avere gia' salvato un Dossier corrispondente
        if(dossier == null) return ResponseEntity.status(HttpStatus.NOT_FOUND).build(); //.body("Dossier not found for nodeId: " + nodeId);
        HeaderRequestType headerRequestType = dossierService.buildHeaderRequestType(dossier);
        SurveyGetRequestType surveyGetRequestType = new SurveyGetRequestType();
        surveyGetRequestType.setNazione(dossier.getCountryIssue());
        surveyGetRequestType.setNumeroDocumento(dossier.getDocumentNumber());
        SurveyGetResponseType surveyGetResponseType = soapClientWfe.performGetSurvey(surveyGetRequestType, headerRequestType);
        CustomSurvey customSurvey = surveyMapper.mapSurveyGetResponseTypeToCustomSurvey(surveyGetResponseType);
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            String response = objectMapper.writeValueAsString(customSurvey);
            return customSurvey != null ? ResponseEntity.ok(response) : ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    @GetMapping("/check_sdi_account") //TODO testa vera chiamata (anche casi eccezionali per SIF e per webapp client), infine aggiungi logging (log e/o eventLog)
    @RolesAllowed("EGATE")
    public ResponseEntity<CheckSdiAccountResponse> check_sdi_account(@RequestParam String userId, @RequestParam String password, @RequestParam(required = false) String newPassword, @RequestParam(required = false) String idAppChiamante) {
        try {
            SIFServizioS000 sifServizioS000 = new SIFServizioS000();
            IAERequestS000 iaERequestS000 = new IAERequestS000();
            iaERequestS000.setUserId(userId);
            iaERequestS000.setPassword(password);
            if(newPassword != null && !newPassword.isBlank()) iaERequestS000.setNewPassword(newPassword);
            if(idAppChiamante != null && !idAppChiamante.isBlank()) iaERequestS000.setIdApplicazioneChiamante(idAppChiamante);

            sifServizioS000.setRic(iaERequestS000);
            SIFServizioS000Response sifServizioS000Response = soapClientSifIae.performS000(sifServizioS000);

            if (sifServizioS000Response != null && sifServizioS000Response.getSIFServizioS000Result() != null) {
                CheckSdiAccountResponse response = new CheckSdiAccountResponse(sifServizioS000Response.getSIFServizioS000Result());
                return ResponseEntity.ok(response);
            } else {
                RispostaS000 errorResult = new RispostaS000();
                errorResult.setEsito(-2);
                return ResponseEntity.status(HttpStatus.NO_CONTENT).body(new CheckSdiAccountResponse(errorResult));
            }
        } catch (Exception e) {
            RispostaS000 errorResult = new RispostaS000();
            errorResult.setEsito(-1);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new CheckSdiAccountResponse(errorResult));
        }
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CheckSdiAccountResponse {
        private RispostaS000 SIFServizioS000Result;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CheckPersonDocumentBcsResponse {
        private String responseJson;
        private Integer esito;
    }

    @PostMapping("/check_person") //TODO testa vera chiamata (anche casi eccezionali per SIF e per webapp client), infine aggiungi logging (log e/o eventLog)
    @RolesAllowed("EGATE")
    public ResponseEntity<CheckPersonDocumentBcsResponse> check_person(@RequestBody CheckPersonRequest checkPersonRequest) {
        try {
            Dossier dossier = dossierService.getEgateDossierByNodeIdAndPassengerId(checkPersonRequest.getNodeId(), checkPersonRequest.getPassengerId());
            if (dossier == null || dossier.getDocumentNumber() == null || dossier.getCountryIssue() == null) {
                RispostaS004 rispostaS004 = new RispostaS004();
                rispostaS004.setEsito(-2);
                SIFServizioS004Response sifServizioS004Response = new SIFServizioS004Response();
                sifServizioS004Response.setSIFServizioS004Result(rispostaS004);
                CheckPersonDocumentBcsResponse checkPersonDocumentBcsResponse = new CheckPersonDocumentBcsResponse();
                checkPersonDocumentBcsResponse.setEsito(-2);
                checkPersonDocumentBcsResponse.setResponseJson(objectMapper.writeValueAsString(sifServizioS004Response.getSIFServizioS004Result()));
                return ResponseEntity.status(HttpStatus.NOT_ACCEPTABLE).body(checkPersonDocumentBcsResponse);
            }

            SIFServizioS004 sifServizioS004 = new SIFServizioS004();
            IAERequestS004 iaERequestS004 = new IAERequestS004();

            iaERequestS004.setUserName(checkPersonRequest.getUsername());
            iaERequestS004.setPassword(checkPersonRequest.getPassword());

            if(dossier.getName() != null && !dossier.getName().isBlank()) iaERequestS004.setNome(dossier.getName());
            if(dossier.getSurname() != null && !dossier.getSurname().isBlank()) iaERequestS004.setCognome(dossier.getSurname());
            /*if(dossier.getSurname() != null && !dossier.getSurname().isBlank()) {
                if(dossier.getSurname().length() + (dossier.getName() != null ? dossier.getName().length() : 0) >= 37) dossier.setSurname(dossier.getSurname().substring(0, 37 - (dossier.getName() != null ? dossier.getName().length() : 0)).concat("%")); //TODO: sarebbe piu' giusto fare solo 36 meno (anzichè 37 meno)=?
                iaERequestS004.setCognome(dossier.getSurname());
            }*/
            if(dossier.getBirthdate() != null && !dossier.getBirthdate().isBlank()) iaERequestS004.setDataNas(DateUtil.convertStringToXmlGregorianCalendar(dossier.getBirthdate()));
            Optional<Kiosk> kiosk = kioskRepository.findById(checkPersonRequest.getNodeId());
            if(kiosk.isEmpty() || kiosk.get().getIdMachine() == null || kiosk.get().getIdMachine().isBlank()) {
                logService.saveEventLog(LogScopeEnum.INTERNAL.getValue(), "PreFile controller check_person", "Kiosk not found for dossier " + dossier.getIdMessage(), null);
                return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
            }
            String idMachine = kiosk.get().getIdMachine();
            iaERequestS004.setIdApplicazioneChiamante(idMachine);
            iaERequestS004.setIdTransazione(SoapUtils.generateSIFTransactionId(idMachine));
            iaERequestS004.setTimeOutClient(smartTimeoutClient ? Math.max(1, Math.min(Math.toIntExact(serverProperties.getTomcat().getConnectionTimeout().getSeconds()) - subConnectionTimeoutSeconds, 30)): 30);

            /*iaERequestS004.setCodiceLuogoNas(); //TODO: cosa fare con questi campi?
            iaERequestS004.setLuogoNas();
            iaERequestS004.setSiglaProvinciaNas();
            iaERequestS004.setIndicatoreDataNas("1");
            iaERequestS004.setIndicatoreInformativa("S");
            iaERequestS004.setIndicatoreAnagraficaCollegata("S");
            iaERequestS004.setIndicatorePermessoSoggiorno("S");
            iaERequestS004.setIndicatoreScomparso("S");
            iaERequestS004.setCodiceTipoInformativa("S");
            iaERequestS004.setDataInizioRicInformativa();
            iaERequestS004.setDataFineRicInformativa();
            iaERequestS004.setIndicatoreAggravanti("S");
            iaERequestS004.setFlagSchengen("S");
            iaERequestS004.setFlagAsf("S");*/

            sifServizioS004.setRic(iaERequestS004);
            com.reco.ees.gateway.generated.sif.iae.SIFServizioS004Response sifServizioS004Response = soapClientSifIae.performS004(sifServizioS004);

            CheckPersonDocumentBcsResponse checkPersonDocumentBcsResponse = new CheckPersonDocumentBcsResponse();
            if (sifServizioS004Response != null && sifServizioS004Response.getSIFServizioS004Result() != null) {
                checkPersonDocumentBcsResponse.setResponseJson(objectMapper.writeValueAsString(sifServizioS004Response.getSIFServizioS004Result()));
                checkPersonDocumentBcsResponse.setEsito(sifServizioS004Response.getSIFServizioS004Result().getEsito());
                return ResponseEntity.ok(checkPersonDocumentBcsResponse);
            } else {
                RispostaS004 rispostaS004 = new RispostaS004();
                rispostaS004.setEsito(-2);
                checkPersonDocumentBcsResponse.setEsito(rispostaS004.getEsito());
                SIFServizioS004Response response = new SIFServizioS004Response();
                response.setSIFServizioS004Result(rispostaS004);
                checkPersonDocumentBcsResponse.setResponseJson(objectMapper.writeValueAsString(response.getSIFServizioS004Result()));
                return ResponseEntity.status(HttpStatus.NO_CONTENT).body(checkPersonDocumentBcsResponse);
            }
        } catch (Exception e) {
            RispostaS004 rispostaS004 = new RispostaS004();
            rispostaS004.setEsito(-1);
            CheckPersonDocumentBcsResponse checkPersonDocumentBcsResponse = new CheckPersonDocumentBcsResponse();
            checkPersonDocumentBcsResponse.setEsito(rispostaS004.getEsito());
            SIFServizioS004Response response = new SIFServizioS004Response();
            response.setSIFServizioS004Result(rispostaS004);
            try {
                checkPersonDocumentBcsResponse.setResponseJson(objectMapper.writeValueAsString(response.getSIFServizioS004Result()));
            } catch (JsonProcessingException ex) {
                throw new RuntimeException(ex);
            }
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(checkPersonDocumentBcsResponse);
        }
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CheckPersonRequest {
        private String passengerId;
        private String nodeId;
        private String username;
        private String password;
    }

    @PostMapping("/check_document") //TODO testa vera chiamata (anche casi eccezionali per SIF e per webapp client), infine aggiungi logging (log e/o eventLog)
    @RolesAllowed("EGATE")
    public ResponseEntity<CheckPersonDocumentBcsResponse> check_document(@RequestBody CheckDocumentRequest checkDocumentRequest) {
        try {
            Dossier dossier = dossierService.getEgateDossierByNodeIdAndPassengerId(checkDocumentRequest.getNodeId(), checkDocumentRequest.getPassengerId());
            if (dossier == null || dossier.getDocumentNumber() == null || dossier.getCountryIssue() == null) {
                RispostaS016 rispostaS016 = new RispostaS016();
                rispostaS016.setEsito(-2);
                SIFServizioS016Response sifServizioS016Response = new SIFServizioS016Response();
                sifServizioS016Response.setSIFServizioS016Result(rispostaS016);
                CheckPersonDocumentBcsResponse checkPersonDocumentBcsResponse = new CheckPersonDocumentBcsResponse();
                checkPersonDocumentBcsResponse.setEsito(rispostaS016.getEsito());
                checkPersonDocumentBcsResponse.setResponseJson(objectMapper.writeValueAsString(sifServizioS016Response.getSIFServizioS016Result()));
                return ResponseEntity.status(HttpStatus.NOT_ACCEPTABLE).body(checkPersonDocumentBcsResponse);
            }

            SIFServizioS016 sifServizioS016 = new SIFServizioS016();
            IAERequestS016 iaERequestS016 = new IAERequestS016();

            iaERequestS016.setUserName(checkDocumentRequest.getUsername());
            iaERequestS016.setPassword(checkDocumentRequest.getPassword());

            iaERequestS016.setNumeroDocumento(dossier.getDocumentNumber());
            iaERequestS016.setLuogoRilascioDocumento(dossier.getCountryIssue());

            Optional<Kiosk> kiosk = kioskRepository.findById(checkDocumentRequest.getNodeId());
            if(kiosk.isEmpty() || kiosk.get().getIdMachine() == null || kiosk.get().getIdMachine().isBlank()) {
                logService.saveEventLog(LogScopeEnum.INTERNAL.getValue(), "PreFile controller check_document", "Kiosk not found for dossier " + dossier.getIdMessage(), null);
                return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
            }
            String idMachine = kiosk.get().getIdMachine();
            iaERequestS016.setIdApplicazioneChiamante(idMachine);

            iaERequestS016.setCodiceTipoDocumento(
                    dossier.getDocumentType().equalsIgnoreCase(AbcEgateMessageDocumentTypeEnum.PASSPORT.getAbcEgateMessageDocumentTypeEnumValue()) ?
                            sifParametersRepository.findByType(SifParametersEnum.PASSPORT.toString()) : sifParametersRepository.findByType(SifParametersEnum.IDENTITY_CARD.toString()));
            /*iaERequestS016.setDescrizioneTipoDocumento(); //TODO: cosa fare con questi campi?
            iaERequestS016.setLuogoRilascioDocumento();
            iaERequestS016.setCodIso3LuogoRilDoc(); //NON ESISTE QUI MA LO LEGGO IN ESEMPI XML
            iaERequestS016.setDatiProprietario();
            iaERequestS016.setFlagSchengen();
            iaERequestS016.setFlagAsf();*/
            iaERequestS016.setIdTransazione(SoapUtils.generateSIFTransactionId(idMachine));
            iaERequestS016.setTimeOutClient(smartTimeoutClient ? Math.max(1, Math.min(Math.toIntExact(serverProperties.getTomcat().getConnectionTimeout().getSeconds()) - subConnectionTimeoutSeconds, 30)): 30);

            sifServizioS016.setRic(iaERequestS016);
            com.reco.ees.gateway.generated.sif.iae.SIFServizioS016Response sifServizioS016Response = soapClientSifIae.performS016(sifServizioS016);

            CheckPersonDocumentBcsResponse checkPersonDocumentBcsResponse = new CheckPersonDocumentBcsResponse();
            if (sifServizioS016Response != null && sifServizioS016Response.getSIFServizioS016Result() != null) {
                checkPersonDocumentBcsResponse.setEsito(sifServizioS016Response.getSIFServizioS016Result().getEsito());
                checkPersonDocumentBcsResponse.setResponseJson(objectMapper.writeValueAsString(sifServizioS016Response.getSIFServizioS016Result()));
                return ResponseEntity.ok(checkPersonDocumentBcsResponse);
            } else {
                RispostaS016 rispostaS016 = new RispostaS016();
                rispostaS016.setEsito(-2);
                SIFServizioS016Response response = new SIFServizioS016Response();
                response.setSIFServizioS016Result(rispostaS016);
                checkPersonDocumentBcsResponse.setEsito(rispostaS016.getEsito());
                checkPersonDocumentBcsResponse.setResponseJson(objectMapper.writeValueAsString(response.getSIFServizioS016Result()));
                return ResponseEntity.status(HttpStatus.NO_CONTENT).body(checkPersonDocumentBcsResponse);
            }
        } catch (Exception e) {
            RispostaS016 rispostaS016 = new RispostaS016();
            rispostaS016.setEsito(-1);
            SIFServizioS016Response response = new SIFServizioS016Response();
            response.setSIFServizioS016Result(rispostaS016);
            CheckPersonDocumentBcsResponse checkPersonDocumentBcsResponse = new CheckPersonDocumentBcsResponse();
            checkPersonDocumentBcsResponse.setEsito(rispostaS016.getEsito());
            try {
                checkPersonDocumentBcsResponse.setResponseJson(objectMapper.writeValueAsString(response.getSIFServizioS016Result()));
            } catch (JsonProcessingException ex) {
                throw new RuntimeException(ex);
            }
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(checkPersonDocumentBcsResponse);
        }
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CheckDocumentRequest {
        private String passengerId;
        private String nodeId;
        private String username;
        private String password;
    }

    @PostMapping("/check_bcs") //TODO testa vera chiamata (anche casi eccezionali per SIF e per webapp client), infine aggiungi logging (log e/o eventLog)
    @RolesAllowed("EGATE")
    public ResponseEntity<CheckPersonDocumentBcsResponse> check_bcs(@RequestBody CheckBCSRequest bcsAlertRequest) { //TODO: sembra inutile che CheckBCSRequest contenga anche username e password
        try {
            Dossier dossier = dossierService.getEgateDossierByNodeIdAndPassengerId(bcsAlertRequest.getNodeId(), bcsAlertRequest.getPassengerId());
            if (dossier == null || dossier.getDocumentNumber() == null || dossier.getCountryIssue() == null) {
                BCSAlertResponse bcsAlertResponse = new BCSAlertResponse();
                bcsAlertResponse.setEsito(-2);
                SIFServizioBCSResponse sifServizioBCSResponse = new SIFServizioBCSResponse();
                sifServizioBCSResponse.setSIFServizioBCSResult(bcsAlertResponse);
                CheckPersonDocumentBcsResponse checkPersonDocumentBcsResponse = new CheckPersonDocumentBcsResponse();
                checkPersonDocumentBcsResponse.setEsito(bcsAlertResponse.getEsito());
                checkPersonDocumentBcsResponse.setResponseJson(objectMapper.writeValueAsString(sifServizioBCSResponse.getSIFServizioBCSResult()));
                return ResponseEntity.status(HttpStatus.NOT_ACCEPTABLE).body(checkPersonDocumentBcsResponse);
            }

            SIFServizioBCS sifServizioBCS = new SIFServizioBCS();
            BCSAlertRequest request = new BCSAlertRequest();

            com.reco.ees.gateway.generated.sif.iae.Header bcsAlertRequestHeader = new com.reco.ees.gateway.generated.sif.iae.Header();

            Optional<Kiosk> kiosk = kioskRepository.findById(bcsAlertRequest.getNodeId());
            if(kiosk.isEmpty() || kiosk.get().getIdMachine() == null || kiosk.get().getIdMachine().isBlank()) {
                logService.saveEventLog(LogScopeEnum.INTERNAL.getValue(), "PreFile controller check_bcs", "Kiosk not found for dossier " + dossier.getIdMessage(), null);
                return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
            }
            String idMachine = kiosk.get().getIdMachine();
            bcsAlertRequestHeader.setIdApplicazioneChiamante(idMachine);
            bcsAlertRequestHeader.setIdTransazione(SoapUtils.generateSIFTransactionId(idMachine));
            bcsAlertRequestHeader.setTimeOutClient(smartTimeoutClient ? Math.max(1, Math.min(Math.toIntExact(serverProperties.getTomcat().getConnectionTimeout().getSeconds()) - subConnectionTimeoutSeconds, 30)) : 30);
            //bcsAlertRequestHeader.setServiceUrl();
            request.setHeader(bcsAlertRequestHeader);
            com.reco.ees.gateway.generated.sif.iae.Traveller bcsAlertRequestTraveller = new com.reco.ees.gateway.generated.sif.iae.Traveller();
            if(!dossier.getName().isBlank()) bcsAlertRequestTraveller.setFirstName(dossier.getName());
            if(!dossier.getSurname().isBlank()) bcsAlertRequestTraveller.setLastName(dossier.getSurname());
            if(!dossier.getCountryIssue().isBlank()) bcsAlertRequestTraveller.setCountryOfIssue(dossier.getCountryIssue());
            if(!dossier.getDocumentNumber().isBlank()) bcsAlertRequestTraveller.setDocNumber(dossier.getDocumentNumber());
            if(!dossier.getBirthdate().isBlank()) bcsAlertRequestTraveller.setDateOfBirth(DateUtil.convertStringToXmlGregorianCalendar(dossier.getBirthdate()));
            //bcsAlertRequestTraveller.setDocType();
            bcsAlertRequestTraveller.setNationality(dossier.getNationality());
            bcsAlertRequestTraveller.setAirportCode(airportCode);
            request.setTraveller(bcsAlertRequestTraveller);

            //TODO riempi altri campi eventualmente (e capire se davvero non servono username e password in questo caso perchè non vedo campo apposito da riempire)
            sifServizioBCS.setRic(request);
            com.reco.ees.gateway.generated.sif.iae.SIFServizioBCSResponse sifServizioBCSResponse = soapClientSifBcs.performBCS(sifServizioBCS);

            CheckPersonDocumentBcsResponse checkPersonDocumentBcsResponse = new CheckPersonDocumentBcsResponse();
            if (sifServizioBCSResponse != null && sifServizioBCSResponse.getSIFServizioBCSResult() != null) {
                checkPersonDocumentBcsResponse.setEsito(sifServizioBCSResponse.getSIFServizioBCSResult().getEsito());
                checkPersonDocumentBcsResponse.setResponseJson(objectMapper.writeValueAsString(sifServizioBCSResponse.getSIFServizioBCSResult()));
                return ResponseEntity.ok(checkPersonDocumentBcsResponse);
            } else {
                BCSAlertResponse bcsAlertResponse = new BCSAlertResponse();
                bcsAlertResponse.setEsito(-2);
                SIFServizioBCSResponse response = new SIFServizioBCSResponse();
                response.setSIFServizioBCSResult(bcsAlertResponse);
                checkPersonDocumentBcsResponse.setEsito(bcsAlertResponse.getEsito());
                checkPersonDocumentBcsResponse.setResponseJson(objectMapper.writeValueAsString(response.getSIFServizioBCSResult()));
                return ResponseEntity.status(HttpStatus.NO_CONTENT).body(checkPersonDocumentBcsResponse);
            }
        } catch (Exception e) {
            CheckPersonDocumentBcsResponse checkPersonDocumentBcsResponse = new CheckPersonDocumentBcsResponse();
            BCSAlertResponse bcsAlertResponse = new BCSAlertResponse();
            bcsAlertResponse.setEsito(-1);
            SIFServizioBCSResponse response = new SIFServizioBCSResponse();
            response.setSIFServizioBCSResult(bcsAlertResponse);
            checkPersonDocumentBcsResponse.setEsito(bcsAlertResponse.getEsito());
            try {
                checkPersonDocumentBcsResponse.setResponseJson(objectMapper.writeValueAsString(response.getSIFServizioBCSResult()));
            } catch (JsonProcessingException ex) {
                throw new RuntimeException(ex);
            }
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(checkPersonDocumentBcsResponse);
        }
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CheckBCSRequest {
        private String passengerId;
        private String nodeId;
        private String username;
        private String password;
    }

    @PutMapping("/force_processing") //TODO aggiungi logging (log e/o eventLog)
    @RolesAllowed("EGATE")
    public ResponseEntity<Boolean> force_processing(@RequestParam String nodeId, @RequestParam String passengerId, @RequestParam(required = false) String causale) {
        try {
            Dossier dossier = dossierService.getEgateDossierByNodeIdAndPassengerId(nodeId, passengerId);
            if (dossier == null || dossier.getEgateHandled().equals(Boolean.FALSE) || dossier.getHasToBeProcessed().equals(Boolean.TRUE)) return ResponseEntity.status(HttpStatus.NOT_ACCEPTABLE).build();
            dossier.setHasToBeProcessed(true);
            dossier.setSifInteractionCount(0);
            //dossier.setLastSifInteractionDate(null);
            if(causale != null && !causale.isEmpty()) dossier.setSurveyVersion(causale); //sto appoggiano temporaneamente causale in un campo comunque non usato dagli egates
            dossierService.save(dossier);
            return ResponseEntity.ok(true);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(false);
        }
    }

    @GetMapping("/get_residual_days") //TODO aggiungi logging (log e/o eventLog)
    @RolesAllowed("EGATE")
    public ResponseEntity<Integer> get_residual_days(@RequestParam String nodeId, @RequestParam String passengerId) {
        try {
            Dossier dossier = dossierService.getEgateDossierByNodeIdAndPassengerId(nodeId, passengerId);
            if (dossier == null || dossier.getEgateHandled().equals(Boolean.FALSE) || dossier.getResidualDays() == null) return ResponseEntity.status(HttpStatus.NOT_ACCEPTABLE).build();
            return ResponseEntity.ok(dossier.getResidualDays());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }

    @GetMapping("/search_by_personal_data_in_ees") //utile per operazioni atomiche //TODO  aggiungi logging (log e/o eventLog) + ees webapp client
    @RolesAllowed("EGATE")
    public ResponseEntity<SearchByPersonalDataResponseMessageType> search_by_personal_data_in_ees(@RequestParam String nodeId, @RequestParam String passengerId, @RequestBody PersonalDataSearchFilters filters) {
        try {
            Dossier dossier = dossierService.getEgateDossierByNodeIdAndPassengerId(nodeId, passengerId);
            if (dossier == null || dossier.getEgateHandled().equals(Boolean.FALSE) || dossier.getHasToBeProcessed().equals(Boolean.FALSE)) return ResponseEntity.status(HttpStatus.NOT_ACCEPTABLE).build();
            SearchByPersonalDataRequestMessageType searchByPersonalDataRequestMessageType = dossierService.mapDossierToSearchByPersonalDataRequest(dossier, filters);
            HeaderRequestType headerRequestType = dossierService.buildHeaderRequestType(dossier);
            SearchByPersonalDataResponseMessageType searchByPersonalDataResponseMessageType = soapClientWfe.performSearchByPersonalDataInEES(searchByPersonalDataRequestMessageType, headerRequestType);
            return ResponseEntity.ok(searchByPersonalDataResponseMessageType);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }

    @GetMapping("/search_by_personal_data_in_vis") //utile per operazioni atomiche //TODO  aggiungi logging (log e/o eventLog) + ees webapp client
    @RolesAllowed("EGATE")
    public ResponseEntity<SearchByPersonalDataInVISResponseMessageType> search_by_personal_data_in_vis(@RequestParam String nodeId, @RequestParam String passengerId, @RequestBody PersonalDataSearchFilters filters) {
        try {
            Dossier dossier = dossierService.getEgateDossierByNodeIdAndPassengerId(nodeId, passengerId);
            if (dossier == null || dossier.getEgateHandled().equals(Boolean.FALSE) || dossier.getHasToBeProcessed().equals(Boolean.FALSE)) return ResponseEntity.status(HttpStatus.NOT_ACCEPTABLE).build();
            SearchByPersonalDataInVISRequestMessageType searchByPersonalDataInVISRequestMessageType = dossierService.mapDossierToSearchByPersonalDataInVISRequest(dossier, filters);
            HeaderRequestType headerRequestType = dossierService.buildHeaderRequestType(dossier);
            SearchByPersonalDataInVISResponseMessageType searchByPersonalDataResponseMessageType = soapClientWfe.performSearchByPersonalDataInVIS(searchByPersonalDataInVISRequestMessageType, headerRequestType);
            return ResponseEntity.ok(searchByPersonalDataResponseMessageType);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }

    @GetMapping("/identification_result") //utile per operazioni atomiche //TODO  aggiungi logging (log e/o eventLog) + ees webapp client
    @RolesAllowed("EGATE")
    public ResponseEntity<IdentificationResultResponseMessageType> identification_result(@RequestParam String nodeId, @RequestParam String passengerId, @RequestParam String travellerFileId, @RequestParam String visaStickerNumber, @RequestParam String visaApplication) {
        try {
            Dossier dossier = dossierService.getEgateDossierByNodeIdAndPassengerId(nodeId, passengerId);
            if (dossier == null || dossier.getEgateHandled().equals(Boolean.FALSE) || dossier.getHasToBeProcessed().equals(Boolean.FALSE)) return ResponseEntity.status(HttpStatus.NOT_ACCEPTABLE).build();
            IdentificationResultRequestMessageType identificationResultRequestMessageType = dossierService.mapDossierToIdentificationResultRequest(dossier, travellerFileId, visaStickerNumber, visaApplication);
            HeaderRequestType headerRequestType = dossierService.buildHeaderRequestType(dossier);
            IdentificationResultResponseMessageType identificationResultResponseMessageType = soapClientWfe.performIdentificationResult(identificationResultRequestMessageType, headerRequestType);
            return ResponseEntity.ok(identificationResultResponseMessageType);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }

    /*@GetMapping("/identification_in_vis") //utile per operazioni atomiche? //TODO aggiungi logging (log e/o eventLog) + ees webapp client
    @RolesAllowed("EGATE")
    public ResponseEntity<IdentificationInVISResponseMessageType> identification_in_vis(@RequestParam String nodeId) { //TODO utile? E parametri come in identification_result()?
        try {
            Dossier dossier = dossierService.getDossierByNodeId(nodeId);
            if (dossier == null || dossier.getEgateHandled().equals(Boolean.FALSE) || dossier.getHasToBeProcessed().equals(Boolean.FALSE)) return ResponseEntity.status(HttpStatus.NOT_ACCEPTABLE).build();
            IdentificationInVISRequestMessageType identificationInVISRequestMessageType = dossierService.mapDossierToIdentificationInVISRequest(dossier);
            HeaderRequestType headerRequestType = dossierService.buildHeaderRequestType(dossier);
            IdentificationInVISResponseMessageType identificationInVISResponseMessageType = soapClientWfe.performIdentificationInVIS(identificationInVISRequestMessageType, headerRequestType);
            return ResponseEntity.ok(identificationInVISResponseMessageType);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }*/

    @GetMapping("/get_attachment") //utile per operazioni atomiche //TODO  aggiungi logging (log e/o eventLog) + ees webapp client
    @RolesAllowed("EGATE")
    public ResponseEntity<AttachmentResponseType> getAttachment(@RequestParam String uriPath, @RequestParam String context) {
        if(!context.equalsIgnoreCase("EES") && !context.equalsIgnoreCase("VIS")) {
            return ResponseEntity.status(HttpStatus.NOT_ACCEPTABLE).build();
        }
        try {
            AttachmentRequestType attachmentRequestType = new AttachmentRequestType();
            attachmentRequestType.setFileName(uriPath);
            attachmentRequestType.setContext(context);
            //TODO: implementa correttamente quando saprai cosa fare (teoricamente utile quando operatore effettua ricerca con SearchByPersonalData e ottiene uriPath di biometrie che vuole visualizzare)
            //HeaderRequestType headerRequestType = dossierService.buildHeaderRequestType(dossier);
            AttachmentResponseType attachmentResponseType = null; //soapClientWfe.performGetAttachment(attachmentRequestType, headerRequestType);
            return ResponseEntity.ok(attachmentResponseType);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SearchFilter {
        @JsonProperty(defaultValue = "esatto")
        private String mode = "esatto";
        @JsonProperty(defaultValue = "100")
        private short weight = 100;
        @JsonProperty(defaultValue = "false")
        private boolean ignore = false;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PersonalDataSearchFilters {
        //@JsonProperty(required = false)
        private SearchFilter cognome;
        private SearchFilter nome;
        private SearchFilter dataNascita;
        private SearchFilter nazionalita;
        private SearchFilter sesso;
        private SearchFilter numeroDoc;
        private SearchFilter tipoDoc;
        private SearchFilter validoFino;
        private SearchFilter emessoDa;
        private boolean anyName = false; //solo per cognome
    }
}
