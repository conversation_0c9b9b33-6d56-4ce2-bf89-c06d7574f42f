package com.reco.ees.gateway.kafka.consumer;

import com.fasterxml.jackson.databind.JsonNode;
import com.reco.ees.gateway.config.KafkaRetryService;
import com.reco.ees.gateway.enums.AbcEgateMessageEssentialField;
import com.reco.ees.gateway.enums.AbcEgateMessageTypeEnum;
import com.reco.ees.gateway.enums.DossierStatusEnum;
import com.reco.ees.gateway.enums.LogScopeEnum;
import com.reco.ees.gateway.kafka.model.AbcEgateMessage;
import com.reco.ees.gateway.kafka.producer.KafkaProducer;
import com.reco.ees.gateway.mapper.DossierMapper;
import com.reco.ees.gateway.repository.model.Dossier;
import com.reco.ees.gateway.service.DossierService;
import com.reco.ees.gateway.service.LogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Field;
import java.time.Duration;
import java.util.Date;
import java.util.EnumMap;
import java.util.List;
import java.util.Map;

import static com.reco.ees.gateway.kafka.producer.KafkaProducer.unknownFieldValue;


@Slf4j
@Component
public class AbcEgateConsumer extends AbstractKafkaConsumer {
    private final KafkaProducer kafkaProducer;
    private final KafkaRetryService kafkaRetryService;
    private final DossierMapper dossierMapper;
    private final DossierService dossierService;
    private final LogService logService;


    @Value("${application.name}")
    private String applicationName;
    @Value("${kafka.received.message.show:false}")
    private boolean showReceivedMessage;

    public AbcEgateConsumer(KafkaProducer kafkaProducer, KafkaRetryService kafkaRetryService, DossierMapper dossierMapper, DossierService dossierService, LogService logService){
        super();
        this.kafkaProducer = kafkaProducer;
        this.kafkaRetryService = kafkaRetryService;
        this.dossierMapper = dossierMapper;
        this.dossierService = dossierService;
        this.logService = logService;
    }

    @KafkaListener(topicPattern = "${kafka.node.abc.process.topicPattern}", groupId = "${kafka.node.abc.process.groupId}", containerFactory = "kafkaListenerContainerFactory")
    @Override
    public void onMessage(@Payload String message, @Header(KafkaHeaders.RECEIVED_TOPIC) String topic, Acknowledgment acknowledgment) {
        Map<AbcEgateMessageEssentialField, String> metadata = null;
        try {
            log.info("AbcEgateConsumer running on {} thread", Thread.currentThread().isVirtual() ? "virtual" : "platform");

            metadata = extractMessageMetadata(message);

            List<String> typesToIgnore = List.of(
                    AbcEgateMessageTypeEnum.QUEUE_ALERT.getAbcEgateMessageTypeEnumValue(),
                    AbcEgateMessageTypeEnum.PANIC_ALERT.getAbcEgateMessageTypeEnumValue(),
                    AbcEgateMessageTypeEnum.PERFORM_ENDBORDERCONTROL.getAbcEgateMessageTypeEnumValue(),
                    AbcEgateMessageTypeEnum.PROBLEM.getAbcEgateMessageTypeEnumValue(),
                    AbcEgateMessageTypeEnum.OPERAZIONI_ATOMICHE.getAbcEgateMessageTypeEnumValue(),
                    unknownFieldValue
            );

            if (typesToIgnore.contains(metadata.get(AbcEgateMessageEssentialField.TYPE))) {
                acknowledgment.acknowledge();
                log.info("Ignored message {} of type {} received from nodeId {} for passenger with passengerId {}",
                        metadata.get(AbcEgateMessageEssentialField.ID), metadata.get(AbcEgateMessageEssentialField.TYPE),
                        metadata.get(AbcEgateMessageEssentialField.NODE_ID), metadata.get(AbcEgateMessageEssentialField.PASSENGER_ID));
                return;
            }

            List<String> typesToProcessWithoutMapping = List.of(AbcEgateMessageTypeEnum.ENDED_PROCESS.getAbcEgateMessageTypeEnumValue());

            AbcEgateMessage abcEgateMessage = new AbcEgateMessage();
            if(!typesToProcessWithoutMapping.contains(metadata.get(AbcEgateMessageEssentialField.TYPE))) {
                abcEgateMessage = mapper.readValue(message, AbcEgateMessage.class);
            } else {
                abcEgateMessage.setId(metadata.get(AbcEgateMessageEssentialField.ID));
                abcEgateMessage.setNodeId(metadata.get(AbcEgateMessageEssentialField.NODE_ID));
                abcEgateMessage.setType(metadata.get(AbcEgateMessageEssentialField.TYPE));
                abcEgateMessage.setPassengerId(metadata.get(AbcEgateMessageEssentialField.PASSENGER_ID));
                abcEgateMessage.setIdChiamante(metadata.get(AbcEgateMessageEssentialField.ID_CHIAMANTE));
                abcEgateMessage.setIs_entry_node(metadata.get(AbcEgateMessageEssentialField.IS_ENTRY_NODE));
            }
            if (showReceivedMessage) log.info("Received message: {}", abcEgateMessage);

            if(missingEssentialInfo(abcEgateMessage)) { //hasNullOrBlankOrUnknownFields(abcEgateMessage)
                acknowledgment.acknowledge();
                log.error("AbcEgateConsumer Message with id {}: has null or blank or unknown fields, acknowledging and giving up", abcEgateMessage.getId());
                logService.saveEventLog(LogScopeEnum.INTERNAL.getValue(), "AbcEgate consumer", "Message with id " + abcEgateMessage.getId() + " has null or blank or unknown fields", null);
                kafkaProducer.sendAbcEgateProblemMessage(new Exception("Message with id " + abcEgateMessage.getId() + " has null or blank or unknown essential fields"), metadata);
                return;
            }
            dossierService.handleNewOrUpdatedKioskOrEgateAndSifAuth(
                    metadata.get(AbcEgateMessageEssentialField.NODE_ID),
                    metadata.get(AbcEgateMessageEssentialField.ID_CHIAMANTE),
                    true,
                    Boolean.valueOf(metadata.get(AbcEgateMessageEssentialField.IS_ENTRY_NODE)),
                    "AbcEgateConsumer"
            );
            handleReceivedAbcMessage(abcEgateMessage, acknowledgment);
        } catch (Exception ex) {
            String messageId;
            // Se metadata non è stato inizializzato a causa di un errore in extractMessageMetadata, sarà null qui.
            if (metadata != null && metadata.get(AbcEgateMessageEssentialField.ID) != null && !metadata.get(AbcEgateMessageEssentialField.ID).equals(unknownFieldValue)) {
                messageId = metadata.get(AbcEgateMessageEssentialField.ID);
            } else {
                // Tentativo di ri-estrarre o generare un ID se metadata è null o l'ID è unknown
                try {
                    Map<AbcEgateMessageEssentialField, String> fallbackMetadata = extractMessageMetadata(message);
                    messageId = fallbackMetadata.getOrDefault(AbcEgateMessageEssentialField.ID, "msg-" + message.hashCode());
                    if (messageId.equals(unknownFieldValue)) messageId = "msg-" + message.hashCode();
                    metadata = fallbackMetadata; // aggiorna metadata per sendAbcEgateProblemMessage
                } catch (Exception e) {
                    messageId = "msg-" + message.hashCode();
                    log.warn("Could not extract message ID even on fallback, using generated ID: {}", messageId);
                }
            }

            int retryCount = kafkaRetryService.getAndIncrementRetryCount(messageId);
            if (retryCount < 3) {
                log.warn("AbcEgateConsumer Message {}: retry {}/3 - Error processing message due to: {}", messageId, retryCount, ex.getMessage());
                acknowledgment.nack(Duration.ofSeconds(60));
                log.info("AbcEgateConsumer Message {} nacked, will be redelivered soon (attempt {}/3)", messageId, retryCount);
            } else {
                log.warn("AbcEgateConsumer Message {}: max retry attempts (3) reached, acknowledging and giving up", messageId);
                logService.saveEventLog(LogScopeEnum.INTERNAL.getValue(), "AbcEgate consumer", "Max retry attempts (3) reached for message " + messageId + " due to " + ex.getMessage(), null);
                kafkaRetryService.clearRetryCount(messageId);
                acknowledgment.acknowledge();
                log.error("AbcEgateConsumer error for app instance {} due to: {}", applicationName, ex.getMessage());
                kafkaProducer.sendAbcEgateProblemMessage(ex, metadata); // metadata potrebbe essere null qui se l'estrazione iniziale fallisce
            }
        }
    }

    @Transactional
    protected void handleReceivedAbcMessage(AbcEgateMessage abcEgateMessage, Acknowledgment acknowledgment) {
        System.out.println("Handling message: " + abcEgateMessage);

        String abcEgateMessageType = abcEgateMessage.getType();

        if(abcEgateMessageType.equalsIgnoreCase(AbcEgateMessageTypeEnum.DOCUMENT.getAbcEgateMessageTypeEnumValue())) { //il tipo document e' sicuramente il primo ad arrivare per un certo nodeId/viaggiatore, poi forse potrebbe arrivare FACE e/o FINGER e/o niente
            //if(Boolean.parseBoolean(abcEgateMessage.getHas_to_be_processed())) {
                //TODO: non dovrebbe mai accadere ma eventualmente cosa fare se esiste già un dossier con lo stesso nodeId/document_number/country_issue?
                Dossier newDossier = dossierMapper.convertAbcEgateMessageToDossier(abcEgateMessage);
                newDossier.setCreationDate(new Date());
                newDossier.setStatus(abcEgateMessage.getHas_to_be_processed().equalsIgnoreCase("True") ? DossierStatusEnum.RECEIVED_KAFKA_DOSSIER.getDossierStatusEnumValue() : DossierStatusEnum.EGATE_FINISHED.getDossierStatusEnumValue());
                newDossier.setSifInteractionCount(0);
                newDossier.setLastSifInteractionDate(null);
                newDossier.setSifTransactionId(null);
                newDossier.setApplication(applicationName);
                dossierService.save(newDossier);

                acknowledgment.acknowledge();
                log.info("Message {} of type {} received from nodeId {} has been processed", abcEgateMessage.getId(), abcEgateMessage.getType(), abcEgateMessage.getNodeId());
            /*} else {
                acknowledgment.acknowledge();
                log.info("Message {} received from nodeId {} has not to be processed", abcEgateMessage.getId(), abcEgateMessage.getNodeId());
            }*/
        } else if(abcEgateMessageType.equalsIgnoreCase(AbcEgateMessageTypeEnum.FACE.getAbcEgateMessageTypeEnumValue()) || abcEgateMessageType.equalsIgnoreCase(AbcEgateMessageTypeEnum.FINGERPRINT.getAbcEgateMessageTypeEnumValue())) {
            Dossier existingEgateDossier = dossierService.getEgateDossierByNodeIdAndPassengerId(abcEgateMessage.getNodeId(), abcEgateMessage.getPassengerId());
            /*if(existingEgateDossier == null || (existingEgateDossier.getStatus().equalsIgnoreCase(DossierStatusEnum.START_BORDER_CONTROL_KO.getDossierStatusEnumValue())) || existingEgateDossier.getStatus().equalsIgnoreCase(DossierStatusEnum.RECEIVED_KAFKA_DOSSIER.getDossierStatusEnumValue())) {
                //acknowledgment.acknowledge();
                int retryCount = kafkaRetryService.getAndIncrementRetryCount(abcEgateMessage.getId());
                if (retryCount < 3) {
                    log.warn("AbcEgateConsumer Message {}: retry {}/3 - il viaggiatore non è mai passato da un eGate o non è stata ancora ricevuta risposta dalla chiamata asincrona precedente (StartBorderControl o AddDataToBorderControl) eseguita per conto di eGate, riproverò più tardi", abcEgateMessage.getId(), retryCount);
                    acknowledgment.nack(Duration.ofSeconds(30));
                    log.info("AbcEgateConsumer Message {} nacked, will be redelivered soon (attempt {}/3)", abcEgateMessage.getId(), retryCount);
                } else {
                    log.warn("AbcEgateConsumer Message {}: max retry attempts (3) reached, acknowledging and giving up", abcEgateMessage.getId());
                    kafkaRetryService.clearRetryCount(abcEgateMessage.getId());
                    acknowledgment.acknowledge();
                }
                return;
                *//*try {
                    // This will pause processing of this partition for 30 seconds
                    acknowledgment.nack(java.time.Duration.ofSeconds(30));
                    log.info("Message {} nacked, will be redelivered in approximately 30 seconds", abcEgateMessage.getId());
                } catch (UnsupportedOperationException e) {
                    // Fallback for older Spring Kafka versions that don't support nack with duration
                    acknowledgment.nack(0);
                    log.info("Message {} nacked using legacy method", abcEgateMessage.getId());
                }*//*
                //posso evitare di scrivere dati su DB perchè li riceverò nuovamente dalla coda dato che non ho fatto ack (assicurati che il messaggio venga ritrasmesso (e max tot volte ogni X secondi) in caso di mancato ack o controlla se devi inviare NACK - eventuale configura proprieta' del topic)
            }*/
            dossierService.updateEgateDossier(existingEgateDossier, abcEgateMessage);
            acknowledgment.acknowledge();
            log.info("Message {} of type {} received from nodeId {} has been handled by AbcEgateConsumer", abcEgateMessage.getId(), abcEgateMessage.getType(), abcEgateMessage.getNodeId());
        } else if(abcEgateMessageType.equalsIgnoreCase(AbcEgateMessageTypeEnum.ENDED_PROCESS.getAbcEgateMessageTypeEnumValue())) {
            Dossier existingEgateDossier = dossierService.getEgateDossierByNodeIdAndPassengerId(abcEgateMessage.getNodeId(), abcEgateMessage.getPassengerId());
            if(existingEgateDossier == null) log.warn("Message with id {}: non ho nulla da cancellare", abcEgateMessage.getId());
            else dossierService.deleteSensitiveData(existingEgateDossier);
            acknowledgment.acknowledge();
            log.info("Message {} of type {} received from nodeId {} for passenger with passengerId {} has been handled by AbcEgateConsumer", abcEgateMessage.getId(), abcEgateMessage.getType(), abcEgateMessage.getNodeId(), abcEgateMessage.getPassengerId());
        }
    }

    private Map<AbcEgateMessageEssentialField, String> extractMessageMetadata(String jsonMessage) {
        Map<AbcEgateMessageEssentialField, String> fieldValues = new EnumMap<>(AbcEgateMessageEssentialField.class);
        try {
            JsonNode rootNode = mapper.readTree(jsonMessage);
            for (AbcEgateMessageEssentialField field : AbcEgateMessageEssentialField.values()) {
                fieldValues.put(field, rootNode.path(field.getKeyName()).asText(unknownFieldValue));
            }
        } catch (Exception e) {
            log.error("Error extracting message metadata: {}. Returning map with unknown values.", e.getMessage());
            // Popola la mappa con unknownFieldValue in caso di errore di parsing
            for (AbcEgateMessageEssentialField field : AbcEgateMessageEssentialField.values()) {
                fieldValues.putIfAbsent(field, unknownFieldValue);
            }
        }
        return fieldValues;
    }

    private boolean missingEssentialInfo(AbcEgateMessage abcEgateMessage) {
        for (AbcEgateMessageEssentialField fieldEnum : AbcEgateMessageEssentialField.values()) {
            // Si assume che AbcEgateMessage abbia campi Java con nomi corrispondenti a quelli definiti in AbcEgateMessageEssentialField.getKeyName() (es. "id", "nodeId", "is_entry_node").
            // Se AbcEgateMessage usa camelCase (es. "isEntryNode") e AbcEgateMessageEssentialField.getKeyName() è snake_case ("is_entry_node"), la reflection diretta fallirà qui.
            // È necessario che i nomi dei campi in AbcEgateMessage o siano identici a getKeyName() o che si usi @JsonProperty in AbcEgateMessage per la mappatura da JSON e poi si usi il nome del campo Java corretto per la reflection.
            // Per semplicità, qui si assume corrispondenza diretta o che Jackson abbia già mappato correttamente se i nomi dei campi Java sono diversi ma annotati con @JsonProperty.
            String fieldNameForReflection = fieldEnum.getKeyName();
            try {
                Field field = AbcEgateMessage.class.getDeclaredField(fieldNameForReflection);
                field.setAccessible(true);
                Object value = field.get(abcEgateMessage);
                if (value == null) return true;
                if (value instanceof String strValue) {
                    if (strValue.isBlank() || strValue.equals(unknownFieldValue)) return true;
                }
                // Aggiungere qui controlli per altri tipi se necessario (es. Boolean, Integer) come if (value instanceof Boolean boolValue) { ... }
            } catch (NoSuchFieldException e) {
                // Questo accade se il nome del campo definito in AbcEgateMessageEssentialField.getKeyName() non corrisponde esattamente a un nome di campo in AbcEgateMessage.
                log.warn("Field '{}' not found in AbcEgateMessage for validation. Assuming it's missing or not essential for this check.", fieldNameForReflection);
                return true; //continue;
            }
            catch (Exception e) { // Cattura IllegalAccessException e altre eccezioni di reflection
                log.error("Error accessing field '{}' for validation: {}", fieldNameForReflection, e.getMessage());
                return true;
            }
        }
        return false;
    }

    /*public static boolean hasNullOrBlankOrUnknownFields(Object obj) {
        return hasNullOrBlankOrUnknownFields(obj, new java.util.HashSet<>());
    }

    private static boolean hasNullOrBlankOrUnknownFields(Object obj, java.util.Set<Object> visited) {
        if (obj == null || visited.contains(obj)) return true;
        visited.add(obj);

        for (var field : obj.getClass().getDeclaredFields()) {
            field.setAccessible(true);
            try {
                Object value = field.get(obj);
                if (value == null) return true;
                if (value instanceof String str) {
                    if (str.isBlank() || str.equals(unknownFieldValue)) return true;
                } else if (value instanceof java.util.Collection<?> col) {
                    for (Object item : col) {
                        if (hasNullOrBlankOrUnknownFields(item, visited)) return true;
                    }
                } else if (!field.getType().isPrimitive() && !field.getType().getName().startsWith("java.")) {
                    if (hasNullOrBlankOrUnknownFields(value, visited)) return true;
                }
            } catch (IllegalAccessException e) {
                return true;
            }
        }
        return false;
    }*/

    /*private synchronized void handleNewOrUpdatedEgateAndItsSifAuth(AbcEgateMessage abcEgateMessage) {
        if(abcEgateMessage.getNodeId() != null && !abcEgateMessage.getNodeId().isBlank() && abcEgateMessage.getIdChiamante() != null && !abcEgateMessage.getIdChiamante().isBlank()) {
            kioskRepository.findById(abcEgateMessage.getNodeId()).ifPresentOrElse(egate -> {
                if(egate.getIdMachine() == null || egate.getIdMachine().isBlank()) {
                    egate.setIdMachine(abcEgateMessage.getIdChiamante());
                    egate.setIsEgate(true);
                    egate.setIsEntryEgate(Boolean.valueOf(abcEgateMessage.getIs_entry_node()));
                    kioskRepository.saveAndFlush(egate);
                    log.info("AbcEgateConsumer: eGate with nodeId {} updated with idMachine {}", abcEgateMessage.getNodeId(), abcEgateMessage.getIdChiamante());
                    logService.saveEventLog(LogScopeEnum.INTERNAL.getValue(), "AbcEgate consumer", "eGate with nodeId " + abcEgateMessage.getNodeId() + " updated with idMachine " + abcEgateMessage.getIdChiamante(), null);
                }
                if(egate.getSifAuth() == null || sifAuthService.shouldDoSifAuth(egate)) waitAndDoSifLogin(abcEgateMessage, egate);
            }, () -> {
                Kiosk egate = new Kiosk();
                egate.setIdKiosk(abcEgateMessage.getNodeId());
                egate.setIdMachine(abcEgateMessage.getIdChiamante());
                egate.setIsEgate(true);
                egate.setIsEntryEgate(Boolean.valueOf(abcEgateMessage.getIs_entry_node()));
                kioskRepository.saveAndFlush(egate);
                log.info("AbcEgateConsumer: saved new eGate with nodeId {} and idMachine {}", abcEgateMessage.getNodeId(), abcEgateMessage.getIdChiamante());
                waitAndDoSifLogin(abcEgateMessage, egate);
            });
        }
    }

    private void waitAndDoSifLogin(AbcEgateMessage abcEgateMessage, Kiosk eGate) {
        String logId = null;
        AutoScheduledJobsLock autoScheduledJobsLock = autoScheduledJobsLockRepository.findByJobName(AutoScheduledJobsLockEnum.SIF_AUTH_JOB.getAutoScheduledJobsLockEnumValue());
        if(autoScheduledJobsLock != null) {
            log.info("AbcEgateConsumer is waiting to be able to do SifAuth for eGate with nodeId {}", abcEgateMessage.getNodeId());
            logId = logService.saveEventLog(LogScopeEnum.INTERNAL.getValue(), "AbcEgate consumer", "Waiting to be able to do SifAuth for eGate with nodeId " + abcEgateMessage.getNodeId(), null);
            while (autoScheduledJobsLock != null) {
                try {
                    Thread.sleep(1000L);
                } catch (InterruptedException e) {
                    log.error("Error sleeping AbcEgateConsumer thread for 1 second due to: {}", e.getMessage());
                }
                autoScheduledJobsLock = autoScheduledJobsLockRepository.findByJobName(AutoScheduledJobsLockEnum.SIF_AUTH_JOB.getAutoScheduledJobsLockEnumValue());
            }
        }
        if(!dossierService.isKioskIdPresent(eGate.getIdKiosk())) {
            dossierService.addKioskId(eGate.getIdKiosk());
            log.info("AbcEgateConsumer triggered SifAuth for eGate with nodeId {}", abcEgateMessage.getNodeId());
            logService.saveEventLog(LogScopeEnum.INTERNAL.getValue(), "AbcEgate consumer", "Triggered SifAuth for eGate with nodeId " + abcEgateMessage.getNodeId(), null, logId);
            //sifAuthJob.sifLogin(List.of(kiosk), false);
            dynamicSchedulingService.runAndWaitSifLoginForSpecificKiosks(List.of(eGate));
            dossierService.removeKioskId(eGate.getIdKiosk());
        }
    }*/

    /*test effettuati con sequenza di sequenze SoapUI salvate in risorse di progetto (oltre ai casi con has_to_be_processed False nei vari tipi di messaggi e oltre ai "manca pre fascicolo" e "lavora insieme a RegistrationConsumer" e problemi di rete/SIF/attesa/crash/timeout e casi di gateway multi-istanza):
    SoapUI #1- doc + finger + face + fine
    SoapUI #2- doc + face + finger + fine
    SoapUI #3- doc + fine
    SoapUI #4- doc + finger + fine
    SoapUI #5- doc + face + fine
    */

    //scarta messaggi con type: QUEUE_ALERT, PANIC_ALERT, PERFORM_ENDBORDERCONTROL, ENDED_PROCESS: ack e dimentica/logga
    //caso document (sicuramente il primo ad arrivare), poi forse potrebbe arrivare FACE e/o FINGER e/o niente:
        //controlla campo has_to_be_processed di document:
            //se "True" allora fai StartBorderControl e quest'ultimo chiede o Face o Finger o niente:
                //se Face allora su coda specifica (contenente il nodeId del gate specifico che mi ha mandato la document precedente) invia: {"DocumentCode":"ITAYC1151399","Result":"NEED_FACE", "Details": ""}
                //se Finger allora su coda specifica (contenente il nodeId del gate specifico che mi ha mandato la document precedente) invia: {"DocumentCode":"ITAYC1151399","Result":"NEED_FINGER", "Details": ""}
                //se niente (cioè ritorna 5019) allora su coda specifica (contenente il nodeId del gate specifico che mi ha mandato la document precedente) invia (avvalorando correttamente il valore di Details ma per ora inventa valore): {"DocumentCode":"ITAYC1151399","Result":"COMPLETE", "Details": "20"}
                //se "manca prefascicolo" (poi capiremo come capirlo) allora su coda specifica (contenente il nodeId del gate specifico che mi ha mandato la document precedente) invia {"DocumentCode":"ITAYC1151399","Result":"REDIRECT_TO_KIOSK", "Details": ""}
            //se "False" allora logga e ack e dimentica

    //caso face/finger
        //controlla campo has_to_be_processed di face/finger:
            //se non hai mai fatto una StartBorderControl corrispondente allora logga e ack e dimentica
            //se hai fatto StartBorderControl in precedenza allora:
                //se "True": allora fai addDataToBorderControl e quest'ultimo chiede o Face o Finger o niente (non fare nulla se chiede ciò che ho appena inviato cioè ad esempio invio Face ma richiede Face):
                    //se Face allora su coda specifica (contenente il nodeId del gate specifico che mi ha mandato la document precedente) invia: {"DocumentCode":"ITAYC1151399","Result":"NEED_FACE", "Details": ""}
                    //se Finger allora su coda specifica (contenente il nodeId del gate specifico che mi ha mandato la document precedente) invia: {"DocumentCode":"ITAYC1151399","Result":"NEED_FINGER", "Details": ""}
                    //se niente (cioè ritorna 5019) allora su coda specifica (contenente il nodeId del gate specifico che mi ha mandato la document precedente) invia (avvalorando correttamente il valore di Details ma per ora inventa valore): {"DocumentCode":"ITAYC1151399","Result":"COMPLETE", "Details": "20"}
                //se "False": salva su DB (come NodeAbcProcess record di webapp ees), ack e logga
}
