package com.reco.ees.gateway.kafka.model;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import lombok.Data;

@Data
public class AbcEgateMessage {
    private String id;
    private String nodeId;
    private String nodeName;
    private String idChiamante;
    private String timestamp;
    private String passengerId;
    private String type;
    private String has_to_be_processed;
    private String is_entry_node;

    @JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXTERNAL_PROPERTY, property = "type")
    @JsonSubTypes({
            @JsonSubTypes.Type(value = AbcEgateMessageContentFingerprint.class, name = "FINGERPRINT"),
            @JsonSubTypes.Type(value = AbcEgateMessageContentFace.class, name = "FACE"),
            @JsonSubTypes.Type(value = AbcEgateMessageContentDocument.class, name = "DOCUMENT"),
    })
    private Object content;
}
