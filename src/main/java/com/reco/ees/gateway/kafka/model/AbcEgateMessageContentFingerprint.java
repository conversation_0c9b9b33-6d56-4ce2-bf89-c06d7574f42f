package com.reco.ees.gateway.kafka.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reco.ees.gateway.util.UtilsService;
import lombok.Data;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class AbcEgateMessageContentFingerprint {
    @JsonProperty("is_complete_hand")
    private boolean is_complete_hand;
    private int score;
    @JsonProperty("is_spoof")
    private boolean is_spoof;
    @JsonProperty("is_no_verification_match")
    private boolean is_no_verification_match;
    @Setter
    //@JsonSerialize(using = CustomImageSerializer.class)
    private transient String image; //base64 cifrata del NIST file
    /*@JsonProperty("image_hand_real") //commentato perchè inutile al gateway
    private String image_hand_real; //base64 cifrata di un'immagine
    @JsonProperty("image_hand_chip") //commentato perchè inutile al gateway
    private String image_hand_chip; //base64 cifrata di un'immagine*/
    private List<Nfiq> nfiq;

    @Data
    public static class Nfiq {
        private String finger;
        private int nfiq;
    }


    public String getImage() {
        try {
            String img = UtilsService.decryptContentWithIV(image.getBytes());
            if(img != null) return img;
            else return image;
        } catch (Exception e) {
            log.error("Error decrypting fingerprint image; returning the original image", e);
            return image;
        }
    }

    /*public ObjectNode toSerializableObject() {
        ObjectMapper mapper = new ObjectMapper();
        ObjectNode node = mapper.valueToTree(this);
        node.put("image", getImage());
        return node;
    }*/

    /*public static class CustomImageSerializer extends JsonSerializer<String> {
        @Override
        public void serialize(String value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
            gen.writeString(value);
        }
    }*/
}