package com.reco.ees.gateway.kafka.producer;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.reco.ees.gateway.enums.AbcEgateMessageEssentialField;
import com.reco.ees.gateway.enums.AbcEgateMessageTypeEnum;
import com.reco.ees.gateway.enums.AbcEgateResponseMessageResultEnum;
import com.reco.ees.gateway.kafka.model.AbcEgateOperazioniAtomicheMessage;
import com.reco.ees.gateway.kafka.model.AbcEgateProblemMessage;
import com.reco.ees.gateway.kafka.model.AbcEgateResponseMessage;
import com.reco.ees.gateway.repository.KioskRepository;
import com.reco.ees.gateway.repository.model.Dossier;
import com.reco.ees.gateway.repository.model.Kiosk;
import com.reco.ees.gateway.util.UtilsService;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Service
@RequiredArgsConstructor
public class KafkaProducer {
    @Value("${kafka.sent.message.show:false}")
    private boolean showSentMessage;
    @Value("${kafka.node.abc.response.topicPattern}")
    private String kafkaNodeAbcResponseTopicPattern;
    @Value("${kafka.node.abc.process.topicPattern}")
    private String kafkaNodeAbcProcessTopicPattern;
    @Getter
    public static final String unknownFieldValue = "unknown";

    private final KafkaTemplate<String, String> kafkaTemplate;
    private final UtilsService utilsService;
    private final KioskRepository kioskRepository;
    private final ObjectMapper objectMapper;

    public CompletableFuture<SendResult<String, String>> sendMessage(String topic, Object message) {
        try {
            String serializedMessage = utilsService.serializeMessage(message);
            // log.debug("Sending message to topic {}: {}", topic, serializedMessage);
            return kafkaTemplate.send(topic, serializedMessage);
        } catch (Exception ex) {
            log.error("Failed to send message to topic {}: {}", topic, message, ex);
            // throw new KafkaProductionException("Failed to send message", ex);
            return CompletableFuture.failedFuture(ex);
        }
    }


    public void sendAbcEgateProblemMessage(Exception ex, Map<AbcEgateMessageEssentialField, String> metadata) {
        AbcEgateProblemMessage abcEgateProblemMessage = new AbcEgateProblemMessage();
        Kiosk egate = kioskRepository.findById(metadata != null ? metadata.get(AbcEgateMessageEssentialField.NODE_ID) : unknownFieldValue).orElse(null);
        abcEgateProblemMessage.setId(UUID.randomUUID().toString());
        abcEgateProblemMessage.setNodeId(metadata != null ? metadata.get(AbcEgateMessageEssentialField.NODE_ID) : unknownFieldValue);
        abcEgateProblemMessage.setTimestamp(new Date().toString());
        abcEgateProblemMessage.setPassengerId(metadata != null ? metadata.get(AbcEgateMessageEssentialField.PASSENGER_ID) : unknownFieldValue);
        abcEgateProblemMessage.setType(AbcEgateMessageTypeEnum.PROBLEM.getAbcEgateMessageTypeEnumValue());
        abcEgateProblemMessage.setHas_to_be_processed("False");
        abcEgateProblemMessage.setIdChiamante(metadata != null ? metadata.get(AbcEgateMessageEssentialField.ID_CHIAMANTE) : unknownFieldValue);
        abcEgateProblemMessage.setNodeName(egate != null ? egate.getIdMachine() : "EES-GATEWAY");
        abcEgateProblemMessage.setIs_entry_node(metadata != null ? metadata.get(AbcEgateMessageEssentialField.IS_ENTRY_NODE) : unknownFieldValue);
        AbcEgateProblemMessage.Content content = new AbcEgateProblemMessage.Content();
        content.setData("True");
        abcEgateProblemMessage.setContent(content);

        String abcEgateProblemMessageSerialized = null;
        try {
            abcEgateProblemMessageSerialized = objectMapper.writeValueAsString(abcEgateProblemMessage);
        } catch (JsonProcessingException e) {
            abcEgateProblemMessageSerialized = abcEgateProblemMessage.getId();
        }
        try {
            String nodeIdForTopic = metadata != null ? metadata.getOrDefault(AbcEgateMessageEssentialField.NODE_ID, unknownFieldValue) : unknownFieldValue;
            if (nodeIdForTopic.equals(unknownFieldValue)) {
                nodeIdForTopic = "unknown_node";
            }
            String topicName = kafkaNodeAbcProcessTopicPattern.replace("*", nodeIdForTopic);

            String finalAbcEgateProblemMessageSerialized = abcEgateProblemMessageSerialized;
            sendMessage(topicName, abcEgateProblemMessage)
                    .thenAccept(kafkaResult -> {
                        log.info("{}: sent message=[{}] with offset=[{}]", utilsService.getCurrentAndCallerMethodInfo(), showSentMessage ? finalAbcEgateProblemMessageSerialized : "", kafkaResult.getRecordMetadata().offset()); //"AbcEgateConsumer.sendAbcEgateProblemMessage"
                    })
                    .exceptionally(exception -> {
                        log.error("{}: unable to send message=[{}] due to : {}", utilsService.getCurrentAndCallerMethodInfo(), showSentMessage ? finalAbcEgateProblemMessageSerialized : "", ex.getMessage() + " - " + exception.getMessage());
                        return null;
                    });
        } catch (Exception e) {
            log.error("{}: unable to send message with id=[{}] due to : {}", utilsService.getCurrentAndCallerMethodInfo(), showSentMessage ? abcEgateProblemMessageSerialized : "", e.getMessage());
        }
    }

    public void sendAbcEgateProblemMessage(Dossier dossier) {
        AbcEgateProblemMessage abcEgateProblemMessage = new AbcEgateProblemMessage();
        abcEgateProblemMessage.setId(UUID.randomUUID().toString());
        abcEgateProblemMessage.setNodeId(dossier.getNodeId());
        abcEgateProblemMessage.setTimestamp(new Date().toString());
        abcEgateProblemMessage.setPassengerId(dossier.getPassengerId());
        abcEgateProblemMessage.setType(AbcEgateMessageTypeEnum.PROBLEM.getAbcEgateMessageTypeEnumValue());
        abcEgateProblemMessage.setHas_to_be_processed("False");
        abcEgateProblemMessage.setIdChiamante(dossier.getIdChiamante());
        Kiosk egate = kioskRepository.findById(dossier.getNodeId()).orElse(null);
        abcEgateProblemMessage.setNodeName(egate != null ? egate.getIdMachine() : "EES-GATEWAY");
        abcEgateProblemMessage.setIs_entry_node(egate != null ? String.valueOf(egate.getIsEntryEgate()) : "");
        AbcEgateProblemMessage.Content content = new AbcEgateProblemMessage.Content();
        content.setData("True");
        abcEgateProblemMessage.setContent(content);

        String abcEgateProblemMessageSerialized = null;
        try {
            abcEgateProblemMessageSerialized = objectMapper.writeValueAsString(abcEgateProblemMessage);
        } catch (JsonProcessingException e) {
            abcEgateProblemMessageSerialized = abcEgateProblemMessage.getId();
        }
        try {
            String nodeIdForTopic = dossier.getNodeId() != null ? dossier.getNodeId() : unknownFieldValue;
            if (nodeIdForTopic.equals(unknownFieldValue)) {
                nodeIdForTopic = "unknown_node";
            }
            String topicName = kafkaNodeAbcProcessTopicPattern.replace("*", nodeIdForTopic);

            String finalAbcEgateProblemMessageSerialized = abcEgateProblemMessageSerialized;
            sendMessage(topicName, abcEgateProblemMessage)
                    .thenAccept(kafkaResult -> {
                        log.info("{}: sent message=[{}] with offset=[{}]", utilsService.getCurrentAndCallerMethodInfo(), showSentMessage ? finalAbcEgateProblemMessageSerialized : "", kafkaResult.getRecordMetadata().offset()); //"DossierResponseJob.sendAbcEgateProblemMessage"
                    })
                    .exceptionally(exception -> {
                        log.error("{}: unable to send message=[{}] due to : {}", utilsService.getCurrentAndCallerMethodInfo(), showSentMessage ? finalAbcEgateProblemMessageSerialized : "", exception.getMessage());
                        return null;
                    });
        } catch (Exception e) {
            log.error("{}: unable to send message with id=[{}] due to : {}", utilsService.getCurrentAndCallerMethodInfo(), showSentMessage ? abcEgateProblemMessageSerialized : "", e.getMessage());
        }
    }

    public void sendAbcEgateResponseMessage(Dossier dossier, AbcEgateResponseMessageResultEnum result, String details) {
        AbcEgateResponseMessage abcEgateResponseMessage = new AbcEgateResponseMessage();
        abcEgateResponseMessage.setPassengerId(dossier.getCountryIssue().concat(dossier.getDocumentNumber()));
        abcEgateResponseMessage.setResult(result.getAbcEgateResponseMessageResultEnumValue());
        abcEgateResponseMessage.setDetails(details);
        try {
            sendMessage(kafkaNodeAbcResponseTopicPattern.replace("*", dossier.getNodeId()), abcEgateResponseMessage)
                    .thenAccept(kafkaResult -> {
                        log.info("{}: sent message=[{}] with offset=[{}]", utilsService.getCurrentAndCallerMethodInfo(), showSentMessage ? abcEgateResponseMessage : "", kafkaResult.getRecordMetadata().offset()); //sendAbcEgateResponseMessage
                    })
                    .exceptionally(ex -> {
                        log.error("{}: unable to send message=[{}] due to : {}", utilsService.getCurrentAndCallerMethodInfo(), showSentMessage ? abcEgateResponseMessage : "", ex.getMessage());
                        return null;
                    });
        } catch (Exception e) {
            log.error("{}: unable to send message=[{}] due to : {}", utilsService.getCurrentAndCallerMethodInfo(), showSentMessage ? abcEgateResponseMessage : "", e.getMessage());
        }
    }

    public void sendAbcEgateOperazioniAtomicheMessage(Dossier dossier, Boolean isAtomicOperationRequiredInVIS) {
        AbcEgateOperazioniAtomicheMessage abcEgateOperazioniAtomicheMessage = new AbcEgateOperazioniAtomicheMessage();
        abcEgateOperazioniAtomicheMessage.setId(UUID.randomUUID().toString());
        abcEgateOperazioniAtomicheMessage.setNodeId(dossier.getNodeId());
        Kiosk egate = kioskRepository.findById(dossier.getNodeId()).orElse(null);
        abcEgateOperazioniAtomicheMessage.setNodeName(egate != null ? egate.getIdMachine() : "EES-GATEWAY");
        abcEgateOperazioniAtomicheMessage.setIdChiamante(dossier.getIdChiamante());
        abcEgateOperazioniAtomicheMessage.setTimestamp(new Date().toString());
        abcEgateOperazioniAtomicheMessage.setPassengerId(dossier.getPassengerId());
        abcEgateOperazioniAtomicheMessage.setType(AbcEgateMessageTypeEnum.OPERAZIONI_ATOMICHE.getAbcEgateMessageTypeEnumValue());
        abcEgateOperazioniAtomicheMessage.setHas_to_be_processed("False");
        abcEgateOperazioniAtomicheMessage.setIs_entry_node(egate != null ? String.valueOf(egate.getIsEntryEgate()) : "");
        AbcEgateOperazioniAtomicheMessage.Content content = new AbcEgateOperazioniAtomicheMessage.Content();
        content.setData("True");
        content.setInVis(String.valueOf(isAtomicOperationRequiredInVIS));
        abcEgateOperazioniAtomicheMessage.setContent(content);

        String abcEgateOperazioniAtomicheMessageSerialized = null;
        try {
            abcEgateOperazioniAtomicheMessageSerialized = objectMapper.writeValueAsString(abcEgateOperazioniAtomicheMessage);
        } catch (JsonProcessingException e) {
            abcEgateOperazioniAtomicheMessageSerialized = abcEgateOperazioniAtomicheMessage.getId();
        }
        try {
            String nodeIdForTopic = dossier.getNodeId() != null ? dossier.getNodeId() : unknownFieldValue;
            if (nodeIdForTopic.equals(unknownFieldValue)) {
                nodeIdForTopic = "unknown_node";
            }
            String topicName = kafkaNodeAbcProcessTopicPattern.replace("*", nodeIdForTopic);

            String finalAbcEgateOperazioniAtomicheMessageSerialized = abcEgateOperazioniAtomicheMessageSerialized;
            sendMessage(topicName, abcEgateOperazioniAtomicheMessage)
                    .thenAccept(kafkaResult -> {
                        log.info("{}: sent message=[{}] with offset=[{}]", utilsService.getCurrentAndCallerMethodInfo(), showSentMessage ? finalAbcEgateOperazioniAtomicheMessageSerialized : "", kafkaResult.getRecordMetadata().offset()); //sendAbcEgateOperazioniAtomicheMessage
                    })
                    .exceptionally(exception -> {
                        log.error("{}: unable to send message=[{}] due to : {}", utilsService.getCurrentAndCallerMethodInfo(), showSentMessage ? finalAbcEgateOperazioniAtomicheMessageSerialized : "", exception.getMessage());
                        return null;
                    });
        } catch (Exception e) {
            log.error("{}: unable to send message with id=[{}] due to : {}", utilsService.getCurrentAndCallerMethodInfo(), showSentMessage ? abcEgateOperazioniAtomicheMessageSerialized : "", e.getMessage());
        }
    }

}
