package com.reco.ees.gateway.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.scheduling.concurrent.ConcurrentTaskExecutor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executors;

@Configuration
public class TaskExecutorConfig {

    @Value("${spring.threads.type:virtual}")
    private String threadsType;

    @Bean(name = "virtualOrPlatformThreadExecutor")
    public AsyncTaskExecutor taskExecutor() {
        if ("virtual".equalsIgnoreCase(threadsType)) {
            return new ConcurrentTaskExecutor(Executors.newVirtualThreadPerTaskExecutor());
        } else {
            ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
            /*executor.setCorePoolSize(10);
            executor.setMaxPoolSize(20);
            executor.setQueueCapacity(100);*/
            executor.initialize();
            return executor;
        }
    }
}