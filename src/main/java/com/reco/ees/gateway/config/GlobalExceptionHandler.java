/*
package com.reco.ees.gateway.config;

import jakarta.persistence.PersistenceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.context.ApplicationContext;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class GlobalExceptionHandler {

    @Autowired
    private ApplicationContext applicationContext;

    @EventListener
    public void handleContextRefresh(ContextRefreshedEvent event) {
        Thread.setDefaultUncaughtExceptionHandler((thread, throwable) -> {
            if (throwable instanceof PersistenceException) {
                log.error("Database error detected. Shutting down the application.", throwable);
                shutdownApplication();
            }
        });
    }

    private void shutdownApplication() {
        SpringApplication.exit(applicationContext, () -> 1);
        System.exit(1);
    }
}*/
//TODO: scommenta se necessario, eventualmente migliora (almeno cambia eccezione specifica da intercettare) e testalo (utile in rari casi forse non verificabili nella realtà ma solo in test) magari avento 3 istanze up e tanti messaggi in processing, quindi modifica record sifAuth e nel peggiore dei casi (anche se non dovrebbe accadere) vedrai tanti errori relativi al DB senza che l'app si arresti (ricorda che sarà automaticamente riavviata da Docker) quindi con tale metodo l'app dovrebbe arrestarsi.
