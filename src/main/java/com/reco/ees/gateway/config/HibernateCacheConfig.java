package com.reco.ees.gateway.config;

import com.reco.ees.gateway.repository.*;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import jakarta.persistence.EntityManagerFactory;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.SessionFactory;
import org.hibernate.cache.spi.CacheImplementor;
import org.hibernate.stat.Statistics;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.support.TransactionTemplate;

import javax.cache.Cache;
import javax.cache.CacheManager;
import javax.cache.configuration.MutableCacheEntryListenerConfiguration;
import javax.cache.event.CacheEntryEvent;
import javax.cache.event.CacheEntryExpiredListener;
import javax.cache.event.CacheEntryRemovedListener;
import java.util.Arrays;
import java.util.List;

@Slf4j
@Configuration
@AutoConfigureAfter({JCacheConfig.class, HibernateJpaAutoConfiguration.class})
@ConditionalOnProperty(name = "spring.jpa.properties.hibernate.cache.use_second_level_cache", havingValue = "true")
public class HibernateCacheConfig {

    @Autowired
    private EntityManagerFactory entityManagerFactory;

    @Autowired
    private CacheManager jCacheManager; // Inietta javax.cache.CacheManager (sarà quello primario da JCacheConfig)

    // Repository per il pre-caricamento
    @Autowired private SifParametersRepository sifParametersRepository;
    @Autowired private SifCountryCodeRepository sifCountryCodeRepository;
    @Autowired private SifAddDataRequestRepository sifAddDataRequestRepository;
    @Autowired private SifGenericCodeRepository sifGenericCodeRepository;
    @Autowired private ParameterRepository parameterRepository; // Per warm-up specifico di Parameter

    @Autowired
    private PlatformTransactionManager transactionManager;

    @Value("${spring.jpa.properties.hibernate.cache.use_query_cache:true}")
    private boolean queryCacheEnabled;

    // Nomi delle regioni cache ENTITÀ
    public static final String SIF_PARAMETER_REGION_NAME = "com.reco.ees.gateway.repository.model.SifParameter";
    public static final String SIF_COUNTRY_CODE_REGION_NAME = "com.reco.ees.gateway.repository.model.SifCountryCode";
    public static final String SIF_ADD_DATA_REQUEST_REGION_NAME = "com.reco.ees.gateway.repository.model.SifAddDataRequest";
    public static final String SIF_GENERIC_CODE_REGION_NAME = "com.reco.ees.gateway.repository.model.SifGenericCode";
    public static final String PARAMETER_REGION_NAME = "com.reco.ees.gateway.repository.model.Parameter";
    public static final String KIOSK_REGION_NAME = "kioskCache";
    public static final String SIF_AUTH_REGION_NAME = "sifAuthCache";

    // Nomi delle regioni cache QUERY
    public static final String SIF_PARAMETER_BY_TYPE_REGION_NAME = "sifParameterByType";
    public static final String SIF_COUNTRY_CODE_BY_KIOSK_VALUE_REGION_NAME = "sifCountryCodeByKioskValue";
    public static final String KIOSK_QUERIES_REGION_NAME = "kioskQueries";
    public static final String SIF_AUTH_QUERIES_REGION_NAME = "sifAuthQueries";
    public static final String SIF_ADD_DATA_REQUEST_QUERIES_REGION_NAME = "sifAddDataRequestQueries";
    public static final String SIF_GENERIC_CODE_QUERIES_REGION_NAME = "sifGenericCodeQueries";
    public static final String SIF_PARAMETERS_FIND_ALL_QUERIES_REGION_NAME = "sifParametersFindAllQueries";
    public static final String SIF_COUNTRY_CODE_FIND_ALL_QUERIES_REGION_NAME = "sifCountryCodeFindAllQueries";
    public static final String PARAMETER_FIND_ALL_QUERIES_REGION_NAME = "parameterFindAllQueries";
    
    //TODO: se vuoi estendi uso cache (Spring Caching + Hibernate L2 Caching con Caffeine) per altre query e/o entita' e aggiorna i nomi delle regioni cache, etc
    // (attento a nativeQueries, non jpa/jdbcTemplate-like queries, lazy loading entita' relate, entita' relate, evict manuale in caso di cambiamenti inattesi ai dati, impossibilita' di fare evict per dati variati manualmente e direttamente in DB, bulk update query su db (esempio: "UPDATE User u SET u.status = :s WHERE u.active = false"), etc)


    @PostConstruct
    public void initializeCachesAndWarmUp() {
        verifyCacheInitialization();
        warmUpStaticEntityCaches();
    }

    public void verifyCacheInitialization() {
        log.info("Verifying Hibernate L2 cache regions post-initialization... Query Cache Enabled: {}", queryCacheEnabled);
        SessionFactory sessionFactory = entityManagerFactory.unwrap(SessionFactory.class);
        if (sessionFactory == null) {
            log.warn("SessionFactory not available for cache verification.");
            return;
        }
        CacheImplementor hibernateCache = (CacheImplementor) sessionFactory.getCache();

        String[] entityCacheNames = {
                SIF_PARAMETER_REGION_NAME, SIF_COUNTRY_CODE_REGION_NAME,
                SIF_ADD_DATA_REQUEST_REGION_NAME, SIF_GENERIC_CODE_REGION_NAME,
                PARAMETER_REGION_NAME,
                KIOSK_REGION_NAME, SIF_AUTH_REGION_NAME
        };

        log.info("--- Verifying Entity Cache Regions ---");
        for (String cacheName : entityCacheNames) {
            checkCacheRegion(hibernateCache, cacheName, true);
        }

        String[] queryCacheNames = {
                SIF_PARAMETER_BY_TYPE_REGION_NAME, SIF_COUNTRY_CODE_BY_KIOSK_VALUE_REGION_NAME,
                KIOSK_QUERIES_REGION_NAME, SIF_AUTH_QUERIES_REGION_NAME,
                SIF_ADD_DATA_REQUEST_QUERIES_REGION_NAME, SIF_GENERIC_CODE_QUERIES_REGION_NAME,
                SIF_PARAMETERS_FIND_ALL_QUERIES_REGION_NAME,
                SIF_COUNTRY_CODE_FIND_ALL_QUERIES_REGION_NAME,
                PARAMETER_FIND_ALL_QUERIES_REGION_NAME
        };

        if (queryCacheEnabled) {
            log.info("--- Verifying Query Cache Regions (NOTE: may not be initialized by Hibernate yet at this stage) ---");
            for (String cacheName : queryCacheNames) {
                checkCacheRegion(hibernateCache, cacheName, false);
            }
        } else {
            log.info("--- Query Cache is DISABLED. Skipping verification of specific query cache regions: {} ---", Arrays.toString(queryCacheNames));
        }

        log.info("--- Verifying Default Hibernate Cache Regions ---");
        String defaultTimestampsRegion = "default-update-timestamps-region";
        String defaultQueryResultsRegion = "default-query-results-region";

        // default-update-timestamps-region
        if (jCacheManager.getCache(defaultTimestampsRegion) != null) {
            log.info("JCacheManager has cache: '{}'.", defaultTimestampsRegion);
        } else {
            if (queryCacheEnabled) { // Se la query cache è attiva, ci si aspetterebbe questa regione
                log.warn("JCacheManager does NOT have cache: '{}'. This is unexpected when Query Cache is ENABLED and needs investigation if Hibernate DEBUG logs do not confirm its usage.", defaultTimestampsRegion);
            } else {
                log.info("JCacheManager does NOT have cache: '{}'. This might be expected as Query Cache is DISABLED.", defaultTimestampsRegion);
            }
        }

        // default-query-results-region
        if (queryCacheEnabled) {
            if (jCacheManager.getCache(defaultQueryResultsRegion) != null) {
                log.info("JCacheManager has cache: '{}'", defaultQueryResultsRegion);
                if (hibernateCache.getRegion(defaultQueryResultsRegion) != null) {
                    log.info("Hibernate has recognized cache region: '{}'.", defaultQueryResultsRegion);
                } else {
                    log.warn("Hibernate has NOT recognized cache region: '{}' from its perspective, though JCacheManager has it. Check Hibernate DEBUG logs for its usage.", defaultQueryResultsRegion);
                }
            } else {
                log.warn("JCacheManager does NOT have cache: '{}', but Query Cache is ENABLED. This is unexpected.", defaultQueryResultsRegion);
            }
        } else {
            if (jCacheManager.getCache(defaultQueryResultsRegion) == null) {
                log.info("JCacheManager does NOT have cache: '{}', which is expected as Query Cache is DISABLED.", defaultQueryResultsRegion);
            } else {
                log.warn("JCacheManager has cache: '{}', but Query Cache is DISABLED. This is unusual.", defaultQueryResultsRegion);
            }
        }
        log.info("Hibernate L2 cache region verification in HibernateCacheConfig completed.");
    }

    private void checkCacheRegion(CacheImplementor hibernateCache, String cacheName, boolean isEntityRegion) {
        if (jCacheManager.getCache(cacheName) != null) {
            log.info("JCacheManager has cache: '{}'", cacheName);
            if (hibernateCache.getRegion(cacheName) != null) {
                log.info("Hibernate has recognized cache region: '{}'. Cache is likely operational.", cacheName);
            } else { // Hibernate non riconosce la regione
                if (isEntityRegion) {
                    log.warn("Hibernate has NOT recognized entity cache region: '{}' from its perspective, though JCacheManager has it. This needs investigation.", cacheName);
                } else { // È una regione di query
                    if (queryCacheEnabled) {
                        log.info("Hibernate has NOT recognized query cache region: '{}' from its perspective during this check (though JCacheManager has it). This is often due to startup timing. Verify with runtime statistics.", cacheName);
                    } else {
                        // Se la query cache è disabilitata, è normale che Hibernate non la riconosca.
                        log.info("Hibernate has NOT recognized query cache region: '{}', which is expected as Query Cache is DISABLED.", cacheName);
                    }
                }
            }
        } else { // JCacheManager non ha la cache
            // Warn solo se ci si aspetta la cache (entità o query cache abilitata)
            if (isEntityRegion || (queryCacheEnabled && !isEntityRegion)) {
                log.warn("JCacheManager does NOT have cache: '{}'. This is unexpected if JCacheConfig ran correctly and properties are set.", cacheName);
            } else {
                log.info("JCacheManager does NOT have cache: '{}', which is expected as Query Cache is DISABLED for this query region.", cacheName);
            }
        }
    }

    public void warmUpStaticEntityCaches() {
        TransactionTemplate transactionTemplate = new TransactionTemplate(transactionManager);
        transactionTemplate.setReadOnly(true);

        transactionTemplate.execute(status -> {
            log.info("Warming up L2 cache for static entities (entities and their findAll queries)...");
            try {
                log.debug("Loading all SifParameter entities to L2 cache...");
                sifParametersRepository.findAll().forEach(p -> {}); // Popola cache entità e query 'sifParametersFindAllQueries'
                log.info("SifParameter entities warmed up for L2 cache ({} entities). Query '{}' also warmed up.", 
                        sifParametersRepository.count(), SIF_PARAMETERS_FIND_ALL_QUERIES_REGION_NAME);
            } catch (Exception e) {
                log.error("Error warming up SifParameter L2 cache", e);
            }

            try {
                log.debug("Loading all SifCountryCode entities to L2 cache...");
                sifCountryCodeRepository.findAll().forEach(c -> {}); // Popola cache entità e query 'sifCountryCodeFindAllQueries'
                log.info("SifCountryCode entities warmed up for L2 cache ({} entities). Query '{}' also warmed up.", 
                        sifCountryCodeRepository.count(), SIF_COUNTRY_CODE_FIND_ALL_QUERIES_REGION_NAME);
            } catch (Exception e) {
                log.error("Error warming up SifCountryCode L2 cache", e);
            }

            try {
                log.debug("Loading all SifAddDataRequest entities to L2 cache...");
                sifAddDataRequestRepository.findAll().forEach(a -> {}); // Popola cache entità e query 'sifAddDataRequestQueries'
                log.info("SifAddDataRequest entities warmed up for L2 cache ({} entities). Query '{}' also warmed up.", 
                        sifAddDataRequestRepository.count(), SIF_ADD_DATA_REQUEST_QUERIES_REGION_NAME);
            } catch (Exception e) {
                log.error("Error warming up SifAddDataRequest L2 cache", e);
            }

            try {
                log.debug("Loading all SifGenericCode entities to L2 cache...");
                sifGenericCodeRepository.findAll().forEach(p -> {}); // Popola cache entità e query 'sifGenericCodeQueries'
                log.info("SifGenericCode entities warmed up for L2 cache ({} entities). Query '{}' also warmed up.", 
                        sifGenericCodeRepository.count(), SIF_GENERIC_CODE_QUERIES_REGION_NAME);
            } catch (Exception e) {
                log.error("Error warming up SifGenericCode L2 cache", e);
            }

            warmUpSpecificParameterCache(); // Chiamato anche qui per coerenza

            log.info("L2 cache warm-up for static entities finished.");
            return null;
        });
    }

    public void warmUpSpecificParameterCache() {
        TransactionTemplate transactionTemplate = new TransactionTemplate(transactionManager);
        transactionTemplate.setReadOnly(true);
        transactionTemplate.execute(status -> {
            try {
                log.debug("Specifically loading all Parameter entities to L2 cache (entities and findAll query)...");
                parameterRepository.findAll().forEach(p -> {}); // Assicura il caricamento di entità e query 'parameterFindAllQueries'
                log.info("Parameter entities specifically warmed up for L2 cache ({} entities). Query '{}' also warmed up.", 
                        parameterRepository.count(), PARAMETER_FIND_ALL_QUERIES_REGION_NAME);
            } catch (Exception e) {
                log.error("Error during specific warm-up of Parameter L2 cache", e);
            }
            return null;
        });
    }


    @Bean
    public Statistics hibernateStatistics() {
        SessionFactory sessionFactory = entityManagerFactory.unwrap(SessionFactory.class);
        Statistics statistics = sessionFactory.getStatistics();
        if (!statistics.isStatisticsEnabled()) {
            log.warn("Hibernate statistics were not enabled via properties. Enabling them programmatically. " +
                    "Ensure 'spring.jpa.properties.hibernate.generate_statistics=true' is set.");
            statistics.setStatisticsEnabled(true);
        }
        return statistics;
    }

    @Scheduled(fixedDelayString = "${cache.reset.fixedDelay:3600000}",
            initialDelayString = "${cache.reset.initialDelay:3600000}")
    public void resetCaches() {
        log.info("Resetting all Hibernate L2 caches...");
        
        // Ottieni il CacheManager
        CacheManager cacheManager = jCacheManager;
        if (cacheManager == null) {
            log.warn("CacheManager is null, cannot reset caches");
            return;
        }
        
        // Elenco di tutte le regioni di cache da pulire
        List<String> cacheRegions = Arrays.asList(
            "default-update-timestamps-region",
            "default-query-results-region",
            SIF_AUTH_REGION_NAME,
            KIOSK_REGION_NAME,
            SIF_PARAMETER_REGION_NAME,
            SIF_COUNTRY_CODE_REGION_NAME,
            SIF_ADD_DATA_REQUEST_REGION_NAME,
            SIF_GENERIC_CODE_REGION_NAME,
            PARAMETER_REGION_NAME,
            SIF_PARAMETER_BY_TYPE_REGION_NAME,
            SIF_COUNTRY_CODE_BY_KIOSK_VALUE_REGION_NAME,
            SIF_AUTH_QUERIES_REGION_NAME,
            KIOSK_QUERIES_REGION_NAME,
            SIF_PARAMETERS_FIND_ALL_QUERIES_REGION_NAME,
            SIF_COUNTRY_CODE_FIND_ALL_QUERIES_REGION_NAME,
            SIF_ADD_DATA_REQUEST_QUERIES_REGION_NAME,
            PARAMETER_FIND_ALL_QUERIES_REGION_NAME,
            SIF_GENERIC_CODE_QUERIES_REGION_NAME
        );
        
        // Pulisci ogni regione di cache
        for (String regionName : cacheRegions) {
            Cache<Object, Object> cache = cacheManager.getCache(regionName);
            if (cache != null) {
                cache.clear();
                log.info("Cleared cache region: {}", regionName);
            } else {
                log.debug("Cache region not found: {}", regionName);
            }
        }
        
        // Ricarica i dati statici
        warmUpStaticEntityCaches();
        
        log.info("All Hibernate L2 caches have been reset and static data reloaded");
    }

    public void evictSpecificCacheRegion(String regionName) {
        SessionFactory sessionFactory = entityManagerFactory.unwrap(SessionFactory.class);
        if (sessionFactory != null && sessionFactory.getCache() != null) {
            log.info("Attempting to manually evict cache region: {}", regionName);
            sessionFactory.getCache().evictRegion(regionName);
            log.info("Cache region {} evicted.", regionName);
        } else {
            log.warn("Cannot evict cache region {}: SessionFactory or its L2 cache is null.", regionName);
        }
    }

    public void evictSpecificEntityFromCache(Class<?> entityClass, String entityId) {
        SessionFactory sessionFactory = entityManagerFactory.unwrap(SessionFactory.class);
        if (sessionFactory != null && sessionFactory.getCache() != null) {
            log.info("Attempting to manually evict entity {}#{}", entityClass.getSimpleName(), entityId);
            sessionFactory.getCache().evict(entityClass, entityId); // Usa evict(Class, Serializable)
            log.info("Entity {}#{} evicted.", entityClass.getSimpleName(), entityId);
        } else {
            log.warn("Cannot evict entity {}#{}: SessionFactory or its L2 cache is null.", entityClass.getSimpleName(), entityId);
        }
    }

    @PreDestroy
    public void cleanupCache() {
        log.info("HibernateCacheConfig: @PreDestroy actions. The main CacheManager will be closed by Spring.");
        try {
            SessionFactory sessionFactory = entityManagerFactory.unwrap(SessionFactory.class);
            if (sessionFactory != null && !sessionFactory.isClosed() && sessionFactory.getStatistics().isStatisticsEnabled()) {
                sessionFactory.getStatistics().setStatisticsEnabled(false);
                log.info("Hibernate statistics disabled via HibernateCacheConfig @PreDestroy.");
            }
        } catch (Exception e) {
            log.warn("Error during Hibernate L2 cache cleanup in HibernateCacheConfig @PreDestroy: {}", e.getMessage());
        }
    }

    @EventListener(ContextRefreshedEvent.class)
    public void handleCacheErrors() {
        // gestore di errori per la cache
        try {
            CacheManager cacheManager = jCacheManager;
            if (cacheManager != null) {
                for (String cacheName : cacheManager.getCacheNames()) {
                    Cache<Object, Object> cache = cacheManager.getCache(cacheName);
                    if (cache != null) {
                        // listener per errori di cache
                        cache.registerCacheEntryListener(new MutableCacheEntryListenerConfiguration<>(
                            () -> (CacheEntryExpiredListener<Object, Object>) events -> {
                                for (CacheEntryEvent<?, ?> event : events) {
                                    log.debug("Cache entry expired in region {}: key={}", cacheName, event.getKey());
                                }
                            },
                            null, true, false));
                        
                        // listener per gli eventi di rimozione
                        cache.registerCacheEntryListener(new MutableCacheEntryListenerConfiguration<>(
                            () -> (CacheEntryRemovedListener<Object, Object>) events -> {
                                for (CacheEntryEvent<?, ?> event : events) {
                                    log.debug("Cache entry removed from region {}: key={}", cacheName, event.getKey());
                                }
                            },
                            null, true, false));
                    }
                }
            }
        } catch (Exception e) {
            log.warn("Failed to register cache error handlers: {}", e.getMessage());
        }
    }
}
