package com.reco.ees.gateway.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class KafkaRetryService { //tenendo conto dell'intera configurazione kafka, usa acknowledgment.nack(java.time.Duration.ofSeconds(X)) per rileggere i messaggi dopo X secondi
    private static class RetryInfo {
        private final int count;
        private final long timestamp;
        public RetryInfo(int count) {
            this.count = count;
            this.timestamp = System.currentTimeMillis();
        }
    }

    private final ConcurrentMap<String, RetryInfo> retryCountMap = new ConcurrentHashMap<>(); //potrei usare Kafka Dead Letter Topics al posto di questo ma la complessità aumenterebbe esageratamente per il semplice utilizzo che ne farei

    public int getAndIncrementRetryCount(String messageId) {
        //cleanupIfNeeded();
        return retryCountMap.compute(messageId, (id, info) -> {
            if (info == null) return new RetryInfo(1);
            else return new RetryInfo(info.count + 1);
        }).count;
    }

    public void clearRetryCount(String messageId) {
        retryCountMap.remove(messageId);
    }

    @Scheduled(cron = "0 0 0 */7 * *") // A mezzanotte ogni 7 giorni
    public void scheduledCleanup() {
        int beforeSize = retryCountMap.size();
        cleanupOldEntries();
        int removedCount = beforeSize - retryCountMap.size();
        if (removedCount > 0) {
            log.info("KafkaRetryService cleaned up {} stale entries from retry count map", removedCount);
        }
    }

    private void cleanupOldEntries() {
        long now = System.currentTimeMillis();
        long maxAgeMillis = TimeUnit.DAYS.toMillis(2); //rimuovo elementi più vecchi di 2 giorni per evitare eventuali (remoti) memory leak (rari anche perchè la retryCountMap si azzera al riavvio dell'app ovviamente e se non viene riavviata allora dovrebbe essere stato già effettuato ack dopo 3 tentativi di nack e riconsumo)
        retryCountMap.entrySet().removeIf(entry -> (now - entry.getValue().timestamp) > maxAgeMillis);
    }

    /*private void cleanupIfNeeded() {
        // 10% chance of cleaning on each operation to reduce overhead
        if (Math.random() < 0.1) {
            cleanupOldEntries();
        }
    }*/

    /*public int getRetryMapSize() {
        return retryCountMap.size();
    }*/
}