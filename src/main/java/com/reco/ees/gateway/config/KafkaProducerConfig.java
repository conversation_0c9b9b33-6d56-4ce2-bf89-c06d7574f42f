package com.reco.ees.gateway.config;

import com.reco.ees.gateway.enums.ParametersEnum;
import com.reco.ees.gateway.repository.ParameterRepository;
import com.reco.ees.gateway.repository.model.Parameter;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;
import org.springframework.kafka.support.ProducerListener;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Configuration
@Order(4)
public class KafkaProducerConfig {
    private final AsyncTaskExecutor virtualOrPlatformThreadExecutor;

    private final ParameterRepository parameterRepository;

    @Value("${spring.threads.type:virtual}")
    private String threadsType;

    @Value("${kafka.bootstrapAddress}")
    private String defaultBootstrapAddress;

    public KafkaProducerConfig(@Qualifier("virtualOrPlatformThreadExecutor") AsyncTaskExecutor virtualOrPlatformThreadExecutor, ParameterRepository parameterRepository) {
        this.virtualOrPlatformThreadExecutor = virtualOrPlatformThreadExecutor;
        this.parameterRepository = parameterRepository;
    }

    @Bean
    public ProducerFactory<String, String> producerFactory() { //TODO: ATTENZIONE: questo topic non viene ri-creato automaticamente quando cancellato e riavviata l'app (questo accade anche per registration o solo per questo topic e solo a Brindisi???)
        Map<String, Object> configProps = new HashMap<>();
        String bootstrapAddress = parameterRepository.findById(ParametersEnum.KAFKA_BOOSTRAP_ADDRESS.getParametersEnumValue())
                .map(Parameter::getValue)
                .orElse(defaultBootstrapAddress);
        configProps.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapAddress);
        configProps.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        configProps.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        configProps.put(ProducerConfig.MAX_REQUEST_SIZE_CONFIG, "262144000"); //nuovi valori di conf applicabili solo a nuovi topic o modificando una tantum il topic esistente con KafkaAdmin (da codice o interfaccia) //TODO: parametrizza questo (ora impostato a 256MB, prima era 20MB cioè 20971520: credo che sia necessario aumentare almeno questo (altrimenti non riesco a inviare in coda il file LDIF ricevuto per DS_AND_RL_CERTIFICATE infatti rimane certificato in stato 4))
        configProps.put(ProducerConfig.BUFFER_MEMORY_CONFIG, "1048576000"); //TODO: parametrizza con valore almeno doppio (ora quadruplo) rispetto a MAX_REQUEST_SIZE_CONFIG
        return new DefaultKafkaProducerFactory<>(configProps);
    }

    @Bean
    public KafkaTemplate<String, String> kafkaTemplate() {
        KafkaTemplate<String, String> kafkaTemplate = new KafkaTemplate<>(producerFactory());
        if ("virtual".equalsIgnoreCase(threadsType)) {
            log.info("Using virtual threads for Kafka producer");
            kafkaTemplate.setProducerListener(new ProducerListener<String, String>() {
                public void onSuccess(RecordMetadata recordMetadata, String topic, Integer partition, String key, String value) {
                    virtualOrPlatformThreadExecutor.execute(() -> {
                        // handle success
                    });
                }

                public void onError(RecordMetadata recordMetadata, String topic, Integer partition, String key, String value, Exception exception) {
                    virtualOrPlatformThreadExecutor.execute(() -> {
                        // handle error
                    });
                }
            });
        } else log.info("Using platform threads for Kafka producer");
        return kafkaTemplate;
    }
}